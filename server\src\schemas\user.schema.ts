import { FastifySchema } from 'fastify';
import { Type } from '@sinclair/typebox';

/**
 * User registration schema
 */
export const registerUserSchema: FastifySchema = {
  body: Type.Object({
    username: Type.String({ minLength: 3, maxLength: 30 }),
    email: Type.String({ pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$' }),
    password: Type.String({ minLength: 8 }),
    firstName: Type.String(),
    lastName: Type.String(),
    displayName: Type.Optional(Type.String()),
  }),
  response: {
    201: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      user: Type.Object({
        id: Type.String(),
        username: Type.String(),
        email: Type.String(),
        firstName: Type.String(),
        lastName: Type.String(),
        displayName: Type.String(),
        createdAt: Type.String({ format: 'date-time' }),
      }),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      error: Type.Optional(Type.String()),
    }),
  },
};

/**
 * User login schema
 */
export const loginUserSchema: FastifySchema = {
  body: Type.Object({
    email: Type.String({ pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$' }),
    password: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      token: Type.String(),
      user: Type.Object({
        id: Type.String(),
        username: Type.String(),
        email: Type.String(),
        firstName: Type.String(),
        lastName: Type.String(),
        displayName: Type.String(),
        roles: Type.Array(Type.String()),
      }),
    }),
    401: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      error: Type.Optional(Type.String()),
    }),
  },
};

/**
 * Get user profile schema
 */
export const getUserProfileSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      user: Type.Object({
        id: Type.String(),
        username: Type.String(),
        email: Type.String(),
        firstName: Type.String(),
        lastName: Type.String(),
        displayName: Type.String(),
        avatar: Type.Optional(Type.String()),
        roles: Type.Array(Type.String()),
        createdAt: Type.String({ format: 'date-time' }),
        updatedAt: Type.String({ format: 'date-time' }),
      }),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Update user profile schema
 */
export const updateUserProfileSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  body: Type.Object({
    firstName: Type.Optional(Type.String()),
    lastName: Type.Optional(Type.String()),
    displayName: Type.Optional(Type.String()),
    avatar: Type.Optional(Type.String()),
    phone: Type.Optional(Type.String()),
    language: Type.Optional(Type.String()),
    timezone: Type.Optional(Type.String()),
    preferences: Type.Optional(
      Type.Object({
        theme: Type.Optional(Type.Enum({ light: 'light', dark: 'dark', system: 'system' })),
        emailNotifications: Type.Optional(
          Type.Object({
            marketing: Type.Optional(Type.Boolean()),
            account: Type.Optional(Type.Boolean()),
            security: Type.Optional(Type.Boolean()),
            social: Type.Optional(Type.Boolean()),
          })
        ),
        pushNotifications: Type.Optional(
          Type.Object({
            messages: Type.Optional(Type.Boolean()),
            comments: Type.Optional(Type.Boolean()),
            followers: Type.Optional(Type.Boolean()),
            videoUploads: Type.Optional(Type.Boolean()),
          })
        ),
      })
    ),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      user: Type.Object({
        id: Type.String(),
        username: Type.String(),
        email: Type.String(),
        firstName: Type.String(),
        lastName: Type.String(),
        displayName: Type.String(),
        avatar: Type.Optional(Type.String()),
        updatedAt: Type.String({ format: 'date-time' }),
      }),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Change password schema
 */
export const changePasswordSchema: FastifySchema = {
  body: Type.Object({
    currentPassword: Type.String(),
    newPassword: Type.String({ minLength: 8 }),
    confirmPassword: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    401: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Get all users schema (admin)
 */
export const getAllUsersSchema: FastifySchema = {
  querystring: Type.Object({
    page: Type.Optional(Type.Number({ minimum: 1, default: 1 })),
    limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100, default: 10 })),
    search: Type.Optional(Type.String()),
    status: Type.Optional(Type.String()),
    role: Type.Optional(Type.String()),
    sortBy: Type.Optional(Type.String()),
    sortOrder: Type.Optional(Type.Enum({ asc: 'asc', desc: 'desc' })),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      users: Type.Array(
        Type.Object({
          id: Type.String(),
          username: Type.String(),
          email: Type.String(),
          firstName: Type.String(),
          lastName: Type.String(),
          displayName: Type.String(),
          status: Type.String(),
          roles: Type.Array(Type.String()),
          createdAt: Type.String({ format: 'date-time' }),
        })
      ),
      pagination: Type.Object({
        total: Type.Number(),
        page: Type.Number(),
        limit: Type.Number(),
        pages: Type.Number(),
      }),
    }),
  },
};

/**
 * Delete user schema (admin)
 */
export const deleteUserSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Update user status schema (admin)
 */
export const updateUserStatusSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  body: Type.Object({
    status: Type.Enum({ active: 'active', pending: 'pending', suspended: 'suspended', banned: 'banned' }),
    statusReason: Type.Optional(Type.String()),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      user: Type.Object({
        id: Type.String(),
        username: Type.String(),
        status: Type.String(),
        statusReason: Type.Optional(Type.String()),
      }),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};
