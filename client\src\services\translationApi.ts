import axios from 'axios';

// API base URL
const API_BASE_URL = '/api/v1/translation';

/**
 * Interface for translation request
 */
interface TranslationRequest {
  text: string;
  sourceLanguage: string;
  targetLanguage: string;
  userId?: string;
  ulcaApiKey?: string;
  usePipeline?: boolean;
  method?: 'bhashini' | 'google' | 'best';
}

/**
 * Interface for translation response
 */
interface TranslationResponse {
  success: boolean;
  data: {
    translatedText: string;
    sourceLanguage: string;
    targetLanguage: string;
  };
}

/**
 * Interface for language detection request
 */
interface LanguageDetectionRequest {
  text: string;
  userId?: string;
  ulcaApiKey?: string;
}

/**
 * Interface for language detection response
 */
interface LanguageDetectionResponse {
  success: boolean;
  data: {
    detectedLanguage: string;
    confidence: number;
  };
}

/**
 * Interface for API test request
 */
interface TestConnectionRequest {
  userId?: string;
  ulcaApiKey?: string;
}

/**
 * Interface for API test response
 */
interface TestConnectionResponse {
  success: boolean;
  message: string;
}

/**
 * Translate text using the server API
 * @param text Text to translate
 * @param sourceLanguage Source language code (ISO)
 * @param targetLanguage Target language code (ISO)
 * @param userId Bhashini User ID (optional)
 * @param ulcaApiKey Bhashini ULCA API Key (optional)
 * @returns Translated text
 */
export async function translateText(
  text: string,
  sourceLanguage: string,
  targetLanguage: string,
  userId?: string,
  ulcaApiKey?: string,
  usePipeline: boolean = true,
  method: 'bhashini' | 'google' | 'best' = 'best'
): Promise<string> {
  try {
    // If source and target languages are the same, return the original text
    if (sourceLanguage === targetLanguage) {
      return text;
    }

    // Use default credentials if not provided
    const bhashiniUserId = userId || 'cee60134c6bb4d179efd3fda48ff32fe';
    const bhashiniUlcaApiKey = ulcaApiKey || '13a647c84b-2747-4f0c-afcd-2ac8235f5318';

    console.log(`Translating text using method: ${method}`);

    const response = await axios.post<TranslationResponse>(
      `${API_BASE_URL}/translate`,
      {
        text,
        sourceLanguage,
        targetLanguage,
        userId: bhashiniUserId,
        ulcaApiKey: bhashiniUlcaApiKey,
        usePipeline,
        method
      }
    );

    if (response.data && response.data.success && response.data.data) {
      return response.data.data.translatedText;
    }

    throw new Error('Invalid response from translation API');
  } catch (error) {
    console.error('Error translating text:', error);

    // Add more detailed error logging
    if (axios.isAxiosError(error)) {
      console.error('API Error details:', error.response?.data);
      console.error('API Error status:', error.response?.status);
      console.error('API Error headers:', error.response?.headers);

      // Return more specific error message
      return `[Translation Error: ${error.response?.status} - ${error.response?.data?.message || error.message}] ${text}`;
    }

    // Return original text with error indicator if translation fails
    return `[Translation Error: ${error instanceof Error ? error.message : 'Unknown error'}] ${text}`;
  }
}

/**
 * Detect language of text using the server API
 * @param text Text to detect language for
 * @param userId Bhashini User ID (optional)
 * @param ulcaApiKey Bhashini ULCA API Key (optional)
 * @returns Detected language code
 */
export async function detectLanguage(
  text: string,
  userId?: string,
  ulcaApiKey?: string
): Promise<{ detectedLanguage: string; confidence: number }> {
  try {
    const response = await axios.post<LanguageDetectionResponse>(
      `${API_BASE_URL}/detect-language`,
      {
        text,
        userId,
        ulcaApiKey
      }
    );

    if (response.data && response.data.success && response.data.data) {
      return {
        detectedLanguage: response.data.data.detectedLanguage,
        confidence: response.data.data.confidence
      };
    }

    throw new Error('Invalid response from language detection API');
  } catch (error) {
    console.error('Error detecting language:', error);
    throw new Error('Failed to detect language');
  }
}

/**
 * Test connection to the Bhashini API
 * @param userId Bhashini User ID (optional)
 * @param ulcaApiKey Bhashini ULCA API Key (optional)
 * @returns Test result
 */
export async function testConnection(
  userId?: string,
  ulcaApiKey?: string
): Promise<{ success: boolean; message: string }> {
  try {
    const response = await axios.post<TestConnectionResponse>(
      `${API_BASE_URL}/test-connection`,
      {
        userId,
        ulcaApiKey
      }
    );

    return {
      success: response.data.success,
      message: response.data.message
    };
  } catch (error: any) {
    console.error('Error testing Bhashini API connection:', error);
    return {
      success: false,
      message: error.response?.data?.message || 'Failed to connect to Bhashini API'
    };
  }
}

export default {
  translateText,
  detectLanguage,
  testConnection
};
