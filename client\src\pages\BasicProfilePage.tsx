import React from 'react';
import { useAuth } from '@/context/AuthContext';
import Navbar from '@/components/layout/Navbar';
import { Link, useNavigate } from 'react-router-dom';
import { LayoutDashboard } from 'lucide-react';

export default function BasicProfilePage() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="container mx-auto py-8">
          <div className="bg-card p-6 rounded-lg shadow">
            <h2 className="text-xl font-bold mb-2">Please Sign In</h2>
            <p className="mb-4">You need to be signed in to view your profile</p>
            <Link to="/signin" className="bg-primary text-white px-4 py-2 rounded">Sign In</Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <div className="container mx-auto py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Sidebar */}
          <div className="md:col-span-1">
            <div className="bg-card p-6 rounded-lg shadow">
              <div className="flex flex-col items-center mb-6">
                <div className="w-24 h-24 rounded-full bg-primary/20 flex items-center justify-center mb-4">
                  <span className="text-2xl font-bold">{currentUser.username.charAt(0).toUpperCase()}</span>
                </div>
                <h2 className="text-xl font-bold">{currentUser.username}</h2>
                <p className="text-muted-foreground">{currentUser.email || 'No email provided'}</p>
              </div>
              
              <div className="border-t pt-4 mt-4">
                <h3 className="font-medium mb-2">Account Type</h3>
                <div className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm inline-block">
                  Regular User
                </div>
              </div>
              
              {isAdmin && (
                <div className="border-t pt-4 mt-4">
                  <h3 className="font-medium mb-2 flex items-center">
                    <LayoutDashboard className="h-4 w-4 mr-2" /> Admin Access
                  </h3>
                  <div className="flex items-center gap-2">
                    <div className="bg-blue-500/20 text-blue-500 text-xs px-2 py-1 rounded-full">
                      Admin User
                    </div>
                    <button 
                      onClick={() => navigate('/admin')}
                      className="text-blue-500 text-sm hover:underline"
                    >
                      Go to Admin Panel
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Main Content */}
          <div className="md:col-span-2">
            <div className="bg-card p-6 rounded-lg shadow">
              <h2 className="text-xl font-bold mb-4">Profile Settings</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Username</label>
                  <input 
                    type="text" 
                    value={currentUser.username}
                    readOnly
                    className="w-full p-2 rounded border bg-background"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Email</label>
                  <input 
                    type="email" 
                    value={currentUser.email || ''}
                    readOnly
                    className="w-full p-2 rounded border bg-background"
                  />
                </div>
                
                {isAdmin && (
                  <div className="mt-6 pt-6 border-t">
                    <h3 className="text-lg font-medium mb-2 flex items-center">
                      <LayoutDashboard className="h-5 w-5 mr-2" /> Admin Options
                    </h3>
                    <button 
                      onClick={() => navigate('/admin')}
                      className="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                    >
                      Go to Admin Panel
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
