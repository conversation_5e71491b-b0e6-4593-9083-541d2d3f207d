import React, { useState, useEffect } from 'react';
import { translateText, SUPPORTED_LANGUAGES } from '../utils/bhashiniTranslation';

interface BhashiniTranslatorProps {
  text: string;
  sourceLang: string;
  targetLang: string;
  onTranslationComplete?: (translatedText: string) => void;
  className?: string;
}

/**
 * Component that handles translation using Bhashini API
 */
const BhashiniTranslator: React.FC<BhashiniTranslatorProps> = ({
  text,
  sourceLang,
  targetLang,
  onTranslationComplete,
  className = '',
}) => {
  const [translatedText, setTranslatedText] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Don't translate if text is empty or languages are the same
    if (!text || sourceLang === targetLang) {
      setTranslatedText('');
      return;
    }

    const performTranslation = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const result = await translateText(text, sourceLang, targetLang);
        
        // Check if result starts with "Error:" which indicates translation failed
        if (result.startsWith('Error:')) {
          setError(result);
          setTranslatedText('');
        } else {
          setTranslatedText(result);
          if (onTranslationComplete) {
            onTranslationComplete(result);
          }
        }
      } catch (err) {
        setError(`Translation failed: ${err}`);
        setTranslatedText('');
      } finally {
        setIsLoading(false);
      }
    };

    performTranslation();
  }, [text, sourceLang, targetLang, onTranslationComplete]);

  if (isLoading) {
    return <div className={`bhashini-translator-loading ${className}`}>Translating...</div>;
  }

  if (error) {
    return <div className={`bhashini-translator-error ${className}`}>{error}</div>;
  }

  return (
    <div className={`bhashini-translator ${className}`}>
      {translatedText}
    </div>
  );
};

/**
 * Hook to use Bhashini translation functionality
 */
export const useBhashiniTranslation = (
  initialText: string = '',
  initialSourceLang: string = 'en',
  initialTargetLang: string = 'hi'
) => {
  const [text, setText] = useState<string>(initialText);
  const [sourceLang, setSourceLang] = useState<string>(initialSourceLang);
  const [targetLang, setTargetLang] = useState<string>(initialTargetLang);
  const [translatedText, setTranslatedText] = useState<string>('');
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const [translationError, setTranslationError] = useState<string | null>(null);

  const translate = async (newText?: string, newSourceLang?: string, newTargetLang?: string) => {
    const textToTranslate = newText !== undefined ? newText : text;
    const sourceLanguage = newSourceLang || sourceLang;
    const targetLanguage = newTargetLang || targetLang;
    
    if (!textToTranslate || sourceLanguage === targetLanguage) {
      setTranslatedText('');
      return '';
    }

    setIsTranslating(true);
    setTranslationError(null);
    
    try {
      const result = await translateText(textToTranslate, sourceLanguage, targetLanguage);
      
      if (result.startsWith('Error:')) {
        setTranslationError(result);
        setTranslatedText('');
        return '';
      } else {
        setTranslatedText(result);
        return result;
      }
    } catch (err) {
      const errorMsg = `Translation failed: ${err}`;
      setTranslationError(errorMsg);
      setTranslatedText('');
      return '';
    } finally {
      setIsTranslating(false);
    }
  };

  return {
    text,
    setText,
    sourceLang,
    setSourceLang,
    targetLang,
    setTargetLang,
    translatedText,
    isTranslating,
    translationError,
    translate,
    supportedLanguages: SUPPORTED_LANGUAGES
  };
};

export default BhashiniTranslator;
