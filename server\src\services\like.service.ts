import { LikeModel, VideoModel } from '../models';

/**
 * Service for like-related operations
 */
export class LikeService {
  /**
   * Toggle like for a video
   * @param videoId Video ID
   * @param userId User ID
   * @returns Object with isLiked status and updated like count
   */
  async toggleLike(videoId: string, userId: string): Promise<{ isLiked: boolean; likeCount: number }> {
    try {
      // Check if the user has already liked the video
      const existingLike = await LikeModel.findOne({
        videoId,
        userId,
        deletedAt: { $exists: false },
      });

      // Get the video to update its stats
      const video = await VideoModel.findById(videoId);
      if (!video) {
        throw new Error('Video not found');
      }

      let isLiked = false;

      if (existingLike) {
        // User already liked the video, so unlike it (soft delete)
        existingLike.deletedAt = new Date();
        existingLike.deletedBy = userId;
        await existingLike.save();

        // Decrement the video's like count
        video.stats.likes = Math.max(0, video.stats.likes - 1);
        await video.save();
      } else {
        // User hasn't liked the video yet, so add a like
        const newLike = new LikeModel({
          videoId,
          userId,
          timestamp: new Date(),
          createdBy: userId,
          updatedBy: userId,
        });
        await newLike.save();

        // Increment the video's like count
        video.stats.likes += 1;
        await video.save();
        
        isLiked = true;
      }

      return {
        isLiked,
        likeCount: video.stats.likes,
      };
    } catch (error) {
      console.error('Error toggling like:', error);
      throw error;
    }
  }

  /**
   * Check if a user has liked a video
   * @param videoId Video ID
   * @param userId User ID
   * @returns Boolean indicating if the user has liked the video
   */
  async hasUserLiked(videoId: string, userId: string): Promise<boolean> {
    try {
      const like = await LikeModel.findOne({
        videoId,
        userId,
        deletedAt: { $exists: false },
      });
      
      return !!like;
    } catch (error) {
      console.error('Error checking if user liked video:', error);
      throw error;
    }
  }

  /**
   * Get like count for a video
   * @param videoId Video ID
   * @returns Number of likes for the video
   */
  async getLikeCount(videoId: string): Promise<number> {
    try {
      const count = await LikeModel.countDocuments({
        videoId,
        deletedAt: { $exists: false },
      });
      
      return count;
    } catch (error) {
      console.error('Error getting like count:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const likeService = new LikeService();
