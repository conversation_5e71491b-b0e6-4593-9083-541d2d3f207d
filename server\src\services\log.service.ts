import LogModel, { ILog } from '../models/log.model';
import { createNotFoundError, createBadRequestError } from '../utils/errors';

/**
 * Log service for handling log-related business logic
 */
export class LogService {
  /**
   * Create a new log entry
   * @param logData Log data to create
   * @param userId User ID of the person creating the log
   * @param userIp IP address of the user
   * @param userAgent User agent string
   * @returns Created log
   */
  async createLog(
    logData: Partial<ILog>,
    userId: string,
    userIp: string,
    userAgent: string
  ): Promise<ILog> {
    try {
      // Create log with user information
      const log = new LogModel({
        ...logData,
        userId,
        userIp,
        userAgent,
        createdBy: userId,
        updatedBy: userId,
      });

      // Save log to database
      await log.save();

      return log;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get log by ID
   * @param id Log ID
   * @returns Log
   */
  async getLogById(id: string): Promise<ILog> {
    try {
      const log = await LogModel.findOne({ id, deletedAt: { $exists: false } });

      if (!log) {
        throw createNotFoundError('Log not found');
      }

      return log;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all logs with filtering and pagination
   * @param options Filter and pagination options
   * @returns Logs and pagination info
   */
  async getAllLogs(options: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    action?: string;
    category?: string;
    userId?: string;
    resourceType?: string;
    resourceId?: string;
    status?: 'success' | 'failure' | 'warning' | 'info';
    severity?: 'low' | 'medium' | 'high' | 'critical';
    startDate?: string;
    endDate?: string;
  }): Promise<{
    logs: ILog[];
    total: number;
    page: number;
    limit: number;
    pages: number;
  }> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        action,
        category,
        userId,
        resourceType,
        resourceId,
        status,
        severity,
        startDate,
        endDate,
      } = options;

      // Build query
      const query: any = {
        deletedAt: { $exists: false },
      };

      // Add filters if provided
      if (action) {
        query.action = action;
      }

      if (category) {
        query.category = category;
      }

      if (userId) {
        query.userId = userId;
      }

      if (resourceType) {
        query.resourceType = resourceType;
      }

      if (resourceId) {
        query.resourceId = resourceId;
      }

      if (status) {
        query.status = status;
      }

      if (severity) {
        query.severity = severity;
      }

      // Add date range filter if provided
      if (startDate || endDate) {
        query.createdAt = {};

        if (startDate) {
          query.createdAt.$gte = new Date(startDate);
        }

        if (endDate) {
          query.createdAt.$lte = new Date(endDate);
        }
      }

      // Count total documents
      const total = await LogModel.countDocuments(query);

      // Calculate pagination
      const pages = Math.ceil(total / limit);
      const skip = (page - 1) * limit;

      // Build sort object
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute query
      const logs = await LogModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);

      return {
        logs,
        total,
        page,
        limit,
        pages,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete a log by ID
   * @param id Log ID
   * @param userId User ID of the person deleting the log
   * @returns Success message
   */
  async deleteLog(id: string, userId: string): Promise<{ success: boolean; message: string }> {
    try {
      const log = await LogModel.findOne({ id, deletedAt: { $exists: false } });

      if (!log) {
        throw createNotFoundError('Log not found');
      }

      // Soft delete the log
      log.deletedAt = new Date();
      log.deletedBy = userId;
      await log.save();

      return {
        success: true,
        message: 'Log deleted successfully',
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create a system log (for internal use)
   * @param action Action performed
   * @param category Category of the action
   * @param resourceType Type of resource affected
   * @param details Additional details
   * @param options Additional options
   * @returns Created log
   */
  async createSystemLog(
    action: string,
    category: string,
    resourceType: string,
    details: string,
    options: {
      resourceId?: string;
      status?: 'success' | 'failure' | 'warning' | 'info';
      severity?: 'low' | 'medium' | 'high' | 'critical';
      previousState?: Record<string, any>;
      newState?: Record<string, any>;
    } = {}
  ): Promise<ILog> {
    try {
      const {
        resourceId,
        status = 'info',
        severity = 'low',
        previousState,
        newState,
      } = options;

      // Create log with system information
      const log = new LogModel({
        action,
        category,
        resourceType,
        resourceId,
        details,
        status,
        severity,
        previousState,
        newState,
        userId: 'system',
        userIp: '127.0.0.1',
        userAgent: 'System',
        createdBy: 'system',
        updatedBy: 'system',
      });

      // Save log to database
      await log.save();

      return log;
    } catch (error) {
      throw error;
    }
  }
}

// Create and export a singleton instance
const logService = new LogService();
export default logService;
