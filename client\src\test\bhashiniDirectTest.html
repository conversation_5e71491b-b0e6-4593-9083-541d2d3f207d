<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bhashini Direct API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, textarea, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .loading {
            display: none;
            margin-top: 20px;
            text-align: center;
            color: #666;
        }
        .error {
            color: #D8000C;
            background-color: #FFD2D2;
            padding: 10px;
            margin-top: 20px;
            border-radius: 4px;
        }
        .request-details, .response-details {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f5f5f5;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .request-details h3, .response-details h3 {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <h1>Bhashini Direct API Translation Test</h1>
    
    <div class="form-group">
        <label for="sourceLanguage">Source Language:</label>
        <select id="sourceLanguage">
            <option value="en">English</option>
            <option value="hi">Hindi</option>
            <option value="mr">Marathi</option>
            <option value="gu">Gujarati</option>
            <option value="ta">Tamil</option>
            <option value="te">Telugu</option>
            <option value="bn">Bengali</option>
            <option value="kn">Kannada</option>
            <option value="ml">Malayalam</option>
            <option value="pa">Punjabi</option>
            <option value="or">Odia</option>
            <option value="as">Assamese</option>
            <option value="ur">Urdu</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="targetLanguage">Target Language:</label>
        <select id="targetLanguage">
            <option value="hi">Hindi</option>
            <option value="en">English</option>
            <option value="mr">Marathi</option>
            <option value="gu">Gujarati</option>
            <option value="ta">Tamil</option>
            <option value="te">Telugu</option>
            <option value="bn">Bengali</option>
            <option value="kn">Kannada</option>
            <option value="ml">Malayalam</option>
            <option value="pa">Punjabi</option>
            <option value="or">Odia</option>
            <option value="as">Assamese</option>
            <option value="ur">Urdu</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="textToTranslate">Text to Translate:</label>
        <textarea id="textToTranslate" rows="5" placeholder="Enter text to translate...">Hello, how are you? This is a test of the Bhashini translation API.</textarea>
    </div>
    
    <div class="form-group">
        <label for="userId">User ID (optional):</label>
        <input type="text" id="userId" value="cee60134c6bb4d179efd3fda48ff32fe">
    </div>
    
    <div class="form-group">
        <label for="apiKey">API Key (optional):</label>
        <input type="text" id="apiKey" value="13a647c84b-2747-4f0c-afcd-2ac8235f5318">
    </div>
    
    <button id="translateBtn">Translate</button>
    
    <div id="loading" class="loading">Translating... Please wait.</div>
    
    <div id="result" class="result" style="display: none;">
        <h3>Translation Result:</h3>
        <div id="translatedText"></div>
    </div>
    
    <div id="request" class="request-details" style="display: none;">
        <h3>Request Details:</h3>
        <div id="requestDetails"></div>
    </div>
    
    <div id="response" class="response-details" style="display: none;">
        <h3>Response Details:</h3>
        <div id="responseDetails"></div>
    </div>
    
    <div id="error" class="error" style="display: none;"></div>
    
    <script>
        document.getElementById('translateBtn').addEventListener('click', async function() {
            const sourceLanguage = document.getElementById('sourceLanguage').value;
            const targetLanguage = document.getElementById('targetLanguage').value;
            const text = document.getElementById('textToTranslate').value;
            const userId = document.getElementById('userId').value;
            const apiKey = document.getElementById('apiKey').value;
            
            // Show loading indicator
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            document.getElementById('request').style.display = 'none';
            document.getElementById('response').style.display = 'none';
            
            try {
                // Prepare request payload
                const payload = {
                    userId: userId,
                    ulcaApiKey: apiKey,
                    sourceLanguage: sourceLanguage,
                    targetLanguage: targetLanguage,
                    domain: "general",
                    text: text
                };
                
                // Display request details
                document.getElementById('request').style.display = 'block';
                document.getElementById('requestDetails').textContent = JSON.stringify(payload, null, 2);
                
                // Make direct API request to Bhashini
                const response = await fetch('https://bhashini.gov.in/api/v1/inference/translation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });
                
                const data = await response.json();
                
                // Display response details
                document.getElementById('response').style.display = 'block';
                document.getElementById('responseDetails').textContent = JSON.stringify(data, null, 2);
                
                // Hide loading indicator
                document.getElementById('loading').style.display = 'none';
                
                // Show result
                document.getElementById('result').style.display = 'block';
                document.getElementById('translatedText').textContent = data.translatedText || data.text || 'No translation found';
            } catch (error) {
                // Hide loading indicator and show error
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').textContent = 'Error: ' + error.message;
            }
        });
    </script>
</body>
</html>
