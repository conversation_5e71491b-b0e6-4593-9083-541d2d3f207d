import React, { useState } from 'react';
import FeatureUsageModal from '@/components/modals/FeatureUsageModal';
import {
  Globe,
  Info,
  Key,
  Code,
  Award,
  DollarSign,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Play,
  Upload,
  Users,
  Crown,
  Smartphone,
  Save,
  Home,
  Settings,
  ChevronRight,
  Search,
  Share2
} from 'lucide-react';

export default function WebsiteInformation() {
  const [isFeatureModalOpen, setIsFeatureModalOpen] = useState(false);
  const [featureModalName, setFeatureModalName] = useState('');
  const [activeTab, setActiveTab] = useState('basic');
  
  return (
    <div>
      <FeatureUsageModal 
        isOpen={isFeatureModalOpen} 
        onClose={() => setIsFeatureModalOpen(false)} 
        featureName={featureModalName} 
      />

      {/* Breadcrumb Navigation */}
      <nav className="flex items-center space-x-1 text-sm font-medium mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg shadow-sm">
        <div className="flex items-center">
          <Home className="h-4 w-4 text-blue-600" />
          <ChevronRight className="h-4 w-4 text-gray-400" />
        </div>
        <span className="text-gray-500">Admin Panel</span>
        <ChevronRight className="h-4 w-4 text-gray-400" />
        <div className="flex items-center">
          <Settings className="h-4 w-4 text-blue-600 mr-1" />
          <span className="text-gray-500">Settings</span>
        </div>
        <ChevronRight className="h-4 w-4 text-gray-400" />
        <div className="flex items-center">
          <Globe className="h-4 w-4 text-blue-600 mr-1" />
          <span className="text-blue-600 font-semibold">Website Information</span>
        </div>
      </nav>
      
      {/* Tabs */}
      <div className="mb-6 border-b border-gray-200">
        <div className="flex space-x-1">
          <button
            onClick={() => setActiveTab('basic')}
            className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
              activeTab === 'basic'
                ? 'bg-white text-blue-600 border-t border-l border-r border-gray-200'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center">
              <Globe className="h-4 w-4 mr-2" />
              Basic Information
            </div>
          </button>
          <button
            onClick={() => setActiveTab('api')}
            className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
              activeTab === 'api'
                ? 'bg-white text-blue-600 border-t border-l border-r border-gray-200'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center">
              <Key className="h-4 w-4 mr-2" />
              API & Integration
            </div>
          </button>
          <button
            onClick={() => setActiveTab('points')}
            className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
              activeTab === 'points'
                ? 'bg-white text-blue-600 border-t border-l border-r border-gray-200'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center">
              <Award className="h-4 w-4 mr-2" />
              Point System
            </div>
          </button>
          <button
            onClick={() => setActiveTab('seo')}
            className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
              activeTab === 'seo'
                ? 'bg-white text-blue-600 border-t border-l border-r border-gray-200'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center">
              <Search className="h-4 w-4 mr-2" />
              SEO Settings
            </div>
          </button>
        </div>
      </div>

      {/* Basic Information Tab Content */}
      {activeTab === 'basic' && (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center mb-6">
            <Globe className="h-5 w-5 text-blue-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Basic Website Information</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              {/* Site Title */}
              <div className="space-y-2">
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <Info className="h-4 w-4 text-blue-500 mr-2" />
                  Site Title
                </label>
                <div className="relative">
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="engave"
                    defaultValue="engave"
                  />
                </div>
                <p className="text-xs text-gray-500">Your website general title, it will appear on Google and on your browser tab.</p>
              </div>

              {/* Site Name */}
              <div className="space-y-2">
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <Info className="h-4 w-4 text-blue-500 mr-2" />
                  Site Name
                </label>
                <div className="relative">
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="engave"
                    defaultValue="engave"
                  />
                </div>
                <p className="text-xs text-gray-500">Your website name, it will appear on website's footer and E-mails.</p>
              </div>
            </div>

            <div className="space-y-6">
              {/* Site Keywords */}
              <div className="space-y-2">
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <Info className="h-4 w-4 text-blue-500 mr-2" />
                  Site Keywords
                </label>
                <div className="relative">
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="interactive video, interactive experience"
                    defaultValue="interactive video, interactive experience"
                  />
                </div>
                <p className="text-xs text-gray-500">Your website's keyword, used mostly for SEO and search engines.</p>
              </div>

              {/* Site Description */}
              <div className="space-y-2">
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <Info className="h-4 w-4 text-blue-500 mr-2" />
                  Site Description
                </label>
                <div className="relative">
                  <textarea
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-24"
                    placeholder="engave allows you to make and experience interactive videos. Let's go interactive!"
                    defaultValue="engave allows you to make and experience interactive videos. Let's go interactive!"
                  ></textarea>
                </div>
                <p className="text-xs text-gray-500">Your website's description, used mostly for SEO and search engines. Max of 100 characters is recommended.</p>
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <button className="flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </button>
          </div>
        </div>
      )}

      {/* API & Integration Tab Content */}
      {activeTab === 'api' && (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center mb-6">
            <Key className="h-5 w-5 text-blue-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">API Keys & Integration</h2>
          </div>

          <div className="space-y-6">
            {/* Google Analytics Code */}
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
              <div className="flex items-start mb-3">
                <Code className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Google Analytics Code / Custom HTML Code</h3>
                  <p className="text-xs text-gray-500 mt-1">Add your Google Analytics tracking code or any custom HTML code to be included in the site header.</p>
                </div>
              </div>
              <textarea
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-32 font-mono text-sm"
                placeholder="<!-- Paste your Google Analytics or custom HTML code here -->"
              ></textarea>
            </div>

            {/* Google Vignette Code */}
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
              <div className="flex items-start mb-3">
                <Code className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Google Vignette Code</h3>
                  <p className="text-xs text-gray-500 mt-1">Add your Google Vignette code for mobile ad implementation.</p>
                </div>
              </div>
              <textarea
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-32 font-mono text-sm"
                placeholder="<!-- Paste your Google Vignette code here -->"
              ></textarea>
            </div>

            {/* Extreme IP Lookup Key */}
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
              <div className="flex items-start mb-3">
                <Key className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Extreme IP Lookup Key</h3>
                  <p className="text-xs text-gray-500 mt-1">Extreme IP Lookup Key to get user continent for geo blocking.</p>
                </div>
              </div>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your Extreme IP Lookup API key"
              />
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <button className="flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </button>
          </div>
        </div>
      )}

      {/* Point System Tab Content */}
      {activeTab === 'points' && (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center mb-6">
            <Award className="h-5 w-5 text-blue-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Point System Settings</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              {/* Point System Toggle */}
              <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
                <div className="flex justify-between items-center">
                  <div className="flex items-start">
                    <Award className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-gray-900">Point System</h3>
                      <p className="text-xs text-gray-500 mt-1">Gives the ability for users to earn points from liking, sharing, commenting and posting.</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>

              {/* Allow Withdrawal */}
              <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
                <div className="flex justify-between items-center">
                  <div className="flex items-start">
                    <DollarSign className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-gray-900">Allow Point Withdrawal</h3>
                      <p className="text-xs text-gray-500 mt-1">Allow users to transfer earned points into money and withdrawal.</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>

              {/* Point Value */}
              <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
                <div className="flex items-start mb-3">
                  <DollarSign className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-gray-900">$1.00 = ? Points</h3>
                    <p className="text-xs text-gray-500 mt-1">How much does 1 dollar equal in points?</p>
                  </div>
                </div>
                <input
                  type="number"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="100"
                  defaultValue="100"
                />
              </div>
            </div>

            <div className="space-y-6">
              {/* Commenting on Videos */}
              <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
                <div className="flex items-start mb-3">
                  <MessageSquare className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-gray-900">Commenting on Videos</h3>
                    <p className="text-xs text-gray-500 mt-1">How many points does a user earn by creating comments?</p>
                  </div>
                </div>
                <input
                  type="number"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="10"
                  defaultValue="10"
                />
              </div>

              {/* Liking Videos */}
              <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
                <div className="flex items-start mb-3">
                  <ThumbsUp className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-gray-900">Liking Videos</h3>
                    <p className="text-xs text-gray-500 mt-1">How many points does a user earn by liking videos?</p>
                  </div>
                </div>
                <input
                  type="number"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="5"
                  defaultValue="5"
                />
              </div>

              {/* Watching Videos */}
              <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
                <div className="flex items-start mb-3">
                  <Play className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-gray-900">Watching Videos</h3>
                    <p className="text-xs text-gray-500 mt-1">How many points does a user earn by watching videos?</p>
                  </div>
                </div>
                <input
                  type="number"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="2"
                  defaultValue="2"
                />
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <button className="flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </button>
          </div>
        </div>
      )}

      {/* SEO Settings Tab Content */}
      {activeTab === 'seo' && (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center mb-6">
            <Search className="h-5 w-5 text-blue-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">SEO Settings</h2>
          </div>

          <div className="space-y-6">
            {/* SEO Links */}
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
              <div className="flex justify-between items-center">
                <div className="flex items-start">
                  <Share2 className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-gray-900">SEO Links</h3>
                    <p className="text-xs text-gray-500 mt-1">Enable SEO links E.g: site.com/this-is-a-video-_ID.html, this will improve your Google Ranking</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>

            {/* Meta Tags */}
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
              <div className="flex items-start mb-3">
                <Code className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Custom Meta Tags</h3>
                  <p className="text-xs text-gray-500 mt-1">Add custom meta tags to improve SEO. These will be added to all pages.</p>
                </div>
              </div>
              <textarea
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-32 font-mono text-sm"
                placeholder="<meta name=\"example\" content=\"value\">"
              ></textarea>
            </div>

            {/* Robots.txt */}
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white">
              <div className="flex items-start mb-3">
                <Code className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Robots.txt Content</h3>
                  <p className="text-xs text-gray-500 mt-1">Configure your robots.txt file to control search engine crawling.</p>
                </div>
              </div>
              <textarea
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-32 font-mono text-sm"
                placeholder="User-agent: *\nAllow: /\nDisallow: /admin/"
                defaultValue="User-agent: *\nAllow: /\nDisallow: /admin/"
              ></textarea>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <button className="flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
