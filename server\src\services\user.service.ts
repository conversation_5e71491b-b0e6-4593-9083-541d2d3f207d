import { UserModel, RoleModel } from '../models';
import bcrypt from 'bcrypt';
import { IUser } from '../models/user.model';
import { generateEmailVerificationToken } from '../utils/token.utils';
import {
  AppError,
  createBadRequestError,
  createNotFoundError,
  createConflictError,
  createUnauthorizedError,
  createInternalServerError,
  ErrorCodes
} from '../utils/errors';
import { logger } from '../utils/logger';

/**
 * User service for handling user-related operations
 */
export class UserService {
  /**
   * Register a new user
   */
  async registerUser(userData: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    displayName?: string;
  }, fastify?: any): Promise<{ user: IUser; verificationToken?: string }> {
    try {
      // Check if user already exists
      const existingUser = await UserModel.findOne({
        $or: [{ email: userData.email }, { username: userData.username }],
        deletedAt: { $exists: false },
      });

      if (existingUser) {
        if (existingUser.email === userData.email) {
          throw createConflictError('Email already in use', ErrorCodes.EMAIL_TAKEN);
        }
        if (existingUser.username === userData.username) {
          throw createConflictError('Username already taken', ErrorCodes.USERNAME_TAKEN);
        }
      }

      // Get default user role
      const userRole = await RoleModel.findOne({ code: 'user' });
      if (!userRole) {
        logger.error('Default user role not found');
        throw createInternalServerError('Failed to create user account', ErrorCodes.ROLE_NOT_FOUND);
      }

      // Create new user
      const user = new UserModel({
        username: userData.username,
        email: userData.email,
        password: userData.password, // Will be hashed by pre-save hook
        firstName: userData.firstName,
        lastName: userData.lastName,
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`,
        status: 'active', // Set to active for development
        emailVerified: true, // Set to true for development
        phoneVerified: false,
        roles: [userRole.id],
        permissions: [],
        preferences: {
          theme: 'system',
          emailNotifications: {
            marketing: true,
            account: true,
            security: true,
            social: true,
          },
          pushNotifications: {
            messages: true,
            comments: true,
            followers: true,
            videoUploads: true,
          },
        },
        stats: {
          videosUploaded: 0,
          totalViews: 0,
          followers: 0,
          following: 0,
          reputation: 0,
        },
        createdBy: 'system',
        updatedBy: 'system',
      });

      await user.save();

      // Generate email verification token if fastify instance is provided
      let verificationToken;
      if (fastify) {
        verificationToken = generateEmailVerificationToken(fastify, user.id, user.email);

        // In a real application, you would send an email with the verification link
        // For now, we'll just return the token
      }

      return { user, verificationToken };
    } catch (error) {
      // If it's already an AppError, just rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Otherwise, log it and wrap it in an AppError
      logger.error('Error in registerUser', error);
      throw createInternalServerError('Failed to register user', ErrorCodes.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Verify user email
   */
  async verifyEmail(userId: string): Promise<boolean> {
    try {
      const user = await UserModel.findOne({
        id: userId,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw createNotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);
      }

      // Update user
      user.emailVerified = true;
      user.status = 'active';
      user.updatedBy = user.id;
      await user.save();

      return true;
    } catch (error) {
      // If it's already an AppError, just rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Otherwise, log it and wrap it in an AppError
      logger.error('Error in verifyEmail', error);
      throw createInternalServerError('Failed to verify email', ErrorCodes.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Authenticate a user
   */
  async loginUser(email: string, password: string): Promise<{ user: IUser | null; isValid: boolean }> {
    try {
      // Find user by email
      const user = await UserModel.findOne({
        email,
        deletedAt: { $exists: false },
      });

      if (!user) {
        return { user: null, isValid: false };
      }

      // Check if user is active
      if (user.status !== 'active') {
        let errorCode;
        switch (user.status) {
          case 'pending':
            errorCode = ErrorCodes.ACCOUNT_NOT_VERIFIED;
            break;
          case 'suspended':
            errorCode = ErrorCodes.ACCOUNT_LOCKED;
            break;
          case 'banned':
            errorCode = ErrorCodes.ACCOUNT_DISABLED;
            break;
          default:
            errorCode = ErrorCodes.UNAUTHORIZED;
        }
        throw createUnauthorizedError(`Account is ${user.status}. Please contact support.`, errorCode);
      }

      // Verify password
      const isValid = await user.comparePassword(password);
      if (!isValid) {
        return { user, isValid: false };
      }

      // Update last login
      user.lastLoginAt = new Date();
      user.lastLoginIp = '127.0.0.1'; // This should be the actual IP in production
      await user.save();

      return { user, isValid: true };
    } catch (error) {
      // If it's already an AppError, just rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Otherwise, log it and wrap it in an AppError
      logger.error('Error in loginUser', error);
      throw createInternalServerError('Failed to authenticate user', ErrorCodes.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<IUser> {
    try {
      const user = await UserModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw createNotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);
      }

      return user;
    } catch (error) {
      // If it's already an AppError, just rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Otherwise, log it and wrap it in an AppError
      logger.error('Error in getUserById', error);
      throw createInternalServerError('Failed to get user', ErrorCodes.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(
    id: string,
    userData: Partial<IUser>,
    updatedBy: string
  ): Promise<IUser> {
    try {
      const user = await UserModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw createNotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);
      }

      // Update allowed fields
      if (userData.firstName) user.firstName = userData.firstName;
      if (userData.lastName) user.lastName = userData.lastName;
      if (userData.displayName) user.displayName = userData.displayName;
      if (userData.avatar) user.avatar = userData.avatar;
      if (userData.phone) user.phone = userData.phone;
      if (userData.language) user.language = userData.language;
      if (userData.timezone) user.timezone = userData.timezone;

      // Update preferences if provided
      if (userData.preferences) {
        if (userData.preferences.theme) {
          user.preferences.theme = userData.preferences.theme;
        }

        if (userData.preferences.emailNotifications) {
          Object.assign(user.preferences.emailNotifications, userData.preferences.emailNotifications);
        }

        if (userData.preferences.pushNotifications) {
          Object.assign(user.preferences.pushNotifications, userData.preferences.pushNotifications);
        }
      }

      user.updatedBy = updatedBy;
      await user.save();

      return user;
    } catch (error) {
      // If it's already an AppError, just rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Otherwise, log it and wrap it in an AppError
      logger.error('Error in updateUserProfile', error);
      throw createInternalServerError('Failed to update user profile', ErrorCodes.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Change user password
   */
  async changePassword(
    id: string,
    currentPassword: string,
    newPassword: string
  ): Promise<boolean> {
    try {
      const user = await UserModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw createNotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);
      }

      // Verify current password
      const isValid = await user.comparePassword(currentPassword);
      if (!isValid) {
        throw createUnauthorizedError('Current password is incorrect', ErrorCodes.INVALID_CREDENTIALS);
      }

      // Update password
      user.password = newPassword; // Will be hashed by pre-save hook
      user.updatedBy = id;
      await user.save();

      return true;
    } catch (error) {
      // If it's already an AppError, just rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Otherwise, log it and wrap it in an AppError
      logger.error('Error in changePassword', error);
      throw createInternalServerError('Failed to change password', ErrorCodes.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get all users (admin)
   */
  async getAllUsers(options: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    role?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ users: IUser[]; total: number; page: number; limit: number; pages: number }> {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        status,
        role,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = options;

      // Build query
      const query: any = {
        deletedAt: { $exists: false },
      };

      // Add search filter
      if (search) {
        query.$or = [
          { username: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } },
          { displayName: { $regex: search, $options: 'i' } },
        ];
      }

      // Add status filter
      if (status) {
        query.status = status;
      }

      // Add role filter
      if (role) {
        query.roles = role;
      }

      // Count total documents
      const total = await UserModel.countDocuments(query);

      // Calculate pagination
      const pages = Math.ceil(total / limit);
      const skip = (page - 1) * limit;

      // Build sort object
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute query
      const users = await UserModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);

      return {
        users,
        total,
        page,
        limit,
        pages,
      };
    } catch (error) {
      // If it's already an AppError, just rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Otherwise, log it and wrap it in an AppError
      logger.error('Error in getAllUsers', error);
      throw createInternalServerError('Failed to get users', ErrorCodes.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Delete user (admin)
   */
  async deleteUser(id: string, deletedBy: string): Promise<boolean> {
    try {
      const user = await UserModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw createNotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);
      }

      // Soft delete
      user.deletedAt = new Date();
      user.deletedBy = deletedBy;
      user.status = 'banned';
      await user.save();

      return true;
    } catch (error) {
      // If it's already an AppError, just rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Otherwise, log it and wrap it in an AppError
      logger.error('Error in deleteUser', error);
      throw createInternalServerError('Failed to delete user', ErrorCodes.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update user status (admin)
   */
  async updateUserStatus(
    id: string,
    status: 'active' | 'pending' | 'suspended' | 'banned',
    statusReason?: string,
    updatedBy?: string
  ): Promise<IUser> {
    try {
      const user = await UserModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw createNotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);
      }

      user.status = status;
      if (statusReason) {
        user.statusReason = statusReason;
      }
      user.updatedBy = updatedBy || 'system';
      await user.save();

      return user;
    } catch (error) {
      // If it's already an AppError, just rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Otherwise, log it and wrap it in an AppError
      logger.error('Error in updateUserStatus', error);
      throw createInternalServerError('Failed to update user status', ErrorCodes.INTERNAL_SERVER_ERROR);
    }
  }
}

export default new UserService();
