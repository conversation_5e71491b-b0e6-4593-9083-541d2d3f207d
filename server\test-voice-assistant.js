/**
 * Test script for Voice Assistant functionality
 * 
 * This script tests the speech-to-text, OpenRouter API, and text-to-speech flow.
 */

// Mock Web Speech API
const mockSpeechRecognition = {
  start: () => console.log('Speech recognition started'),
  stop: () => console.log('Speech recognition stopped'),
  onresult: null,
  onerror: null,
  onend: null,
  onstart: null
};

// Mock speech synthesis
const mockSpeechSynthesis = {
  speak: (utterance) => console.log(`Speaking: ${utterance.text}`),
  cancel: () => console.log('Speech synthesis canceled')
};

// Mock OpenRouter API call
async function mockOpenRouterCall(text) {
  console.log(`Sending to OpenRouter API: "${text}"`);
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return {
    choices: [
      {
        message: {
          content: `This is a mock response to: "${text}"`
        }
      }
    ]
  };
}

// Test the complete flow
async function testVoiceAssistantFlow() {
  console.log('=== Testing Voice Assistant Flow ===');
  
  // Step 1: Simulate speech recognition
  console.log('\n1. Starting speech recognition...');
  mockSpeechRecognition.start();
  
  // Step 2: Simulate speech recognition result
  console.log('\n2. Simulating speech recognition result...');
  const mockTranscript = 'Hello, this is a test of the voice assistant';
  console.log(`Transcript: "${mockTranscript}"`);
  
  // Step 3: Send to OpenRouter API
  console.log('\n3. Sending transcript to OpenRouter API...');
  const apiResponse = await mockOpenRouterCall(mockTranscript);
  
  // Step 4: Process API response
  console.log('\n4. Processing API response...');
  const responseText = apiResponse.choices[0].message.content;
  console.log(`Response text: "${responseText}"`);
  
  // Step 5: Convert to speech
  console.log('\n5. Converting response to speech...');
  mockSpeechSynthesis.speak({ text: responseText });
  
  console.log('\n=== Test completed successfully ===');
}

// Run the test
testVoiceAssistantFlow();
