import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import UserSearch from '../components/messaging/UserSearch';
import axios from 'axios';
import { API_BASE_URL, WS_URL, TYPING_INDICATOR_TIMEOUT } from '../config';
import '../components/messaging/UserSearch.css';
import './MessagesPage.css';
import { translateText as simpleTranslateText } from '../utils/basicTranslator';
import { translateText as apiTranslateText } from '../services/translationApi';
import { translateText as bhashiniDirectTranslate } from '../services/bhashiniDirectApi';

// For debugging - set to false to disable debug logs
const DEBUG = false;

const MessagesPage = () => {
  const { currentUser, token } = useAuth();
  const { theme } = useTheme();
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [messageInput, setMessageInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [typing, setTyping] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [translatedMessages, setTranslatedMessages] = useState({});
  const [isTranslating, setIsTranslating] = useState(false);
  const messagesEndRef = useRef(null);
  const socketRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  // Translate text using the appropriate API
  const translateText = async (text, targetLanguage) => {
    if (!text || targetLanguage === 'en') {
      return text;
    }

    try {
      // Use Bhashini API for translation
      console.log('Using Bhashini API for translation via translateText');

      // First try the server API
      let translatedContent;
      try {
        translatedContent = await apiTranslateText(
          text,
          'en', // Source language is English
          targetLanguage,
          'cee60134c6bb4d179efd3fda48ff32fe', // User ID
          '13a647c84b-2747-4f0c-afcd-2ac8235f5318', // ULCA API Key
          true, // Use pipeline
          'best' // Use best available method
        );
      } catch (apiError) {
        console.error('API translation failed, falling back to simple translator:', apiError);
        // Fallback to simple translator
        translatedContent = simpleTranslateText(text, targetLanguage);
      }

      return translatedContent;
    } catch (error) {
      console.error('Error translating text:', error);
      return text; // Return original text on error
    }
  };

  if (DEBUG) console.log('MessagesPage: Initialized with token:', token ? 'Token exists' : 'No token', 'User:', currentUser?.username);

  // Connect to WebSocket on component mount
  useEffect(() => {
    if (token) {
      if (DEBUG) console.log('MessagesPage: Token available, connecting to WebSocket');
      connectWebSocket();
    } else {
      if (DEBUG) console.log('MessagesPage: No token available for WebSocket');
    }

    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
    };
  }, [token]);

  // Fetch conversations on component mount
  useEffect(() => {
    if (token) {
      if (DEBUG) console.log('MessagesPage: Token available, fetching conversations');
      fetchConversations();
    } else {
      if (DEBUG) console.log('MessagesPage: No token available for fetching conversations');
    }
  }, [token]);

  // Fetch messages when active conversation changes
  useEffect(() => {
    if (activeConversation) {
      fetchMessages(activeConversation.id);
    }
  }, [activeConversation]);

  // Translate messages when language changes or when messages change
  useEffect(() => {
    if (currentLanguage !== 'en' && messages.length > 0) {
      handleTranslateConversation(currentLanguage);
    }
  }, [currentLanguage, messages.length]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Connect to WebSocket
  const connectWebSocket = () => {
    if (DEBUG) console.log('MessagesPage: Connecting to WebSocket at', WS_URL);
    const socket = new WebSocket(WS_URL);

    socket.onopen = () => {
      console.log('WebSocket connected');
      if (DEBUG) console.log('MessagesPage: WebSocket connected, authenticating with token');
      // Authenticate with token
      socket.send(JSON.stringify({
        type: 'auth',
        payload: {
          token,
        },
      }));
    };

    socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      console.log('WebSocket message:', data);

      switch (data.type) {
        case 'auth_success':
          console.log('Authentication successful');
          break;

        case 'auth_error':
          console.error('Authentication failed:', data.payload.message);
          break;

        case 'message_sent':
          console.log('Message sent event received:', data.payload.message);
          // Add message to UI if it's for the active conversation
          if (activeConversation && data.payload.message.conversationId === activeConversation.id) {
            setMessages((prevMessages) => [...prevMessages, data.payload.message]);

            // Always translate the message if a non-English language is selected
            if (currentLanguage !== 'en') {
              console.log('Auto-translating sent message to:', currentLanguage);

              // Get message content and create a message ID
              const content = data.payload.message.content;
              const messageId = data.payload.message.id || `msg_${data.payload.message.createdAt}_${data.payload.message.senderId}`;

              // Translate immediately using our auto translator
              translateText(content, currentLanguage).then(translatedContent => {
                // Store the translation immediately for better user experience
                setTranslatedMessages(prev => ({
                  ...prev,
                  [messageId]: {
                    ...prev[messageId],
                    [currentLanguage]: translatedContent
                  }
                }));
              });
            }
          }
          break;

        case 'new_message':
          console.log('New message event received:', data.payload.message);
          // Add message to UI if it's for the active conversation
          if (activeConversation && data.payload.message.conversationId === activeConversation.id) {
            setMessages((prevMessages) => [...prevMessages, data.payload.message]);

            // Always translate the message if a non-English language is selected
            if (currentLanguage !== 'en') {
              console.log('Auto-translating received message to:', currentLanguage);

              // Get the message content
              const content = data.payload.message.content;

              // Create a message ID
              const messageId = data.payload.message.id || `msg_${data.payload.message.createdAt}_${data.payload.message.senderId}`;

              // Translate the message immediately
              getTranslation(content, currentLanguage).then(translatedContent => {
                console.log('Translation result:', { original: content, translated: translatedContent });

                // Store the translation
                setTranslatedMessages(prev => ({
                  ...prev,
                  [messageId]: {
                    ...prev[messageId],
                    [currentLanguage]: translatedContent
                  }
                }));
              });
            }

            // Send read receipt
            socket.send(JSON.stringify({
              type: 'read_receipt',
              payload: {
                messageId: data.payload.message.id,
              },
            }));
          }

          // Refresh conversations to update unread count
          fetchConversations();
          break;

        case 'typing_indicator':
          // Show typing indicator if it's for the active conversation
          if (activeConversation && data.payload.conversationId === activeConversation.id) {
            setTyping(data.payload.isTyping);
          }
          break;

        case 'user_status':
          // Update user status in conversations
          fetchConversations();
          break;

        case 'error':
          console.error('WebSocket error:', data.payload.message);
          break;
      }
    };

    socket.onclose = () => {
      console.log('WebSocket disconnected');
      // Try to reconnect after 5 seconds
      setTimeout(connectWebSocket, 5000);
    };

    socket.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    socketRef.current = socket;
  };

  // Fetch conversations
  const fetchConversations = async () => {
    try {
      setLoading(true);
      if (DEBUG) console.log('MessagesPage: Fetching conversations with token:', token ? 'Token exists' : 'No token');

      const response = await axios.get(`${API_BASE_URL}/conversations`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (DEBUG) console.log('MessagesPage: Conversations response:', response.data);

      if (response.data.success) {
        setConversations(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
      if (DEBUG) console.log('MessagesPage: Error details:', error.response?.data || error.message);
      setError('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  };

  // Fetch messages for a conversation
  const fetchMessages = async (conversationId) => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/messages/conversation/${conversationId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.data.success) {
        setMessages(response.data.data);
      }

      // Mark conversation as read
      await axios.post(`${API_BASE_URL}/conversations/${conversationId}/read`, {}, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Refresh conversations to update unread count
      fetchConversations();
    } catch (error) {
      console.error('Error fetching messages:', error);
      setError('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  // Create a new conversation
  const createConversation = async (selectedUser) => {
    try {
      setLoading(true);
      setError(null);

      if (DEBUG) console.log('Creating conversation with user:', selectedUser);

      // Check if conversation already exists
      const existingConversation = conversations.find(
        (conv) =>
          (conv.creatorId === selectedUser.id && conv.userId === currentUser.id) ||
          (conv.userId === selectedUser.id && conv.creatorId === currentUser.id)
      );

      if (existingConversation) {
        if (DEBUG) console.log('Found existing conversation:', existingConversation);
        setActiveConversation(existingConversation);
        return;
      }

      // Determine if current user is creator or regular user
      const isCreator = currentUser.roles?.includes('creator');

      if (DEBUG) console.log('Current user is creator:', isCreator);

      // Prepare request data with all required fields
      const requestData = {
        creatorId: isCreator ? currentUser.id : selectedUser.id,
        userId: isCreator ? selectedUser.id : currentUser.id,
        subject: `Chat with ${selectedUser.displayName || selectedUser.username}`,
        // Add optional fields with default values
        settings: {
          notifications: true,
          readReceipts: true
        },
        metadata: {
          tags: [],
          customFields: {}
        }
      };

      if (DEBUG) console.log('Creating conversation with data:', requestData);

      const response = await axios.post(
        `${API_BASE_URL}/conversations`,
        requestData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (DEBUG) console.log('Conversation creation response:', response.data);

      if (response.data.success) {
        // Refresh conversations
        await fetchConversations();

        // Set active conversation
        setActiveConversation(response.data.data);

        // Fetch messages for the new conversation
        await fetchMessages(response.data.data.id);
      } else {
        setError(response.data.message || 'Failed to create conversation');
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
      if (DEBUG) console.log('Error details:', error.response?.data || error.message);
      setError('Failed to create conversation: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // Send a message
  const sendMessage = async () => {
    if (!activeConversation || !messageInput.trim()) {
      return;
    }

    const messageContent = messageInput.trim();
    const clientId = Date.now().toString();

    // Send message via WebSocket
    socketRef.current.send(JSON.stringify({
      type: 'message',
      payload: {
        conversationId: activeConversation.id,
        content: messageContent,
        contentType: 'text',
        clientId: clientId,
      },
    }));

    // Clear input
    setMessageInput('');

    // Always translate the message if a non-English language is selected
    if (currentLanguage !== 'en') {
      console.log('Auto-translating outgoing message to:', currentLanguage);

      // Create a temporary message ID
      const tempMessageId = `temp-${clientId}`;

      // Translate the message immediately
      getTranslation(messageContent, currentLanguage).then(translatedContent => {
        console.log('Translation result for outgoing message:', { original: messageContent, translated: translatedContent });

        // Store the translation
        setTranslatedMessages(prev => ({
          ...prev,
          [tempMessageId]: {
            ...prev[tempMessageId],
            [currentLanguage]: translatedContent
          }
        }));

        // Also store with the actual message ID that will be assigned by the server
        // This ensures the translation is available when the message is displayed
        setTimeout(() => {
          // Find the message we just sent in the messages array
          const sentMessage = messages.find(m =>
            m.content === messageContent &&
            m.senderId === currentUser.id &&
            new Date(m.createdAt).getTime() > Date.now() - 5000 // Sent in the last 5 seconds
          );

          if (sentMessage) {
            const messageId = sentMessage.id;
            console.log('Found sent message, storing translation with ID:', messageId);

            setTranslatedMessages(prev => ({
              ...prev,
              [messageId]: {
                ...prev[messageId],
                [currentLanguage]: translatedContent
              }
            }));
          }
        }, 1000); // Wait 1 second to ensure the message has been added to the messages array
      });
    }
  };

  // Handle message input change
  const handleMessageInputChange = (e) => {
    setMessageInput(e.target.value);

    // Send typing indicator
    if (activeConversation && socketRef.current) {
      socketRef.current.send(JSON.stringify({
        type: 'typing',
        payload: {
          conversationId: activeConversation.id,
          isTyping: true,
        },
      }));

      // Clear previous timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set timeout to send stop typing indicator
      typingTimeoutRef.current = setTimeout(() => {
        if (socketRef.current) {
          socketRef.current.send(JSON.stringify({
            type: 'typing',
            payload: {
              conversationId: activeConversation.id,
              isTyping: false,
            },
          }));
        }
      }, TYPING_INDICATOR_TIMEOUT);
    }
  };

  // Handle user selection from search
  const handleSelectUser = (selectedUser) => {
    if (selectedUser.conversationId) {
      // If user has an existing conversation, set it as active
      const conversation = conversations.find(
        (conv) => conv.id === selectedUser.conversationId
      );
      if (conversation) {
        setActiveConversation(conversation);
      }
    } else {
      // Create a new conversation with the selected user
      createConversation(selectedUser);
    }
  };

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Format time for display
  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Translate a single message
  const translateMessage = async (message, targetLanguage) => {
    // Get message content and ID
    const content = message.content;
    const messageId = message.id || `msg_${message.createdAt}_${message.senderId}`;

    console.log('TRANSLATING MESSAGE:', { messageId, content, targetLanguage });

    if (targetLanguage === 'en') {
      // If target is English, just use the original content
      return content;
    }

    try {
      // Check if the selected language is an Indian language
      const isIndianLanguage = ['hi', 'mr', 'gu', 'ta', 'te', 'bn', 'kn', 'ml', 'pa', 'or', 'as', 'ur'].includes(targetLanguage);

      console.log(`Using ${isIndianLanguage ? 'Bhashini API' : 'standard translation'} for ${targetLanguage}`);

      // First try the server API
      let translatedContent;
      try {
        if (isIndianLanguage) {
          // Use direct Bhashini API for Indian languages
          console.log('Using direct Bhashini API for Indian language translation');
          try {
            // First try the direct Bhashini API implementation
            translatedContent = await bhashiniDirectTranslate(
              content,
              'en', // Source language is English
              targetLanguage,
              {
                userId: 'cee60134c6bb4d179efd3fda48ff32fe',
                apiKey: '13a647c84b-2747-4f0c-afcd-2ac8235f5318',
                authorization: 'W9QK_zzb7Lnsc_xYAl30Gk64Z8rJU4r_2NBiguZjiMIdkUI_8p-E38M-zc_VVhun'
              }
            );

            // If the result contains an error message, throw an error to try the server API
            if (translatedContent.includes('[Error:') || translatedContent.includes('Error parsing')) {
              throw new Error('Direct Bhashini API failed: ' + translatedContent);
            }
          } catch (directApiError) {
            console.error('Direct Bhashini API failed, falling back to server API:', directApiError);
            // Fall back to the server API
            translatedContent = await apiTranslateText(
              content,
              'en', // Source language is English
              targetLanguage,
              'cee60134c6bb4d179efd3fda48ff32fe', // User ID
              '13a647c84b-2747-4f0c-afcd-2ac8235f5318', // ULCA API Key
              true, // Use pipeline
              'bhashini' // Specifically use Bhashini method
            );
          }
        } else {
          // Use standard translation for non-Indian languages
          console.log('Using standard translation API');
          translatedContent = await apiTranslateText(
            content,
            'en', // Source language is English
            targetLanguage,
            'cee60134c6bb4d179efd3fda48ff32fe', // User ID
            '13a647c84b-2747-4f0c-afcd-2ac8235f5318', // ULCA API Key
            true, // Use pipeline
            'best' // Use best available method
          );
        }
      } catch (apiError) {
        console.error('API translation failed, falling back to simple translator:', apiError);
        // Fallback to simple translator
        translatedContent = simpleTranslateText(content, targetLanguage);
      }

      console.log('TRANSLATION RESULT:', { original: content, translated: translatedContent });

      // Store the translation
      setTranslatedMessages(prev => {
        console.log('UPDATING TRANSLATIONS:', {
          messageId,
          targetLanguage,
          translatedContent,
          prevTranslations: prev
        });

        const newTranslations = {
          ...prev,
          [messageId]: {
            ...prev[messageId],
            [targetLanguage]: translatedContent
          }
        };

        console.log('NEW TRANSLATIONS:', newTranslations);
        return newTranslations;
      });

      return translatedContent;
    } catch (error) {
      console.error('Error translating message:', error);
      return content; // Return original content on error
    }
  };

  // Translate all messages in the conversation using Bhashini API
  const handleTranslateConversation = async (targetLanguage) => {
    console.log('Translating conversation to:', targetLanguage);
    setCurrentLanguage(targetLanguage);

    if (targetLanguage === 'en') {
      // If target is English, reset translations
      setTranslatedMessages({});
      return;
    }

    setIsTranslating(true);

    try {
      // Always create a new translations object to ensure state updates
      const translations = {};

      // Check if the selected language is an Indian language
      const isIndianLanguage = ['hi', 'mr', 'gu', 'ta', 'te', 'bn', 'kn', 'ml', 'pa', 'or', 'as', 'ur'].includes(targetLanguage);

      console.log(`Using ${isIndianLanguage ? 'Bhashini API' : 'standard translation'} for ${targetLanguage}`);

      // Translate each message
      for (const message of messages) {
        // Create a fallback ID if message doesn't have one
        const messageId = message.id || `msg_${message.createdAt}_${message.senderId}`;

        console.log(`Translating message ${messageId} to ${targetLanguage}`);

        // Translate the message using Bhashini API for Indian languages
        let translatedContent;

        try {
          // Use the appropriate translation method based on language
          if (isIndianLanguage) {
            // Use direct Bhashini API for Indian languages
            console.log('Using direct Bhashini API for conversation translation');
            try {
              // First try the direct Bhashini API implementation
              translatedContent = await bhashiniDirectTranslate(
                message.content,
                'en', // Source language is English
                targetLanguage,
                {
                  userId: 'cee60134c6bb4d179efd3fda48ff32fe',
                  apiKey: '13a647c84b-2747-4f0c-afcd-2ac8235f5318',
                  authorization: 'W9QK_zzb7Lnsc_xYAl30Gk64Z8rJU4r_2NBiguZjiMIdkUI_8p-E38M-zc_VVhun'
                }
              );

              // If the result contains an error message, throw an error to try the server API
              if (translatedContent.includes('[Error:') || translatedContent.includes('Error parsing')) {
                throw new Error('Direct Bhashini API failed: ' + translatedContent);
              }
            } catch (directApiError) {
              console.error('Direct Bhashini API failed, falling back to server API:', directApiError);
              // Fall back to the server API
              translatedContent = await apiTranslateText(
                message.content,
                'en', // Source language is English
                targetLanguage,
                'cee60134c6bb4d179efd3fda48ff32fe', // User ID
                '13a647c84b-2747-4f0c-afcd-2ac8235f5318', // ULCA API Key
                true, // Use pipeline
                'bhashini' // Specifically use Bhashini method
              );
            }
          } else {
            // Use standard translation for non-Indian languages
            translatedContent = await translateMessage(message, targetLanguage);
          }
        } catch (error) {
          console.error(`Error translating message ${messageId}:`, error);
          // Fallback to simple translator on error
          translatedContent = simpleTranslateText(message.content, targetLanguage);
        }

        translations[messageId] = {
          ...translatedMessages[messageId], // Keep any existing translations for other languages
          [targetLanguage]: translatedContent
        };

        // Update translations incrementally for better UX
        setTranslatedMessages(prev => ({
          ...prev,
          [messageId]: {
            ...prev[messageId],
            [targetLanguage]: translatedContent
          }
        }));
      }

      console.log('All messages translated successfully');

    } catch (error) {
      console.error('Error translating conversation:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  // Show translation options for a single message
  const handleTranslateMessage = (message, event) => {
    // Create a dropdown menu with language options - only Indian languages
    const languages = [
      // Indian languages
      { code: 'hi', name: 'Hindi 🇮🇳' },
      { code: 'mr', name: 'Marathi 🇮🇳' },
      { code: 'gu', name: 'Gujarati 🇮🇳' },
      { code: 'ta', name: 'Tamil 🇮🇳' },
      { code: 'te', name: 'Telugu 🇮🇳' },
      { code: 'bn', name: 'Bengali 🇮🇳' },
      { code: 'kn', name: 'Kannada 🇮🇳' },
      { code: 'ml', name: 'Malayalam 🇮🇳' },
      { code: 'pa', name: 'Punjabi 🇮🇳' },
      { code: 'or', name: 'Odia 🇮🇳' },
      { code: 'as', name: 'Assamese 🇮🇳' },
      { code: 'ur', name: 'Urdu 🇮🇳' }
    ];

    // Create a temporary dropdown
    const dropdown = document.createElement('div');
    dropdown.className = 'message-translate-dropdown';

    // Add a header
    const header = document.createElement('div');
    header.className = 'message-translate-dropdown-header';
    header.textContent = 'Translate to:';
    dropdown.appendChild(header);

    // Add language options
    languages.forEach(lang => {
      const option = document.createElement('div');
      option.className = 'message-translate-dropdown-option';
      option.textContent = lang.name;
      option.onclick = async () => {
        // Translate the message
        await translateMessage(message, lang.code);

        // Switch to the language
        setCurrentLanguage(lang.code);

        // Remove the dropdown
        document.body.removeChild(dropdown);
      };
      dropdown.appendChild(option);
    });

    // Position the dropdown near the translate button
    const rect = event.target.getBoundingClientRect();
    dropdown.style.top = `${rect.bottom + window.scrollY}px`;
    dropdown.style.left = `${rect.left + window.scrollX}px`;

    // Add the dropdown to the body
    document.body.appendChild(dropdown);

    // Remove the dropdown when clicking outside
    const handleClickOutside = (e) => {
      if (!dropdown.contains(e.target) && e.target !== event.target) {
        document.body.removeChild(dropdown);
        document.removeEventListener('click', handleClickOutside);
      }
    };

    // Add a small delay to prevent immediate closing
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 100);
  };

  // Get translation for a message
  const getTranslation = async (text, targetLanguage) => {
    if (!text || targetLanguage === 'en') {
      return text;
    }

    try {
      // Check if the selected language is an Indian language
      const isIndianLanguage = ['hi', 'mr', 'gu', 'ta', 'te', 'bn', 'kn', 'ml', 'pa', 'or', 'as', 'ur'].includes(targetLanguage);

      console.log(`Using ${isIndianLanguage ? 'Bhashini API' : 'standard translation'} for ${targetLanguage} via getTranslation`);

      // First try the server API
      let translatedContent;
      try {
        if (isIndianLanguage) {
          // Use direct Bhashini API for Indian languages
          console.log('Using direct Bhashini API for Indian language translation');
          try {
            // First try the direct Bhashini API implementation
            translatedContent = await bhashiniDirectTranslate(
              text,
              'en', // Source language is English
              targetLanguage,
              {
                userId: 'cee60134c6bb4d179efd3fda48ff32fe',
                apiKey: '13a647c84b-2747-4f0c-afcd-2ac8235f5318',
                authorization: 'W9QK_zzb7Lnsc_xYAl30Gk64Z8rJU4r_2NBiguZjiMIdkUI_8p-E38M-zc_VVhun'
              }
            );

            // If the result contains an error message, throw an error to try the server API
            if (translatedContent.includes('[Error:') || translatedContent.includes('Error parsing')) {
              throw new Error('Direct Bhashini API failed: ' + translatedContent);
            }
          } catch (directApiError) {
            console.error('Direct Bhashini API failed, falling back to server API:', directApiError);
            // Fall back to the server API
            translatedContent = await apiTranslateText(
              text,
              'en', // Source language is English
              targetLanguage,
              'cee60134c6bb4d179efd3fda48ff32fe', // User ID
              '13a647c84b-2747-4f0c-afcd-2ac8235f5318', // ULCA API Key
              true, // Use pipeline
              'bhashini' // Specifically use Bhashini method
            );
          }
        } else {
          // Use standard translation for non-Indian languages
          console.log('Using standard translation API');
          translatedContent = await apiTranslateText(
            text,
            'en', // Source language is English
            targetLanguage,
            'cee60134c6bb4d179efd3fda48ff32fe', // User ID
            '13a647c84b-2747-4f0c-afcd-2ac8235f5318', // ULCA API Key
            true, // Use pipeline
            'best' // Use best available method
          );
        }
      } catch (apiError) {
        console.error('API translation failed, falling back to simple translator:', apiError);
        // Fallback to simple translator
        translatedContent = simpleTranslateText(text, targetLanguage);
      }

      return translatedContent;
    } catch (error) {
      console.error('Error getting translation:', error);
      return text; // Return original text on error
    }
  };

  // Get message content (original or translated)
  const getMessageContent = (message) => {
    // Create a fallback ID if message doesn't have one
    const messageId = message.id || `msg_${message.createdAt}_${message.senderId}`;

    console.log('Getting message content:', {
      messageId,
      content: message.content,
      currentLanguage
    });

    // If we're in English mode, just return the original content
    if (currentLanguage === 'en') {
      return message.content;
    }

    // If we don't have a translation yet, translate it now
    if (!translatedMessages[messageId] || !translatedMessages[messageId][currentLanguage]) {
      console.log('No translation found, translating now:', message.content);

      // Schedule translation for this message
      translateMessage(message, currentLanguage).catch(error => {
        console.error('Error translating message:', error);
      });

      // Return a loading indicator
      return "Translating...";
    }

    // Return the translated content
    return translatedMessages[messageId][currentLanguage];
  };

  // Get original message content
  const getOriginalContent = (message) => {
    return message.content;
  };

  // Get user display name
  const getUserDisplayName = (user) => {
    return user?.displayName || user?.username || `${user?.firstName || ''} ${user?.lastName || ''}`.trim();
  };

  // Get user avatar or placeholder
  const getUserAvatar = (user) => {
    return user?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(getUserDisplayName(user) || 'User')}&background=random`;
  };

  // Get other participant in conversation
  const getOtherParticipant = (conversation) => {
    if (!conversation) return null;

    try {
      const isCreator = currentUser.id === conversation.creatorId;

      // Check if user and creator objects exist
      if (isCreator && !conversation.user) {
        if (DEBUG) console.log('User object missing in conversation:', conversation);
        return {
          id: conversation.userId,
          username: 'Unknown User',
          displayName: 'Unknown User',
          presence: { isOnline: false, lastActiveAt: new Date().toISOString() }
        };
      }

      if (!isCreator && !conversation.creator) {
        if (DEBUG) console.log('Creator object missing in conversation:', conversation);
        return {
          id: conversation.creatorId,
          username: 'Unknown Creator',
          displayName: 'Unknown Creator',
          presence: { isOnline: false, lastActiveAt: new Date().toISOString() }
        };
      }

      return isCreator ? conversation.user : conversation.creator;
    } catch (error) {
      console.error('Error getting other participant:', error);
      return {
        username: 'Unknown User',
        displayName: 'Unknown User',
        presence: { isOnline: false, lastActiveAt: new Date().toISOString() }
      };
    }
  };

  return (
    <div className={`messages-page ${theme === 'light' ? 'light-theme' : theme === 'dark' ? 'dark-theme' : ''}`}>
      {/* Back button */}
      <div className="back-button-container">
        <Link to="/" className="back-button">
          Back to Home
        </Link>
      </div>

      <div className="messages-main-content">
        <div className="messages-sidebar">
          <div className="messages-header">
            <h2>Messages</h2>
          </div>

          <div className="search-container">
            <UserSearch onSelectUser={handleSelectUser} />
          </div>

          <div className="conversations-list">
            {loading && conversations.length === 0 ? (
              <div className="loading">Loading conversations...</div>
            ) : error ? (
              <div className="error">{error}</div>
            ) : conversations.length === 0 ? (
              <div className="no-conversations">No conversations yet</div>
            ) : (
            conversations.map((conversation) => {
              const otherParticipant = getOtherParticipant(conversation);
              const unreadCount = currentUser.id === conversation.creatorId
                ? conversation.creatorUnreadCount
                : conversation.userUnreadCount;

              return (
                <div
                  key={conversation.id}
                  className={`conversation-item ${activeConversation?.id === conversation.id ? 'active' : ''}`}
                  onClick={() => setActiveConversation(conversation)}
                >
                  <div className="conversation-avatar">
                    <img src={getUserAvatar(otherParticipant)} alt={getUserDisplayName(otherParticipant)} />
                    <span className={`status-indicator ${otherParticipant?.presence?.isOnline ? 'online' : 'offline'}`}></span>
                  </div>
                  <div className="conversation-info">
                    <div className="conversation-name">{getUserDisplayName(otherParticipant)}</div>
                    <div className="conversation-subject">{conversation.subject}</div>
                  </div>
                  <div className="conversation-meta">
                    <div className="conversation-time">{formatTime(conversation.lastMessageAt)}</div>
                    {unreadCount > 0 && (
                      <div className="unread-badge">{unreadCount}</div>
                    )}
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      <div className="messages-content">
        {activeConversation ? (
          <>
            <div className="conversation-header">
              <div className="conversation-avatar">
                <img
                  src={getUserAvatar(getOtherParticipant(activeConversation))}
                  alt={getUserDisplayName(getOtherParticipant(activeConversation))}
                />
                <span className={`status-indicator ${getOtherParticipant(activeConversation)?.presence?.isOnline ? 'online' : 'offline'}`}></span>
              </div>
              <div className="conversation-info">
                <div className="conversation-name">{getUserDisplayName(getOtherParticipant(activeConversation))}</div>
                <div className="conversation-status">
                  {getOtherParticipant(activeConversation)?.presence?.isOnline ? 'Online' : 'Offline'}
                </div>
              </div>
              <div className="conversation-actions">
                <div className="language-selector">
                  <select
                    className="language-select"
                    onChange={(e) => handleTranslateConversation(e.target.value)}
                    value={currentLanguage || 'en'}
                    data-translating={isTranslating.toString()}
                  >
                    <option value="en">🇺🇸 English</option>
                    <optgroup label="Indian Languages">
                      <option value="hi">🇮🇳 Hindi</option>
                      <option value="mr">🇮🇳 Marathi</option>
                      <option value="gu">🇮🇳 Gujarati</option>
                      <option value="ta">🇮🇳 Tamil</option>
                      <option value="te">🇮🇳 Telugu</option>
                      <option value="bn">🇮🇳 Bengali</option>
                      <option value="kn">🇮🇳 Kannada</option>
                      <option value="ml">🇮🇳 Malayalam</option>
                      <option value="pa">🇮🇳 Punjabi</option>
                      <option value="or">🇮🇳 Odia</option>
                      <option value="as">🇮🇳 Assamese</option>
                      <option value="ur">🇮🇳 Urdu</option>
                    </optgroup>
                  </select>
                </div>
              </div>
            </div>

            <div className="messages-container">
              {/* Translation indicator */}
              {currentLanguage !== 'en' && (
                <div className="translation-indicator">
                  <span className="translation-icon">🌐</span>
                  <span className="translation-text">
                    Messages are being displayed in {
                      {
                        // Indian languages only
                        'hi': 'Hindi 🇮🇳',
                        'mr': 'Marathi 🇮🇳',
                        'gu': 'Gujarati 🇮🇳',
                        'ta': 'Tamil 🇮🇳',
                        'te': 'Telugu 🇮🇳',
                        'bn': 'Bengali 🇮🇳',
                        'kn': 'Kannada 🇮🇳',
                        'ml': 'Malayalam 🇮🇳',
                        'pa': 'Punjabi 🇮🇳',
                        'or': 'Odia 🇮🇳',
                        'as': 'Assamese 🇮🇳',
                        'ur': 'Urdu 🇮🇳'
                      }[currentLanguage] || currentLanguage
                    }
                  </span>
                  {isTranslating && <span className="translation-loading">Translating...</span>}
                </div>
              )}

              {loading && messages.length === 0 ? (
                <div className="loading">Loading messages...</div>
              ) : error ? (
                <div className="error">{error}</div>
              ) : messages.length === 0 ? (
                <div className="no-messages">No messages yet</div>
              ) : (
                <>
                  <div className="messages-list">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`message ${message.senderId === currentUser.id ? 'sent' : 'received'}`}
                      >

                        <div className="message-content">
                          {message.senderId !== currentUser.id && (
                            <div className="message-sender">{getUserDisplayName(message.sender)}</div>
                          )}
                          <div className="message-content-wrapper">
                            {/* Show both original and translated content when in a non-English language */}
                            {currentLanguage !== 'en' && (
                              <>
                                {/* Original English message */}
                                <div className="message-original">
                                  <div className="message-language-label">English:</div>
                                  <div className="message-text original">
                                    {getOriginalContent(message)}
                                  </div>
                                </div>

                                {/* Translated message - always show, even if not yet translated */}
                                <div
                                  className="message-text"
                                  data-translated="true"
                                >
                                  <div className="message-language-label">
                                    {
                                      {
                                        'hi': 'Hindi 🇮🇳',
                                        'mr': 'Marathi 🇮🇳',
                                        'gu': 'Gujarati 🇮🇳',
                                        'ta': 'Tamil 🇮🇳',
                                        'te': 'Telugu 🇮🇳',
                                        'bn': 'Bengali 🇮🇳',
                                        'kn': 'Kannada 🇮🇳',
                                        'ml': 'Malayalam 🇮🇳',
                                        'pa': 'Punjabi 🇮🇳',
                                        'or': 'Odia 🇮🇳',
                                        'as': 'Assamese 🇮🇳',
                                        'ur': 'Urdu 🇮🇳'
                                      }[currentLanguage] || currentLanguage
                                    }:
                                  </div>
                                  {/* If message is not yet translated, translate it now */}
                                  {(() => {
                                    const messageId = message.id || `msg_${message.createdAt}_${message.senderId}`;
                                    if (!translatedMessages[messageId] || !translatedMessages[messageId][currentLanguage]) {
                                      // Schedule translation for this message
                                      translateMessage(message, currentLanguage).catch(error => {
                                        console.error('Error translating message:', error);
                                      });

                                      // Show a loading indicator while translating
                                      return <span className="translating-indicator">Translating...</span>;
                                    }
                                    return getMessageContent(message);
                                  })()}
                                </div>
                              </>
                            )}

                            {/* Show only original content when in English */}
                            {currentLanguage === 'en' && (
                              <div className="message-text">
                                {getMessageContent(message)}
                              </div>
                            )}
                          </div>
                          <div className="message-footer">
                            <div className="message-time">{formatTime(message.createdAt)}</div>

                          </div>
                        </div>

                      </div>
                    ))}
                  </div>
                  {typing && (
                    <div className="typing-indicator">
                      {getUserDisplayName(getOtherParticipant(activeConversation))} is typing...
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </>
              )}
            </div>

            <div className="message-input-container">
              <input
                type="text"
                className="message-input"
                placeholder="Type a message..."
                value={messageInput}
                onChange={handleMessageInputChange}
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              />
              <button className="send-button" onClick={sendMessage}>
                Send
              </button>
            </div>
          </>
        ) : (
          <div className="no-conversation-selected">
            <div className="no-conversation-message">
              <h3>Select a conversation or start a new one</h3>
              <p>Use the search bar to find users and start chatting</p>
            </div>
          </div>
        )}
      </div>
      </div>
    </div>
  );
};

export default MessagesPage;
