import { UserModel, RoleModel } from '../models';
import bcrypt from 'bcrypt';

/**
 * Update existing users with enhanced information
 */
async function updateUsers() {
  try {
    console.log('🔄 Starting user updates...');

    // Get role IDs
    const adminRole = await RoleModel.findOne({ code: 'admin' });
    const moderatorRole = await RoleModel.findOne({ code: 'moderator' });
    const creatorRole = await RoleModel.findOne({ code: 'creator' });
    const userRole = await RoleModel.findOne({ code: 'user' });

    if (!adminRole || !moderatorRole || !creatorRole || !userRole) {
      throw new Error('Required roles not found. Please seed roles first.');
    }

    // Update admin user
    const adminUser = await UserModel.findOne({ email: '<EMAIL>' });
    if (adminUser) {
      adminUser.firstName = 'Super';
      adminUser.lastName = 'Administrator';
      adminUser.displayName = 'System Super Admin';
      adminUser.avatar = 'https://ui-avatars.com/api/?name=Super+Admin&background=0D8ABC&color=fff';
      adminUser.phone = '+91 **********';
      adminUser.language = 'en';
      adminUser.timezone = 'Asia/Kolkata';
      adminUser.status = 'active';
      adminUser.emailVerified = true;
      adminUser.phoneVerified = true;
      adminUser.preferences = {
        theme: 'system',
        emailNotifications: {
          marketing: true,
          account: true,
          security: true,
          social: true
        },
        pushNotifications: {
          messages: true,
          comments: true,
          followers: true,
          videoUploads: true
        }
      };
      adminUser.stats = {
        videosUploaded: 0,
        totalViews: 0,
        followers: 0,
        following: 0,
        reputation: 1000
      };
      adminUser.limits = {
        storageUsed: 0,
        storageLimit: ***********, // 10GB
        videoUploadLimit: 1000
      };
      adminUser.updatedAt = new Date();
      adminUser.updatedBy = 'system';

      await adminUser.save();
      console.log('✅ Admin user updated successfully');
    } else {
      console.log('❌ Admin user not found');
    }

    // Update moderator user
    const moderatorUser = await UserModel.findOne({ email: '<EMAIL>' });
    if (moderatorUser) {
      moderatorUser.firstName = 'Content';
      moderatorUser.lastName = 'Moderator';
      moderatorUser.displayName = 'Content Moderator';
      moderatorUser.avatar = 'https://ui-avatars.com/api/?name=Content+Moderator&background=4CAF50&color=fff';
      moderatorUser.phone = '+91 **********';
      moderatorUser.language = 'en';
      moderatorUser.timezone = 'Asia/Kolkata';
      moderatorUser.status = 'active';
      moderatorUser.emailVerified = true;
      moderatorUser.phoneVerified = true;
      moderatorUser.preferences = {
        theme: 'light',
        emailNotifications: {
          marketing: false,
          account: true,
          security: true,
          social: true
        },
        pushNotifications: {
          messages: true,
          comments: true,
          followers: true,
          videoUploads: true
        }
      };
      moderatorUser.stats = {
        videosUploaded: 0,
        totalViews: 0,
        followers: 0,
        following: 0,
        reputation: 500
      };
      moderatorUser.limits = {
        storageUsed: 0,
        storageLimit: **********, // 5GB
        videoUploadLimit: 500
      };
      moderatorUser.updatedAt = new Date();
      moderatorUser.updatedBy = 'system';

      await moderatorUser.save();
      console.log('✅ Moderator user updated successfully');
    } else {
      console.log('❌ Moderator user not found');
    }

    // Update creator user
    const creatorUser = await UserModel.findOne({ email: '<EMAIL>' });
    if (creatorUser) {
      creatorUser.firstName = 'Legal';
      creatorUser.lastName = 'Expert';
      creatorUser.displayName = 'Legal Content Creator';
      creatorUser.avatar = 'https://ui-avatars.com/api/?name=Legal+Expert&background=FF5722&color=fff';
      creatorUser.phone = '+91 **********';
      creatorUser.language = 'en';
      creatorUser.timezone = 'Asia/Kolkata';
      creatorUser.status = 'active';
      creatorUser.emailVerified = true;
      creatorUser.phoneVerified = true;
      creatorUser.preferences = {
        theme: 'dark',
        emailNotifications: {
          marketing: false,
          account: true,
          security: true,
          social: true
        },
        pushNotifications: {
          messages: true,
          comments: true,
          followers: true,
          videoUploads: true
        }
      };
      creatorUser.stats = {
        videosUploaded: 15,
        totalViews: 5000,
        followers: 120,
        following: 50,
        reputation: 750
      };
      creatorUser.limits = {
        storageUsed: **********, // 1GB
        storageLimit: **********, // 8GB
        videoUploadLimit: 800
      };
      creatorUser.updatedAt = new Date();
      creatorUser.updatedBy = 'system';

      await creatorUser.save();
      console.log('✅ Creator user updated successfully');
    } else {
      console.log('❌ Creator user not found');
    }

    // Update regular user
    const regularUser = await UserModel.findOne({ email: '<EMAIL>' });
    if (regularUser) {
      regularUser.firstName = 'Regular';
      regularUser.lastName = 'User';
      regularUser.displayName = 'Standard User';
      regularUser.avatar = 'https://ui-avatars.com/api/?name=Regular+User&background=9C27B0&color=fff';
      regularUser.phone = '+91 **********';
      regularUser.language = 'en';
      regularUser.timezone = 'Asia/Kolkata';
      regularUser.status = 'active';
      regularUser.emailVerified = true;
      regularUser.phoneVerified = false;
      regularUser.preferences = {
        theme: 'system',
        emailNotifications: {
          marketing: false,
          account: true,
          security: true,
          social: false
        },
        pushNotifications: {
          messages: true,
          comments: false,
          followers: true,
          videoUploads: false
        }
      };
      regularUser.stats = {
        videosUploaded: 0,
        totalViews: 0,
        followers: 0,
        following: 10,
        reputation: 50
      };
      regularUser.limits = {
        storageUsed: 0,
        storageLimit: **********, // 1GB
        videoUploadLimit: 100
      };
      regularUser.updatedAt = new Date();
      regularUser.updatedBy = 'system';

      await regularUser.save();
      console.log('✅ Regular user updated successfully');
    } else {
      console.log('❌ Regular user not found');
    }

    console.log('🎉 User updates completed successfully');
  } catch (error) {
    console.error('❌ Error updating users:', error);
    throw error;
  }
}

export default updateUsers;
