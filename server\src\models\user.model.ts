import mongoose, { <PERSON>hem<PERSON>, Document } from 'mongoose';
import bcrypt from 'bcrypt';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * User interface extending the base entity
 */
export interface IUser extends IBaseEntity {
  /**
   * Unique username for the user
   * Used for login and @mentions
   */
  username: string;

  /**
   * User's email address
   * Used for login, notifications, and account recovery
   */
  email: string;

  /**
   * Hashed password for authentication
   * Never stored in plain text for security
   */
  password: string;

  /** User's first/given name */
  firstName: string;

  /** User's last/family name */
  lastName: string;

  /**
   * User's display name shown publicly
   * May be different from first+last name for privacy or preference
   */
  displayName: string;

  /**
   * URL to user's profile picture
   * Optional as users may not upload a profile picture
   */
  avatar?: string;

  /**
   * User's phone number
   * Optional as not all users provide a phone number
   */
  phone?: string;

  /**
   * User's preferred language for the interface
   * ISO 639-1 language code (e.g., 'en', 'es', 'fr')
   */
  language: string;

  /**
   * User's timezone
   * IANA timezone identifier (e.g., 'America/New_York', 'Europe/London')
   */
  timezone: string;

  /**
   * Account status
   *
   * Possible values:
   * - active: Normal active account
   * - pending: Account created but not yet verified
   * - suspended: Temporarily disabled account
   * - banned: Permanently disabled account
   */
  status: 'active' | 'pending' | 'suspended' | 'banned';

  /**
   * Reason for account suspension or ban
   * Only applicable when status is 'suspended' or 'banned'
   */
  statusReason?: string;

  /**
   * Whether email address has been verified
   * Prevents account takeover via email spoofing
   */
  emailVerified: boolean;

  /**
   * Whether phone number has been verified
   * Prevents account takeover via phone spoofing
   */
  phoneVerified: boolean;

  /**
   * Whether two-factor authentication is enabled
   * Adds an extra layer of security
   */
  twoFactorEnabled: boolean;

  /**
   * Two-factor authentication method
   *
   * Possible values:
   * - app: Authenticator app (TOTP)
   * - sms: SMS verification code
   * - email: Email verification code
   */
  twoFactorMethod?: 'app' | 'sms' | 'email';

  /**
   * Secret key for two-factor authentication
   * Used with authenticator apps
   */
  twoFactorSecret?: string;

  /**
   * Backup codes for two-factor authentication recovery
   * Used when the primary 2FA method is unavailable
   */
  twoFactorBackupCodes?: string[];

  /**
   * Array of role IDs assigned to this user
   * Controls permissions and access levels
   */
  roles: string[];

  /**
   * Array of direct permission codes granted to this user
   * Supplements role-based permissions with user-specific ones
   */
  permissions: string[];

  /**
   * User preferences and settings
   * Customizable options for user experience
   */
  preferences: {
    /**
     * Theme preference
     *
     * Possible values:
     * - light: Light theme
     * - dark: Dark theme
     * - system: Follow system preference
     */
    theme: 'light' | 'dark' | 'system';

    /**
     * Email notification preferences
     * Controls which emails the user receives
     */
    emailNotifications: {
      /** Whether to send marketing emails */
      marketing: boolean;

      /** Whether to send account-related emails */
      account: boolean;

      /** Whether to send security-related emails */
      security: boolean;

      /** Whether to send social interaction emails */
      social: boolean;
    };

    /**
     * Push notification preferences
     * Controls which push notifications the user receives
     */
    pushNotifications: {
      /** Whether to send new message notifications */
      messages: boolean;

      /** Whether to send new comment notifications */
      comments: boolean;

      /** Whether to send new follower notifications */
      followers: boolean;

      /** Whether to send video upload notifications */
      videoUploads: boolean;
    };
  };

  /** Timestamp of user's most recent login */
  lastLoginAt: Date;

  /** IP address from which user last logged in */
  lastLoginIp: string;

  /** User activity statistics */
  stats: {
    /** Number of videos uploaded by this user */
    videosUploaded: number;

    /** Total views across all user's content */
    totalViews: number;

    /** Number of users following this user */
    followers: number;

    /** Number of users this user is following */
    following: number;

    /** Reputation/karma points in the system */
    reputation: number;
  };

  /** Resource usage and limits */
  limits?: {
    /** Current storage space used in bytes */
    storageUsed: number;

    /** Maximum storage space allowed in bytes */
    storageLimit: number;

    /** Maximum number of videos user can upload */
    videoUploadLimit: number;
  };

  /** Engaxe integration */
  engaxe?: {
    /** Engaxe user ID */
    userId: string;

    /** Engaxe session ID */
    sessionId: string;

    /** Engaxe device ID */
    deviceId?: string;

    /** When the session expires */
    expiresAt: Date;
  };

  /** User presence information */
  presence: {
    /** Whether the user is currently online */
    isOnline: boolean;

    /** When the user was last active */
    lastActiveAt: Date;

    /** User's current status message */
    statusMessage?: string;
  };

  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>;
}

/**
 * User schema definition
 */
const UserSchema = new Schema<IUser>(
  {
    username: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
      minlength: 3,
      maxlength: 30,
      match: /^[a-z0-9_.-]+$/,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
      match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
    password: {
      type: String,
      required: true,
      minlength: 8,
    },
    firstName: {
      type: String,
      required: true,
      trim: true,
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
    },
    displayName: {
      type: String,
      required: true,
      trim: true,
    },
    avatar: {
      type: String,
    },
    phone: {
      type: String,
      trim: true,
    },
    language: {
      type: String,
      default: 'en',
    },
    timezone: {
      type: String,
      default: 'UTC',
    },
    status: {
      type: String,
      enum: ['active', 'pending', 'suspended', 'banned'],
      default: 'pending',
    },
    statusReason: {
      type: String,
    },
    emailVerified: {
      type: Boolean,
      default: false,
    },
    phoneVerified: {
      type: Boolean,
      default: false,
    },
    twoFactorEnabled: {
      type: Boolean,
      default: false,
    },
    twoFactorMethod: {
      type: String,
      enum: ['app', 'sms', 'email'],
    },
    twoFactorSecret: {
      type: String,
    },
    twoFactorBackupCodes: {
      type: [String],
    },
    roles: {
      type: [String],
      default: [],
    },
    permissions: {
      type: [String],
      default: [],
    },
    preferences: {
      theme: {
        type: String,
        enum: ['light', 'dark', 'system'],
        default: 'system',
      },
      emailNotifications: {
        marketing: {
          type: Boolean,
          default: true,
        },
        account: {
          type: Boolean,
          default: true,
        },
        security: {
          type: Boolean,
          default: true,
        },
        social: {
          type: Boolean,
          default: true,
        },
      },
      pushNotifications: {
        messages: {
          type: Boolean,
          default: true,
        },
        comments: {
          type: Boolean,
          default: true,
        },
        followers: {
          type: Boolean,
          default: true,
        },
        videoUploads: {
          type: Boolean,
          default: true,
        },
      },
    },
    lastLoginAt: {
      type: Date,
    },
    lastLoginIp: {
      type: String,
    },
    stats: {
      videosUploaded: {
        type: Number,
        default: 0,
      },
      totalViews: {
        type: Number,
        default: 0,
      },
      followers: {
        type: Number,
        default: 0,
      },
      following: {
        type: Number,
        default: 0,
      },
      reputation: {
        type: Number,
        default: 0,
      },
    },
    limits: {
      storageUsed: {
        type: Number,
        default: 0,
      },
      storageLimit: {
        type: Number,
        default: 1073741824, // 1GB in bytes
      },
      videoUploadLimit: {
        type: Number,
        default: 100,
      },
    },
    engaxe: {
      userId: {
        type: String,
      },
      sessionId: {
        type: String,
      },
      deviceId: {
        type: String,
      },
      expiresAt: {
        type: Date,
      },
    },
    presence: {
      isOnline: {
        type: Boolean,
        default: false,
      },
      lastActiveAt: {
        type: Date,
        default: Date.now,
      },
      statusMessage: {
        type: String,
      },
    },
  }
);

// Merge with base schema
UserSchema.add(BaseSchema);

// Add compound indexes for unique fields with soft delete support
UserSchema.index({ username: 1, deletedAt: 1 }, { unique: true, sparse: true });
UserSchema.index({ email: 1, deletedAt: 1 }, { unique: true, sparse: true });
UserSchema.index({ phone: 1, deletedAt: 1 }, { unique: true, sparse: true });

// Pre-save middleware to hash password
UserSchema.pre('save', async function (next) {
  // Only hash the password if it's modified or new
  if (!this.isModified('password')) return next();

  try {
    // Generate salt and hash password
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error: any) {
    next(error);
  }
});

// Method to compare password
UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

// Create and export the User model
const UserModel = mongoose.model<IUser>('User', UserSchema);
export default UserModel;
