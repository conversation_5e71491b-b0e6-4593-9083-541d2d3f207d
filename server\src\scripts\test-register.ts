import axios from 'axios';

async function testRegister() {
  try {
    console.log('Attempting to register a new user...');

    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'Password123!',
      firstName: 'Test',
      lastName: 'User',
      displayName: 'Test User'
    };

    console.log('Request payload:', JSON.stringify(userData, null, 2));

    const response = await axios.post('http://localhost:3001/api/v1/users/register', userData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Registration successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error: any) {
    console.error('Registration failed!');

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
      console.error('Headers:', JSON.stringify(error.response.headers, null, 2));
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error:', error.message);
    }
  }
}

testRegister();
