import { JsonStorageUtil } from '../utils/json-storage.util';

/**
 * Service for managing video JSON storage operations
 */
export class VideoJsonStorageService {
  
  /**
   * Initialize the JSON storage system
   */
  async initialize(): Promise<void> {
    try {
      await JsonStorageUtil.initializeStorage();
      console.log('✅ Video JSON storage initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize video JSON storage:', error);
      throw error;
    }
  }

  /**
   * Add a new video to JSON storage
   */
  async addVideo(video: any): Promise<void> {
    try {
      const videosData = await JsonStorageUtil.readAllVideos();
      const formattedVideo = JsonStorageUtil.formatVideoForStorage(video);
      
      // Check if video already exists
      const existingIndex = videosData.videos.findIndex((v: any) => v.id === video.id);
      
      if (existingIndex >= 0) {
        // Update existing video
        videosData.videos[existingIndex] = formattedVideo;
        console.log(`📝 Updated video in JSON storage: ${video.title} (ID: ${video.id})`);
      } else {
        // Add new video
        videosData.videos.push(formattedVideo);
        console.log(`➕ Added new video to JSON storage: ${video.title} (ID: ${video.id})`);
      }

      await JsonStorageUtil.writeAllVideos(videosData);
    } catch (error) {
      console.error('❌ Error adding video to JSON storage:', error);
      throw error;
    }
  }

  /**
   * Update an existing video in JSON storage
   */
  async updateVideo(video: any): Promise<void> {
    try {
      const videosData = await JsonStorageUtil.readAllVideos();
      const formattedVideo = JsonStorageUtil.formatVideoForStorage(video);
      
      const existingIndex = videosData.videos.findIndex((v: any) => v.id === video.id);
      
      if (existingIndex >= 0) {
        videosData.videos[existingIndex] = formattedVideo;
        await JsonStorageUtil.writeAllVideos(videosData);
        console.log(`📝 Updated video in JSON storage: ${video.title} (ID: ${video.id})`);
      } else {
        console.warn(`⚠️ Video not found in JSON storage for update: ${video.id}`);
        // Add as new video if not found
        await this.addVideo(video);
      }
    } catch (error) {
      console.error('❌ Error updating video in JSON storage:', error);
      throw error;
    }
  }

  /**
   * Remove a video from JSON storage
   */
  async removeVideo(videoId: string): Promise<void> {
    try {
      const videosData = await JsonStorageUtil.readAllVideos();
      const initialLength = videosData.videos.length;
      
      videosData.videos = videosData.videos.filter((v: any) => v.id !== videoId);
      
      if (videosData.videos.length < initialLength) {
        await JsonStorageUtil.writeAllVideos(videosData);
        console.log(`🗑️ Removed video from JSON storage: ${videoId}`);
      } else {
        console.warn(`⚠️ Video not found in JSON storage for removal: ${videoId}`);
      }
    } catch (error) {
      console.error('❌ Error removing video from JSON storage:', error);
      throw error;
    }
  }

  /**
   * Get all videos from JSON storage
   */
  async getAllVideos(): Promise<any[]> {
    try {
      const videosData = await JsonStorageUtil.readAllVideos();
      return videosData.videos || [];
    } catch (error) {
      console.error('❌ Error getting all videos from JSON storage:', error);
      return [];
    }
  }

  /**
   * Search videos in JSON storage by keywords
   */
  async searchVideos(query: string): Promise<any[]> {
    try {
      const videosData = await JsonStorageUtil.readAllVideos();
      const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 2);
      
      if (searchTerms.length === 0) {
        return videosData.videos || [];
      }

      const matchedVideos = videosData.videos.filter((video: any) => {
        // Search only in title
        const titleMatch = searchTerms.some(term =>
          video.title?.toLowerCase().includes(term)
        );

        return titleMatch;
      });

      console.log(`🔍 Found ${matchedVideos.length} videos matching query: "${query}"`);
      return matchedVideos;
    } catch (error) {
      console.error('❌ Error searching videos in JSON storage:', error);
      return [];
    }
  }

  /**
   * Get video by ID from JSON storage
   */
  async getVideoById(videoId: string): Promise<any | null> {
    try {
      const videosData = await JsonStorageUtil.readAllVideos();
      const video = videosData.videos.find((v: any) => v.id === videoId);
      return video || null;
    } catch (error) {
      console.error('❌ Error getting video by ID from JSON storage:', error);
      return null;
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<any> {
    try {
      const videosData = await JsonStorageUtil.readAllVideos();
      return {
        totalVideos: videosData.videos?.length || 0,
        lastUpdated: videosData.metadata?.lastUpdated,
        version: videosData.metadata?.version,
        categories: this.getCategoriesStats(videosData.videos || []),
        visibility: this.getVisibilityStats(videosData.videos || [])
      };
    } catch (error) {
      console.error('❌ Error getting storage stats:', error);
      return {
        totalVideos: 0,
        lastUpdated: null,
        version: '1.0.0',
        categories: {},
        visibility: {}
      };
    }
  }

  /**
   * Get categories statistics
   */
  private getCategoriesStats(videos: any[]): Record<string, number> {
    const stats: Record<string, number> = {};
    videos.forEach(video => {
      if (video.category) {
        stats[video.category] = (stats[video.category] || 0) + 1;
      }
    });
    return stats;
  }

  /**
   * Get visibility statistics
   */
  private getVisibilityStats(videos: any[]): Record<string, number> {
    const stats: Record<string, number> = {};
    videos.forEach(video => {
      if (video.visibility) {
        stats[video.visibility] = (stats[video.visibility] || 0) + 1;
      }
    });
    return stats;
  }

  /**
   * Sync all videos from database to JSON storage
   */
  async syncAllVideosFromDatabase(videos: any[]): Promise<void> {
    try {
      const videosData = {
        metadata: {
          totalVideos: videos.length,
          lastUpdated: new Date().toISOString(),
          version: '1.0.0',
          syncedAt: new Date().toISOString()
        },
        videos: videos.map(video => JsonStorageUtil.formatVideoForStorage(video))
      };

      await JsonStorageUtil.writeAllVideos(videosData);
      console.log(`🔄 Synced ${videos.length} videos from database to JSON storage`);
    } catch (error) {
      console.error('❌ Error syncing videos from database:', error);
      throw error;
    }
  }
}
