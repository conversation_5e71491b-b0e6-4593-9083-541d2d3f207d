import { FastifyReply } from 'fastify';

/**
 * Standard error response structure
 */
export interface ErrorResponse {
  success: boolean;
  message: string;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}

/**
 * Custom error class with status code and error code
 */
export class AppError extends Error {
  statusCode: number;
  errorCode: string;
  details?: any;

  constructor(message: string, statusCode: number = 400, errorCode: string = 'BAD_REQUEST', details?: any) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.details = details;
    
    // Ensure the name of this error is the same as the class name
    this.name = this.constructor.name;
    
    // This captures the proper stack trace in Node.js
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Handle controller errors in a consistent way
 * @param error The error to handle
 * @param reply The Fastify reply object
 * @returns A standardized error response
 */
export function handleControllerError(error: any, reply: FastifyReply): FastifyReply {
  console.error('Controller error:', error);
  
  // If it's our custom AppError, use its properties
  if (error instanceof AppError) {
    return reply.code(error.statusCode).send({
      success: false,
      message: error.message,
      error: {
        code: error.errorCode,
        message: error.message,
        details: error.details
      }
    });
  }
  
  // Handle Mongoose validation errors
  if (error.name === 'ValidationError') {
    return reply.code(400).send({
      success: false,
      message: 'Validation error',
      error: {
        code: 'VALIDATION_ERROR',
        message: error.message,
        details: error.errors
      }
    });
  }
  
  // Handle MongoDB duplicate key errors
  if (error.name === 'MongoError' && error.code === 11000) {
    return reply.code(409).send({
      success: false,
      message: 'Duplicate key error',
      error: {
        code: 'DUPLICATE_KEY',
        message: 'A record with this key already exists',
        details: error.keyValue
      }
    });
  }
  
  // Handle other errors
  const statusCode = error.statusCode || 500;
  const errorCode = error.errorCode || 'INTERNAL_SERVER_ERROR';
  const message = error.message || 'An unexpected error occurred';
  
  return reply.code(statusCode).send({
    success: false,
    message,
    error: {
      code: errorCode,
      message,
      details: error.details || undefined
    }
  });
}
