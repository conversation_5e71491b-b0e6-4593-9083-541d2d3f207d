import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Home, ChevronRight, ArrowUpDown, Search, Ban, Trash2, AlertCircle, Shield, ShieldAlert, ShieldCheck, ShieldX, Mail, Globe, Wifi } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Mock data for banned users
const mockBannedUsers = [
  { id: '1', banned: 'IP Address', value: '***********', date: '2023-05-15' },
  { id: '2', banned: 'E-mail', value: '<EMAIL>', date: '2023-06-20' },
  { id: '3', banned: 'IP Range', value: '******** - **********', date: '2023-07-10' },
  { id: '4', banned: 'IP Address', value: '**********', date: '2023-08-05' },
  { id: '5', banned: 'E-mail', value: '<EMAIL>', date: '2023-09-12' },
];

export default function BanUsersPage() {
  const [bannedUsers, setBannedUsers] = useState(mockBannedUsers);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [ipOrEmailInput, setIpOrEmailInput] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [banType, setBanType] = useState('auto');
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  // Filter banned users based on search term
  const filteredUsers = bannedUsers.filter(user => {
    const matchesSearch = 
      user.value.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.banned.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = 
      activeTab === 'all' ? true :
      activeTab === 'ip' ? user.banned === 'IP Address' :
      activeTab === 'email' ? user.banned === 'E-mail' :
      activeTab === 'range' ? user.banned === 'IP Range' : true;
    
    return matchesSearch && matchesType;
  });

  // Sort banned users
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    if (!sortField) return 0;

    const fieldA = a[sortField as keyof typeof a];
    const fieldB = b[sortField as keyof typeof b];

    if (fieldA < fieldB) return sortDirection === 'asc' ? -1 : 1;
    if (fieldA > fieldB) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Handle user selection
  const handleSelectUser = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers([...selectedUsers, id]);
    } else {
      setSelectedUsers(selectedUsers.filter(userId => userId !== id));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(sortedUsers.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  // Show success alert
  const displaySuccessAlert = (message: string) => {
    setSuccessMessage(message);
    setShowSuccessAlert(true);
    setTimeout(() => setShowSuccessAlert(false), 3000);
  };

  // Handle ban
  const handleBan = () => {
    if (!ipOrEmailInput.trim()) return;

    // Determine if input is IP, IP range, or email
    let bannedType = banType === 'auto' ? 'IP Address' : banType;
    
    if (banType === 'auto') {
      if (ipOrEmailInput.includes('@')) {
        bannedType = 'E-mail';
      } else if (ipOrEmailInput.includes('-')) {
        bannedType = 'IP Range';
      }
    }

    const newBannedUser = {
      id: (Math.max(...bannedUsers.map(u => parseInt(u.id)), 0) + 1).toString(),
      banned: bannedType,
      value: ipOrEmailInput,
      date: new Date().toISOString().split('T')[0]
    };

    setBannedUsers([...bannedUsers, newBannedUser]);
    setIpOrEmailInput('');
    displaySuccessAlert(`Successfully banned ${bannedType}: ${ipOrEmailInput}`);
  };

  // Handle delete selected
  const handleDeleteSelected = () => {
    if (selectedUsers.length === 0) return;

    if (window.confirm('Are you sure you want to delete the selected banned users?')) {
      setBannedUsers(bannedUsers.filter(user => !selectedUsers.includes(user.id)));
      setSelectedUsers([]);
      displaySuccessAlert(`Successfully deleted ${selectedUsers.length} banned entries`);
    }
  };

  // Get icon for ban type
  const getBanTypeIcon = (type: string) => {
    switch (type) {
      case 'IP Address':
        return <Globe className="h-4 w-4 text-blue-500" />;
      case 'E-mail':
        return <Mail className="h-4 w-4 text-green-500" />;
      case 'IP Range':
        return <Wifi className="h-4 w-4 text-purple-500" />;
      default:
        return <Ban className="h-4 w-4 text-red-500" />;
    }
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Ban Users</h1>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Tools
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Ban Users</span>
            </div>

            {showSuccessAlert && (
              <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>
                  {successMessage}
                </AlertDescription>
              </Alert>
            )}

            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Add Ban</CardTitle>
                <CardDescription>Ban users by IP address, IP range, or email</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="banType">Ban Type</Label>
                    <Select value={banType} onValueChange={setBanType}>
                      <SelectTrigger id="banType">
                        <SelectValue placeholder="Select ban type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="auto">Auto Detect</SelectItem>
                        <SelectItem value="IP Address">IP Address</SelectItem>
                        <SelectItem value="IP Range">IP Range</SelectItem>
                        <SelectItem value="E-mail">Email</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="ipOrEmail">IP Address, IP Range, or Email</Label>
                    <div className="flex gap-2">
                      <Input
                        id="ipOrEmail"
                        value={ipOrEmailInput}
                        onChange={(e) => setIpOrEmailInput(e.target.value)}
                        placeholder={banType === 'auto' ? "Enter IP, IP range, or email" : 
                          banType === 'IP Address' ? "Enter IP address (e.g., ***********)" :
                          banType === 'IP Range' ? "Enter IP range (e.g., ******** - **********)" :
                          "Enter email address"}
                      />
                      <Button
                        onClick={handleBan}
                        className="gap-2"
                      >
                        <Ban className="h-4 w-4" />
                        Ban
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Manage Banned Users</CardTitle>
                    <CardDescription>View and manage banned IPs and emails</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="all" className="w-full mb-6" value={activeTab} onValueChange={setActiveTab}>
                  <TabsList>
                    <TabsTrigger value="all" className="flex items-center gap-2">
                      <Ban className="h-4 w-4" />
                      All Bans
                    </TabsTrigger>
                    <TabsTrigger value="ip" className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      IP Addresses
                    </TabsTrigger>
                    <TabsTrigger value="range" className="flex items-center gap-2">
                      <Wifi className="h-4 w-4" />
                      IP Ranges
                    </TabsTrigger>
                    <TabsTrigger value="email" className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Emails
                    </TabsTrigger>
                  </TabsList>
                </Tabs>

                <div className="flex flex-col md:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search banned users..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedUsers.length === sortedUsers.length && sortedUsers.length > 0}
                            onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                          />
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('id')}>
                          <div className="flex items-center">
                            ID
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('banned')}>
                          <div className="flex items-center">
                            TYPE
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('value')}>
                          <div className="flex items-center">
                            VALUE
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('date')}>
                          <div className="flex items-center">
                            DATE
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead className="text-right">ACTION</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {sortedUsers.length > 0 ? (
                        sortedUsers.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedUsers.includes(user.id)}
                                onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                              />
                            </TableCell>
                            <TableCell className="font-medium">{user.id}</TableCell>
                            <TableCell>
                              <Badge variant="outline" className="flex items-center gap-1 w-fit">
                                {getBanTypeIcon(user.banned)}
                                {user.banned}
                              </Badge>
                            </TableCell>
                            <TableCell>{user.value}</TableCell>
                            <TableCell>{user.date}</TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                                onClick={() => {
                                  if (window.confirm('Are you sure you want to delete this banned user?')) {
                                    setBannedUsers(bannedUsers.filter(u => u.id !== user.id));
                                    displaySuccessAlert(`Successfully deleted ban for ${user.value}`);
                                  }
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                            <div className="flex flex-col items-center justify-center py-4">
                              <ShieldCheck className="h-10 w-10 text-muted-foreground mb-2 opacity-20" />
                              <p>No banned users found</p>
                              <p className="text-sm text-muted-foreground">Add a ban using the form above</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>

                {selectedUsers.length > 0 && (
                  <div className="flex items-center justify-between bg-muted/30 p-3 rounded-md mt-4">
                    <div className="text-sm">
                      <span className="font-medium">{selectedUsers.length}</span> {selectedUsers.length === 1 ? 'item' : 'items'} selected
                    </div>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleDeleteSelected}
                      className="gap-2"
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete Selected
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
