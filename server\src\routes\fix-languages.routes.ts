import { FastifyInstance } from 'fastify';
import { FixLanguagesController } from '../controllers/fix-languages.controller';
import { authenticate } from '../middleware/auth';

export default async function (fastify: FastifyInstance) {
  const fixLanguagesController = new FixLanguagesController();

  // Fix languages for all videos (admin only)
  fastify.post(
    '/fix-all',
    {
      preHandler: authenticate, // Add authentication to ensure only admins can access this endpoint
    },
    (request, reply) => fixLanguagesController.fixAllLanguages(request as any, reply)
  );
}
