const mongoose = require('mongoose');

async function checkLanguages() {
  try {
    await mongoose.connect('mongodb://localhost:27017/lawengaxe');
    console.log('Connected to MongoDB');

    // Define a simple schema for the Video model
    const VideoSchema = new mongoose.Schema({
      id: String,
      title: String,
      url: String,
      languages: [{
        code: String,
        name: String,
        flag: String,
        isDefault: Boolean,
        url: String
      }]
    });

    const Video = mongoose.model('Video', VideoSchema);

    // Find videos with the language URL "KgLQEX"
    console.log('Searching for videos with language URL "KgLQEX"...');
    const videos = await Video.find({ 'languages.url': 'KgLQEX' });

    if (videos.length === 0) {
      console.log('No videos found with language URL "KgLQEX"');
    } else {
      console.log(`Found ${videos.length} videos with language URL "KgLQEX":`);
      
      videos.forEach((video, index) => {
        console.log(`\nVideo ${index + 1}:`);
        console.log(`ID: ${video.id}`);
        console.log(`Title: ${video.title}`);
        console.log(`URL: ${video.url}`);
        console.log('Languages:');
        
        video.languages.forEach((lang, langIndex) => {
          console.log(`  Language ${langIndex + 1}:`);
          console.log(`    Code: ${lang.code}`);
          console.log(`    Name: ${lang.name}`);
          console.log(`    Flag: ${lang.flag}`);
          console.log(`    isDefault: ${lang.isDefault}`);
          console.log(`    URL: ${lang.url}`);
        });
      });
    }

    // Now let's check all videos to see their languages
    console.log('\n\nChecking all videos for their languages...');
    const allVideos = await Video.find({});
    
    console.log(`Found ${allVideos.length} total videos in the database`);
    
    allVideos.forEach((video, index) => {
      if (video.languages && video.languages.length > 0) {
        console.log(`\nVideo ${index + 1}:`);
        console.log(`ID: ${video.id}`);
        console.log(`Title: ${video.title}`);
        console.log(`URL: ${video.url}`);
        console.log(`Languages: ${video.languages.length}`);
        
        video.languages.forEach((lang, langIndex) => {
          console.log(`  Language ${langIndex + 1}:`);
          console.log(`    Code: ${lang.code}`);
          console.log(`    Name: ${lang.name}`);
          console.log(`    Flag: ${lang.flag}`);
          console.log(`    isDefault: ${lang.isDefault}`);
          console.log(`    URL: ${lang.url}`);
        });
      }
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Disconnected from MongoDB');
  }
}

checkLanguages();
