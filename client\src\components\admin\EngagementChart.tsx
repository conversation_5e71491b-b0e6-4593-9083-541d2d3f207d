import React, { useState } from 'react';
import { useTheme } from '@/context/ThemeContext';

interface EngagementChartProps {
  timeRange?: 'Today' | 'Yesterday' | 'This Week' | 'This Month' | 'Last Month' | 'This Year';
}

export default function EngagementChart({ timeRange = 'This Year' }: EngagementChartProps) {
  const { theme } = useTheme();
  const [activeDatasets, setActiveDatasets] = useState<{ comments: boolean; likes: boolean; dislikes: boolean }>({
    comments: true,
    likes: true,
    dislikes: true
  });

  // Define different data sets based on time range
  const getChartData = () => {
    switch (timeRange) {
      case 'Today':
        return {
          labels: ['12am', '4am', '8am', '12pm', '4pm', '8pm'],
          commentsData: [0.2, 0.1, 0.4, 0.8, 0.5, 0.3],
          likesData: [0.1, 0.05, 0.3, 0.6, 0.4, 0.2],
          dislikesData: [0.05, 0.02, 0.1, 0.3, 0.2, 0.1]
        };
      case 'Yesterday':
        return {
          labels: ['12am', '4am', '8am', '12pm', '4pm', '8pm'],
          commentsData: [0.15, 0.08, 0.3, 0.7, 0.4, 0.2],
          likesData: [0.08, 0.04, 0.2, 0.5, 0.3, 0.15],
          dislikesData: [0.04, 0.01, 0.08, 0.25, 0.15, 0.08]
        };
      case 'This Week':
        return {
          labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          commentsData: [0.9, 0.7, 0.5, 0.6, 0.8, 0.4, 0.3],
          likesData: [0.7, 0.5, 0.4, 0.5, 0.6, 0.3, 0.2],
          dislikesData: [0.3, 0.2, 0.15, 0.2, 0.25, 0.1, 0.08]
        };
      case 'This Month':
        return {
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          commentsData: [0.8, 0.6, 0.7, 0.5],
          likesData: [0.6, 0.5, 0.55, 0.4],
          dislikesData: [0.25, 0.2, 0.22, 0.18]
        };
      case 'Last Month':
        return {
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          commentsData: [0.7, 0.5, 0.6, 0.4],
          likesData: [0.5, 0.4, 0.45, 0.3],
          dislikesData: [0.2, 0.15, 0.18, 0.12]
        };
      case 'This Year':
      default:
        return {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          commentsData: [1.0, 0.9, 0.85, 0.8, 0.75, 0.7, 0.65, 0.6, 0.55, 0.5, 0.45, 0.4],
          likesData: [0.8, 0.75, 0.7, 0.65, 0.6, 0.55, 0.5, 0.45, 0.4, 0.35, 0.3, 0.25],
          dislikesData: [0.4, 0.35, 0.3, 0.25, 0.2, 0.18, 0.16, 0.14, 0.12, 0.1, 0.08, 0.06]
        };
    }
  };

  const { labels, commentsData, likesData, dislikesData } = getChartData();

  const maxValue = Math.max(
    ...(activeDatasets.comments ? commentsData : [0]),
    ...(activeDatasets.likes ? likesData : [0]),
    ...(activeDatasets.dislikes ? dislikesData : [0])
  ) || 1.0; // Fallback to 1.0 if all are disabled

  const toggleDataset = (dataset: 'comments' | 'likes' | 'dislikes') => {
    setActiveDatasets(prev => ({
      ...prev,
      [dataset]: !prev[dataset]
    }));
  };

  return (
    <div className={`p-3 ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-800'} rounded-lg shadow`}>
      <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} mb-2`}>Comments, Likes, Dislikes Chart</div>

      <div className="flex items-center justify-center space-x-3 mb-2">
        <div
          className={`flex items-center cursor-pointer ${!activeDatasets.comments && 'opacity-50'}`}
          onClick={() => toggleDataset('comments')}
        >
          <div className="w-4 h-4 bg-yellow-300 mr-2"></div>
          <span className={`text-sm ${activeDatasets.comments ? (theme === 'dark' ? 'text-white' : 'text-gray-800') + ' font-medium' : 'text-gray-500 line-through'}`}>Comments</span>
        </div>
        <div
          className={`flex items-center cursor-pointer ${!activeDatasets.likes && 'opacity-50'}`}
          onClick={() => toggleDataset('likes')}
        >
          <div className="w-4 h-4 bg-blue-300 mr-2"></div>
          <span className={`text-sm ${activeDatasets.likes ? (theme === 'dark' ? 'text-white' : 'text-gray-800') + ' font-medium' : 'text-gray-500 line-through'}`}>Likes</span>
        </div>
        <div
          className={`flex items-center cursor-pointer ${!activeDatasets.dislikes && 'opacity-50'}`}
          onClick={() => toggleDataset('dislikes')}
        >
          <div className="w-4 h-4 bg-pink-300 mr-2"></div>
          <span className={`text-sm ${activeDatasets.dislikes ? (theme === 'dark' ? 'text-white' : 'text-gray-800') + ' font-medium' : 'text-gray-500 line-through'}`}>Dislikes</span>
        </div>
      </div>

      <div className="relative px-1">
        {/* Y-axis labels */}
        <div className={`absolute left-0 top-0 h-full flex flex-col justify-between text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} py-2`}>
          <div>1.0</div>
          <div>0.9</div>
          <div>0.8</div>
          <div>0.7</div>
          <div>0.6</div>
          <div>0.5</div>
          <div>0.4</div>
          <div>0.3</div>
          <div>0.2</div>
          <div>0.1</div>
          <div>0</div>
        </div>

        {/* Chart grid lines */}
        <div className={`ml-8 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="h-32 flex flex-col justify-between">
            {[...Array(10)].map((_, i) => (
              <div key={i} className={`border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} w-full h-0`}></div>
            ))}
          </div>
        </div>

        <div className="ml-8 h-32 relative">
          {/* Area chart representation */}
          <svg className="w-full h-full" viewBox="0 0 1200 160" preserveAspectRatio="none">
            {/* Comments area */}
            {activeDatasets.comments && (
              <path
                d={`
                  M0,${160 - (commentsData[0] / maxValue) * 120}
                  ${labels.map((_, i) => `L${(i * 100) + 100},${160 - (commentsData[i] / maxValue) * 120}`).join(' ')}
                  L1200,160 L0,160 Z
                `}
                fill="rgba(253, 224, 71, 0.2)"
                stroke="rgba(253, 224, 71, 0.5)"
                strokeWidth="2"
              />
            )}

            {/* Likes line */}
            {activeDatasets.likes && (
              <path
                d={`
                  M0,${160 - (likesData[0] / maxValue) * 120}
                  ${labels.map((_, i) => `L${(i * 100) + 100},${160 - (likesData[i] / maxValue) * 120}`).join(' ')}
                `}
                fill="none"
                stroke="rgba(125, 211, 252, 0.8)"
                strokeWidth="2"
              />
            )}

            {/* Dislikes line */}
            {activeDatasets.dislikes && (
              <path
                d={`
                  M0,${160 - (dislikesData[0] / maxValue) * 120}
                  ${labels.map((_, i) => `L${(i * 100) + 100},${160 - (dislikesData[i] / maxValue) * 120}`).join(' ')}
                `}
                fill="none"
                stroke="rgba(249, 168, 212, 0.8)"
                strokeWidth="2"
              />
            )}
          </svg>

          {/* X-axis labels */}
          <div className="grid grid-cols-12 gap-0 mt-2 mx-1 px-1">
            {labels.map((label) => (
              <div key={label} className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} text-center overflow-hidden`} style={{ fontSize: '0.65rem' }}>{label}</div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
