import React from 'react';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, ExternalLink, Settings } from "lucide-react";
import { useNavigate } from 'react-router-dom';

interface AIAssistantErrorProps {
  error: string;
  errorType?: string;
  onRetry?: () => void;
}

/**
 * AIAssistantErrorHandler component
 *
 * This component displays error messages from the AI Assistant API
 * and provides guidance on how to fix common issues.
 */
export function AIAssistantErrorHandler({ error, errorType, onRetry }: AIAssistantErrorProps) {
  const navigate = useNavigate();

  const navigateToSettings = () => {
    navigate('/settings?tab=ai-providers');
  };

  const openOpenRouterWebsite = () => {
    window.open('https://openrouter.ai/keys', '_blank');
  };

  // Render different content based on error type
  const renderErrorContent = () => {
    switch (errorType) {
      case 'auth_error':
        return (
          <>
            <AlertTitle className="text-red-600">Authentication Error</AlertTitle>
            <AlertDescription className="mt-2">
              <p className="mb-2">{error || 'Failed to authenticate with OpenRouter API. Please check your API key.'}</p>
              <div className="mt-4 space-y-2">
                <p className="font-medium">How to fix this:</p>
                <ol className="list-decimal pl-5 space-y-1">
                  <li>Go to Settings → AI Providers</li>
                  <li>Make sure you have a valid OpenRouter API key</li>
                  <li>If you don't have an API key, get one from OpenRouter</li>
                </ol>
                <div className="flex flex-wrap gap-2 mt-3">
                  <Button variant="outline" size="sm" onClick={navigateToSettings}>
                    <Settings className="mr-2 h-4 w-4" />
                    Go to Settings
                  </Button>
                  <Button size="sm" onClick={openOpenRouterWebsite}>
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Get OpenRouter API Key
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </>
        );

      case 'permission_error':
        return (
          <>
            <AlertTitle className="text-red-600">Permission Error</AlertTitle>
            <AlertDescription className="mt-2">
              <p className="mb-2">{error || 'Your API key does not have permission to use this service.'}</p>
              <div className="mt-4">
                <p className="font-medium">How to fix this:</p>
                <ol className="list-decimal pl-5 space-y-1">
                  <li>Check if your OpenRouter account has access to the selected model</li>
                  <li>Try using a different model in the settings</li>
                  <li>Contact OpenRouter support if the issue persists</li>
                </ol>
                <Button variant="outline" size="sm" className="mt-3" onClick={navigateToSettings}>
                  <Settings className="mr-2 h-4 w-4" />
                  Adjust Settings
                </Button>
              </div>
            </AlertDescription>
          </>
        );

      case 'rate_limit_error':
        return (
          <>
            <AlertTitle className="text-amber-600">Rate Limit Exceeded</AlertTitle>
            <AlertDescription className="mt-2">
              <p className="mb-2">{error || 'You have reached the rate limit for the OpenRouter API.'}</p>
              <p>Please wait a few minutes and try again, or upgrade your OpenRouter plan for higher limits.</p>
              {onRetry && (
                <Button variant="outline" size="sm" className="mt-3" onClick={onRetry}>
                  Try Again
                </Button>
              )}
            </AlertDescription>
          </>
        );

      case 'openrouter_error':
        return (
          <>
            <AlertTitle className="text-red-600">OpenRouter API Error</AlertTitle>
            <AlertDescription className="mt-2">
              <p className="mb-2">{error || 'There was an error with the OpenRouter API.'}</p>
              <div className="mt-4 space-y-2">
                <p className="font-medium">Possible solutions:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Check your API key in the settings</li>
                  <li>Make sure your OpenRouter account is active</li>
                  <li>Try a different AI model</li>
                </ul>
                <div className="flex flex-wrap gap-2 mt-3">
                  <Button variant="outline" size="sm" onClick={navigateToSettings}>
                    <Settings className="mr-2 h-4 w-4" />
                    Check Settings
                  </Button>
                  {onRetry && (
                    <Button size="sm" onClick={onRetry}>
                      Try Again
                    </Button>
                  )}
                </div>
              </div>
            </AlertDescription>
          </>
        );

      default:
        return (
          <>
            <AlertTitle className="text-red-600">Error</AlertTitle>
            <AlertDescription className="mt-2">
              <p>{error || 'An error occurred while processing your message.'}</p>
              {onRetry && (
                <Button variant="outline" size="sm" className="mt-3" onClick={onRetry}>
                  Try Again
                </Button>
              )}
            </AlertDescription>
          </>
        );
    }
  };

  return (
    <Alert variant="destructive" className="mb-4 border-red-200 bg-red-50">
      <AlertCircle className="h-5 w-5 text-red-600" />
      <div className="ml-3 w-full">{renderErrorContent()}</div>
    </Alert>
  );
}

export default AIAssistantErrorHandler;
