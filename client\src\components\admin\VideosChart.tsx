import React, { useState } from 'react';
import { useTheme } from '@/context/ThemeContext';

interface VideosChartProps {
  timeRange?: 'Today' | 'Yesterday' | 'This Week' | 'This Month' | 'Last Month' | 'This Year';
}

export default function VideosChart({ timeRange = 'This Year' }: VideosChartProps) {
  const { theme } = useTheme();
  const [activeDatasets, setActiveDatasets] = useState<{ posts: boolean; videos: boolean }>({ posts: true, videos: true });

  // Define different data sets based on time range
  const getChartData = () => {
    switch (timeRange) {
      case 'Today':
        return {
          labels: ['12am', '4am', '8am', '12pm', '4pm', '8pm'],
          videosData: [5, 3, 8, 15, 10, 7],
          postsData: [2, 1, 5, 8, 6, 3]
        };
      case 'Yesterday':
        return {
          labels: ['12am', '4am', '8am', '12pm', '4pm', '8pm'],
          videosData: [4, 2, 7, 12, 8, 5],
          postsData: [1, 0, 4, 6, 5, 2]
        };
      case 'This Week':
        return {
          labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          videosData: [45, 52, 38, 41, 55, 40, 30],
          postsData: [25, 30, 20, 22, 28, 18, 15]
        };
      case 'This Month':
        return {
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          videosData: [180, 150, 165, 140],
          postsData: [90, 75, 85, 70]
        };
      case 'Last Month':
        return {
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          videosData: [160, 140, 150, 130],
          postsData: [80, 70, 75, 65]
        };
      case 'This Year':
      default:
        return {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          videosData: [200, 180, 160, 140, 150, 165, 175, 185, 195, 210, 225, 240],
          postsData: [100, 90, 85, 80, 85, 90, 95, 100, 110, 120, 130, 140]
        };
    }
  };

  const { labels, videosData, postsData } = getChartData();

  const maxValue = Math.max(
    ...(activeDatasets.videos ? videosData : [0]),
    ...(activeDatasets.posts ? postsData : [0])
  ) || 200; // Fallback to 200 if both are disabled

  const toggleDataset = (dataset: 'posts' | 'videos') => {
    setActiveDatasets(prev => ({
      ...prev,
      [dataset]: !prev[dataset]
    }));
  };

  return (
    <div className={`p-3 ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-800'} rounded-lg shadow`}>
      <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} mb-2`}>Videos, Posts Chart</div>

      <div className="flex items-center justify-center space-x-3 mb-2">
        <div
          className={`flex items-center cursor-pointer ${!activeDatasets.posts && 'opacity-50'}`}
          onClick={() => toggleDataset('posts')}
        >
          <div className="w-4 h-4 bg-indigo-500 mr-2"></div>
          <span className={`text-sm ${activeDatasets.posts ? (theme === 'dark' ? 'text-white' : 'text-gray-800') + ' font-medium' : 'text-gray-500 line-through'}`}>Posts</span>
        </div>
        <div
          className={`flex items-center cursor-pointer ${!activeDatasets.videos && 'opacity-50'}`}
          onClick={() => toggleDataset('videos')}
        >
          <div className="w-4 h-4 bg-green-400 mr-2"></div>
          <span className={`text-sm ${activeDatasets.videos ? (theme === 'dark' ? 'text-white' : 'text-gray-800') + ' font-medium' : 'text-gray-500 line-through'}`}>Videos</span>
        </div>
      </div>

      <div className="relative px-1">
        {/* Y-axis labels */}
        <div className={`absolute left-0 top-0 h-full flex flex-col justify-between text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} py-2`}>
          <div>200</div>
          <div>100</div>
          <div>0</div>
        </div>

        {/* Chart grid lines */}
        <div className={`ml-8 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="h-32 flex flex-col justify-between">
            {[...Array(2)].map((_, i) => (
              <div key={i} className={`border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} w-full h-0`}></div>
            ))}
          </div>
        </div>

        <div className="ml-8 h-32 relative">
          {/* Area chart representation */}
          <svg className="w-full h-full" viewBox="0 0 1200 160" preserveAspectRatio="none">
            {/* Videos area */}
            {activeDatasets.videos && (
              <path
                d={`
                  M0,${160 - (videosData[0] / maxValue) * 120}
                  ${labels.map((_, i) => `L${(i * 100) + 100},${160 - (videosData[i] / maxValue) * 120}`).join(' ')}
                  L1200,160 L0,160 Z
                `}
                fill="rgba(74, 222, 128, 0.2)"
                stroke="rgba(74, 222, 128, 0.5)"
                strokeWidth="2"
              />
            )}

            {/* Posts line */}
            {activeDatasets.posts && (
              <path
                d={`
                  M0,${160 - (postsData[0] / maxValue) * 120}
                  ${labels.map((_, i) => `L${(i * 100) + 100},${160 - (postsData[i] / maxValue) * 120}`).join(' ')}
                `}
                fill="none"
                stroke="rgba(99, 102, 241, 0.8)"
                strokeWidth="2"
              />
            )}
          </svg>

          {/* X-axis labels */}
          <div className="grid grid-cols-12 gap-0 mt-2 mx-1 px-1">
            {labels.map((label) => (
              <div key={label} className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} text-center overflow-hidden`} style={{ fontSize: '0.65rem' }}>{label}</div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
