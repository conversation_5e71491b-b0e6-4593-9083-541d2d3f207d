/**
 * Error codes for the application
 * These are used for client-side error handling
 */
export const ErrorCodes = {
  // General errors
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  BAD_REQUEST: 'BAD_REQUEST',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',

  // Authentication errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_DISABLED: 'ACCOUNT_DISABLED',
  ACCOUNT_NOT_VERIFIED: 'ACCOUNT_NOT_VERIFIED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  TOKEN_REVOKED: 'TOKEN_REVOKED',
  PASSWORD_EXPIRED: 'PASSWORD_EXPIRED',
  PASSWORD_WEAK: 'PASSWORD_WEAK',
  PASSWORD_MISMATCH: 'PASSWORD_MISMATCH',

  // User errors
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  USERNAME_TAKEN: 'USERNAME_TAKEN',
  EMAIL_TAKEN: 'EMAIL_TAKEN',
  INVALID_USER_DATA: 'INVALID_USER_DATA',

  // Role and permission errors
  ROLE_NOT_FOUND: 'ROLE_NOT_FOUND',
  PERMISSION_NOT_FOUND: 'PERMISSION_NOT_FOUND',
  ROLE_ALREADY_EXISTS: 'ROLE_ALREADY_EXISTS',
  PERMISSION_ALREADY_EXISTS: 'PERMISSION_ALREADY_EXISTS',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  SYSTEM_ROLE_MODIFICATION: 'SYSTEM_ROLE_MODIFICATION',

  // Database errors
  DATABASE_ERROR: 'DATABASE_ERROR',
  DATABASE_CONNECTION_ERROR: 'DATABASE_CONNECTION_ERROR',
  DATABASE_QUERY_ERROR: 'DATABASE_QUERY_ERROR',
  DATABASE_VALIDATION_ERROR: 'DATABASE_VALIDATION_ERROR',

  // File errors
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  FILE_UPLOAD_ERROR: 'FILE_UPLOAD_ERROR',

  // Video errors
  VIDEO_NOT_FOUND: 'VIDEO_NOT_FOUND',
  VIDEO_PROCESSING_ERROR: 'VIDEO_PROCESSING_ERROR',
  VIDEO_ALREADY_EXISTS: 'VIDEO_ALREADY_EXISTS',
  VIDEO_UPLOAD_ERROR: 'VIDEO_UPLOAD_ERROR',
  VIDEO_IMPORT_ERROR: 'VIDEO_IMPORT_ERROR',
  VIDEO_TRANSCODE_ERROR: 'VIDEO_TRANSCODE_ERROR',
  VIDEO_PERMISSION_DENIED: 'VIDEO_PERMISSION_DENIED',
  UNSUPPORTED_PLATFORM: 'UNSUPPORTED_PLATFORM',

  // Channel errors
  CHANNEL_NOT_FOUND: 'CHANNEL_NOT_FOUND',
  CHANNEL_NAME_TAKEN: 'CHANNEL_NAME_TAKEN',
  CHANNEL_LIMIT_REACHED: 'CHANNEL_LIMIT_REACHED',
  CHANNEL_PERMISSION_DENIED: 'CHANNEL_PERMISSION_DENIED',

  // Messaging errors
  CONVERSATION_NOT_FOUND: 'CONVERSATION_NOT_FOUND',
  MESSAGE_NOT_FOUND: 'MESSAGE_NOT_FOUND',
  CONVERSATION_LIMIT_REACHED: 'CONVERSATION_LIMIT_REACHED',
  MESSAGE_LIMIT_REACHED: 'MESSAGE_LIMIT_REACHED',
  CONVERSATION_CLOSED: 'CONVERSATION_CLOSED',
  INVALID_MESSAGE_TYPE: 'INVALID_MESSAGE_TYPE',
  INVALID_RECIPIENT: 'INVALID_RECIPIENT',
  MESSAGE_TOO_LONG: 'MESSAGE_TOO_LONG',
  TRANSLATION_ERROR: 'TRANSLATION_ERROR',
  WEBSOCKET_ERROR: 'WEBSOCKET_ERROR',

  // Email errors
  EMAIL_SENDING_ERROR: 'EMAIL_SENDING_ERROR',
  INVALID_EMAIL: 'INVALID_EMAIL',

  // External service errors
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  PAYMENT_PROCESSING_ERROR: 'PAYMENT_PROCESSING_ERROR',
  THIRD_PARTY_API_ERROR: 'THIRD_PARTY_API_ERROR',
  EXTERNAL_API_ERROR: 'EXTERNAL_API_ERROR',

  // Input validation errors
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  INVALID_URL: 'INVALID_URL',

  // Business logic errors
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
  RESOURCE_LIMIT_EXCEEDED: 'RESOURCE_LIMIT_EXCEEDED',
};
