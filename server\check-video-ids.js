/**
 * <PERSON><PERSON><PERSON> to check and validate video IDs in the database
 * This script will check if video IDs match the expected Engaxe format
 * and report any issues without automatically changing them
 */

const mongoose = require('mongoose');
const axios = require('axios');

// MongoDB connection string
const MONGODB_URI = 'mongodb://localhost:27017/lawengaxe';

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('Connected to MongoDB');
    checkVideoIds();
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });

// Define the Video schema
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  languages: [{
    code: String,
    name: String,
    isDefault: Boolean,
    url: String
  }],
  source: mongoose.Schema.Types.Mixed
});

// Create the Video model
const Video = mongoose.model('Video', VideoSchema);

// Function to check if a video ID is valid by making a request to Engaxe
async function isValidEngaxeId(videoId) {
  try {
    // Skip checking if the ID doesn't match the expected format
    if (!/^[a-zA-Z0-9]{6,10}$/.test(videoId)) {
      return false;
    }
    
    // Try to fetch video info from Engaxe
    const response = await axios.get(`https://engaxe.com/aj/get-video?id=${videoId}`, {
      timeout: 5000 // 5 second timeout
    });
    
    // If we get a successful response with title, it's valid
    return response.data && response.data.title;
  } catch (error) {
    console.error(`Error checking video ID ${videoId}:`, error.message);
    return false;
  }
}

// Function to check all video IDs
async function checkVideoIds() {
  try {
    console.log('Starting to check video IDs...');
    
    // Get all videos
    const videos = await Video.find({});
    console.log(`Found ${videos.length} videos in the database`);
    
    let validCount = 0;
    let invalidCount = 0;
    let validIds = [];
    
    // Process each video
    for (const video of videos) {
      console.log(`Checking video ${video.id} with URL: ${video.url}`);
      
      // Check if the URL looks like a valid Engaxe ID
      const isLikelyValidFormat = /^[a-zA-Z0-9]{6,10}$/.test(video.url);
      
      if (isLikelyValidFormat) {
        console.log(`Video ${video.id} has a URL that matches the expected Engaxe ID format: ${video.url}`);
        
        // Verify if it's actually a valid Engaxe ID by making a request
        const isValid = await isValidEngaxeId(video.url);
        
        if (isValid) {
          console.log(`✅ Confirmed valid Engaxe ID: ${video.url}`);
          validCount++;
          validIds.push(video.url);
        } else {
          console.log(`❌ ID matches format but is not a valid Engaxe ID: ${video.url}`);
          invalidCount++;
        }
      } else {
        console.log(`❌ Video ${video.id} has a URL that does NOT match the expected Engaxe ID format: ${video.url}`);
        invalidCount++;
      }
    }
    
    console.log('\n--- SUMMARY ---');
    console.log(`Total videos: ${videos.length}`);
    console.log(`Valid Engaxe IDs: ${validCount}`);
    console.log(`Invalid or unverified IDs: ${invalidCount}`);
    
    if (validIds.length > 0) {
      console.log('\nValid Engaxe IDs found:');
      console.log(validIds.join(', '));
      console.log('\nYou can use these IDs in your DirectEngaxeEmbed component as known valid IDs.');
    }
    
    // Disconnect from MongoDB
    mongoose.disconnect()
      .then(() => {
        console.log('Disconnected from MongoDB');
        process.exit(0);
      })
      .catch(err => {
        console.error('Error disconnecting from MongoDB:', err);
        process.exit(1);
      });
  } catch (error) {
    console.error('Error checking video IDs:', error);
    process.exit(1);
  }
}
