import { NotificationTemplateModel } from '../models';

/**
 * Seed notification templates data
 */
async function seedNotificationTemplates() {
  try {
    // Clear existing notification templates
    await NotificationTemplateModel.deleteMany({});
    
    // Define notification templates to seed
    const notificationTemplates = [
      {
        name: 'Welcome Email',
        description: 'Sent to new users after registration',
        code: 'welcome_email',
        category: 'account',
        channels: ['email'],
        isActive: true,
        priority: 2,
        templates: {
          email: {
            subject: 'Welcome to LawEngaxe!',
            body: 'Hello {{user_name}},\n\nWelcome to LawEngaxe! We\'re excited to have you join our community.\n\nTo get started, please verify your email by clicking the link below:\n{{verification_link}}\n\nBest regards,\nThe LawEngaxe Team',
            html: '<h1>Welcome to LawEngaxe!</h1><p>Hello {{user_name}},</p><p>Welcome to LawEngaxe! We\'re excited to have you join our community.</p><p>To get started, please <a href="{{verification_link}}">verify your email</a>.</p><p>Best regards,<br>The LawEngaxe Team</p>',
          },
        },
        variables: [
          {
            name: 'user_name',
            type: 'string',
            required: true,
            defaultValue: 'there',
          },
          {
            name: 'verification_link',
            type: 'url',
            required: true,
          },
        ],
        defaultPreferences: {
          enabled: true,
          channels: ['email'],
        },
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Password Reset',
        description: 'Sent when a user requests a password reset',
        code: 'password_reset',
        category: 'account',
        channels: ['email'],
        isActive: true,
        priority: 1,
        templates: {
          email: {
            subject: 'Reset Your LawEngaxe Password',
            body: 'Hello {{user_name}},\n\nWe received a request to reset your password. If you didn\'t make this request, you can ignore this email.\n\nTo reset your password, click the link below:\n{{reset_link}}\n\nThis link will expire in {{expiry_hours}} hours.\n\nBest regards,\nThe LawEngaxe Team',
            html: '<h1>Reset Your Password</h1><p>Hello {{user_name}},</p><p>We received a request to reset your password. If you didn\'t make this request, you can ignore this email.</p><p>To reset your password, <a href="{{reset_link}}">click here</a>.</p><p>This link will expire in {{expiry_hours}} hours.</p><p>Best regards,<br>The LawEngaxe Team</p>',
          },
        },
        variables: [
          {
            name: 'user_name',
            type: 'string',
            required: true,
            defaultValue: 'there',
          },
          {
            name: 'reset_link',
            type: 'url',
            required: true,
          },
          {
            name: 'expiry_hours',
            type: 'number',
            required: true,
            defaultValue: '24',
          },
        ],
        defaultPreferences: {
          enabled: true,
          channels: ['email'],
        },
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'New Message',
        description: 'Sent when a user receives a new message',
        code: 'new_message',
        category: 'social',
        channels: ['email', 'push', 'in_app'],
        isActive: true,
        priority: 3,
        templates: {
          email: {
            subject: 'New Message from {{sender_name}}',
            body: 'Hello {{user_name}},\n\nYou have received a new message from {{sender_name}}.\n\nMessage: "{{message_preview}}"\n\nTo view and reply to this message, click here: {{message_link}}\n\nBest regards,\nThe LawEngaxe Team',
            html: '<h1>New Message</h1><p>Hello {{user_name}},</p><p>You have received a new message from <strong>{{sender_name}}</strong>.</p><p>Message: "{{message_preview}}"</p><p><a href="{{message_link}}">View and reply to this message</a></p><p>Best regards,<br>The LawEngaxe Team</p>',
          },
          push: {
            title: 'New message from {{sender_name}}',
            body: '{{message_preview}}',
          },
          inApp: {
            content: '{{sender_name}} sent you a message: "{{message_preview}}"',
          },
        },
        variables: [
          {
            name: 'user_name',
            type: 'string',
            required: true,
            defaultValue: 'there',
          },
          {
            name: 'sender_name',
            type: 'string',
            required: true,
          },
          {
            name: 'message_preview',
            type: 'string',
            required: true,
          },
          {
            name: 'message_link',
            type: 'url',
            required: true,
          },
        ],
        defaultPreferences: {
          enabled: true,
          channels: ['email', 'push', 'in_app'],
        },
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'New Video',
        description: 'Sent when a creator uploads a new video',
        code: 'new_video',
        category: 'content',
        channels: ['email', 'push', 'in_app'],
        isActive: true,
        priority: 3,
        templates: {
          email: {
            subject: 'New Video: {{video_title}}',
            body: 'Hello {{user_name}},\n\n{{creator_name}} has uploaded a new video: "{{video_title}}"\n\n{{video_description}}\n\nWatch it now: {{video_link}}\n\nBest regards,\nThe LawEngaxe Team',
            html: '<h1>New Video</h1><p>Hello {{user_name}},</p><p><strong>{{creator_name}}</strong> has uploaded a new video:</p><h2>{{video_title}}</h2><p>{{video_description}}</p><p><a href="{{video_link}}">Watch it now</a></p><p>Best regards,<br>The LawEngaxe Team</p>',
          },
          push: {
            title: 'New video from {{creator_name}}',
            body: '{{video_title}}',
          },
          inApp: {
            content: '{{creator_name}} uploaded "{{video_title}}"',
          },
        },
        variables: [
          {
            name: 'user_name',
            type: 'string',
            required: true,
            defaultValue: 'there',
          },
          {
            name: 'creator_name',
            type: 'string',
            required: true,
          },
          {
            name: 'video_title',
            type: 'string',
            required: true,
          },
          {
            name: 'video_description',
            type: 'string',
            required: false,
            defaultValue: '',
          },
          {
            name: 'video_link',
            type: 'url',
            required: true,
          },
        ],
        defaultPreferences: {
          enabled: true,
          channels: ['email', 'push', 'in_app'],
          scheduling: {
            timezone: 'UTC',
            quietHours: {
              start: '22:00',
              end: '08:00',
            },
          },
        },
        createdBy: 'system',
        updatedBy: 'system',
      },
    ];
    
    // Insert notification templates
    await NotificationTemplateModel.insertMany(notificationTemplates);
    
    console.log(`✅ ${notificationTemplates.length} notification templates seeded successfully`);
  } catch (error) {
    console.error('❌ Error seeding notification templates:', error);
    throw error;
  }
}

export default seedNotificationTemplates;
