import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Home, 
  Key, 
  RefreshCw, 
  Copy, 
  CheckCircle, 
  AlertCircle, 
  Shield, 
  Eye, 
  EyeOff,
  Info
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

// Import shadcn components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from '@/components/ui/tooltip';

// API Key interface
interface APIKey {
  id: string;
  name: string;
  key: string;
  type: 'server' | 'client';
  createdAt: string;
  lastUsed: string | null;
  status: 'active' | 'revoked';
}

export default function ManageAPIKeysPage() {
  const [apiKeys, setApiKeys] = useState<APIKey[]>([
    {
      id: '1',
      name: 'Mobile App Server Key',
      key: '1312a113c58715637a94437389326a49',
      type: 'server',
      createdAt: '2023-05-15T10:30:00Z',
      lastUsed: '2023-11-20T14:45:00Z',
      status: 'active'
    },
    {
      id: '2',
      name: 'Web Client Key',
      key: '7f4e8d3b2a1c9e8d7f6a5b4c3d2e1f0a',
      type: 'client',
      createdAt: '2023-08-10T09:15:00Z',
      lastUsed: '2023-11-18T11:20:00Z',
      status: 'active'
    }
  ]);
  
  const [showKey, setShowKey] = useState<Record<string, boolean>>({});
  const [showResetDialog, setShowResetDialog] = useState(false);
  const [keyToReset, setKeyToReset] = useState<APIKey | null>(null);
  const [showCopied, setShowCopied] = useState<Record<string, boolean>>({});
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newKeyName, setNewKeyName] = useState('');
  const [newKeyType, setNewKeyType] = useState<'server' | 'client'>('server');

  // Toggle key visibility
  const toggleKeyVisibility = (id: string) => {
    setShowKey(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Copy key to clipboard
  const copyToClipboard = (key: string, id: string) => {
    navigator.clipboard.writeText(key);
    setShowCopied(prev => ({
      ...prev,
      [id]: true
    }));
    
    setTimeout(() => {
      setShowCopied(prev => ({
        ...prev,
        [id]: false
      }));
    }, 2000);
  };

  // Handle reset key
  const handleResetKey = () => {
    if (!keyToReset) return;
    
    const newKey = Math.random().toString(36).substring(2, 15) + 
                  Math.random().toString(36).substring(2, 15) +
                  Math.random().toString(36).substring(2, 15);
    
    setApiKeys(keys => keys.map(key => 
      key.id === keyToReset.id 
        ? { 
            ...key, 
            key: newKey, 
            createdAt: new Date().toISOString(),
            lastUsed: null
          } 
        : key
    ));
    
    setShowResetDialog(false);
    setKeyToReset(null);
    
    // Show the key after reset
    setShowKey(prev => ({
      ...prev,
      [keyToReset.id]: true
    }));
  };

  // Handle create new key
  const handleCreateKey = () => {
    if (!newKeyName.trim()) return;
    
    const newKey = Math.random().toString(36).substring(2, 15) + 
                  Math.random().toString(36).substring(2, 15) +
                  Math.random().toString(36).substring(2, 15);
    
    const newApiKey: APIKey = {
      id: (apiKeys.length + 1).toString(),
      name: newKeyName,
      key: newKey,
      type: newKeyType,
      createdAt: new Date().toISOString(),
      lastUsed: null,
      status: 'active'
    };
    
    setApiKeys(prev => [...prev, newApiKey]);
    setShowCreateDialog(false);
    setNewKeyName('');
    
    // Show the new key
    setShowKey(prev => ({
      ...prev,
      [newApiKey.id]: true
    }));
  };

  // Handle revoke key
  const handleRevokeKey = (id: string) => {
    setApiKeys(keys => keys.map(key => 
      key.id === id 
        ? { ...key, status: 'revoked' } 
        : key
    ));
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header and Breadcrumb */}
            <div>
              <h1 className="text-2xl font-bold mb-2">Manage API Access Keys</h1>
              
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="/admin" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Admin Panel
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Mobile & API Settings</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Manage API Access Keys</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {/* Info Alert */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>API Keys Information</AlertTitle>
              <AlertDescription>
                API keys are used to authenticate requests to our API. Keep your API keys secure and do not share them in publicly accessible areas.
              </AlertDescription>
            </Alert>

            {/* Main Content */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div>
                  <CardTitle>API Keys</CardTitle>
                  <CardDescription>
                    Manage your API keys for accessing our services
                  </CardDescription>
                </div>
                <Button onClick={() => setShowCreateDialog(true)} className="flex items-center gap-1">
                  <Key className="h-4 w-4" /> Create New Key
                </Button>
              </CardHeader>
              
              <CardContent className="space-y-4 pt-4">
                <Tabs defaultValue="server" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="server">Server Keys</TabsTrigger>
                    <TabsTrigger value="client">Client Keys</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="server" className="space-y-4 pt-4">
                    {apiKeys.filter(key => key.type === 'server').map(key => (
                      <Card key={key.id} className={key.status === 'revoked' ? 'opacity-70' : ''}>
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <CardTitle className="text-base flex items-center gap-2">
                                {key.name}
                                {key.status === 'revoked' ? (
                                  <Badge variant="outline" className="bg-red-50 text-red-700">Revoked</Badge>
                                ) : (
                                  <Badge className="bg-green-50 text-green-700">Active</Badge>
                                )}
                              </CardTitle>
                              <CardDescription>Server API Key</CardDescription>
                            </div>
                            <div className="flex items-center gap-2">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button 
                                      variant="outline" 
                                      size="sm"
                                      className="h-8 w-8 p-0"
                                      onClick={() => toggleKeyVisibility(key.id)}
                                    >
                                      {showKey[key.id] ? (
                                        <EyeOff className="h-4 w-4" />
                                      ) : (
                                        <Eye className="h-4 w-4" />
                                      )}
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{showKey[key.id] ? 'Hide' : 'Show'} API Key</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <div className="flex items-center gap-2">
                            <div className="relative flex-1">
                              <Input
                                value={showKey[key.id] ? key.key : '•'.repeat(32)}
                                readOnly
                                className="pr-10 font-mono text-sm"
                              />
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button 
                                        variant="ghost" 
                                        size="sm"
                                        className="h-8 w-8 p-0"
                                        onClick={() => copyToClipboard(key.key, key.id)}
                                      >
                                        {showCopied[key.id] ? (
                                          <CheckCircle className="h-4 w-4 text-green-500" />
                                        ) : (
                                          <Copy className="h-4 w-4" />
                                        )}
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>{showCopied[key.id] ? 'Copied!' : 'Copy to clipboard'}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-between mt-4 text-xs text-muted-foreground">
                            <div>Created: {formatDate(key.createdAt)}</div>
                            <div>Last used: {formatDate(key.lastUsed)}</div>
                          </div>
                        </CardContent>
                        <CardFooter className="flex justify-end gap-2 pt-2">
                          {key.status !== 'revoked' && (
                            <>
                              <Button 
                                variant="outline" 
                                size="sm"
                                className="h-8 bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200"
                                onClick={() => {
                                  setKeyToReset(key);
                                  setShowResetDialog(true);
                                }}
                              >
                                <RefreshCw className="h-3.5 w-3.5 mr-1" /> Reset
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm"
                                className="h-8 bg-red-50 hover:bg-red-100 text-red-600 border-red-200"
                                onClick={() => handleRevokeKey(key.id)}
                              >
                                <Shield className="h-3.5 w-3.5 mr-1" /> Revoke
                              </Button>
                            </>
                          )}
                        </CardFooter>
                      </Card>
                    ))}
                    
                    {apiKeys.filter(key => key.type === 'server').length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        No server API keys found. Create your first key.
                      </div>
                    )}
                  </TabsContent>
                  
                  <TabsContent value="client" className="space-y-4 pt-4">
                    {apiKeys.filter(key => key.type === 'client').map(key => (
                      <Card key={key.id} className={key.status === 'revoked' ? 'opacity-70' : ''}>
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <CardTitle className="text-base flex items-center gap-2">
                                {key.name}
                                {key.status === 'revoked' ? (
                                  <Badge variant="outline" className="bg-red-50 text-red-700">Revoked</Badge>
                                ) : (
                                  <Badge className="bg-green-50 text-green-700">Active</Badge>
                                )}
                              </CardTitle>
                              <CardDescription>Client API Key</CardDescription>
                            </div>
                            <div className="flex items-center gap-2">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button 
                                      variant="outline" 
                                      size="sm"
                                      className="h-8 w-8 p-0"
                                      onClick={() => toggleKeyVisibility(key.id)}
                                    >
                                      {showKey[key.id] ? (
                                        <EyeOff className="h-4 w-4" />
                                      ) : (
                                        <Eye className="h-4 w-4" />
                                      )}
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{showKey[key.id] ? 'Hide' : 'Show'} API Key</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <div className="flex items-center gap-2">
                            <div className="relative flex-1">
                              <Input
                                value={showKey[key.id] ? key.key : '•'.repeat(32)}
                                readOnly
                                className="pr-10 font-mono text-sm"
                              />
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button 
                                        variant="ghost" 
                                        size="sm"
                                        className="h-8 w-8 p-0"
                                        onClick={() => copyToClipboard(key.key, key.id)}
                                      >
                                        {showCopied[key.id] ? (
                                          <CheckCircle className="h-4 w-4 text-green-500" />
                                        ) : (
                                          <Copy className="h-4 w-4" />
                                        )}
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>{showCopied[key.id] ? 'Copied!' : 'Copy to clipboard'}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-between mt-4 text-xs text-muted-foreground">
                            <div>Created: {formatDate(key.createdAt)}</div>
                            <div>Last used: {formatDate(key.lastUsed)}</div>
                          </div>
                        </CardContent>
                        <CardFooter className="flex justify-end gap-2 pt-2">
                          {key.status !== 'revoked' && (
                            <>
                              <Button 
                                variant="outline" 
                                size="sm"
                                className="h-8 bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200"
                                onClick={() => {
                                  setKeyToReset(key);
                                  setShowResetDialog(true);
                                }}
                              >
                                <RefreshCw className="h-3.5 w-3.5 mr-1" /> Reset
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm"
                                className="h-8 bg-red-50 hover:bg-red-100 text-red-600 border-red-200"
                                onClick={() => handleRevokeKey(key.id)}
                              >
                                <Shield className="h-3.5 w-3.5 mr-1" /> Revoke
                              </Button>
                            </>
                          )}
                        </CardFooter>
                      </Card>
                    ))}
                    
                    {apiKeys.filter(key => key.type === 'client').length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        No client API keys found. Create your first key.
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* API Usage Guidelines */}
            <Card>
              <CardHeader>
                <CardTitle>API Usage Guidelines</CardTitle>
                <CardDescription>
                  Best practices for using our API
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Keep Keys Secure</h3>
                    <p className="text-sm text-muted-foreground">Never expose your API keys in client-side code or public repositories. Server keys should only be used in secure server environments.</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Rate Limiting</h3>
                    <p className="text-sm text-muted-foreground">Our API has rate limits to ensure fair usage. Implement proper error handling for rate limit responses (HTTP 429).</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Rotate Keys Regularly</h3>
                    <p className="text-sm text-muted-foreground">For security best practices, rotate your API keys periodically, especially if you suspect they may have been compromised.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>

      {/* Reset Key Dialog */}
      <Dialog open={showResetDialog} onOpenChange={setShowResetDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset API Key</DialogTitle>
            <DialogDescription>
              Are you sure you want to reset this API key? This action cannot be undone and any applications using this key will need to be updated.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                Resetting this key will immediately invalidate the current key. Make sure you update all applications using this key.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowResetDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleResetKey}>
              Reset Key
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Key Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New API Key</DialogTitle>
            <DialogDescription>
              Create a new API key for your application.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="key-name" className="text-right">
                Key Name
              </Label>
              <Input
                id="key-name"
                placeholder="e.g., Mobile App Key"
                value={newKeyName}
                onChange={(e) => setNewKeyName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="key-type" className="text-right">
                Key Type
              </Label>
              <select
                id="key-type"
                value={newKeyType}
                onChange={(e) => setNewKeyType(e.target.value as 'server' | 'client')}
                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="server">Server Key</option>
                <option value="client">Client Key</option>
              </select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateKey}>
              Create Key
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
