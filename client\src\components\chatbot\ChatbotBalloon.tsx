import { useState, useEffect, useRef } from 'react';
import { MessageCircle, X, Mic, MicOff, Volume2, VolumeX } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useChatbot } from '@/context/ChatbotContext';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/context/LanguageContext';
import aiAssistantAPI from '@/services/aiAssistantApi';
import * as speechSynthesis from '@/utils/speechSynthesis';

interface ChatbotBalloonProps {
  isOpen?: boolean;
  onClose?: () => void;
}

export default function ChatbotBalloon({ isOpen = false, onClose }: ChatbotBalloonProps) {
  const {
    isVoiceOutputEnabled,
    aiProviders,
    getEnabledProviders
  } = useChatbot();
  const { currentUser } = useAuth();
  const { currentLanguage } = useLanguage();
  const { toast } = useToast();
  const [isExpanded, setIsExpanded] = useState(isOpen);
  const [message, setMessage] = useState('');
  const [chatHistory, setChatHistory] = useState<{ sender: 'user' | 'bot'; text: string }[]>([
    { sender: 'bot', text: 'Hello! I can help you understand this video content. Ask me anything about the topic, concepts, or details covered in this video.' },
  ]);
  const [isSending, setIsSending] = useState(false);

  // Get the first enabled provider or fall back to OpenRouter
  const enabledProviders = getEnabledProviders();
  // Prioritize OpenRouter if it's enabled
  const openRouterEnabled = aiProviders.openrouter.enabled;
  const activeProvider = openRouterEnabled ? 'openrouter' :
                         (enabledProviders.length > 0 ? enabledProviders[0] : 'openrouter');
  const providerConfig = aiProviders[activeProvider];

  // Speech synthesis and recognition
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isListening, setIsListening] = useState(false);

  useEffect(() => {
    setIsExpanded(isOpen);

    // Stop any ongoing speech when the chatbot is closed
    if (!isOpen) {
      speechSynthesis.stop();
      setIsSpeaking(false);
    }
  }, [isOpen]);

  // Speak text using speech synthesis
  const speakText = (text: string) => {
    try {
      console.log('Speaking text:', text);

      // Important: We no longer abort the recognition when AI starts speaking
      // This allows the microphone to stay active (green) while AI is speaking

      // Get language code for speech synthesis based on current language
      const langCode = currentLanguage.code === 'hi' ? 'hi-IN' :
                      currentLanguage.code === 'mr' ? 'mr-IN' :
                      currentLanguage.code === 'gu' ? 'gu-IN' :
                      currentLanguage.code === 'ta' ? 'ta-IN' :
                      currentLanguage.code === 'te' ? 'te-IN' :
                      currentLanguage.code === 'bn' ? 'bn-IN' :
                      currentLanguage.code === 'kn' ? 'kn-IN' :
                      currentLanguage.code === 'ml' ? 'ml-IN' :
                      currentLanguage.code === 'fr' ? 'fr-FR' :
                      currentLanguage.code === 'es' ? 'es-ES' :
                      currentLanguage.code === 'de' ? 'de-DE' :
                      currentLanguage.code === 'zh' ? 'zh-CN' :
                      currentLanguage.code === 'ja' ? 'ja-JP' :
                      'en-US';

      console.log(`Using speech synthesis language: ${langCode}`);

      // Use our speechSynthesis utility
      speechSynthesis.speak(text, {
        lang: langCode,
        rate: 1.0,
        pitch: 1.0,
        volume: 1.0,
        onStart: () => {
          console.log('Speech started');
          setIsSpeaking(true);

          // Notify user that microphone remains active during AI speech
          // toast({
          //   title: "AI Speaking",
          //   description: "Microphone remains active while AI is speaking. Click microphone to turn off.",
          //   variant: "default"
          // });
        },
        onEnd: () => {
          console.log('Speech ended');
          setIsSpeaking(false);

          // We don't need to restart listening as it should remain active
          // Only show a notification that AI has finished speaking
          // toast({
          //   title: "AI Finished",
          //   description: "AI has finished speaking. Microphone remains active.",
          //   variant: "default"
          // });
        },
        onError: (error) => {
          console.error('Speech synthesis error:', error);
          setIsSpeaking(false);

          // Silent error handling - no toast notification for errors
        }
      }).catch(error => {
        console.error('Error with speech synthesis:', error);
        setIsSpeaking(false);

        // Silent error handling - no toast notification
      });
    } catch (error) {
      console.error('Error with speech synthesis:', error);
      setIsSpeaking(false);

      // Silent error handling - no toast notification
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim() || isSending) return;

    // Add user message to chat
    setChatHistory([...chatHistory, { sender: 'user', text: message }]);

    // Store the message text and clear input
    const messageText = message;
    setMessage('');
    setIsSending(true);

    try {
      // Generate a local response instead of calling the API
      const botResponse = generateLocalResponse(messageText);

      // Add bot response to chat
      setChatHistory(prev => [
        ...prev,
        {
          sender: 'bot',
          text: botResponse
        }
      ]);

      // Don't automatically speak the response when message is sent
      // Speech will only be triggered by microphone button
    } catch (error) {
      console.error('Error generating response:', error);

      // Handle error
      const errorMessage = "Sorry, an error occurred. Please try again.";
      setChatHistory(prev => [
        ...prev,
        {
          sender: 'bot',
          text: errorMessage
        }
      ]);

      // Don't automatically speak the error message
      // Speech will only be triggered by microphone button

      toast({
        title: "Error",
        description: "An error occurred while processing your message.",
        variant: "destructive"
      });
    } finally {
      setIsSending(false);
    }
  };

  // Generate a local response based on the user's message
  const generateLocalResponse = (userMessage: string): string => {
    const lowerCaseMessage = userMessage.toLowerCase();

    // Check for greetings
    if (lowerCaseMessage.includes('hello') || lowerCaseMessage.includes('hi') || lowerCaseMessage.includes('hey')) {
      return "Hello! How can I help you with this video today?";
    }

    // Check for thanks
    if (lowerCaseMessage.includes('thank')) {
      return "You're welcome! Feel free to ask if you have any other questions.";
    }

    // Check for questions about how things work
    if ((lowerCaseMessage.includes('how') && lowerCaseMessage.includes('work')) ||
        lowerCaseMessage.includes('explain') || lowerCaseMessage.includes('what is')) {
      return "That's a great question! This concept involves understanding the core principles and applying them correctly. The video explains this in detail.";
    }

    // Check for questions about timing
    if (lowerCaseMessage.includes('when') || lowerCaseMessage.includes('time')) {
      return "The timing depends on several factors mentioned in the video. Generally, it takes about 2-3 weeks to see results, but it can vary based on your specific situation.";
    }

    // Check for questions about the platform
    if (lowerCaseMessage.includes('platform') || lowerCaseMessage.includes('lawengaxe') || lowerCaseMessage.includes('engaxe')) {
      return "LawEngaxe is a specialized video platform focused on legal education and resources. It offers video content from legal experts, interactive features, and community resources for legal professionals and students.";
    }

    // Check for questions about features
    if (lowerCaseMessage.includes('feature') || lowerCaseMessage.includes('can do')) {
      return "LawEngaxe offers several powerful features including video content on legal topics, AI-powered chat assistance, voice calling with AI assistants, video translation, and community forums.";
    }

    // Default response
    return "I'd be happy to help answer your questions about this video. Could you please provide more details about what you'd like to know?";
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  // Create a reference to store the recognition object
  const recognitionRef = useRef<any>(null);

  // Start voice recognition
  const startVoiceRecognition = () => {
    // If already listening, don't restart
    if (isListening && recognitionRef.current) {
      console.log('Already listening, not restarting recognition');
      return;
    }

    // If user clicked the microphone while it was active, stop listening
    if (isListening) {
      stopVoiceRecognition();
      return;
    }

    // Get OpenRouter configuration
    const openRouterConfig = aiProviders.openrouter;
    const isOpenRouterEnabled = openRouterConfig.enabled && openRouterConfig.apiKey;

    if (!isOpenRouterEnabled) {
      toast({
        title: "API Not Configured",
        description: "Please enable OpenRouter or OpenAI and add your API key in settings.",
        variant: "destructive"
      });
      return;
    }

    // Validate API key format
    if (!openRouterConfig.apiKey.startsWith('sk-or-') && !openRouterConfig.apiKey.startsWith('sk-proj-')) {
      toast({
        title: "Invalid API Key Format",
        description: "API key should start with 'sk-or-' or 'sk-proj-'.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Use the Web Speech API for voice recognition
      if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        // Create a new recognition instance and store it in the ref
        recognitionRef.current = new SpeechRecognition();
        const recognition = recognitionRef.current;

        // Set language based on current language selection
        const langCode = currentLanguage.code === 'hi' ? 'hi-IN' :
                        currentLanguage.code === 'mr' ? 'mr-IN' :
                        currentLanguage.code === 'gu' ? 'gu-IN' :
                        currentLanguage.code === 'ta' ? 'ta-IN' :
                        currentLanguage.code === 'te' ? 'te-IN' :
                        currentLanguage.code === 'bn' ? 'bn-IN' :
                        currentLanguage.code === 'kn' ? 'kn-IN' :
                        currentLanguage.code === 'ml' ? 'ml-IN' :
                        currentLanguage.code === 'fr' ? 'fr-FR' :
                        currentLanguage.code === 'es' ? 'es-ES' :
                        currentLanguage.code === 'de' ? 'de-DE' :
                        currentLanguage.code === 'zh' ? 'zh-CN' :
                        currentLanguage.code === 'ja' ? 'ja-JP' :
                        'en-US';

        console.log(`Setting speech recognition language to: ${langCode}`);
        recognition.lang = langCode;
        recognition.continuous = true;
        recognition.interimResults = false;

        recognition.onstart = () => {
          console.log('Voice recognition started');
          setIsListening(true);
          // toast({
          //   title: "Listening Mode Active",
          //   description: "Microphone will stay on continuously. Click microphone button to turn off.",
          //   variant: "default"
          // });
        };

        recognition.onresult = async (event) => {
          // Get the last result (most recent speech)
          const resultIndex = event.results.length - 1;
          const transcript = event.results[resultIndex][0].transcript;
          console.log('Voice recognition result:', transcript);

          if (transcript && transcript.trim()) {
            // Add user message to chat
            setChatHistory(prev => [...prev, { sender: 'user', text: transcript }]);

            // Process with OpenRouter directly
            await processWithOpenRouter(transcript);

            // Note: We don't stop recognition here, it continues listening
          }
        };

        recognition.onerror = (event) => {
          console.error('Voice recognition error:', event.error);
          setIsListening(false);
          toast({
            title: "Voice Recognition Error",
            description: `Error: ${event.error}`,
            variant: "destructive"
          });
        };

        recognition.onend = () => {
          console.log('Voice recognition ended');

          // If we're still supposed to be listening (user hasn't clicked to stop),
          // try to restart recognition after a short delay
          // Note: We always restart regardless of whether AI is speaking or not
          if (isListening) {
            console.log('Recognition ended but we should still be listening. Restarting...');
            setTimeout(() => {
              try {
                if (recognitionRef.current) {
                  recognitionRef.current.start();
                }
              } catch (error) {
                console.error('Failed to restart recognition:', error);
                setIsListening(false);
              }
            }, 1000);
          } else {
            setIsListening(false);
          }
        };

        recognition.start();
      } else {
        toast({
          title: "Voice Recognition Not Supported",
          description: "Your browser doesn't support voice recognition. Try Chrome or Edge.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      setIsListening(false);
      toast({
        title: "Voice Recognition Error",
        description: "Could not start voice recognition.",
        variant: "destructive"
      });
    }
  };

  // Process text with local responses instead of OpenRouter API
  const processWithOpenRouter = async (text: string) => {
    setIsSending(true);

    try {
      // toast({
      //   title: "Processing",
      //   description: `"${text}" - Processing your request...`,
      // });

      // Generate a local response instead of calling the API
      const responseText = generateLocalResponse(text);

      // Add assistant message to chat
      setChatHistory(prev => [
        ...prev,
        {
          sender: 'bot',
          text: responseText
        }
      ]);

      // Notify user that response is ready
      // toast({
      //   title: "Response Ready",
      //   description: "Click the microphone button to hear the AI response",
      //   variant: "default"
      // });

      // Don't automatically speak the response
      // Speech will only be triggered by microphone button
    } catch (error) {
      console.error('Error processing message:', error);
      toast({
        title: "Error",
        description: "Failed to process your message",
        variant: "destructive"
      });

      // Add error message to chat
      setChatHistory(prev => [
        ...prev,
        {
          sender: 'bot',
          text: "Sorry, I encountered an error processing your request. Please try again."
        }
      ]);
    } finally {
      setIsSending(false);
    }
  };

  // Stop voice recognition and any ongoing AI speech
  const stopVoiceRecognition = () => {
    setIsListening(false);

    // Try to abort any active recognition using our reference
    try {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
        recognitionRef.current = null;
      }
    } catch (error) {
      console.error('Error stopping recognition:', error);
    }

    // Also stop any ongoing AI speech immediately
    if (isSpeaking) {
      speechSynthesis.stop();
      setIsSpeaking(false);
    }

    // toast({
    //   title: "Voice Input Stopped",
    //   description: "Voice recognition and AI speech have been stopped",
    //   variant: "default"
    // });
  };

  // Stop voice assistant (speech synthesis)
  const stopVoiceAssistant = () => {
    speechSynthesis.stop();
    setIsSpeaking(false);
    // toast({
    //   title: "Voice Stopped",
    //   description: "Voice assistant has been stopped",
    //   variant: "default"
    // });
  };

  // Don't automatically speak the welcome message when the chatbot is opened
  // Speech will only be triggered by microphone button
  useEffect(() => {
    // No automatic speech when expanded
  }, [isExpanded]);

  if (!isExpanded) {
    return (
      <div className="fixed bottom-8 right-8 z-50 opacity-100 transition-opacity duration-300">
        <div className="group relative">
          {/* Enhanced glow effect that appears on hover */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/30 to-primary/40 rounded-full scale-0 group-hover:scale-125 opacity-0 group-hover:opacity-100 transition-all duration-500 blur-md"></div>

          {/* Outer ring animation */}
          <div className="absolute inset-0 border border-primary/30 rounded-full animate-ping opacity-70 group-hover:opacity-100"></div>

          {/* Main button with hover effect */}
          <button
            onClick={() => setIsExpanded(true)}
            className="relative flex items-center justify-center w-10 h-10 bg-gradient-to-r from-primary to-primary/80 rounded-full shadow-lg hover:shadow-primary/50 transition-all duration-300 group-hover:scale-110 border border-white/20"
            title="AI Assistant"
          >
            {/* Icon with animation */}
            <div className="text-white relative">
              <MessageCircle className="h-4 w-4 group-hover:opacity-0 transition-opacity duration-300" />
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="animate-pulse">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
              </div>
            </div>
          </button>

          {/* Tooltip that appears on hover */}
          <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-black/80 backdrop-blur-sm text-white text-xs py-1 px-2.5 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap pointer-events-none">
            AI Assistant
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed bottom-8 right-8 z-50">
      <Card className="w-72 h-80 shadow-xl flex flex-col rounded-xl overflow-hidden border-primary/20 animate-in slide-in-from-bottom-5 duration-300">
        <CardHeader className="p-2.5 border-b bg-gradient-to-r from-primary via-primary/90 to-primary/80 text-white shadow-md rounded-t-xl">
          {/* Chatbot header with improved corners */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2.5">
              <div className="relative">
                <Avatar className="h-8 w-8 ring-2 ring-white/40 shadow-lg">
                  <AvatarImage src="/creator-avatar.png" alt="Creator" />
                  <AvatarFallback className="bg-white text-primary font-bold text-xs">AI</AvatarFallback>
                </Avatar>
                {/* Enhanced status indicator with pulse animation */}
                <span className="absolute bottom-0 right-0 h-2.5 w-2.5 bg-green-400 rounded-full border-2 border-white animate-pulse"></span>
              </div>
              <div>
                <span className="font-semibold text-white text-sm tracking-wide">AI Assistant</span>
                <div className="text-xs text-white/90 flex items-center">
                  {isSpeaking ? (
                    <span className="flex items-center">
                      <span className="mr-1">💬</span> Speaking <span className="ml-1 inline-block w-1.5 h-1.5 bg-white rounded-full animate-ping"></span>
                    </span>
                  ) : (
                    <span className="flex items-center">
                      Ready to help
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                className="text-white hover:bg-white/30 h-7 w-7 p-0 rounded-full transition-all duration-300 hover:rotate-90"
                onClick={() => {
                  // Stop any ongoing speech or voice recognition
                  if (isSpeaking) {
                    speechSynthesis.stop();
                    setIsSpeaking(false);
                  }

                  if (isListening) {
                    stopVoiceRecognition();
                  }

                  // Close the chatbot
                  setIsExpanded(false);
                  if (onClose) onClose();
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-y-auto p-3 space-y-2.5 bg-gradient-to-b from-white/10 to-transparent">
          {/* Enhanced chat history */}
          <>
            {chatHistory.map((msg, index) => (
              <div
                key={index}
                className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'} mb-2.5`}
              >
                {/* Bot avatar for bot messages */}
                {msg.sender === 'bot' && (
                  <div className="flex-shrink-0 mr-2">
                    <div className="w-6 h-6 rounded-full bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center text-white text-xs font-bold shadow-md">
                      AI
                    </div>
                  </div>
                )}

                <div
                  className={`max-w-[75%] p-2.5 ${
                    msg.sender === 'user'
                      ? 'bg-gradient-to-r from-primary to-primary/90 text-primary-foreground rounded-t-lg rounded-bl-lg rounded-br-sm shadow-md'
                      : 'bg-white/90 rounded-t-lg rounded-br-lg rounded-bl-sm shadow-md border border-primary/10'
                  }`}
                >
                  <div className="flex justify-between items-start gap-1.5">
                    <div className={`text-xs ${msg.sender === 'bot' ? 'text-gray-800' : 'text-white'} ${isSpeaking && msg === chatHistory[chatHistory.length - 1] ? "animate-pulse" : ""}`}>
                      {msg.text}
                    </div>
                  </div>
                </div>

                {/* User avatar for user messages */}
                {msg.sender === 'user' && (
                  <div className="flex-shrink-0 ml-2">
                    <div className="w-6 h-6 rounded-full bg-gradient-to-r from-gray-600 to-gray-500 flex items-center justify-center text-white text-xs font-bold shadow-md">
                      {currentUser?.username?.[0]?.toUpperCase() || 'U'}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </>
        </CardContent>

        <CardFooter className="p-2 border-t bg-muted/30">
          <div className="flex w-full gap-1.5 items-center">
            <Input
              placeholder="Ask a question..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              className="flex-1 rounded-full border-primary/20 focus-visible:ring-primary/40 bg-white/90 px-3 py-1 h-8 text-xs shadow-sm"
              disabled={isSending || isListening}
            />

            <Button
              onClick={handleSendMessage}
              disabled={isSending || isListening}
              className="rounded-full h-9 w-9 p-0 bg-primary hover:bg-primary/90"
            >
              {isSending ? (
                <Loader2 className="h-3.5 w-3.5 animate-spin" />
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 2L11 13"></path>
                  <path d="M22 2l-7 20-4-9-9-4 20-7z"></path>
                </svg>
              )}
            </Button>

            {/* Voice input microphone button */}
            <button
              inputstate="default"
              state={isSending ? "disabled" : "default"}
              disablereason={isSending ? "sending_message" : ""}
              data-testid="composer-speech-button"
              aria-label="Start voice mode"
              className={`relative flex h-9 items-center justify-center rounded-full ${
                isListening
                  ? "bg-green-500 text-white"
                  : "bg-orange-500 text-white"
              } transition-colors disabled:text-gray-50 disabled:opacity-30 can-hover:hover:opacity-70 w-9`}
              style={{viewTransitionName: "var(--vt-composer-speech-button)"}}
              onClick={() => {
                // If AI is currently speaking, stop the speech
                if (isSpeaking) {
                  speechSynthesis.stop();
                  setIsSpeaking(false);
                  return;
                }

                // Otherwise, handle voice recognition toggle
                if (isListening) {
                  stopVoiceRecognition();
                } else {
                  startVoiceRecognition();
                }
              }}
              disabled={isSending}
              title={
                isSpeaking
                  ? "Click to stop AI speech"
                  : isListening
                    ? "Click to stop voice input"
                    : "Click to start voice input"
              }
            >
              <div className="flex items-center justify-center">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="icon-md">
                  <path d="M5.66699 14.4165V3.5835C5.66699 2.89314 6.22664 2.3335 6.91699 2.3335C7.6072 2.33367 8.16699 2.89325 8.16699 3.5835V14.4165C8.16699 15.1068 7.6072 15.6663 6.91699 15.6665C6.22664 15.6665 5.66699 15.1069 5.66699 14.4165ZM9.83301 11.9165V6.0835C9.83301 5.39325 10.3928 4.83367 11.083 4.8335C11.7734 4.8335 12.333 5.39314 12.333 6.0835V11.9165C12.333 12.6069 11.7734 13.1665 11.083 13.1665C10.3928 13.1663 9.83301 12.6068 9.83301 11.9165ZM1.5 10.2505V7.75049C1.5 7.06013 2.05964 6.50049 2.75 6.50049C3.44036 6.50049 4 7.06013 4 7.75049V10.2505C3.99982 10.9407 3.44025 11.5005 2.75 11.5005C2.05975 11.5005 1.50018 10.9407 1.5 10.2505ZM14 10.2505V7.75049C14 7.06013 14.5596 6.50049 15.25 6.50049C15.9404 6.50049 16.5 7.06013 16.5 7.75049V10.2505C16.4998 10.9407 15.9402 11.5005 15.25 11.5005C14.5598 11.5005 14.0002 10.9407 14 10.2505Z" fill="currentColor"/>
                </svg>
              </div>
            </button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
