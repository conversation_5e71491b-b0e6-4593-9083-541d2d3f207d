import React, { useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { Home, Download, Calendar, Users, DollarSign, BarChart3, PieC<PERSON>, TrendingUp, Filter } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { useTheme } from '@/context/ThemeContext';

// Sample analytics data
const analyticsData = {
  users: {
    total: 245,
    active: 198,
    new: 32,
    growth: 12.5
  },
  revenue: {
    total: 12450,
    recurring: 9850,
    oneTime: 2600,
    growth: 8.2
  },
  subscriptions: {
    total: 87,
    active: 82,
    canceled: 5,
    growth: 5.3
  },
  content: {
    totalViews: 12580,
    avgTimeOnPage: '3:45',
    bounceRate: '32%',
    growth: 15.8
  },
  monthlyData: [
    { month: 'Jan', users: 35, revenue: 1200, subscriptions: 12 },
    { month: 'Feb', users: 42, revenue: 1450, subscriptions: 15 },
    { month: 'Mar', users: 58, revenue: 2100, subscriptions: 22 },
    { month: 'Apr', users: 63, revenue: 2300, subscriptions: 25 },
    { month: 'May', users: 72, revenue: 2800, subscriptions: 30 },
    { month: 'Jun', users: 90, revenue: 3500, subscriptions: 38 },
    { month: 'Jul', users: 105, revenue: 4200, subscriptions: 45 },
    { month: 'Aug', users: 120, revenue: 5100, subscriptions: 52 },
    { month: 'Sep', users: 143, revenue: 6300, subscriptions: 62 },
    { month: 'Oct', users: 158, revenue: 7500, subscriptions: 68 },
    { month: 'Nov', users: 192, revenue: 9800, subscriptions: 78 },
    { month: 'Dec', users: 245, revenue: 12450, subscriptions: 87 }
  ],
  userTypes: [
    { type: 'Free', count: 158, color: '#38bdf8' },
    { type: 'Basic', count: 52, color: '#818cf8' },
    { type: 'Premium', count: 35, color: '#a855f7' }
  ],
  topContent: [
    { title: 'Getting Started Guide', views: 1250, avgTime: '4:32' },
    { title: 'Legal Document Templates', views: 980, avgTime: '5:18' },
    { title: 'Contract Review Checklist', views: 845, avgTime: '3:55' },
    { title: 'Client Onboarding Process', views: 720, avgTime: '2:48' },
    { title: 'Legal Research Tips', views: 650, avgTime: '3:12' }
  ]
};

export default function TenantAnalyticsPage() {
  const { theme } = useTheme();
  const { id } = useParams<{ id: string }>();
  const [timeRange, setTimeRange] = useState('year');
  const [dataType, setDataType] = useState('users');

  // Tenant name (would come from API in real app)
  const tenantName = 'LegalAid Corp';

  const handleExportData = () => {
    // In a real app, this would generate and download a CSV/PDF
    alert('Exporting analytics data...');
  };

  const getGrowthClass = (growth: number) => {
    return growth >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? (
      <TrendingUp size={14} className="mr-1" />
    ) : (
      <TrendingUp size={14} className="mr-1 rotate-180" />
    );
  };

  return (
    <div className="flex h-screen bg-[#f5f7fb] overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Tenant Analytics</h1>
              <button
                onClick={handleExportData}
                className="bg-gray-100 text-gray-700 rounded-md px-4 py-2 flex items-center hover:bg-gray-200 transition-colors"
              >
                <Download size={16} className="mr-2" />
                Export Data
              </button>
            </div>
            
            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-gray-600 hover:text-gray-900 flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-gray-500">&gt;</span>
              <Link to="/admin/tenants" className="text-gray-600 hover:text-gray-900">
                Tenant Management
              </Link>
              <span className="mx-2 text-gray-500">&gt;</span>
              <span className="text-[#38bdf8]">{tenantName} Analytics</span>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg shadow p-4 mb-6 flex flex-wrap items-center gap-4">
              <div className="flex items-center">
                <Filter size={16} className="text-gray-500 mr-2" />
                <span className="text-sm font-medium text-gray-700">Filters:</span>
              </div>
              
              <div>
                <label className="text-sm text-gray-600 mr-2">Time Range:</label>
                <select
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value)}
                >
                  <option value="week">Last 7 Days</option>
                  <option value="month">Last 30 Days</option>
                  <option value="quarter">Last 90 Days</option>
                  <option value="year">Last 12 Months</option>
                </select>
              </div>
              
              <div>
                <label className="text-sm text-gray-600 mr-2">Data Type:</label>
                <select
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                  value={dataType}
                  onChange={(e) => setDataType(e.target.value)}
                >
                  <option value="users">Users</option>
                  <option value="revenue">Revenue</option>
                  <option value="subscriptions">Subscriptions</option>
                  <option value="content">Content</option>
                </select>
              </div>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Total Users</p>
                    <h3 className="text-2xl font-bold">{analyticsData.users.total}</h3>
                  </div>
                  <div className="bg-blue-100 p-3 rounded-full">
                    <Users size={20} className="text-blue-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <div className={`text-sm flex items-center ${getGrowthClass(analyticsData.users.growth)}`}>
                    {getGrowthIcon(analyticsData.users.growth)}
                    {Math.abs(analyticsData.users.growth)}%
                  </div>
                  <span className="text-xs text-gray-500 ml-2">vs previous period</span>
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Monthly Revenue</p>
                    <h3 className="text-2xl font-bold">${analyticsData.revenue.total}</h3>
                  </div>
                  <div className="bg-green-100 p-3 rounded-full">
                    <DollarSign size={20} className="text-green-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <div className={`text-sm flex items-center ${getGrowthClass(analyticsData.revenue.growth)}`}>
                    {getGrowthIcon(analyticsData.revenue.growth)}
                    {Math.abs(analyticsData.revenue.growth)}%
                  </div>
                  <span className="text-xs text-gray-500 ml-2">vs previous period</span>
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Active Subscriptions</p>
                    <h3 className="text-2xl font-bold">{analyticsData.subscriptions.active}</h3>
                  </div>
                  <div className="bg-purple-100 p-3 rounded-full">
                    <Calendar size={20} className="text-purple-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <div className={`text-sm flex items-center ${getGrowthClass(analyticsData.subscriptions.growth)}`}>
                    {getGrowthIcon(analyticsData.subscriptions.growth)}
                    {Math.abs(analyticsData.subscriptions.growth)}%
                  </div>
                  <span className="text-xs text-gray-500 ml-2">vs previous period</span>
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Content Views</p>
                    <h3 className="text-2xl font-bold">{analyticsData.content.totalViews}</h3>
                  </div>
                  <div className="bg-orange-100 p-3 rounded-full">
                    <BarChart3 size={20} className="text-orange-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <div className={`text-sm flex items-center ${getGrowthClass(analyticsData.content.growth)}`}>
                    {getGrowthIcon(analyticsData.content.growth)}
                    {Math.abs(analyticsData.content.growth)}%
                  </div>
                  <span className="text-xs text-gray-500 ml-2">vs previous period</span>
                </div>
              </div>
            </div>

            {/* Main Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
              <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
                <h3 className="text-lg font-medium mb-4">Growth Trends</h3>
                <div className="h-80">
                  <div className="h-64 flex items-end space-x-2">
                    {analyticsData.monthlyData.map((data, index) => (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div
                          className="w-full bg-blue-500 rounded-t"
                          style={{ 
                            height: `${(data[dataType as keyof typeof data] as number) / 
                              Math.max(...analyticsData.monthlyData.map(d => d[dataType as keyof typeof d] as number)) * 100}%` 
                          }}
                        ></div>
                        <div className="text-xs text-gray-500 mt-1">
                          {data.month}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-4">User Distribution</h3>
                <div className="flex justify-center mb-4">
                  <div className="relative w-48 h-48">
                    <svg viewBox="0 0 100 100" className="w-full h-full">
                      {analyticsData.userTypes.map((type, index, array) => {
                        const total = array.reduce((sum, item) => sum + item.count, 0);
                        const startAngle = array
                          .slice(0, index)
                          .reduce((sum, item) => sum + (item.count / total) * 360, 0);
                        const angle = (type.count / total) * 360;
                        
                        // Convert to radians for calculations
                        const startRad = (startAngle - 90) * (Math.PI / 180);
                        const endRad = (startAngle + angle - 90) * (Math.PI / 180);
                        
                        // Calculate path
                        const x1 = 50 + 40 * Math.cos(startRad);
                        const y1 = 50 + 40 * Math.sin(startRad);
                        const x2 = 50 + 40 * Math.cos(endRad);
                        const y2 = 50 + 40 * Math.sin(endRad);
                        
                        // Determine if the arc should be drawn as a large arc
                        const largeArcFlag = angle > 180 ? 1 : 0;
                        
                        return (
                          <path
                            key={index}
                            d={`M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`}
                            fill={type.color}
                          />
                        );
                      })}
                    </svg>
                  </div>
                </div>
                <div className="space-y-2">
                  {analyticsData.userTypes.map((type, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: type.color }}></div>
                        <span className="text-sm">{type.type}</span>
                      </div>
                      <div className="text-sm font-medium">{type.count} ({Math.round((type.count / analyticsData.users.total) * 100)}%)</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Top Content Table */}
            <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
              <div className="p-6 border-b">
                <h3 className="text-lg font-medium">Top Performing Content</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Title
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Views
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Avg. Time on Page
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {analyticsData.topContent.map((content, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{content.title}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{content.views}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{content.avgTime}</div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Additional Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-4">User Activity</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Active Users</span>
                    <div className="flex items-center">
                      <span className="text-sm font-medium mr-2">{analyticsData.users.active}</span>
                      <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-blue-500 rounded-full" 
                          style={{ width: `${(analyticsData.users.active / analyticsData.users.total) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">New Users (This Month)</span>
                    <div className="flex items-center">
                      <span className="text-sm font-medium mr-2">{analyticsData.users.new}</span>
                      <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-green-500 rounded-full" 
                          style={{ width: `${(analyticsData.users.new / analyticsData.users.total) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Inactive Users</span>
                    <div className="flex items-center">
                      <span className="text-sm font-medium mr-2">{analyticsData.users.total - analyticsData.users.active}</span>
                      <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-red-500 rounded-full" 
                          style={{ width: `${((analyticsData.users.total - analyticsData.users.active) / analyticsData.users.total) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-4">Revenue Breakdown</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Recurring Revenue</span>
                    <div className="flex items-center">
                      <span className="text-sm font-medium mr-2">${analyticsData.revenue.recurring}</span>
                      <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-purple-500 rounded-full" 
                          style={{ width: `${(analyticsData.revenue.recurring / analyticsData.revenue.total) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">One-Time Purchases</span>
                    <div className="flex items-center">
                      <span className="text-sm font-medium mr-2">${analyticsData.revenue.oneTime}</span>
                      <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-yellow-500 rounded-full" 
                          style={{ width: `${(analyticsData.revenue.oneTime / analyticsData.revenue.total) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Average Revenue Per User</span>
                    <span className="text-sm font-medium">
                      ${Math.round((analyticsData.revenue.total / analyticsData.users.active) * 100) / 100}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
