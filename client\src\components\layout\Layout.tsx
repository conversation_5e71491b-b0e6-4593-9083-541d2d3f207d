import { ReactNode } from 'react';
import Navbar from './Navbar';
import Sidebar from './Sidebar';

interface LayoutProps {
  children: ReactNode;
  showSidebar?: boolean;
}

export default function Layout({ children, showSidebar = true }: LayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      {/* Add padding-top to account for fixed navbar height */}
      <div className="flex flex-1 pt-[60px]">
        {showSidebar && <Sidebar />}
        <main
          className={`flex-1 overflow-y-auto scrollbar-auto-hide ${
            showSidebar ? 'md:ml-64 py-6 px-6' : 'container py-6'
          }`}
        >
          {children}
        </main>
      </div>
    </div>
  );
}
