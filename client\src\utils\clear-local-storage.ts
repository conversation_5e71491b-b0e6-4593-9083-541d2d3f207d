/**
 * Utility to clear video-related data from localStorage
 */
export function clearVideoLocalStorage() {
  // Clear all video-related items from localStorage
  const keysToRemove: string[] = [];

  // Find all video-related keys
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (
      key.startsWith('video-') ||
      key.startsWith('videoId-') ||
      key.startsWith('videoData-') ||
      key === 'lawengaxe-saved-videos'
    )) {
      keysToRemove.push(key);
    }
  }

  // Remove all found keys
  keysToRemove.forEach(key => {
    console.log(`Removing ${key} from localStorage`);
    localStorage.removeItem(key);
  });

  console.log(`Cleared ${keysToRemove.length} video-related items from localStorage`);
  return keysToRemove.length;
}
