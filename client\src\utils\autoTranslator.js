/**
 * Auto Translator - Simulates a real translation API
 */

// Language prefixes (used for simulation only)
const languagePrefixes = {
  'hi': 'हिंदी: ',
  'mr': 'मराठी: ',
  'gu': 'ગુજરાતી: ',
  'ta': 'தமிழ்: ',
  'te': 'తెలుగు: ',
  'bn': 'বাংলা: ',
  'kn': 'ಕನ್ನಡ: ',
  'ml': 'മലയാളം: ',
  'es': 'Español: ',
  'fr': 'Français: ',
  'de': 'Deutsch: ',
  'zh': '中文: ',
  'ja': '日本語: ',
  'ru': 'Русский: ',
  'ar': 'العربية: '
};

// Common translations for demonstration
const commonTranslations = {
  'hello': {
    'hi': 'नमस्ते',
    'gu': 'નમસ્તે'
  },
  'how are you': {
    'hi': 'आप कैसे हैं?',
    'gu': 'તમે કેમ છો?'
  },
  'good morning': {
    'hi': 'सुप्रभात',
    'gu': 'સુપ્રભાત'
  },
  'i am happy': {
    'hi': 'मैं खुश हूँ',
    'gu': 'હું ખુશ છું'
  },
  'why are you happy': {
    'hi': 'आप खुश क्यों हैं?',
    'gu': 'તમે શા માટે ખુશ છો?'
  },
  'because my classes are ending': {
    'hi': 'क्योंकि मेरी कक्षाएं समाप्त हो रही हैं',
    'gu': 'કારણ કે મારા વર્ગો સમાપ્ત થઈ રહ્યા છે'
  },
  'do want to play game': {
    'hi': 'क्या आप गेम खेलना चाहते हैं?',
    'gu': 'શું તમે રમત રમવા માંગો છો?'
  },
  'it is ok': {
    'hi': 'यह ठीक है',
    'gu': 'તે બરાબર છે'
  }
};

/**
 * Translates text to the target language
 * In a real app, this would call a translation API
 * 
 * @param {string} text - Text to translate
 * @param {string} targetLanguage - Target language code
 * @returns {string} - Translated text
 */
export function translateText(text, targetLanguage) {
  if (!text || targetLanguage === 'en') {
    return text;
  }

  // Check if we have a common translation
  const lowerText = text.toLowerCase().trim();
  if (commonTranslations[lowerText] && commonTranslations[lowerText][targetLanguage]) {
    return commonTranslations[lowerText][targetLanguage];
  }

  // In a real app, this would call a translation API
  // For simulation, we'll just add a prefix
  return languagePrefixes[targetLanguage] + text;
}

export default {
  translateText
};
