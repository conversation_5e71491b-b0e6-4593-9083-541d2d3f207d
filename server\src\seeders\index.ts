import mongoose from 'mongoose';
import { config } from 'dotenv';
import { connectDB } from '../config/database';
import seedUsers from './user.seeder';
import seedRoles from './role.seeder';
import seedPermissions from './permission.seeder';
import seedSystemConfig from './system-config.seeder';
import seedNotificationTemplates from './notification-template.seeder';
import seedVideos from './video.seeder';
import updateUsers from './update-users.seeder';

// Load environment variables
config();

/**
 * Main seeder function that runs all seeders
 */
async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');

    // Connect to the database
    await connectDB();

    // Run seeders in sequence to handle dependencies
    console.log('🔑 Seeding permissions...');
    await seedPermissions();

    console.log('👑 Seeding roles...');
    await seedRoles();

    console.log('👤 Seeding users...');
    await seedUsers();

    console.log('⚙️ Seeding system configuration...');
    await seedSystemConfig();

    console.log('📧 Seeding notification templates...');
    await seedNotificationTemplates();

    console.log('🎥 Seeding videos...');
    await seedVideos();

    console.log('👤 Updating users with enhanced information...');
    await updateUsers();

    console.log('✅ Database seeding completed successfully!');

    // Close the database connection
    await mongoose.connection.close();
    console.log('📝 Database connection closed');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding database:', error);

    // Close the database connection
    await mongoose.connection.close();
    console.log('📝 Database connection closed');

    process.exit(1);
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

export default seedDatabase;
