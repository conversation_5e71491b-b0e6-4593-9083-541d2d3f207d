import { UserModel, RoleModel } from '../models';
import { connectDB } from '../config/database';
import mongoose from 'mongoose';

async function findSpecificUsers() {
  try {
    // Connect to the database
    await connectDB();
    
    // Find admin user
    const adminUser = await UserModel.findOne({ email: '<EMAIL>' });
    
    if (adminUser) {
      console.log('Found admin user:');
      console.log(`
User ID: ${adminUser.id}
Username: ${adminUser.username}
Email: ${adminUser.email}
Status: ${adminUser.status}
Email Verified: ${adminUser.emailVerified}
Roles: ${adminUser.roles}
      `);
      
      // Find the role details
      const adminRoleId = adminUser.roles[0];
      const adminRole = await RoleModel.findOne({ id: adminRoleId });
      
      if (adminRole) {
        console.log('Admin role details:');
        console.log(`
Role ID: ${adminRole.id}
Name: ${adminRole.name}
Code: ${adminRole.code}
Description: ${adminRole.description}
Permissions: ${adminRole.permissions.length} permissions
        `);
      } else {
        console.log(`Admin role with ID ${adminRoleId} not found`);
      }
    } else {
      console.log('Admin user not found');
    }
    
    // Find your user
    const yourUser = await UserModel.findOne({ email: '<EMAIL>' });
    
    if (yourUser) {
      console.log('\nFound your user:');
      console.log(`
User ID: ${yourUser.id}
Username: ${yourUser.username}
Email: ${yourUser.email}
Status: ${yourUser.status}
Email Verified: ${yourUser.emailVerified}
Roles: ${yourUser.roles}
      `);
      
      // Find the role details
      const yourRoleId = yourUser.roles[0];
      const yourRole = await RoleModel.findOne({ id: yourRoleId });
      
      if (yourRole) {
        console.log('Your role details:');
        console.log(`
Role ID: ${yourRole.id}
Name: ${yourRole.name}
Code: ${yourRole.code}
Description: ${yourRole.description}
Permissions: ${yourRole.permissions.length} permissions
        `);
      } else {
        console.log(`Your role with ID ${yourRoleId} not found`);
      }
    } else {
      console.log('Your user not found');
    }
    
    // Close the database connection
    await mongoose.connection.close();
    
  } catch (error) {
    console.error('Error finding specific users:', error);
    process.exit(1);
  }
}

// Run the function
findSpecificUsers().catch(console.error);
