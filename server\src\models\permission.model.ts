import mongoose, { Schem<PERSON>, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * Permission interface extending the base entity
 */
export interface IPermission extends IBaseEntity {
  /** Human-readable name of the permission */
  name: string;

  /** Detailed description explaining what this permission allows */
  description: string;

  /**
   * Unique permission code used for programmatic access
   * Example: 'video:create', 'user:delete', 'settings:read'
   */
  code: string;

  /**
   * Category for grouping related permissions
   * Examples: 'content', 'users', 'settings', 'billing'
   */
  category: string;

  /**
   * Whether this permission is currently active
   * Inactive permissions are not enforced even if assigned
   */
  isActive: boolean;

  /**
   * Resource type this permission applies to
   * Examples: 'video', 'user', 'comment', 'system'
   */
  resourceType: string;

  /**
   * Action this permission allows
   * Examples: 'create', 'read', 'update', 'delete', 'manage'
   */
  action: string;

  /**
   * Scope of the permission
   *
   * Possible values:
   * - 'global': Applies to all resources of the type
   * - 'own': Applies only to resources owned by the user
   * - 'group': Applies to resources within the user's group
   */
  scope: 'global' | 'own' | 'group';

  /**
   * Conditions that must be met for the permission to apply
   * Stored as a JSON string that can be evaluated at runtime
   */
  conditions?: string;

  /**
   * Dependencies on other permissions
   * Array of permission codes that are required for this permission to be effective
   */
  dependencies?: string[];

  /**
   * Conflicts with other permissions
   * Array of permission codes that cannot be assigned together with this permission
   */
  conflicts?: string[];
}

/**
 * Permission schema definition
 */
const PermissionSchema = new Schema<IPermission>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
    },
    code: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      match: /^[a-z0-9_:.-]+$/,
    },
    category: {
      type: String,
      required: true,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    resourceType: {
      type: String,
      required: true,
      trim: true,
    },
    action: {
      type: String,
      required: true,
      trim: true,
    },
    scope: {
      type: String,
      enum: ['global', 'own', 'group'],
      default: 'own',
    },
    conditions: {
      type: String,
    },
    dependencies: {
      type: [String],
    },
    conflicts: {
      type: [String],
    },
  }
);

// Merge with base schema
PermissionSchema.add(BaseSchema);

// Add compound index for code with soft delete support
PermissionSchema.index({ code: 1, deletedAt: 1 }, { unique: true, sparse: true });

// Create and export the Permission model
const PermissionModel = mongoose.model<IPermission>('Permission', PermissionSchema);
export default PermissionModel;
