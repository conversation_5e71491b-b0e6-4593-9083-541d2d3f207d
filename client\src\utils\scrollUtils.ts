/**
 * Utility functions for handling scrollbar visibility
 */

/**
 * Adds scroll event listeners to an element to show/hide scrollbars
 * @param element - The DOM element to add scroll listeners to
 */
const addScrollListeners = (element: Element): void => {
  let scrollTimeout: number | null = null;

  element.addEventListener('scroll', () => {
    // Add a class to show the scrollbar when scrolling
    element.classList.add('is-scrolling');

    // Clear the previous timeout
    if (scrollTimeout !== null) {
      window.clearTimeout(scrollTimeout);
    }

    // Set a timeout to hide the scrollbar after scrolling stops
    scrollTimeout = window.setTimeout(() => {
      element.classList.remove('is-scrolling');
    }, 1000); // Hide scrollbar after 1 second of inactivity
  });
};

/**
 * Initializes auto-hiding scrollbars for elements with the scrollbar-auto-hide class
 * Shows scrollbars only when actively scrolling and hides them after a delay
 */
export const initAutoHideScrollbars = (): void => {
  // Find all elements with the scrollbar-auto-hide class
  const scrollableElements = document.querySelectorAll('.scrollbar-auto-hide');

  // Add scroll event listeners to each element
  scrollableElements.forEach(addScrollListeners);

  // Set up a MutationObserver to watch for new elements with the scrollbar-auto-hide class
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          // Check if the node is an element
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the element has the scrollbar-auto-hide class
            if ((node as Element).classList?.contains('scrollbar-auto-hide')) {
              addScrollListeners(node as Element);
            }

            // Check for child elements with the scrollbar-auto-hide class
            const childScrollables = (node as Element).querySelectorAll?.('.scrollbar-auto-hide');
            if (childScrollables) {
              childScrollables.forEach(addScrollListeners);
            }
          }
        });
      }
    });
  });

  // Start observing the document body for changes
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
};
