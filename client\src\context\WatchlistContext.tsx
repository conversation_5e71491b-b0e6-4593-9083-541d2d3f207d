import React, { createContext, useContext, useState, useEffect } from 'react';

// Define the video type
export interface WatchlistVideo {
  id: string;
  title: string;
  thumbnail: string;
  creator: {
    name: string;
    avatar: string;
  };
  views: number;
  duration: string;
  addedAt: string;
}

interface WatchlistContextType {
  watchlist: WatchlistVideo[];
  addToWatchlist: (video: WatchlistVideo) => void;
  removeFromWatchlist: (videoId: string) => void;
  isInWatchlist: (videoId: string) => boolean;
  clearWatchlist: () => void;
}

const WatchlistContext = createContext<WatchlistContextType | undefined>(undefined);

export const useWatchlist = () => {
  const context = useContext(WatchlistContext);
  if (!context) {
    throw new Error('useWatchlist must be used within a WatchlistProvider');
  }
  return context;
};

export const WatchlistProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize watchlist from localStorage if available
  const [watchlist, setWatchlist] = useState<WatchlistVideo[]>(() => {
    const savedWatchlist = localStorage.getItem('watchlist');
    return savedWatchlist ? JSON.parse(savedWatchlist) : [];
  });

  // Save watchlist to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('watchlist', JSON.stringify(watchlist));
  }, [watchlist]);

  const addToWatchlist = (video: WatchlistVideo) => {
    // Don't add if already in watchlist
    if (isInWatchlist(video.id)) return;
    
    // Add current date as addedAt if not provided
    const videoWithDate = {
      ...video,
      addedAt: video.addedAt || new Date().toISOString()
    };
    
    setWatchlist(prev => [...prev, videoWithDate]);
  };

  const removeFromWatchlist = (videoId: string) => {
    setWatchlist(prev => prev.filter(video => video.id !== videoId));
  };

  const isInWatchlist = (videoId: string) => {
    return watchlist.some(video => video.id === videoId);
  };

  const clearWatchlist = () => {
    setWatchlist([]);
  };

  return (
    <WatchlistContext.Provider value={{ 
      watchlist, 
      addToWatchlist, 
      removeFromWatchlist, 
      isInWatchlist,
      clearWatchlist 
    }}>
      {children}
    </WatchlistContext.Provider>
  );
};
