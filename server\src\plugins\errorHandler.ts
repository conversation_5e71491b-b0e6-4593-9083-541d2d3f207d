import { FastifyInstance, FastifyPluginAsync } from 'fastify';
import fastifyPlugin from 'fastify-plugin';
import { errorHandler } from '../utils/errors';

/**
 * Plugin to add centralized error handling to Fastify
 */
const errorHandlerPlugin: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  // Set the error handler
  fastify.setErrorHandler(errorHandler);
};

export default fastifyPlugin(errorHandlerPlugin);
