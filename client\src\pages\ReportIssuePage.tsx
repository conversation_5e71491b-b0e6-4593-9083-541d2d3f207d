import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Bug, Lightbulb, Upload, X } from 'lucide-react';
import { reportAPI } from '@/services/report-api';

export default function ReportIssuePage() {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  
  // Form state
  const [title, setTitle] = useState('');
  const [type, setType] = useState<'bug' | 'suggestion' | 'other'>('bug');
  const [description, setDescription] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [fileUrls, setFileUrls] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles) return;

    // Convert FileList to array and add to state
    const newFiles = Array.from(selectedFiles);
    setFiles(prev => [...prev, ...newFiles]);

    // Create URLs for preview
    const newUrls = newFiles.map(file => URL.createObjectURL(file));
    setFileUrls(prev => [...prev, ...newUrls]);
  };

  // Remove a file
  const removeFile = (index: number) => {
    // Revoke the object URL to avoid memory leaks
    URL.revokeObjectURL(fileUrls[index]);
    
    // Remove file from state
    setFiles(prev => prev.filter((_, i) => i !== index));
    setFileUrls(prev => prev.filter((_, i) => i !== index));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      setError('Please enter a title for your report');
      return;
    }

    if (!description.trim()) {
      setError('Please provide a description of the issue');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // TODO: In a real implementation, you would upload the files to a server
      // and get back URLs to include in the report
      // For now, we'll just use the file names as placeholders
      const fileNames = files.map(file => file.name);

      const response = await reportAPI.createIssueReport({
        title,
        type,
        description,
        evidence: fileNames,
      });

      if (response.success) {
        toast({
          title: 'Report Submitted',
          description: 'Thank you for your feedback! Your report has been submitted successfully.',
        });
        navigate('/');
      } else {
        setError('Failed to submit report. Please try again.');
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while submitting your report');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Layout>
      <div className="container mx-auto py-8">
        <h1 className="text-2xl font-bold mb-6">Report an Issue</h1>
        
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle>Submit Feedback or Report a Bug</CardTitle>
            <CardDescription>
              Help us improve by reporting bugs or suggesting new features
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">Issue Title</Label>
                <Input
                  id="title"
                  placeholder="Enter a brief title for the issue"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  disabled={isSubmitting}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="type">Issue Type</Label>
                <Select
                  value={type}
                  onValueChange={(value) => setType(value as 'bug' | 'suggestion' | 'other')}
                  disabled={isSubmitting}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Select issue type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bug" className="flex items-center">
                      <div className="flex items-center">
                        <Bug className="mr-2 h-4 w-4" />
                        Bug Report
                      </div>
                    </SelectItem>
                    <SelectItem value="suggestion">
                      <div className="flex items-center">
                        <Lightbulb className="mr-2 h-4 w-4" />
                        Feature Suggestion
                      </div>
                    </SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Please describe the issue in detail. Include steps to reproduce if reporting a bug."
                  rows={6}
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  disabled={isSubmitting}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="files">Attachments (Optional)</Label>
                <div className="border border-input rounded-md p-4">
                  <Input
                    id="files"
                    type="file"
                    multiple
                    onChange={handleFileUpload}
                    className="cursor-pointer"
                    disabled={isSubmitting}
                  />
                  
                  {fileUrls.length > 0 && (
                    <div className="mt-4 grid grid-cols-2 gap-2">
                      {fileUrls.map((url, index) => (
                        <div key={index} className="relative group">
                          {url.startsWith('blob:') && url.match(/\.(jpeg|jpg|gif|png)$/i) ? (
                            <img
                              src={url}
                              alt={`Attachment ${index + 1}`}
                              className="h-24 w-full object-cover rounded-md border border-border"
                            />
                          ) : (
                            <div className="h-24 w-full flex items-center justify-center bg-muted rounded-md border border-border">
                              <span className="text-xs text-muted-foreground truncate px-2">
                                {files[index]?.name || `File ${index + 1}`}
                              </span>
                            </div>
                          )}
                          <button
                            type="button"
                            onClick={() => removeFile(index)}
                            className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            disabled={isSubmitting}
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  You can upload screenshots or other files to help explain the issue.
                </p>
              </div>
            </form>
          </CardContent>
          
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
                  Submitting...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  Submit Report
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </Layout>
  );
}
