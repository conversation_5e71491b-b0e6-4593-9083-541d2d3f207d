import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Trash2 } from 'lucide-react';
import { useLanguage } from '@/context/LanguageContext';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Language } from '@/types';

interface AddVideoFormProps {
  languages: Language[];
  categories: string[];
  onSave: (data: {
    engaxeUrl: string;
    title: string;
    description: string;
    thumbnail: string | null;
    thumbnailFile: File | null;
    category: string;
    defaultLanguage: string;
    defaultLanguageUrl: string;
    additionalLanguages: {
      id: string;
      languageCode: string;
      url: string;
    }[];
  }) => void;
  onCancel: () => void;
}

export default function AddVideoForm({
  languages,
  categories,
  onSave,
  onCancel
}: AddVideoFormProps) {
  const { t } = useLanguage();

  // Video data
  const [engaxeUrl, setEngaxeUrl] = useState('');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [thumbnail, setThumbnail] = useState<string | null>(null);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [category, setCategory] = useState('');
  const [defaultLanguage, setDefaultLanguage] = useState('en');
  const [defaultLanguageUrl, setDefaultLanguageUrl] = useState('');

  // Additional languages
  const [additionalLanguages, setAdditionalLanguages] = useState<{
    id: string;
    languageCode: string;
    url: string;
  }[]>([]);

  // Loading state
  const [isLoading, setIsLoading] = useState(false);

  // Function to handle thumbnail upload
  const handleThumbnailUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      alert("Please upload an image file");
      return;
    }

    // Set the thumbnail file
    setThumbnailFile(file);

    // Create a URL for the thumbnail preview
    const objectUrl = URL.createObjectURL(file);
    setThumbnail(objectUrl);
  };

  // Function to add a new language entry
  const addLanguageEntry = () => {
    const newEntry = {
      id: `lang-${Date.now()}`,
      languageCode: '',
      url: ''
    };

    setAdditionalLanguages(prev => [...prev, newEntry]);
  };

  // Function to update a language entry
  const updateLanguageEntry = (id: string, field: 'languageCode' | 'url', value: string) => {
    setAdditionalLanguages(prev =>
      prev.map(entry =>
        entry.id === id ? { ...entry, [field]: value } : entry
      )
    );
  };

  // Function to remove a language entry
  const removeLanguageEntry = (id: string) => {
    setAdditionalLanguages(prev => prev.filter(entry => entry.id !== id));
  };

  // Get available languages (excluding the default language and already selected ones)
  const getAvailableLanguages = () => {
    const selectedCodes = [defaultLanguage, ...additionalLanguages.map(entry => entry.languageCode)].filter(Boolean);
    return languages.filter(lang => !selectedCodes.includes(lang.code));
  };

  // Function to extract Engaxe video ID from URL
  const extractEngaxeVideoId = (url: string): string | null => {
    if (!url) return null;

    // Trim the input
    const trimmedUrl = url.trim();

    // If it's just a video ID (no slashes, dots, or protocol)
    if (!trimmedUrl.includes('/') && !trimmedUrl.includes('.') && !trimmedUrl.includes(':')) {
      if (trimmedUrl.length > 3 && trimmedUrl.length < 20 && /^[a-zA-Z0-9]+$/.test(trimmedUrl)) {
        console.log(`Input appears to be a direct video ID: ${trimmedUrl}`);
        return trimmedUrl;
      }
    }

    // Engaxe URL patterns
    const patterns = [
      // Format: engaxe.com/videos/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/videos\/([^/?]+)/i,
      // Format: engaxe.com/v/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/v\/([^/?]+)/i,
      // Format: engaxe.com/watch/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/watch\/([^/?]+)/i,
      // Format: engaxe.com/embed/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/embed\/([^/?]+)/i
    ];

    // Try each pattern
    for (const pattern of patterns) {
      const match = trimmedUrl.match(pattern);
      if (match && match[1]) {
        console.log(`Successfully extracted video ID: ${match[1]} using pattern: ${pattern}`);
        return match[1];
      }
    }

    // If no pattern matches, try a simple extraction as a fallback
    try {
      // Try to parse as URL first
      const urlObj = new URL(trimmedUrl.startsWith('http') ? trimmedUrl : `https://${trimmedUrl}`);
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

      if (pathParts.length > 0) {
        const lastPart = pathParts[pathParts.length - 1];
        if (lastPart && lastPart.length > 0 && !lastPart.includes('.') && !lastPart.includes('?')) {
          console.log(`Using URL path extraction, got video ID: ${lastPart}`);
          return lastPart;
        }
      }
    } catch (error) {
      // If URL parsing fails, try simple string splitting
      const parts = trimmedUrl.split('/');
      const lastPart = parts[parts.length - 1];
      if (lastPart && lastPart.length > 0 && !lastPart.includes('.') && !lastPart.includes('?')) {
        console.log(`Using fallback string extraction, got video ID: ${lastPart}`);
        return lastPart;
      }
    }

    console.log('Failed to extract video ID from input');
    return null;
  };

  // Handle save
  const handleSave = () => {
    if (!engaxeUrl) {
      alert("Please enter an Engaxe URL");
      return;
    }

    if (!title) {
      alert("Please enter a title");
      return;
    }

    if (!category) {
      alert("Please select a category");
      return;
    }

    if (!defaultLanguage) {
      alert("Please select a default language");
      return;
    }

    if (!defaultLanguageUrl) {
      alert("Please enter a URL for the default language");
      return;
    }

    setIsLoading(true);

    // Extract video ID from URL if possible
    const videoId = extractEngaxeVideoId(engaxeUrl);
    const processedUrl = videoId || engaxeUrl;

    // Also process the default language URL
    const defaultLangVideoId = extractEngaxeVideoId(defaultLanguageUrl);
    const processedDefaultLangUrl = defaultLangVideoId || defaultLanguageUrl;

    if (videoId) {
      console.log(`Using extracted video ID: ${videoId} (original URL: ${engaxeUrl})`);
    }

    if (defaultLangVideoId) {
      console.log(`Using extracted default language video ID: ${defaultLangVideoId} (original URL: ${defaultLanguageUrl})`);
    }

    // Process additional language URLs
    const processedAdditionalLanguages = additionalLanguages.map(lang => {
      // Skip empty URLs
      if (!lang.url) {
        console.log(`No URL provided for language ${lang.languageCode}, using default language URL`);
        return { ...lang, url: processedDefaultLangUrl };
      }

      // Extract video ID from URL if possible
      const langVideoId = extractEngaxeVideoId(lang.url);
      if (langVideoId) {
        console.log(`Using extracted video ID for language ${lang.languageCode}: ${langVideoId} (original URL: ${lang.url})`);
        return { ...lang, url: langVideoId };
      }

      // If standard extraction fails, try a more aggressive approach for URLs like https://engaxe.com/v/XjKFqQ
      if (lang.url.includes('/')) {
        const engaxeIdMatch = lang.url.match(/\/([a-zA-Z0-9]{6,7})(?:\/|$)/);
        if (engaxeIdMatch && engaxeIdMatch[1]) {
          const extractedId = engaxeIdMatch[1];
          console.log(`Extracted video ID using fallback method for language ${lang.languageCode}: ${extractedId} (original URL: ${lang.url})`);
          return { ...lang, url: extractedId };
        }
      }

      // If extraction fails, use the original URL
      console.log(`Could not extract video ID for language ${lang.languageCode}, using original URL: ${lang.url}`);
      return lang;
    });

    // Validate that each language has a unique URL
    console.log('Processed additional languages:', processedAdditionalLanguages);

    // Call the onSave callback with the form data
    onSave({
      engaxeUrl: processedUrl,
      title,
      description,
      thumbnail,
      thumbnailFile,
      category,
      defaultLanguage,
      defaultLanguageUrl: processedDefaultLangUrl,
      additionalLanguages: processedAdditionalLanguages
    });

    setIsLoading(false);
  };

  return (
    <div className="space-y-6">
      {/* Engaxe URL */}
      <div className="space-y-2">
        <label htmlFor="engaxeUrl" className="text-sm font-medium">Engaxe URL</label>
        <Input
          id="engaxeUrl"
          value={engaxeUrl}
          onChange={(e) => {
            setEngaxeUrl(e.target.value);
            // Also update the default language URL
            setDefaultLanguageUrl(e.target.value);
          }}
          placeholder="https://engaxe.com/videos/1234"
        />
      </div>

      {/* Title */}
      <div className="space-y-2">
        <label htmlFor="title" className="text-sm font-medium">{t('creator.video_title')}</label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter video title"
        />
      </div>

      {/* Thumbnail */}
      <div className="space-y-2">
        <label htmlFor="thumbnail" className="text-sm font-medium">Thumbnail</label>
        <div className="flex flex-col gap-2">
          {thumbnail && (
            <div className="relative aspect-video w-full overflow-hidden rounded-md border border-gray-700">
              <img
                src={thumbnail}
                alt="Video thumbnail"
                className="h-full w-full object-cover"
              />
            </div>
          )}
          <Input
            id="thumbnail"
            type="file"
            accept="image/*"
            onChange={handleThumbnailUpload}
            className="cursor-pointer"
          />
          <p className="text-xs text-lingstream-muted">
            Upload a thumbnail for your video
          </p>
        </div>
      </div>

      {/* Description */}
      <div className="space-y-2">
        <label htmlFor="description" className="text-sm font-medium">{t('creator.description')}</label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Enter video description"
          rows={3}
        />
      </div>

      {/* Category */}
      <div className="space-y-2">
        <label htmlFor="category" className="text-sm font-medium">{t('creator.category')}</label>
        <Select value={category} onValueChange={setCategory}>
          <SelectTrigger>
            <SelectValue placeholder="Select a category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((cat) => (
              <SelectItem key={cat} value={cat}>{cat}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Default Language Section */}
      <div className="space-y-2 border-t pt-4 mt-4">
        <label className="text-sm font-medium">{t('creator.video_language')}</label>
        <div className="grid grid-cols-2 gap-2">
          <Select value={defaultLanguage} onValueChange={setDefaultLanguage}>
            <SelectTrigger>
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent>
              {languages.map((lang) => (
                <SelectItem key={lang.code} value={lang.code}>
                  <div className="flex items-center gap-2">
                    <span>{lang.flag}</span>
                    <span>{lang.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Input
            value={defaultLanguageUrl}
            onChange={(e) => setDefaultLanguageUrl(e.target.value)}
            placeholder="Engaxe URL for this language"
          />
        </div>
      </div>

      {/* Additional Languages Section */}
      <div className="space-y-2 mt-4">
        <div className="flex justify-between items-center">
          <label className="text-sm font-medium">{t('creator.language_versions')}</label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addLanguageEntry}
            disabled={getAvailableLanguages().length === 0}
          >
            <Plus className="h-4 w-4 mr-1" />
            {t('common.add')}
          </Button>
        </div>

        {additionalLanguages.length === 0 ? (
          <p className="text-xs text-lingstream-muted">
            Add additional language versions of this video
          </p>
        ) : (
          <div className="space-y-3">
            {additionalLanguages.map((entry) => (
              <div key={entry.id} className="flex items-end gap-2">
                <div className="flex-1">
                  <label className="text-xs mb-1 block">Language</label>
                  <Select
                    value={entry.languageCode}
                    onValueChange={(value) => updateLanguageEntry(entry.id, 'languageCode', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableLanguages().map((lang) => (
                        <SelectItem key={lang.code} value={lang.code}>
                          <div className="flex items-center gap-2">
                            <span>{lang.flag}</span>
                            <span>{lang.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex-1">
                  <label className="text-xs mb-1 block">Engaxe URL</label>
                  <Input
                    value={entry.url}
                    onChange={(e) => updateLanguageEntry(entry.id, 'url', e.target.value)}
                    placeholder="Paste Engaxe URL"
                  />
                </div>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeLanguageEntry(entry.id)}
                  className="text-red-500 hover:text-red-600 hover:bg-red-100/10"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="flex justify-end gap-2 pt-4 border-t">
        <Button variant="outline" onClick={onCancel}>
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleSave}
          disabled={!title || !category || !defaultLanguage || !defaultLanguageUrl || isLoading}
        >
          {t('common.save')}
        </Button>
      </div>
    </div>
  );
}
