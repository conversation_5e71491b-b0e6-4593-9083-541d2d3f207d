import Typesense from 'typesense';
import { config } from 'dotenv';

// Load environment variables
config();

// Typesense client configuration
const typesenseConfig = {
  nodes: [
    {
      host: process.env.TYPESENSE_HOST || 'localhost',
      port: process.env.TYPESENSE_PORT ? parseInt(process.env.TYPESENSE_PORT, 10) : 8108,
      protocol: process.env.TYPESENSE_PROTOCOL || 'http',
    },
  ],
  apiKey: process.env.TYPESENSE_API_KEY || 'xyz',
  connectionTimeoutSeconds: 10,
};

// Create Typesense client
const typesenseClient = new Typesense.Client(typesenseConfig);

// Initialize Typesense collections
export const initializeTypesense = async (): Promise<void> => {
  try {
    // Check if collections exist, if not create them

    // Example: Create a 'videos' collection
    try {
      await typesenseClient.collections('videos').retrieve();
      console.log('Videos collection already exists in Typesense');
    } catch (error) {
      // Collection doesn't exist, create it
      const videosSchema = {
        name: 'videos',
        fields: [
          { name: 'id', type: 'string' },
          { name: 'title', type: 'string' },
          { name: 'description', type: 'string' },
          { name: 'tags', type: 'string[]' },
          { name: 'createdAt', type: 'int64' },
          { name: 'views', type: 'int32' },
          { name: 'channelId', type: 'string' },
          { name: 'channelName', type: 'string' },
          { name: 'languages', type: 'object[]' },
        ],
        default_sorting_field: 'createdAt',
      };

      await typesenseClient.collections().create(videosSchema as any);
      console.log('Videos collection created in Typesense');
    }

    // Example: Create a 'users' collection
    try {
      await typesenseClient.collections('users').retrieve();
      console.log('Users collection already exists in Typesense');
    } catch (error) {
      // Collection doesn't exist, create it
      const usersSchema = {
        name: 'users',
        fields: [
          { name: 'id', type: 'string' },
          { name: 'username', type: 'string' },
          { name: 'displayName', type: 'string' },
          { name: 'email', type: 'string' },
          { name: 'createdAt', type: 'int64' },
        ],
        default_sorting_field: 'createdAt',
      };

      await typesenseClient.collections().create(usersSchema as any);
      console.log('Users collection created in Typesense');
    }

    console.log('Typesense initialized successfully');
  } catch (error) {
    console.error(`Error initializing Typesense: ${error}`);
    throw error;
  }
};

export default typesenseClient;
