import { VideoIdMappingModel } from '../models';
import { generateEngaxeId, isValidEngaxeId } from '../utils/id';

/**
 * Service for managing video ID mappings
 */
export class VideoIdMappingService {
  /**
   * Transform a video object to ensure it uses Engaxe IDs consistently
   * @param video The video object to transform
   * @returns The transformed video object
   */
  async transformVideoForClient(video: any): Promise<any> {
    if (!video) return video;

    console.log('VideoIdMappingService: Transforming video', video.id);

    // Create a copy of the video object
    const transformedVideo = { ...video };

    // Check if the ID is already a valid Engaxe ID
    if (transformedVideo.id && isValidEngaxeId(transformedVideo.id)) {
      console.log(`VideoIdMappingService: ID ${transformedVideo.id} is already a valid Engaxe ID`);

      // Ensure URL matches ID for consistency
      if (transformedVideo.url !== transformedVideo.id) {
        console.log(`VideoIdMappingService: Updating URL to match ID: ${transformedVideo.id}`);
        transformedVideo.url = transformedVideo.id;
      }
    }
    // If ID is not a valid Engaxe ID (legacy hash ID), try to convert it
    else if (transformedVideo.id) {
      console.log(`VideoIdMappingService: ID ${transformedVideo.id} is not a valid Engaxe ID, checking for mapping`);

      // Try to get the Engaxe ID from the mapping service
      const engaxeId = await this.getEngaxeIdFromHashId(transformedVideo.id);

      if (engaxeId) {
        console.log(`VideoIdMappingService: Found mapping for legacy ID ${transformedVideo.id}: ${engaxeId}`);
        // Keep the original ID for backward compatibility but ensure URL is the Engaxe ID
        transformedVideo.url = engaxeId;
      } else {
        // If no mapping exists, generate a new Engaxe ID
        const newEngaxeId = generateEngaxeId();
        console.log(`VideoIdMappingService: Generated new Engaxe ID for legacy ID ${transformedVideo.id}: ${newEngaxeId}`);

        // Create a mapping for backward compatibility
        await this.createMapping(transformedVideo.id, newEngaxeId, transformedVideo.title || 'Untitled Video');

        // Set the URL to the new Engaxe ID
        transformedVideo.url = newEngaxeId;
      }
    } else {
      console.log(`VideoIdMappingService: Video is missing id`,
                 'id: missing',
                 transformedVideo.url ? `url: ${transformedVideo.url}` : 'url: missing');
    }

    // If the video has languages, ensure they also use valid Engaxe IDs
    if (transformedVideo.languages && Array.isArray(transformedVideo.languages)) {
      console.log(`VideoIdMappingService: Video has ${transformedVideo.languages.length} languages`);

      transformedVideo.languages = transformedVideo.languages.map((lang: any) => {
        if (lang.url && !isValidEngaxeId(lang.url)) {
          console.log(`VideoIdMappingService: Language ${lang.code} has invalid URL: ${lang.url}`);
          // Use the video's URL only for languages with invalid URLs
          lang.url = transformedVideo.url;
          console.log(`VideoIdMappingService: Updated language ${lang.code} URL to: ${lang.url}`);
        } else if (lang.url) {
          console.log(`VideoIdMappingService: Language ${lang.code} already has valid URL: ${lang.url}, preserving it`);
        } else {
          console.log(`VideoIdMappingService: Language ${lang.code} has no URL, setting to video's URL: ${transformedVideo.url}`);
          lang.url = transformedVideo.url;
        }
        return lang;
      });
    } else {
      console.log(`VideoIdMappingService: Video has no languages`);
    }

    console.log(`VideoIdMappingService: Transformation complete. Final URL: ${transformedVideo.url}`);
    return transformedVideo;
  }

  /**
   * Transform an array of video objects for client use
   * @param videos Array of video objects
   * @returns Transformed array
   */
  async transformVideosForClient(videos: any[]): Promise<any[]> {
    if (!videos || !Array.isArray(videos)) return videos;

    const transformedVideos = [];
    for (const video of videos) {
      transformedVideos.push(await this.transformVideoForClient(video));
    }

    return transformedVideos;
  }
  /**
   * Get the Engaxe ID for a given hash ID
   * @param hashId The hash ID to look up
   * @returns The corresponding Engaxe ID, or null if not found
   */
  async getEngaxeIdFromHashId(hashId: string): Promise<string | null> {
    try {
      // Look up the mapping
      const mapping = await VideoIdMappingModel.findOne({ hashId });

      // If found, return the Engaxe ID
      if (mapping) {
        console.log(`Found mapping for hash ID ${hashId}: ${mapping.engaxeId}`);
        return mapping.engaxeId;
      }

      // If not found, return null
      console.log(`No mapping found for hash ID ${hashId}`);
      return null;
    } catch (error) {
      console.error(`Error getting Engaxe ID for hash ID ${hashId}:`, error);
      return null;
    }
  }

  /**
   * Get the hash ID for a given Engaxe ID
   * @param engaxeId The Engaxe ID to look up
   * @returns The corresponding hash ID, or null if not found
   */
  async getHashIdFromEngaxeId(engaxeId: string): Promise<string | null> {
    try {
      // Look up the mapping
      const mapping = await VideoIdMappingModel.findOne({ engaxeId });

      // If found, return the hash ID
      if (mapping) {
        console.log(`Found mapping for Engaxe ID ${engaxeId}: ${mapping.hashId}`);
        return mapping.hashId;
      }

      // If not found, return null
      console.log(`No mapping found for Engaxe ID ${engaxeId}`);
      return null;
    } catch (error) {
      console.error(`Error getting hash ID for Engaxe ID ${engaxeId}:`, error);
      return null;
    }
  }

  /**
   * Create a new mapping between a hash ID and an Engaxe ID
   * @param hashId The hash ID to map
   * @param engaxeId The Engaxe ID to map (optional, will generate if not provided)
   * @param videoTitle The title of the video (for reference)
   * @returns The created mapping, or null if creation failed
   */
  async createMapping(hashId: string, engaxeId?: string, videoTitle: string = 'Untitled Video'): Promise<{ hashId: string, engaxeId: string } | null> {
    try {
      // If no Engaxe ID is provided, generate one
      let finalEngaxeId = engaxeId;

      if (!finalEngaxeId || !isValidEngaxeId(finalEngaxeId)) {
        finalEngaxeId = generateEngaxeId();
        console.log(`Generated new Engaxe ID for hash ID ${hashId}: ${finalEngaxeId}`);
      }

      // Check if a mapping already exists for this hash ID
      const existingHashMapping = await VideoIdMappingModel.findOne({ hashId });
      if (existingHashMapping) {
        console.log(`Mapping already exists for hash ID ${hashId}: ${existingHashMapping.engaxeId}`);
        return { hashId, engaxeId: existingHashMapping.engaxeId };
      }

      // Check if a mapping already exists for this Engaxe ID
      const existingEngaxeMapping = await VideoIdMappingModel.findOne({ engaxeId: finalEngaxeId });
      if (existingEngaxeMapping) {
        console.log(`Mapping already exists for Engaxe ID ${finalEngaxeId}: ${existingEngaxeMapping.hashId}`);

        // Generate a new Engaxe ID to avoid conflicts
        finalEngaxeId = generateEngaxeId();
        console.log(`Generated new Engaxe ID to avoid conflict: ${finalEngaxeId}`);
      }

      // Create the mapping
      const mapping = new VideoIdMappingModel({
        hashId,
        engaxeId: finalEngaxeId,
        videoTitle,
      });

      // Save the mapping
      await mapping.save();
      console.log(`Created new mapping: ${hashId} -> ${finalEngaxeId}`);

      return { hashId, engaxeId: finalEngaxeId };
    } catch (error) {
      console.error(`Error creating mapping for hash ID ${hashId}:`, error);
      return null;
    }
  }

  /**
   * Update an existing mapping
   * @param hashId The hash ID to update
   * @param engaxeId The new Engaxe ID
   * @param videoTitle The new video title (optional)
   * @returns The updated mapping, or null if update failed
   */
  async updateMapping(hashId: string, engaxeId: string, videoTitle?: string): Promise<{ hashId: string, engaxeId: string } | null> {
    try {
      // Find the existing mapping
      const mapping = await VideoIdMappingModel.findOne({ hashId });

      // If not found, create a new mapping
      if (!mapping) {
        console.log(`No mapping found for hash ID ${hashId}, creating new mapping`);
        return this.createMapping(hashId, engaxeId, videoTitle);
      }

      // Update the mapping
      mapping.engaxeId = engaxeId;
      if (videoTitle) {
        mapping.videoTitle = videoTitle;
      }

      // Save the mapping
      await mapping.save();
      console.log(`Updated mapping: ${hashId} -> ${engaxeId}`);

      return { hashId, engaxeId };
    } catch (error) {
      console.error(`Error updating mapping for hash ID ${hashId}:`, error);
      return null;
    }
  }

  /**
   * Delete a mapping
   * @param hashId The hash ID to delete
   * @returns True if deletion was successful, false otherwise
   */
  async deleteMapping(hashId: string): Promise<boolean> {
    try {
      // Delete the mapping
      const result = await VideoIdMappingModel.deleteOne({ hashId });

      // Check if deletion was successful
      if (result.deletedCount > 0) {
        console.log(`Deleted mapping for hash ID ${hashId}`);
        return true;
      }

      // If no mapping was deleted, return false
      console.log(`No mapping found for hash ID ${hashId}`);
      return false;
    } catch (error) {
      console.error(`Error deleting mapping for hash ID ${hashId}:`, error);
      return false;
    }
  }

  /**
   * Get all mappings
   * @returns An array of all mappings
   */
  async getAllMappings(): Promise<Array<{ hashId: string, engaxeId: string, videoTitle: string }>> {
    try {
      // Get all mappings
      const mappings = await VideoIdMappingModel.find();

      // Return the mappings
      return mappings.map(mapping => ({
        hashId: mapping.hashId,
        engaxeId: mapping.engaxeId,
        videoTitle: mapping.videoTitle,
      }));
    } catch (error) {
      console.error('Error getting all mappings:', error);
      return [];
    }
  }

  /**
   * Get or create a mapping for a hash ID
   * @param hashId The hash ID to get or create a mapping for
   * @param videoTitle The title of the video (for reference)
   * @returns The Engaxe ID for the hash ID
   */
  async getOrCreateEngaxeId(hashId: string, videoTitle: string = 'Untitled Video'): Promise<string> {
    // Try to get an existing mapping
    const existingEngaxeId = await this.getEngaxeIdFromHashId(hashId);

    // If found, return it
    if (existingEngaxeId) {
      return existingEngaxeId;
    }

    // If not found, create a new mapping
    const newMapping = await this.createMapping(hashId, undefined, videoTitle);

    // Return the new Engaxe ID
    return newMapping ? newMapping.engaxeId : generateEngaxeId();
  }

  /**
   * Get a mapping by a custom key (used for language-specific mappings)
   * @param key The custom key to look up
   * @returns The corresponding Engaxe ID, or null if not found
   */
  getMappingByKey(key: string): string | null {
    try {
      // For now, we don't have a database model for custom keys
      // This is a placeholder for future implementation
      console.log(`Looking up mapping for key: ${key}`);

      // In a real implementation, we would look up the mapping in the database
      // For now, return null to indicate no mapping found
      return null;
    } catch (error) {
      console.error(`Error getting mapping for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Create a mapping with a custom key
   * @param key The custom key to map
   * @param engaxeId The Engaxe ID to map
   * @param description A description of the mapping
   * @returns True if creation was successful, false otherwise
   */
  async createMappingWithKey(key: string, engaxeId: string, description: string = ''): Promise<boolean> {
    try {
      // For now, we don't have a database model for custom keys
      // This is a placeholder for future implementation
      console.log(`Creating mapping for key ${key}: ${engaxeId} (${description})`);

      // In a real implementation, we would create the mapping in the database
      // For now, return true to indicate success
      return true;
    } catch (error) {
      console.error(`Error creating mapping for key ${key}:`, error);
      return false;
    }
  }
}
