import pyaudio
import wave
import os
import json
import requests
from datetime import datetime
import base64
import audioop
import openai

class VoiceRecorderLanguageDetector:
    def __init__(self):
        # Bhashini API credentials
        self.user_id = "cee60134c6bb4d179efd3fda48ff32fe"
        self.ulca_api_key = "13a647c84b-2747-4f0c-afcd-2ac8235f5318"

        # Load configuration from response.json
        try:
            with open(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'response.json'), 'r') as f:
                self.config = json.load(f)
                print("Successfully loaded Bhashini API configuration from response.json")
        except Exception as e:
            print(f"Error loading response.json: {e}")
            self.config = None

        # Get API endpoints from config or use defaults
        if self.config and 'pipelineInferenceAPIEndPoint' in self.config:
            self.pipeline_api_endpoint = self.config['pipelineInferenceAPIEndPoint']['callbackUrl']
            self.auth_token = self.config['pipelineInferenceAPIEndPoint']['inferenceApiKey']['value']
            print(f"Using pipeline API endpoint from config: {self.pipeline_api_endpoint}")
        else:
            # Fallback to default pipeline endpoint
            self.pipeline_api_endpoint = "https://dhruva-api.bhashini.gov.in/services/inference/pipeline"
            self.auth_token = None
            print("Using default pipeline API endpoint")

        # Also set up the compute API endpoint for direct model access
        self.compute_api_endpoint = "https://meity-auth.ulcacontrib.org/ulca/apis/v0/model/compute"
        print(f"Using compute API endpoint: {self.compute_api_endpoint}")

        # Default to using the compute API first as it might provide more complete information
        self.api_endpoint = self.compute_api_endpoint

        # Audio recording parameters
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        self.chunk = 1024
        self.record_seconds = 5  # Default recording time

        # Voice activity detection parameters
        self.silence_threshold = 500  # Adjust this value based on your microphone sensitivity
        self.silence_count_threshold = 30  # Number of chunks to consider silence
        self.speech_count_threshold = 10  # Number of chunks to consider speech
        self.max_record_seconds = 10  # Maximum recording time
        self.min_record_seconds = 1  # Minimum recording time

        # Create a directory for temporary audio files if it doesn't exist
        self.temp_dir = "temp_audio"
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)

    def detect_voice_activity(self, stream, _):
        """
        Detect voice activity and record audio when speech is detected.

        Args:
            stream: Audio stream
            _: Unused parameter (kept for compatibility)

        Returns:
            list: Recorded audio frames
        """
        print("Listening for speech... (speak now)")

        frames = []
        silent_chunks = 0
        speech_chunks = 0
        recording = False
        total_chunks = 0
        max_chunks = int(self.rate / self.chunk * self.max_record_seconds)
        min_chunks = int(self.rate / self.chunk * self.min_record_seconds)

        while True:
            data = stream.read(self.chunk, exception_on_overflow=False)
            frames.append(data)
            total_chunks += 1

            # Calculate audio energy
            rms = audioop.rms(data, 2)

            # Detect speech
            if rms > self.silence_threshold:
                silent_chunks = 0
                speech_chunks += 1
                if speech_chunks >= self.speech_count_threshold and not recording:
                    print("Speech detected! Recording...")
                    recording = True
            else:
                silent_chunks += 1
                speech_chunks = 0

            # Stop conditions
            if recording and silent_chunks > self.silence_count_threshold and total_chunks > min_chunks:
                print("Silence detected, stopping recording.")
                break

            if total_chunks >= max_chunks:
                print(f"Maximum recording time ({self.max_record_seconds}s) reached.")
                break

        return frames

    def record_audio(self, duration=None):
        """
        Record audio from the microphone and save it as a WAV file.
        Automatically starts recording when speech is detected.

        Args:
            duration (int, optional): Maximum recording duration in seconds. Defaults to self.max_record_seconds.

        Returns:
            str: Path to the recorded audio file
        """
        if duration is not None:
            self.max_record_seconds = duration

        audio = pyaudio.PyAudio()

        # Open audio stream
        stream = audio.open(
            format=self.format,
            channels=self.channels,
            rate=self.rate,
            input=True,
            frames_per_buffer=self.chunk
        )

        print("Waiting for speech...")

        # Detect voice activity and record
        frames = self.detect_voice_activity(stream, audio)

        print("Recording finished.")

        # Stop and close the stream
        stream.stop_stream()
        stream.close()
        audio.terminate()

        # Generate a unique filename using timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(self.temp_dir, f"recording_{timestamp}.wav")

        # Save the recorded audio as a WAV file
        with wave.open(filename, 'wb') as wf:
            wf.setnchannels(self.channels)
            wf.setsampwidth(audio.get_sample_size(self.format))
            wf.setframerate(self.rate)
            wf.writeframes(b''.join(frames))

        print(f"Audio saved as {filename}")
        return filename

    def get_language_name(self, language_code):
        """
        Get the full language name from a language code.

        Args:
            language_code (str): The language code (e.g., 'hi', 'en')

        Returns:
            str: The full language name
        """
        language_names = {
            'hi': 'Hindi',
            'en': 'English',
            'bn': 'Bengali',
            'ta': 'Tamil',
            'te': 'Telugu',
            'kn': 'Kannada',
            'ml': 'Malayalam',
            'mr': 'Marathi',
            'gu': 'Gujarati',
            'pa': 'Punjabi',
            'or': 'Oriya',
            'as': 'Assamese',
            'ur': 'Urdu',
            'auto': 'Auto-detected'
        }

        return language_names.get(language_code, f"Unknown ({language_code})")

    def infer_language_from_text(self, text):
        """
        Infer the language from the text based on character sets.

        Args:
            text (str): The text to analyze

        Returns:
            str: The inferred language code
        """
        # Define character ranges for different languages
        devanagari_range = range(0x0900, 0x097F)  # Hindi, Marathi, Sanskrit, etc.
        bengali_range = range(0x0980, 0x09FF)
        gurmukhi_range = range(0x0A00, 0x0A7F)  # Punjabi
        gujarati_range = range(0x0A80, 0x0AFF)
        oriya_range = range(0x0B00, 0x0B7F)
        tamil_range = range(0x0B80, 0x0BFF)
        telugu_range = range(0x0C00, 0x0C7F)
        kannada_range = range(0x0C80, 0x0CFF)
        malayalam_range = range(0x0D00, 0x0D7F)

        # Count characters in each range
        counts = {
            'hi': 0,  # Hindi (Devanagari)
            'bn': 0,  # Bengali
            'pa': 0,  # Punjabi (Gurmukhi)
            'gu': 0,  # Gujarati
            'or': 0,  # Oriya
            'ta': 0,  # Tamil
            'te': 0,  # Telugu
            'kn': 0,  # Kannada
            'ml': 0,  # Malayalam
            'en': 0   # English (default)
        }

        for char in text:
            code_point = ord(char)
            if code_point in devanagari_range:
                counts['hi'] += 1
            elif code_point in bengali_range:
                counts['bn'] += 1
            elif code_point in gurmukhi_range:
                counts['pa'] += 1
            elif code_point in gujarati_range:
                counts['gu'] += 1
            elif code_point in oriya_range:
                counts['or'] += 1
            elif code_point in tamil_range:
                counts['ta'] += 1
            elif code_point in telugu_range:
                counts['te'] += 1
            elif code_point in kannada_range:
                counts['kn'] += 1
            elif code_point in malayalam_range:
                counts['ml'] += 1
            elif 'a' <= char.lower() <= 'z':
                counts['en'] += 1

        # Find the language with the most characters
        max_lang = max(counts.items(), key=lambda x: x[1])

        # If no clear language is detected, default to English
        if max_lang[1] == 0:
            return 'en'

        return max_lang[0]

    def detect_audio_language(self, audio_file_path):
        """
        Detect the language of the recorded audio using Bhashini ALD (Audio Language Detection) API.

        Args:
            audio_file_path (str): Path to the audio file

        Returns:
            str: Detected language code or None if detection fails
        """
        print("\n=== Detecting Language from Audio using ALD ===")
        print("Sending audio to ALD service...")

        # Read the audio file as binary data
        with open(audio_file_path, 'rb') as audio_file:
            audio_content = audio_file.read()

        # Encode audio content to base64
        audio_base64 = base64.b64encode(audio_content).decode('utf-8')

        # Try to load response_ald.json for ALD configuration
        try:
            with open("response_ald.json", "r", encoding="utf-8") as f:
                ald_template = json.load(f)
                print("Loaded response_ald.json for ALD configuration")
        except Exception as e:
            print(f"Warning: Could not load response_ald.json: {e}")
            # Create a default template with enhanced configuration for all Indian languages
            ald_template = {
                "pipelineTasks": [
                    {
                        "taskType": "audio-lang-detection",
                        "config": {
                            "language": {
                                "sourceLanguage": "auto",
                                "targetLanguage": "auto"
                            },
                            "serviceId": "bhashini/iitmandi/audio-lang-detection/gpu",
                            "modelId": "66cf53e58cfc565ee0d1a8e2",
                            "audioFormat": "wav",
                            "samplingRate": 16000,
                            "supportedLanguages": [
                                "hi", "bn", "ta", "te", "kn", "ml", "mr", "gu", "pa", "or",
                                "as", "ur", "sa", "sd", "ne", "ks", "mai", "doi", "kok",
                                "bho", "raj", "mni", "sat", "brx", "lus", "garo", "khasi"
                            ],
                            "multilingualDetection": True,
                            "numLanguagesToDetect": 5,
                            "minConfidenceScore": 0.1
                        }
                    }
                ],
                "inputData": {
                    "audio": [
                        {
                            "audioContent": ""
                        }
                    ]
                }
            }

        # Update the template with the audio content
        if "inputData" in ald_template and "audio" in ald_template["inputData"] and len(ald_template["inputData"]["audio"]) > 0:
            ald_template["inputData"]["audio"][0]["audioContent"] = audio_base64
        else:
            print("Warning: ALD template does not have the expected structure. Creating a new one.")
            ald_template = {
                "pipelineTasks": [
                    {
                        "taskType": "audio-lang-detection",
                        "config": {
                            "language": {
                                "sourceLanguage": "auto",
                                "targetLanguage": "auto"
                            },
                            "serviceId": "bhashini/iitmandi/audio-lang-detection/gpu",
                            "modelId": "66cf53e58cfc565ee0d1a8e2",
                            "audioFormat": "wav",
                            "samplingRate": 16000,
                            "supportedLanguages": [
                                "hi", "bn", "ta", "te", "kn", "ml", "mr", "gu", "pa", "or",
                                "as", "ur", "sa", "sd", "ne", "ks", "mai", "doi", "kok",
                                "bho", "raj", "mni", "sat", "brx", "lus", "garo", "khasi"
                            ],
                            "multilingualDetection": True,
                            "numLanguagesToDetect": 5,
                            "minConfidenceScore": 0.1
                        }
                    }
                ],
                "inputData": {
                    "audio": [
                        {
                            "audioContent": audio_base64
                        }
                    ]
                }
            }

        # Set up headers with authentication
        headers = {
            "userID": self.user_id,
            "ulcaApiKey": self.ulca_api_key,
            "Content-Type": "application/json"
        }

        # Add authorization token if available
        if hasattr(self, 'auth_token') and self.auth_token:
            headers["Authorization"] = self.auth_token

        # Use the pipeline API endpoint for ALD
        endpoint = self.pipeline_api_endpoint if hasattr(self, 'pipeline_api_endpoint') else "https://dhruva-api.bhashini.gov.in/services/inference/pipeline"

        print(f"Sending ALD request to: {endpoint}")
        print(f"Payload structure: {json.dumps(ald_template, indent=2)[:200]}...")  # Print first 200 chars of payload

        try:
            # Make the API request
            response = requests.post(endpoint, headers=headers, json=ald_template)

            print(f"Response status code: {response.status_code}")
            print(f"Response headers: {response.headers}")

            # Save the response for debugging and display
            with open("last_ald_response.json", "w", encoding="utf-8") as f:
                try:
                    f.write(response.text)
                    print("Raw response saved to last_ald_response.json")
                except Exception as e:
                    print(f"Error saving raw response: {e}")

            # Also save a copy to ald_response.json for reference
            with open("ald_response.json", "w", encoding="utf-8") as f:
                try:
                    f.write(response.text)
                    print("ALD response saved to ald_response.json")
                except Exception as e:
                    print(f"Error saving ALD response: {e}")

            # Try to parse the response as JSON
            try:
                response_data = response.json()
                print(f"Response data: {json.dumps(response_data, indent=2)[:500]}...")

                # Extract the detected language
                if response.status_code == 200:
                    try:
                        detected_language = None

                        # Parse response based on the API format
                        if "pipelineResponse" in response_data:
                            # Pipeline API format
                            for task_response in response_data["pipelineResponse"]:
                                # Check for "audio-lang-detection" task type
                                if task_response.get("taskType") == "audio-lang-detection" and "output" in task_response:
                                    # Initialize output and detected_language
                                    output = task_response["output"][0] if task_response["output"] else {}
                                    detected_language = output.get("sourceLanguage", "")

                                    # Check for langPrediction in the output
                                    lang_predictions = []

                                    # First check if langPrediction is directly in the output
                                    if "langPrediction" in output:
                                        lang_predictions = output["langPrediction"]
                                        print(f"ALD language predictions found in output: {json.dumps(lang_predictions, indent=2)}")

                                    # Also check if there's an audio object with langPrediction
                                    elif "audio" in output and output["audio"] is not None:
                                        if "langPrediction" in output:
                                            lang_predictions = output["langPrediction"]
                                            print(f"ALD language predictions found in audio: {json.dumps(lang_predictions, indent=2)}")

                                    # If we still don't have predictions, check the task_response directly
                                    if not lang_predictions and "langPrediction" in task_response:
                                        lang_predictions = task_response["langPrediction"]
                                        print(f"ALD language predictions found in task_response: {json.dumps(lang_predictions, indent=2)}")

                                    # Save detailed language predictions if we have them
                                    if lang_predictions:
                                        try:
                                            with open("ald_language_details.json", "w", encoding="utf-8") as f:
                                                json.dump(lang_predictions, f, indent=2)
                                            print("Saved detailed language predictions to ald_language_details.json")

                                            # Print all language predictions for debugging
                                            print("\nALL LANGUAGE PREDICTIONS FROM ALD:")
                                            for i, pred in enumerate(lang_predictions):
                                                pred_lang_code = pred.get("langCode", "unknown")
                                                pred_score = pred.get("langScore", "N/A")
                                                pred_lang_name = self.get_language_name(pred_lang_code)
                                                print(f"{i+1}. {pred_lang_name} ({pred_lang_code}) - Score: {pred_score}")
                                        except Exception as e:
                                            print(f"Error saving language predictions: {e}")

                                        # If we have language predictions but no detected language, use the first prediction
                                        if not detected_language and len(lang_predictions) > 0:
                                            if "langCode" in lang_predictions[0] and lang_predictions[0]["langCode"]:
                                                detected_language = lang_predictions[0]["langCode"]
                                                print(f"Using language from predictions: {detected_language}")

                                    # If we have a detected language, return it
                                    if detected_language:
                                        language_name = self.get_language_name(detected_language)
                                        print(f"\n=== ALD DETECTED LANGUAGE: {language_name} ({detected_language}) ===\n")

                                        # Save detailed information about all detected languages
                                        try:
                                            # Include more detailed information in the output
                                            language_info = f"Detected Language: {language_name}\nLanguage Code: {detected_language}"

                                            # Add all language predictions if available
                                            if lang_predictions and len(lang_predictions) > 0:
                                                language_info += "\n\nAll Detected Languages:"
                                                for i, pred in enumerate(lang_predictions):
                                                    pred_lang_code = pred.get("langCode", "unknown")
                                                    pred_lang_name = self.get_language_name(pred_lang_code)
                                                    pred_score = pred.get("langScore", "N/A")
                                                    language_info += f"\n{i+1}. {pred_lang_name} ({pred_lang_code})"
                                                    if pred_score:
                                                        language_info += f" - Confidence: {pred_score}"

                                            # Save to file for display
                                            with open("detected_language.txt", "w", encoding="utf-8") as f:
                                                f.write(language_info)

                                            # Print all detected languages
                                            print("All detected languages:")
                                            for i, pred in enumerate(lang_predictions):
                                                pred_lang_code = pred.get("langCode", "unknown")
                                                pred_lang_name = self.get_language_name(pred_lang_code)
                                                pred_score = pred.get("langScore", "N/A")
                                                print(f"{i+1}. {pred_lang_name} ({pred_lang_code}) - Score: {pred_score}")

                                        except Exception as e:
                                            print(f"Error saving detected language: {e}")
                                        return detected_language

                                    # If we still don't have a detected language but have predictions, use the first one
                                    if not detected_language and lang_predictions and len(lang_predictions) > 0:
                                        if "langCode" in lang_predictions[0] and lang_predictions[0]["langCode"]:
                                            detected_language = lang_predictions[0]["langCode"]
                                            language_name = self.get_language_name(detected_language)
                                            print(f"\n=== ALD DETECTED LANGUAGE FROM PREDICTIONS: {language_name} ({detected_language}) ===\n")

                                            # Save detailed information about all detected languages
                                            try:
                                                # Include more detailed information in the output
                                                language_info = f"Detected Language: {language_name}\nLanguage Code: {detected_language}"

                                                # Add all language predictions if available
                                                if lang_predictions and len(lang_predictions) > 0:
                                                    language_info += "\n\nAll Detected Languages:"
                                                    for i, pred in enumerate(lang_predictions):
                                                        pred_lang_code = pred.get("langCode", "unknown")
                                                        pred_lang_name = self.get_language_name(pred_lang_code)
                                                        pred_score = pred.get("langScore", "N/A")
                                                        language_info += f"\n{i+1}. {pred_lang_name} ({pred_lang_code})"
                                                        if pred_score:
                                                            language_info += f" - Confidence: {pred_score}"

                                                # Save to file for display
                                                with open("detected_language.txt", "w", encoding="utf-8") as f:
                                                    f.write(language_info)

                                                # Print all detected languages
                                                print("All detected languages:")
                                                for i, pred in enumerate(lang_predictions):
                                                    pred_lang_code = pred.get("langCode", "unknown")
                                                    pred_lang_name = self.get_language_name(pred_lang_code)
                                                    pred_score = pred.get("langScore", "N/A")
                                                    print(f"{i+1}. {pred_lang_name} ({pred_lang_code}) - Score: {pred_score}")

                                            except Exception as e:
                                                print(f"Error saving detected language: {e}")
                                            return detected_language

                        # If we get here, we need to check the response structure more carefully
                        # Check if the response has a taskType at the top level
                        if "taskType" in response_data and response_data["taskType"] == "audio-lang-detection":
                            if "output" in response_data and response_data["output"]:
                                output = response_data["output"][0] if isinstance(response_data["output"], list) else response_data["output"]

                                # Check for langPrediction in the output
                                if "langPrediction" in output and output["langPrediction"]:
                                    lang_predictions = output["langPrediction"]
                                    print(f"ALD language predictions found in top-level output: {json.dumps(lang_predictions, indent=2)}")

                                    # Save detailed language predictions
                                    try:
                                        with open("ald_language_details.json", "w", encoding="utf-8") as f:
                                            json.dump(lang_predictions, f, indent=2)
                                        print("Saved detailed language predictions to ald_language_details.json")
                                    except Exception as e:
                                        print(f"Error saving language predictions: {e}")

                                    # Use the first prediction as the detected language
                                    if lang_predictions and len(lang_predictions) > 0:
                                        if "langCode" in lang_predictions[0] and lang_predictions[0]["langCode"]:
                                            detected_language = lang_predictions[0]["langCode"]
                                            language_name = self.get_language_name(detected_language)
                                            print(f"\n=== ALD DETECTED LANGUAGE FROM TOP-LEVEL: {language_name} ({detected_language}) ===\n")

                                            # Save detailed information about all detected languages
                                            try:
                                                # Include more detailed information in the output
                                                language_info = f"Detected Language: {language_name}\nLanguage Code: {detected_language}"

                                                # Add all language predictions if available
                                                if lang_predictions and len(lang_predictions) > 0:
                                                    language_info += "\n\nAll Detected Languages:"
                                                    for i, pred in enumerate(lang_predictions):
                                                        pred_lang_code = pred.get("langCode", "unknown")
                                                        pred_lang_name = self.get_language_name(pred_lang_code)
                                                        pred_score = pred.get("langScore", "N/A")
                                                        language_info += f"\n{i+1}. {pred_lang_name} ({pred_lang_code})"
                                                        if pred_score:
                                                            language_info += f" - Confidence: {pred_score}"

                                                # Save to file for display
                                                with open("detected_language.txt", "w", encoding="utf-8") as f:
                                                    f.write(language_info)

                                                # Print all detected languages
                                                print("All detected languages:")
                                                for i, pred in enumerate(lang_predictions):
                                                    pred_lang_code = pred.get("langCode", "unknown")
                                                    pred_lang_name = self.get_language_name(pred_lang_code)
                                                    pred_score = pred.get("langScore", "N/A")
                                                    print(f"{i+1}. {pred_lang_name} ({pred_lang_code}) - Score: {pred_score}")

                                            except Exception as e:
                                                print(f"Error saving detected language: {e}")
                                            return detected_language

                        # If we get here, we need to check if there are any langPrediction in the entire response
                        # This is a last resort to find language predictions anywhere in the response
                        try:
                            # Convert the response to a string and check if it contains langPrediction
                            response_str = json.dumps(response_data)
                            if "langPrediction" in response_str:
                                print("Found langPrediction in response, attempting to extract...")

                                # Try to find and extract langPrediction from the response
                                # This is a more aggressive approach to find the language code
                                import re
                                lang_code_match = re.search(r'"langCode"\s*:\s*"([^"]+)"', response_str)
                                if lang_code_match:
                                    detected_language = lang_code_match.group(1)
                                    language_name = self.get_language_name(detected_language)
                                    print(f"\n=== ALD DETECTED LANGUAGE FROM REGEX: {language_name} ({detected_language}) ===\n")

                                    # Save detailed information about the detected language
                                    try:
                                        # Include more detailed information in the output
                                        language_info = f"Detected Language: {language_name}\nLanguage Code: {detected_language}"

                                        # Try to extract more language predictions using regex
                                        import re
                                        all_lang_codes = re.findall(r'"langCode"\s*:\s*"([^"]+)"', response_str)
                                        all_lang_scores = re.findall(r'"langScore"\s*:\s*([0-9.]+)', response_str)

                                        if all_lang_codes and len(all_lang_codes) > 1:
                                            language_info += "\n\nAll Detected Languages:"
                                            for i, code in enumerate(all_lang_codes):
                                                pred_lang_name = self.get_language_name(code)
                                                language_info += f"\n{i+1}. {pred_lang_name} ({code})"
                                                if all_lang_scores and i < len(all_lang_scores):
                                                    language_info += f" - Confidence: {all_lang_scores[i]}"

                                            # Print all detected languages
                                            print("All detected languages from regex:")
                                            for i, code in enumerate(all_lang_codes):
                                                pred_lang_name = self.get_language_name(code)
                                                score = all_lang_scores[i] if all_lang_scores and i < len(all_lang_scores) else "N/A"
                                                print(f"{i+1}. {pred_lang_name} ({code}) - Score: {score}")

                                        # Save to file for display
                                        with open("detected_language.txt", "w", encoding="utf-8") as f:
                                            f.write(language_info)
                                    except Exception as e:
                                        print(f"Error saving detected language: {e}")
                                    return detected_language
                        except Exception as e:
                            print(f"Error trying to extract language with regex: {e}")

                        # If no language is detected, return None
                        print("No language detected by ALD service")
                        return None

                    except (KeyError, IndexError) as e:
                        print(f"Error parsing ALD API response: {e}")
                        print(f"API Response structure: {json.dumps(response_data, indent=2)}")
                        return None
                else:
                    print(f"ALD API request failed with status code {response.status_code}")
                    print(f"Error message: {response_data}")
                    return None

            except json.JSONDecodeError:
                print(f"Failed to parse ALD response as JSON. Raw response: {response.text[:500]}...")
                return None

        except Exception as e:
            print(f"Error calling Bhashini ALD API: {e}")
            import traceback
            traceback.print_exc()
            return None

    def is_likely_english(self, audio_file_path):
        """
        Simple check to determine if the audio is likely English.
        This is a basic implementation that can be improved with more sophisticated methods.

        Args:
            audio_file_path (str): Path to the audio file

        Returns:
            bool: True if the audio is likely English, False otherwise
        """
        print("Checking if audio is likely English...")

        # For now, we'll use a simple approach - transcribe with English ASR
        # and check if the result looks like English text

        # Read the audio file as binary data
        with open(audio_file_path, 'rb') as audio_file:
            audio_content = audio_file.read()

        # Encode audio content to base64
        audio_base64 = base64.b64encode(audio_content).decode('utf-8')

        # Use service ID and model ID for English ASR
        service_id = "ai4bharat/conformer-multilingual-indo-aryan-dravidian-gpu--t4"
        model_id = "648025f27cdd753e77f461a9"

        # Prepare the request payload for English ASR
        pipeline_payload = {
            "pipelineTasks": [
                {
                    "taskType": "asr",
                    "config": {
                        "language": {
                            "sourceLanguage": "en"  # Force English language
                        },
                        "serviceId": service_id,
                        "modelId": model_id,
                        "audioFormat": "flac",
                        "samplingRate": 16000,
                        "preProcessors": ["vad"],
                        "postProcessors": ["itn", "punctuation"]
                    }
                }
            ],
            "inputData": {
                "audio": [
                    {
                        "audioContent": audio_base64
                    }
                ]
            }
        }

        # Set up headers with authentication
        headers = {
            "userID": self.user_id,
            "ulcaApiKey": self.ulca_api_key,
            "Content-Type": "application/json"
        }

        # Add authorization token if available
        if hasattr(self, 'auth_token') and self.auth_token:
            headers["Authorization"] = self.auth_token

        # Use the pipeline API endpoint
        endpoint = self.pipeline_api_endpoint

        try:
            # Make the API request
            response = requests.post(endpoint, headers=headers, json=pipeline_payload)

            if response.status_code == 200:
                response_data = response.json()

                # Extract the transcribed text
                transcribed_text = ""

                if "pipelineResponse" in response_data:
                    for task_response in response_data["pipelineResponse"]:
                        if task_response.get("taskType") == "asr" and "output" in task_response:
                            output = task_response["output"][0] if task_response["output"] else {}
                            transcribed_text = output.get("source", "")
                            break

                if transcribed_text:
                    print(f"English ASR result: {transcribed_text}")

                    # Count English characters vs. non-English characters
                    english_chars = sum(1 for c in transcribed_text if 'a' <= c.lower() <= 'z' or c in " .,?!-'\"")
                    total_chars = len(transcribed_text.strip())

                    if total_chars > 0:
                        english_ratio = english_chars / total_chars
                        print(f"English character ratio: {english_ratio:.2f}")

                        # If more than 80% of characters are English, consider it English
                        if english_ratio > 0.8:
                            print("Audio is likely English based on character analysis")

                            # Save the transcribed text for later use
                            self.english_transcription = transcribed_text
                            return True

            print("Audio is not detected as English")
            return False

        except Exception as e:
            print(f"Error in English detection: {e}")
            return False

    def detect_language(self, audio_file_path):
        """
        Detect the language of the recorded audio.
        For English, bypass ALD and use direct ASR.
        For other languages, use ALD for detection.

        Args:
            audio_file_path (str): Path to the audio file

        Returns:
            tuple: (detected_language_code, transcribed_text)
        """
        print("\n=== Detecting Language and Transcribing Audio ===")

        # First check if the audio is likely English
        if self.is_likely_english(audio_file_path):
            print("English speech detected! Bypassing ALD and using English ASR directly.")
            detected_language = "en"
            source_language = "en"

            # Get the language name for better user feedback
            language_name = self.get_language_name(detected_language)
            print(f"Detected Language for ASR: {language_name} ({detected_language})")

            # Create a detected_language.txt file with English information
            try:
                language_info = f"Detected Language: {language_name}\nLanguage Code: {detected_language}\n\nUsing English for ASR (speech-to-text)"
                with open("detected_language.txt", "w", encoding="utf-8") as f:
                    f.write(language_info)
            except Exception as e:
                print(f"Error creating detected language file: {e}")

            # We'll continue with the normal ASR process below
        else:
            # For non-English audio, use ALD for language detection
            print("Audio not detected as English. Using ALD for language detection...")
            detected_language = self.detect_audio_language(audio_file_path)

            # If ALD detected a language, use it for ASR
            if detected_language:
                print(f"Using ALD detected language '{detected_language}' for ASR")
                source_language = detected_language

                # Get the language name for better user feedback
                language_name = self.get_language_name(detected_language)
                print(f"Detected Language for ASR: {language_name} ({detected_language})")

                # Update the detected_language.txt file with ASR information
                try:
                    with open("detected_language.txt", "r", encoding="utf-8") as f:
                        current_content = f.read()

                    # Add ASR information
                    updated_content = current_content + f"\n\nUsing this language for ASR (speech-to-text)"

                    with open("detected_language.txt", "w", encoding="utf-8") as f:
                        f.write(updated_content)
                except Exception as e:
                    print(f"Error updating detected language file with ASR info: {e}")
            else:
                print("ALD failed to detect language. Cannot proceed without language detection.")
                return None, None

        # If we already have the English transcription, return it
        if detected_language == "en" and hasattr(self, 'english_transcription') and self.english_transcription:
            print(f"Using already transcribed English text: {self.english_transcription}")
            return detected_language, self.english_transcription

        print("Transcribing audio with ASR...")

        # Read the audio file as binary data
        with open(audio_file_path, 'rb') as audio_file:
            audio_content = audio_file.read()

        # Encode audio content to base64
        audio_base64 = base64.b64encode(audio_content).decode('utf-8')

        # Get ASR service configuration from response.json
        asr_config = None
        if self.config and 'pipelineResponseConfig' in self.config:
            for task_config in self.config['pipelineResponseConfig']:
                if task_config.get('taskType') == 'asr':
                    asr_config = task_config.get('config', [])[0] if task_config.get('config') else None
                    break

        # Use service ID and model ID from config if available
        service_id = asr_config.get('serviceId') if asr_config else "ai4bharat/conformer-multilingual-indo-aryan-dravidian-gpu--t4"
        model_id = asr_config.get('modelId') if asr_config else "648025f27cdd753e77f461a9"

        # Use the language detected by ALD or English if detected
        source_language = detected_language  # This will be "en" for English or the ALD detected language

        print(f"Using ASR service: {service_id}")
        print(f"Using model ID: {model_id}")
        print(f"Source language: {source_language}")

        # Prepare the request payload for Bhashini API pipeline
        # Based on the error messages, we need to ensure modelId is included at the top level
        payload = {
            "modelId": model_id,  # Include modelId at the top level
            "task": "asr",        # Specify the task
            "source": audio_base64,  # Provide the audio content directly
            "userId": self.user_id,
            "config": {
                "language": {
                    "sourceLanguage": "xx"  # Use "xx" for better language detection
                },
                "serviceId": service_id,
                "audioFormat": "flac",
                "samplingRate": 16000,
                "preProcessors": ["vad"],
                "postProcessors": ["itn", "punctuation"]
            }
        }

        # Alternative payload format for pipeline API with enhanced configuration
        pipeline_payload = {
            "pipelineTasks": [
                {
                    "taskType": "asr",
                    "config": {
                        "language": {
                            "sourceLanguage": "xx"  # Use "xx" for better language detection
                        },
                        "serviceId": service_id,
                        "modelId": model_id,
                        "audioFormat": "flac",
                        "samplingRate": 16000,
                        "preProcessors": ["vad"],
                        "postProcessors": ["itn", "punctuation"]
                    }
                }
            ],
            "inputData": {
                "audio": [
                    {
                        "audioContent": audio_base64
                    }
                ]
            }
        }

        # Determine which API endpoint to use
        if "dhruva-api.bhashini.gov.in" in self.api_endpoint:
            # Use pipeline format for dhruva API
            payload = pipeline_payload
            print("Using pipeline API format")
        else:
            # Use direct format for compute API
            print("Using direct API format")

        # Set up headers with authentication
        headers = {
            "userID": self.user_id,
            "ulcaApiKey": self.ulca_api_key,
            "Content-Type": "application/json"
        }

        # Add authorization token if available
        if self.auth_token:
            headers["Authorization"] = self.auth_token

        # Try both API endpoints if needed
        endpoints_to_try = [self.api_endpoint]

        # Add the other API endpoint as fallback
        if self.api_endpoint == self.pipeline_api_endpoint:
            endpoints_to_try.append(self.compute_api_endpoint)
        else:
            endpoints_to_try.append(self.pipeline_api_endpoint)

        for endpoint in endpoints_to_try:
            try:
                current_payload = payload

                # Adjust payload based on endpoint
                if "dhruva-api.bhashini.gov.in" in endpoint:
                    # Use pipeline format for dhruva API
                    current_payload = pipeline_payload
                    print(f"Using pipeline API format for {endpoint}")
                else:
                    # Use direct format for compute API
                    print(f"Using direct API format for {endpoint}")
                    # For compute API, we need to wrap the audio in input array
                    if "source" in current_payload:
                        current_payload = {
                            "modelId": model_id,
                            "task": "asr",
                            "input": [{"source": audio_base64}],
                            "userId": self.user_id,
                            "config": {
                                "language": {
                                    "sourceLanguage": "xx"  # Use "xx" for better language detection
                                },
                                "serviceId": service_id,
                                "audioFormat": "flac",
                                "samplingRate": 16000,
                                "preProcessors": ["vad"],
                                "postProcessors": ["itn", "punctuation"]
                            }
                        }

                print(f"Sending request to: {endpoint}")
                print(f"Headers: {headers}")
                print(f"Payload structure: {json.dumps(current_payload, indent=2)[:200]}...")  # Print first 200 chars of payload

                # Make the API request
                response = requests.post(endpoint, headers=headers, json=current_payload)

                print(f"Response status code: {response.status_code}")
                print(f"Response headers: {response.headers}")

                # Try to parse the response as JSON
                try:
                    response_data = response.json()
                    print(f"Response data: {json.dumps(response_data, indent=2)}")

                    # Extract the transcribed text and detected language
                    if response.status_code == 200:
                        try:
                            transcribed_text = ""
                            detected_language = ""

                            # Parse response based on the API format
                            if "pipelineResponse" in response_data:
                                # Pipeline API format
                                for task_response in response_data["pipelineResponse"]:
                                    if task_response.get("taskType") == "asr" and "output" in task_response:
                                        output = task_response["output"][0] if task_response["output"] else {}
                                        transcribed_text = output.get("source", "")
                                        # Use only the language detected by ALD, which is already in source_language
                                        detected_language = source_language
                                        break
                            elif "output" in response_data:
                                # Compute API format
                                output = response_data["output"][0] if response_data["output"] else {}
                                transcribed_text = output.get("source", "")
                                # Use only the language detected by ALD, which is already in source_language
                                detected_language = source_language

                            # If we got a result, return it
                            if transcribed_text:
                                print(f"Detected language: {detected_language}")
                                print(f"Transcribed text: {transcribed_text}")

                                # Get the language name for better user feedback
                                language_name = self.get_language_name(detected_language)
                                print(f"Detected Language: {language_name} ({detected_language})")

                                # We're only using ALD for language detection, so no fallback needed here

                                return detected_language, transcribed_text
                            else:
                                print("No transcription found in response")
                                # Continue to next endpoint if available

                        except (KeyError, IndexError) as e:
                            print(f"Error parsing API response: {e}")
                            print(f"API Response structure: {json.dumps(response_data, indent=2)}")
                            # Continue to next endpoint if available
                    else:
                        print(f"API request failed with status code {response.status_code}")
                        print(f"Error message: {response_data}")
                        # Continue to next endpoint if available

                except json.JSONDecodeError:
                    print(f"Failed to parse response as JSON. Raw response: {response.text[:500]}...")
                    # Continue to next endpoint if available

            except Exception as e:
                print(f"Error calling Bhashini API at {endpoint}: {e}")
                import traceback
                traceback.print_exc()
                # Continue to next endpoint if available

        # If we've tried all endpoints and still have no result
        print("All API endpoints failed to return a valid transcription")
        return None, None

    def translate_to_english(self, text, source_language):
        """
        Translate text to English using Bhashini NMT API.

        Args:
            text (str): Text to translate
            source_language (str): Source language code

        Returns:
            str: Translated text in English
        """
        print(f"Translating text from {source_language} to English...")

        # If already in English, return the text as is
        if source_language == "en":
            print("Text is already in English. No translation needed.")
            return text

        # For very short text (like single characters), translation might not work well
        if len(text) <= 2:
            print(f"Text is too short ({len(text)} characters). Translation might not be accurate.")

        # Try to use the same API endpoints as ASR for consistency
        endpoints_to_try = []

        # Add the pipeline API endpoint if available
        if hasattr(self, 'pipeline_api_endpoint'):
            endpoints_to_try.append(self.pipeline_api_endpoint)

        # Add the compute API endpoint if available
        if hasattr(self, 'compute_api_endpoint'):
            endpoints_to_try.append(self.compute_api_endpoint)

        # If no endpoints are available, use the default
        if not endpoints_to_try:
            endpoints_to_try = ["https://dhruva-api.bhashini.gov.in/services/inference/pipeline"]

        # Prepare the request payload for Bhashini NMT API
        pipeline_payload = {
            "pipelineTasks": [
                {
                    "taskType": "translation",
                    "config": {
                        "language": {
                            "sourceLanguage": source_language,
                            "targetLanguage": "en"
                        },
                        "serviceId": "ai4bharat/indictrans-v2-all-gpu--t4"
                    }
                }
            ],
            "inputData": {
                "input": [
                    {
                        "source": text
                    }
                ]
            }
        }

        # Direct compute API payload
        compute_payload = {
            "modelId": "6475ade3f6c68e2c3f0d8f3a",  # Model ID for translation
            "task": "translation",
            "input": [{"source": text}],
            "userId": self.user_id,
            "config": {
                "language": {
                    "sourceLanguage": source_language,
                    "targetLanguage": "en"
                },
                "serviceId": "ai4bharat/indictrans-v2-all-gpu--t4"
            }
        }

        # Set up headers with authentication
        headers = {
            "userID": self.user_id,
            "ulcaApiKey": self.ulca_api_key,
            "Content-Type": "application/json"
        }

        # Add authorization token if available
        if hasattr(self, 'auth_token') and self.auth_token:
            headers["Authorization"] = self.auth_token

        # Try each endpoint
        for endpoint in endpoints_to_try:
            try:
                # Select the appropriate payload based on the endpoint
                if "dhruva-api.bhashini.gov.in" in endpoint:
                    current_payload = pipeline_payload
                    print(f"Using pipeline API format for translation: {endpoint}")
                else:
                    current_payload = compute_payload
                    print(f"Using compute API format for translation: {endpoint}")

                print(f"Sending NMT request to: {endpoint}")
                print(f"Payload: {json.dumps(current_payload, ensure_ascii=False)[:200]}...")

                # Make the API request
                response = requests.post(endpoint, headers=headers, json=current_payload)

                print(f"Response status code: {response.status_code}")

                # Try to parse the response as JSON
                try:
                    response_data = response.json()
                    print(f"Response data: {json.dumps(response_data, ensure_ascii=False)[:200]}...")

                    # Save the response for debugging
                    with open("last_nmt_response.json", "w", encoding="utf-8") as f:
                        json.dump(response_data, f, ensure_ascii=False, indent=2)

                    # Extract the translated text based on the API format
                    translated_text = ""

                    if "pipelineResponse" in response_data:
                        # Pipeline API format
                        for task_response in response_data["pipelineResponse"]:
                            if task_response.get("taskType") == "translation" and "output" in task_response:
                                output = task_response["output"][0] if task_response["output"] else {}
                                translated_text = output.get("target", "")
                                break
                    elif "output" in response_data:
                        # Compute API format
                        output = response_data["output"][0] if response_data["output"] else {}
                        translated_text = output.get("target", "")

                    # If we got a result, return it
                    if translated_text:
                        print(f"Translated text: {translated_text}")
                        return translated_text
                    else:
                        print("No translation found in response")
                        # Continue to next endpoint if available

                except json.JSONDecodeError:
                    print(f"Failed to parse response as JSON. Raw response: {response.text[:500]}...")
                    # Continue to next endpoint if available

            except Exception as e:
                print(f"Error calling Bhashini NMT API at {endpoint}: {e}")
                import traceback
                traceback.print_exc()
                # Continue to next endpoint if available

        # If all endpoints failed, use a simple dictionary-based translation for common words
        print("All translation endpoints failed. Using simple dictionary-based translation.")

        # Simple dictionary for common Hindi words to English
        hindi_to_english = {
            "नमस्ते": "Hello",
            "धन्यवाद": "Thank you",
            "हां": "Yes",
            "नहीं": "No",
            "मैं": "I",
            "तुम": "You",
            "वह": "He/She",
            "हम": "We",
            "आप": "You (formal)",
            "वे": "They",
            "है": "Is",
            "हूँ": "Am",
            "हो": "Are",
            "क्या": "What",
            "कौन": "Who",
            "कब": "When",
            "कहाँ": "Where",
            "क्यों": "Why",
            "कैसे": "How",
            "अच्छा": "Good",
            "बुरा": "Bad",
            "ठीक": "Okay",
            "प्यार": "Love",
            "खुश": "Happy",
            "दुखी": "Sad",
            "बड़ा": "Big",
            "छोटा": "Small",
            "ह": "H"  # Single character fallback
        }

        # Check if the text is in our dictionary
        if text in hindi_to_english:
            translated_text = hindi_to_english[text]
            print(f"Dictionary translation: {translated_text}")
            return translated_text

        # If not in dictionary, return the original text
        return text

    def translate_from_english(self, text, target_language):
        """
        Translate text from English back to the original language using Bhashini NMT API.

        Args:
            text (str): English text to translate
            target_language (str): Target language code

        Returns:
            str: Translated text in the target language
        """
        print(f"Translating response from English to {target_language}...")

        # If target is English, return the text as is
        if target_language == "en":
            print("Target language is English. No translation needed.")
            return text

        # Try to load response_nmt.json for translation configuration
        try:
            with open("response_nmt.json", "r", encoding="utf-8") as f:
                # Just load to verify it exists, we'll use it in the API call
                json.load(f)
                print("Loaded response_nmt.json for translation configuration")
        except Exception as e:
            print(f"Could not load response_nmt.json: {e}")
            # Continue without the config

        # Try to use the same API endpoints as ASR for consistency
        endpoints_to_try = []

        # Add the pipeline API endpoint if available
        if hasattr(self, 'pipeline_api_endpoint'):
            endpoints_to_try.append(self.pipeline_api_endpoint)

        # Add the compute API endpoint if available
        if hasattr(self, 'compute_api_endpoint'):
            endpoints_to_try.append(self.compute_api_endpoint)

        # If no endpoints are available, use the default
        if not endpoints_to_try:
            endpoints_to_try = ["https://dhruva-api.bhashini.gov.in/services/inference/pipeline"]

        # Prepare the request payload for Bhashini NMT API
        pipeline_payload = {
            "pipelineTasks": [
                {
                    "taskType": "translation",
                    "config": {
                        "language": {
                            "sourceLanguage": "en",
                            "targetLanguage": target_language
                        },
                        "serviceId": "ai4bharat/indictrans-v2-all-gpu--t4"
                    }
                }
            ],
            "inputData": {
                "input": [
                    {
                        "source": text
                    }
                ]
            }
        }

        # Direct compute API payload
        compute_payload = {
            "modelId": "6475ade3f6c68e2c3f0d8f3a",  # Model ID for translation
            "task": "translation",
            "input": [{"source": text}],
            "userId": self.user_id,
            "config": {
                "language": {
                    "sourceLanguage": "en",
                    "targetLanguage": target_language
                },
                "serviceId": "ai4bharat/indictrans-v2-all-gpu--t4"
            }
        }

        # Set up headers with authentication
        headers = {
            "userID": self.user_id,
            "ulcaApiKey": self.ulca_api_key,
            "Content-Type": "application/json"
        }

        # Add authorization token if available
        if hasattr(self, 'auth_token') and self.auth_token:
            headers["Authorization"] = self.auth_token

        # Try each endpoint
        for endpoint in endpoints_to_try:
            try:
                # Select the appropriate payload based on the endpoint
                if "dhruva-api.bhashini.gov.in" in endpoint:
                    current_payload = pipeline_payload
                    print(f"Using pipeline API format for translation: {endpoint}")
                else:
                    current_payload = compute_payload
                    print(f"Using compute API format for translation: {endpoint}")

                print(f"Sending NMT request to: {endpoint}")
                print(f"Payload: {json.dumps(current_payload, ensure_ascii=False)[:200]}...")

                # Make the API request
                response = requests.post(endpoint, headers=headers, json=current_payload)

                print(f"Response status code: {response.status_code}")

                # Try to parse the response as JSON
                try:
                    response_data = response.json()
                    print(f"Response data: {json.dumps(response_data, ensure_ascii=False)[:200]}...")

                    # Save the response for debugging
                    with open("last_response_nmt_response.json", "w", encoding="utf-8") as f:
                        json.dump(response_data, f, ensure_ascii=False, indent=2)

                    # Extract the translated text based on the API format
                    translated_text = ""

                    if "pipelineResponse" in response_data:
                        # Pipeline API format
                        for task_response in response_data["pipelineResponse"]:
                            if task_response.get("taskType") == "translation" and "output" in task_response:
                                output = task_response["output"][0] if task_response["output"] else {}
                                translated_text = output.get("target", "")
                                break
                    elif "output" in response_data:
                        # Compute API format
                        output = response_data["output"][0] if response_data["output"] else {}
                        translated_text = output.get("target", "")

                    # If we got a result, return it
                    if translated_text:
                        print(f"Translated response: {translated_text}")
                        return translated_text
                    else:
                        print("No translation found in response")
                        # Continue to next endpoint if available

                except json.JSONDecodeError:
                    print(f"Failed to parse response as JSON. Raw response: {response.text[:500]}...")
                    # Continue to next endpoint if available

            except Exception as e:
                print(f"Error calling Bhashini NMT API at {endpoint}: {e}")
                import traceback
                traceback.print_exc()
                # Continue to next endpoint if available

        # If all endpoints failed, return the original English text
        print("All translation endpoints failed. Returning original English text.")
        return text

    def text_to_speech(self, text, language_code):
        """
        Convert text to speech using Bhashini TTS API.

        Args:
            text (str): Text to convert to speech
            language_code (str): Language code of the text

        Returns:
            str: Path to the generated audio file
        """
        print(f"\n=== Converting Text to Speech using TTS ===")
        print(f"Converting text in {language_code} to speech...")

        # Prepare the request payload for Bhashini TTS API using the provided template
        # Using a different format that's known to work with Bhashini
        pipeline_payload = {
            "pipelineTasks": [
                {
                    "taskType": "tts",
                    "config": {
                        "language": {
                            "sourceLanguage": language_code
                        },
                        # Using the service ID provided by the user
                        "serviceId": "ai4bharat/indic-tts-coqui-indo_aryan-gpu--t4",
                        "gender": "female",
                        "samplingRate": 8000
                    }
                }
            ],
            "inputData": {
                "input": [
                    {
                        "source": text
                    }
                ]
            }
        }

        # Print the exact payload for debugging
        print(f"Full TTS payload: {json.dumps(pipeline_payload, ensure_ascii=False)}")

        # Set up headers with authentication
        headers = {
            "userID": self.user_id,
            "ulcaApiKey": self.ulca_api_key,
            "Content-Type": "application/json"
        }

        # Add authorization token if available
        if hasattr(self, 'auth_token') and self.auth_token:
            headers["Authorization"] = self.auth_token

        # Use the pipeline API endpoint for TTS
        endpoint = self.pipeline_api_endpoint if hasattr(self, 'pipeline_api_endpoint') else "https://dhruva-api.bhashini.gov.in/services/inference/pipeline"

        # Generate a unique filename using timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        audio_filename = os.path.join(self.temp_dir, f"tts_output_{timestamp}.wav")

        # List of service IDs to try, starting with the one provided by the user
        service_ids = [
            "ai4bharat/indic-tts-coqui-indo_aryan-gpu--t4",  # This is the service ID provided by the user
            "ai4bharat/indictts",
            "ai4bharat/indic-tts",
            "ai4bharat/tts-unified-gpu--t4",
            "ai4bharat/indic-tts-gpu--t4",
            "ai4bharat/indic-tts-hi"
        ]

        for service_id in service_ids:
            try:
                # Update the service ID in the payload
                pipeline_payload["pipelineTasks"][0]["config"]["serviceId"] = service_id

                print(f"Trying TTS with service ID: {service_id}")
                print(f"Sending TTS request to: {endpoint}")
                print(f"Payload: {json.dumps(pipeline_payload, ensure_ascii=False)[:200]}...")

                # Make the API request
                response = requests.post(endpoint, headers=headers, json=pipeline_payload)

                print(f"Response status code: {response.status_code}")

                # Try to parse the response as JSON
                try:
                    response_data = response.json()
                    print(f"Response data: {json.dumps(response_data, ensure_ascii=False)[:200]}...")

                    # Save the response for debugging
                    with open("last_tts_response.json", "w", encoding="utf-8") as f:
                        json.dump(response_data, f, ensure_ascii=False, indent=2)

                    # Check if there's an error in the response
                    if response.status_code != 200 or "detail" in response_data:
                        error_msg = response_data.get("detail", {}).get("message", "Unknown error")
                        print(f"Error with service ID {service_id}: {error_msg}")
                        continue  # Try the next service ID

                    # Extract the audio content from the response
                    audio_content = None

                    if "pipelineResponse" in response_data:
                        # Pipeline API format
                        for task_response in response_data["pipelineResponse"]:
                            if task_response.get("taskType") == "tts" and "audio" in task_response:
                                audio = task_response["audio"][0] if task_response["audio"] else {}
                                audio_content = audio.get("audioContent", "")
                                break

                    # If we got audio content, save it to a file
                    if audio_content:
                        print(f"Audio content received, saving to file: {audio_filename}")

                        # Decode base64 audio content
                        audio_bytes = base64.b64decode(audio_content)

                        # Save to file
                        with open(audio_filename, "wb") as f:
                            f.write(audio_bytes)

                        print(f"Audio saved to {audio_filename}")

                        # Play the audio file
                        try:
                            print("Playing audio...")
                            # Since we're on Windows, use the Windows-specific command
                            os.system(f'start {audio_filename}')
                            print("Audio playback started")
                        except Exception as e:
                            print(f"Error playing audio: {e}")

                        return audio_filename
                    else:
                        print(f"No audio content found in response for service ID {service_id}")
                        # Continue to the next service ID

                except json.JSONDecodeError:
                    print(f"Failed to parse response as JSON. Raw response: {response.text[:500]}...")
                    # Continue to the next service ID

            except Exception as e:
                print(f"Error calling Bhashini TTS API with service ID {service_id}: {e}")
                import traceback
                traceback.print_exc()
                # Continue to the next service ID

        # If all service IDs failed
        print("All TTS service IDs failed. Could not generate speech.")
        return None



    def answer_question(self, question):
        """
        Send a question to OpenAI API and get a response.

        Args:
            question (str): The question to be answered

        Returns:
            str: The response from the API
        """
        print(f"Sending question to OpenAI: {question}")

        # OpenAI API key
        openai_api_key = "********************************************************************************************************************************************************************"

        try:
            # Prepare the request payload
            payload = {
                "model": "anthropic/claude-3-opus",
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant that can answer questions in any language."},
                    {"role": "user", "content": question}
                ],
                "max_tokens": 250  # Reduced from 290 to ensure we stay well within the credit limit
            }

            # Set up headers with authentication
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {openai_api_key}",
                "HTTP-Referer": "https://voice-assistant-app.com",
                "X-Title": "Voice Assistant"
            }

            # Make the API request to OpenRouter
            api_endpoint = "https://openrouter.ai/api/v1/chat/completions"
            print(f"Sending request to: {api_endpoint}")
            print(f"Payload: {json.dumps(payload, ensure_ascii=False)[:200]}...")

            response = requests.post(api_endpoint, headers=headers, json=payload)

            print(f"Response status code: {response.status_code}")

            # Save the raw response for debugging
            with open("last_openai_response.json", "w", encoding="utf-8") as f:
                try:
                    f.write(response.text)
                    print("Raw response saved to last_openai_response.json")
                except Exception as e:
                    print(f"Error saving raw response: {e}")

            # Extract the response text
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print(f"Response data structure: {json.dumps(response_data, ensure_ascii=False, indent=2)[:500]}...")

                    # Check for different response formats
                    if "choices" in response_data and len(response_data["choices"]) > 0:
                        # Standard OpenAI/OpenRouter format
                        if "message" in response_data["choices"][0]:
                            answer = response_data["choices"][0]["message"]["content"]
                        elif "text" in response_data["choices"][0]:
                            # Alternative format
                            answer = response_data["choices"][0]["text"]
                        else:
                            # Try to extract any text content
                            answer = str(response_data["choices"][0])
                    elif "response" in response_data:
                        # Some APIs use a direct response field
                        answer = response_data["response"]
                    elif "output" in response_data:
                        # Some APIs use an output field
                        answer = response_data["output"]
                    elif "result" in response_data:
                        # Some APIs use a result field
                        answer = response_data["result"]
                    else:
                        # If we can't find a standard field, return a user-friendly message
                        answer = "I'm sorry, I couldn't understand your question properly. Could you please try asking in a different way?"

                    print(f"OpenAI response received: {answer[:100]}...")
                    return answer
                except (KeyError, IndexError) as e:
                    print(f"Error parsing API response: {e}")
                    print(f"API Response structure: {json.dumps(response_data, ensure_ascii=False, indent=2)}")

                    # Return a user-friendly message instead of the error details
                    return "I'm sorry, I couldn't process your request properly. Please try asking a simpler question."
            else:
                print(f"API request failed with status code {response.status_code}")
                try:
                    response_data = response.json()
                    print(f"Error message: {json.dumps(response_data, ensure_ascii=False, indent=2)}")

                    # Check for credit limit error
                    if 'error' in response_data and 'message' in response_data['error']:
                        error_msg = response_data['error']['message']
                        print(f"Error message from API: {error_msg}")
                        if 'credits' in error_msg or 'token' in error_msg or 'afford' in error_msg:
                            print("Credit limit error detected. Using fallback response.")
                            # Try with a smaller model that requires fewer tokens
                            print("Trying with a smaller model (gpt-3.5-turbo)...")
                            try:
                                # Create a new payload with a smaller model
                                smaller_payload = {
                                    "model": "openai/gpt-3.5-turbo",  # Use a smaller model
                                    "messages": [
                                        {"role": "system", "content": "You are a helpful assistant."},
                                        {"role": "user", "content": question}
                                    ],
                                    "max_tokens": 30  # Further reduced to ensure it works with limited credits
                                }

                                # Make the API request with the smaller model
                                smaller_response = requests.post(api_endpoint, headers=headers, json=smaller_payload)

                                if smaller_response.status_code == 200:
                                    smaller_data = smaller_response.json()
                                    # Save the smaller model response for debugging
                                    with open("last_smaller_model_response.json", "w", encoding="utf-8") as f:
                                        json.dump(smaller_data, f, ensure_ascii=False, indent=2)

                                    # Try to extract the response from various possible formats
                                    if "choices" in smaller_data and len(smaller_data["choices"]) > 0:
                                        if "message" in smaller_data["choices"][0]:
                                            return smaller_data["choices"][0]["message"]["content"]
                                        elif "text" in smaller_data["choices"][0]:
                                            return smaller_data["choices"][0]["text"]

                                    # If we can't find a standard field, return a user-friendly message
                                    return "I'm sorry, I couldn't process your request properly. Could you please try again with a simpler question?"
                            except Exception as e2:
                                print(f"Error with smaller model: {e2}")

                            # If smaller model also fails, provide direct responses
                            print("Falling back to hardcoded responses...")
                            if "how are you" in question.lower():
                                return "I'm doing well, thank you for asking! How can I help you today?"
                            elif "hello" in question.lower() or "hi" in question.lower():
                                return "Hello! It's nice to meet you. How can I assist you today?"
                            elif "what" in question.lower() or "who" in question.lower() or "how" in question.lower() or "why" in question.lower():
                                return "That's an interesting question. I'd like to provide a detailed answer, but I'm currently experiencing some API limitations. Could you please try again with a simpler question?"
                            else:
                                return "I'd be happy to help with your question, but I'm currently experiencing some API limitations due to token usage constraints. I'll try to provide a better response next time."
                except Exception as e:
                    print(f"Error parsing error response: {e}")
                    print(f"Raw response: {response.text[:500]}...")

                # Try to use a direct OpenAI API call as a fallback
                try:
                    print("Trying direct OpenAI API as fallback...")
                    # Different API endpoint and format for direct OpenAI
                    direct_api_endpoint = "https://api.openai.com/v1/chat/completions"
                    direct_payload = {
                        "model": "gpt-3.5-turbo",
                        "messages": [
                            {"role": "system", "content": "You are a helpful assistant."},
                            {"role": "user", "content": question}
                        ],
                        "max_tokens": 30  # Reduced to ensure it works with limited credits
                    }
                    direct_headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {openai_api_key}"
                    }

                    direct_response = requests.post(direct_api_endpoint, headers=direct_headers, json=direct_payload)
                    if direct_response.status_code == 200:
                        direct_data = direct_response.json()
                        if "choices" in direct_data and len(direct_data["choices"]) > 0:
                            return direct_data["choices"][0]["message"]["content"]
                except Exception as e:
                    print(f"Direct OpenAI API fallback failed: {e}")

                return f"Sorry, I encountered an error: API request failed with status code {response.status_code}"

        except Exception as e:
            print(f"Error calling OpenAI API: {e}")
            import traceback
            traceback.print_exc()

            # If the API call fails, provide a simple response based on the question
            print("API call failed. Providing a simple response.")

            # Simple response generator with more comprehensive fallbacks
            if "name" in question.lower() or "who are you" in question.lower():
                return "I am an AI assistant designed to help answer your questions."
            elif "weather" in question.lower():
                return "I'm sorry, I don't have access to current weather information."
            elif "time" in question.lower():
                return "I don't have access to the current time."
            elif "thank" in question.lower():
                return "You're welcome! Is there anything else I can help you with?"
            elif "hello" in question.lower() or "hi" in question.lower():
                return "Hello! How can I assist you today?"
            elif "how are you" in question.lower():
                return "I'm functioning well, thank you for asking. How can I help you?"
            elif "help" in question.lower() or "assist" in question.lower():
                return "I'm here to help you with information, answer questions, or assist with various tasks. What would you like to know?"
            elif "what can you do" in question.lower():
                return "I can answer questions, provide information, assist with language translation, and help with various other tasks. How can I assist you today?"
            elif any(word in question.lower() for word in ["what", "who", "where", "when", "why", "how"]):
                return "That's an interesting question. I'd like to provide a detailed answer, but I'm currently experiencing some API limitations. Could you please try again later?"
            else:
                return "I'm sorry, I couldn't process your question through the API. Could you please try again or rephrase your question?"

# Removed standalone get_language_name function as we're using the class method

def main():
    recorder = VoiceRecorderLanguageDetector()

    # Step 1: Record voice
    print("\n=== Step 1: Recording Voice ===")
    print("The system will automatically start recording when you speak.")
    print("It will stop recording when you pause or after 10 seconds.")
    audio_file = recorder.record_audio()

    # Step 2: Detect language using ONLY ALD, then transcribe using ASR
    print("\n=== Step 2: Detecting Language and Transcribing Audio ===")
    print("Detecting language ONLY using ALD, then transcribing with ASR...")
    language_code, transcribed_text = recorder.detect_language(audio_file)

    if language_code and transcribed_text:
        language_name = recorder.get_language_name(language_code)
        print(f"Detected Language: {language_name} ({language_code})")
        print(f"Transcription: {transcribed_text}")

        # Display ALD detected language if available
        try:
            with open("detected_language.txt", "r", encoding="utf-8") as f:
                ald_language = f.read().strip()
                print(f"\n*** ALD Detected Language: {ald_language} ***\n")

            # Also display detailed language predictions if available
            try:
                with open("ald_language_details.json", "r", encoding="utf-8") as f:
                    lang_details = json.load(f)
                    print("ALD Language Detection Details:")
                    for lang_pred in lang_details:
                        lang_code = lang_pred.get("langCode", "unknown")
                        lang_name = recorder.get_language_name(lang_code)
                        lang_score = lang_pred.get("langScore", "N/A")
                        print(f"  - {lang_name} ({lang_code}): Score {lang_score}")
            except Exception as e:
                print(f"Note: Could not read detailed language predictions: {e}")
        except Exception as e:
            print(f"Note: Could not read ALD detected language: {e}")

        # Step 3: Translate text to English using NMT
        print("\n=== Step 3: Translating to English using NMT ===")
        print("Using response_nmt.json as template for translation")

        # Check if response_nmt.json exists and print its content
        try:
            with open("response_nmt.json", "r", encoding="utf-8") as f:
                nmt_template = json.load(f)
                print(f"response_nmt.json content: {json.dumps(nmt_template, ensure_ascii=False)}")
        except Exception as e:
            print(f"Warning: Could not load response_nmt.json: {e}")

        english_text = recorder.translate_to_english(transcribed_text, language_code)
        print(f"English Translation: {english_text}")

        # Step 4: Send English text to OpenAI for response
        print("\n=== Step 4: Sending to OpenAI for Response ===")
        english_response = recorder.answer_question(english_text)
        print(f"OpenAI Response (English): {english_response}")

        # Step 5: Translate the response back to the original language if needed
        print("\n=== Step 5: Translating Response Back to Original Language ===")
        if language_code != "en":
            translated_response = recorder.translate_from_english(english_response, language_code)
            language_name = recorder.get_language_name(language_code)
            print(f"Translated Response ({language_name}): {translated_response}")
        else:
            translated_response = english_response
            print("Original language is English. No translation needed.")

        # Step 6: Convert translated response to speech
        print("\n=== Step 6: Converting Text to Speech ===")
        if language_code != "en":
            tts_output_file = recorder.text_to_speech(translated_response, language_code)
            if tts_output_file:
                print(f"TTS Output: {tts_output_file}")
            else:
                print("TTS conversion failed.")
        else:
            # For English, use the original response
            tts_output_file = recorder.text_to_speech(english_response, "en")
            if tts_output_file:
                print(f"TTS Output: {tts_output_file}")
            else:
                print("TTS conversion failed.")

        # Step 7: Show the final results
        print("\n=== Step 7: Final Results ===")
        print("\nResults:")
        language_name = recorder.get_language_name(language_code)

        # Display ALD detected language in final results
        ald_language_info = ""
        try:
            with open("detected_language.txt", "r", encoding="utf-8") as f:
                ald_language_info = f.read().strip()
        except Exception:
            ald_language_info = "Not available"

        print(f"ALD Detected Language: {ald_language_info}")

        # Display detailed ALD language predictions in final results
        print("\nALD Language Detection Details:")
        try:
            with open("ald_language_details.json", "r", encoding="utf-8") as f:
                lang_details = json.load(f)
                if lang_details:
                    for lang_pred in lang_details:
                        lang_code = lang_pred.get("langCode", "unknown")
                        lang_name = recorder.get_language_name(lang_code)
                        lang_score = lang_pred.get("langScore", "N/A")
                        print(f"  - {lang_name} ({lang_code}): Score {lang_score}")
                else:
                    print("  No detailed language predictions available")
        except Exception:
            print("  Detailed language predictions not available")
        print(f"Final Detected Language: {language_name} ({language_code})")
        print(f"Original Text ({language_name}): {transcribed_text}")
        print(f"English Translation: {english_text}")
        print(f"OpenAI Response (English): {english_response}")
        if language_code != "en":
            print(f"Translated Response ({language_name}): {translated_response}")
    else:
        print("\n=== Error ===")
        print("Language detection through ALD failed or speech recognition failed.")
        print("The system is configured to use ONLY ALD for language detection.")
        print("Please try again with a clearer audio recording.")

if __name__ == "__main__":
    main()

