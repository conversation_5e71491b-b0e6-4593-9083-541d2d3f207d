const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/lawengaxe', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Define video schema
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  tags: [String],
  category: String,
  contentRating: String,
  processingStatus: String,
  stats: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    playlistAdds: { type: Number, default: 0 },
    averageWatchTime: { type: Number, default: 0 },
    retentionRate: { type: Number, default: 0 }
  },
  file: {
    originalName: String,
    size: Number,
    mimeType: String
  },
  createdAt: Date,
  updatedAt: Date,
  deletedAt: Date
});

const Video = mongoose.model('Video', VideoSchema);

async function updateVideoDurations() {
  try {
    console.log('🔄 Updating video durations...');
    
    const videos = await Video.find({});
    console.log(`Found ${videos.length} videos to update`);
    
    for (let i = 0; i < videos.length; i++) {
      const video = videos[i];
      
      // Generate realistic duration based on video type/title
      let duration;
      const title = video.title.toLowerCase();
      
      if (title.includes('tutorial') || title.includes('concept') || title.includes('theory')) {
        // Educational videos: 10-25 minutes
        duration = Math.floor(Math.random() * 900) + 600; // 10-25 minutes
      } else if (title.includes('game') || title.includes('play')) {
        // Game videos: 5-15 minutes
        duration = Math.floor(Math.random() * 600) + 300; // 5-15 minutes
      } else if (title.includes('basic') || title.includes('introduction')) {
        // Basic/intro videos: 8-20 minutes
        duration = Math.floor(Math.random() * 720) + 480; // 8-20 minutes
      } else {
        // General videos: 5-30 minutes
        duration = Math.floor(Math.random() * 1500) + 300; // 5-30 minutes
      }
      
      // Update the video duration
      await Video.updateOne(
        { _id: video._id },
        { $set: { duration: duration } }
      );
      
      console.log(`✅ Updated "${video.title}" - Duration: ${Math.floor(duration / 60)}:${(duration % 60).toString().padStart(2, '0')}`);
    }
    
    console.log('🎉 All video durations updated successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error updating video durations:', error);
    process.exit(1);
  }
}

updateVideoDurations();
