import { FastifyRequest, FastifyReply } from 'fastify';

/**
 * Request logger middleware
 * Logs request details for debugging
 */
export async function requestLogger(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  const startTime = Date.now();
  
  // Log request details
  request.log.info({
    url: request.url,
    method: request.method,
    headers: request.headers,
    query: request.query,
    params: request.params,
    body: request.body,
  }, 'Incoming request');
  
  // Continue processing the request
  reply.raw.on('finish', () => {
    const responseTime = Date.now() - startTime;
    
    // Log response details
    request.log.info({
      statusCode: reply.statusCode,
      responseTime: `${responseTime}ms`,
    }, 'Request completed');
  });
}
