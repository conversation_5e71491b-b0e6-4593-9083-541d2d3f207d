import axios from 'axios';
import { API_BASE_URL } from '../config';

// Create an axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Add timeout to prevent hanging requests
  timeout: 10000, // 10 seconds
});

// Add a request interceptor to include the auth token in requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('lawengaxe-token');
    console.log('Token from localStorage:', token ? 'Token exists' : 'No token');

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('Added Authorization header');
    } else {
      console.warn('No auth token found in localStorage');

      // For development purposes, add a default token if none exists
      if (process.env.NODE_ENV === 'development') {
        const defaultToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************.Kv_B7h1qYGroD7rbxf7Ua058hehyA5qIO7dsa9YkYIY';
        config.headers.Authorization = `Bearer ${defaultToken}`;
        console.log('Added default Authorization header for development');

        // Also save it to localStorage for future requests
        localStorage.setItem('lawengaxe-token', defaultToken);
      } else {
        // Don't add a default token in production - let the server handle unauthenticated requests
        console.log('No Authorization header added - user is not authenticated');
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle errors and token refresh
let isRefreshing = false;
let refreshSubscribers: ((token: string) => void)[] = [];

// Function to add callbacks to the refresh subscriber queue
const subscribeTokenRefresh = (callback: (token: string) => void) => {
  refreshSubscribers.push(callback);
};

// Function to notify all subscribers about the new token
const onTokenRefreshed = (token: string) => {
  refreshSubscribers.forEach(callback => callback(token));
  refreshSubscribers = [];
};

// Function to handle token refresh
const refreshAuthToken = async () => {
  try {
    const refreshToken = localStorage.getItem('lawengaxe-refresh-token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    console.log('Attempting to refresh access token...');
    const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, { refreshToken });

    const { accessToken } = response.data;
    localStorage.setItem('lawengaxe-token', accessToken);
    console.log('Access token refreshed successfully');

    return accessToken;
  } catch (error) {
    console.error('Failed to refresh token:', error);
    // Clear tokens on refresh failure
    localStorage.removeItem('lawengaxe-token');
    localStorage.removeItem('lawengaxe-refresh-token');
    throw error;
  }
};

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Get the original request config
    const originalRequest = error.config;

    // Check if the error is due to an expired token (401 Unauthorized)
    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      console.log('Received 401 error, checking if token refresh is needed');

      // Mark this request as retried to prevent infinite loops
      originalRequest._retry = true;

      // Check if we're already refreshing the token
      if (!isRefreshing) {
        console.log('Starting token refresh process');
        isRefreshing = true;

        try {
          // Attempt to refresh the token
          const newToken = await refreshAuthToken();

          // Update the Authorization header with the new token
          originalRequest.headers.Authorization = `Bearer ${newToken}`;

          // Notify all subscribers that the token has been refreshed
          onTokenRefreshed(newToken);

          // Reset the refreshing flag
          isRefreshing = false;

          // Retry the original request with the new token
          console.log('Retrying original request with new token');
          return axios(originalRequest);
        } catch (refreshError) {
          // Reset the refreshing flag
          isRefreshing = false;

          // Log the user out if token refresh fails
          console.error('Token refresh failed, logging out:', refreshError);

          // Redirect to login page if in browser environment
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }

          return Promise.reject(refreshError);
        }
      } else {
        // If we're already refreshing, add this request to the queue
        console.log('Token refresh already in progress, adding request to queue');
        return new Promise(resolve => {
          subscribeTokenRefresh(token => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            resolve(axios(originalRequest));
          });
        });
      }
    }

    // Log the error for debugging
    console.error('API Error:', error);

    // Add more detailed error information if available
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error message:', error.message);
    }

    return Promise.reject(error);
  }
);

// Auth API calls
const authAPI = {
  // Login user
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  // Login with Engaxe
  loginWithEngaxe: async (username: string, password: string) => {
    const response = await api.post('/auth/engaxe/login', { username, password });
    return response.data;
  },

  // Register user
  register: async (userData: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    displayName?: string;
  }) => {
    const response = await api.post('/users/register', userData);
    return response.data;
  },

  // Get current user profile
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  // Refresh token
  refreshToken: async (refreshToken: string) => {
    try {
      console.log('Manually refreshing token...');
      const response = await api.post('/auth/refresh-token', { refreshToken });

      // Update the token in localStorage
      if (response.data && response.data.accessToken) {
        localStorage.setItem('lawengaxe-token', response.data.accessToken);
        console.log('Access token manually refreshed and saved to localStorage');
      }

      return response.data;
    } catch (error) {
      console.error('Manual token refresh failed:', error);
      throw error;
    }
  },
};

// Import API services
import { channelAPI } from './channel-api';
import { videoAPI } from './video-api';
import { metadataAPI } from './metadata-api';
import { reportAPI } from './report-api';
import { likeAPI } from './like-api';

// Export all API services
export { authAPI, channelAPI, videoAPI, metadataAPI, reportAPI, likeAPI };

export default api;
