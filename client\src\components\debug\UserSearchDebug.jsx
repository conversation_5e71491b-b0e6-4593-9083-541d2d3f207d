import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import axios from 'axios';
import { API_BASE_URL } from '../../config';

const UserSearchDebug = () => {
  const { token, currentUser } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [query, setQuery] = useState('');

  const searchUsers = async () => {
    if (!token) {
      setError('No token available');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('Searching users with query:', query);
      
      const response = await axios.get(`${API_BASE_URL}/users/search`, {
        params: { query },
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      console.log('Search response:', response.data);

      if (response.data.success) {
        setUsers(response.data.data);
      } else {
        setError(response.data.message || 'Unknown error');
      }
    } catch (error) {
      console.error('Error searching users:', error);
      setError(error.response?.data?.message || error.message || 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const fetchRecentUsers = async () => {
    if (!token) {
      setError('No token available');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('Fetching recent users');
      
      const response = await axios.get(`${API_BASE_URL}/users/recent`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      console.log('Recent users response:', response.data);

      if (response.data.success) {
        setUsers(response.data.data);
      } else {
        setError(response.data.message || 'Unknown error');
      }
    } catch (error) {
      console.error('Error fetching recent users:', error);
      setError(error.response?.data?.message || error.message || 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed bottom-0 left-0 bg-black bg-opacity-80 text-white p-4 text-xs z-50 max-w-xs overflow-auto">
      <h3 className="font-bold mb-2">User Search Debug</h3>
      <div className="mb-2">
        <p><strong>User:</strong> {currentUser ? currentUser.username : 'Not logged in'}</p>
        <p><strong>Token:</strong> {token ? `${token.substring(0, 10)}...` : 'No token'}</p>
      </div>
      
      <div className="mb-2">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search query"
          className="bg-gray-800 text-white p-1 w-full mb-1"
        />
        <button
          onClick={searchUsers}
          className="bg-blue-600 text-white px-2 py-1 mr-1"
          disabled={loading}
        >
          Search
        </button>
        <button
          onClick={fetchRecentUsers}
          className="bg-green-600 text-white px-2 py-1"
          disabled={loading}
        >
          Recent
        </button>
      </div>
      
      {loading && <p>Loading...</p>}
      {error && <p className="text-red-400">Error: {error}</p>}
      
      <div>
        <h4 className="font-bold mb-1">Results ({users.length})</h4>
        {users.length > 0 ? (
          <ul className="max-h-40 overflow-y-auto">
            {users.map((user) => (
              <li key={user.id} className="mb-1 border-b border-gray-700 pb-1">
                <p><strong>{user.username}</strong> ({user.displayName || 'No display name'})</p>
                <p className="text-gray-400">{user.email}</p>
              </li>
            ))}
          </ul>
        ) : (
          <p>No users found</p>
        )}
      </div>
    </div>
  );
};

export default UserSearchDebug;
