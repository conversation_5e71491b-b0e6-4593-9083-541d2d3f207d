import { FastifySchema } from 'fastify';
import { Type } from '@sinclair/typebox';

/**
 * Create log schema
 */
export const createLogSchema: FastifySchema = {
  body: Type.Object({
    action: Type.String(),
    category: Type.String(),
    resourceType: Type.String(),
    resourceId: Type.Optional(Type.String()),
    previousState: Type.Optional(Type.Record(Type.String(), Type.Any())),
    newState: Type.Optional(Type.Record(Type.String(), Type.Any())),
    status: Type.Optional(Type.Enum({ success: 'success', failure: 'failure', warning: 'warning', info: 'info' })),
    details: Type.Optional(Type.String()),
    severity: Type.Optional(Type.Enum({ low: 'low', medium: 'medium', high: 'high', critical: 'critical' })),
  }),
  response: {
    201: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      log: Type.Object({
        id: Type.String(),
        action: Type.String(),
        category: Type.String(),
        userId: Type.String(),
        userIp: Type.String(),
        userAgent: Type.String(),
        resourceType: Type.String(),
        resourceId: Type.Optional(Type.String()),
        status: Type.String(),
        severity: Type.String(),
        createdAt: Type.String(),
      }),
    }),
  },
};

/**
 * Get all logs schema
 */
export const getAllLogsSchema: FastifySchema = {
  querystring: Type.Object({
    page: Type.Optional(Type.Number({ minimum: 1, default: 1 })),
    limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100, default: 20 })),
    sortBy: Type.Optional(Type.String({ default: 'createdAt' })),
    sortOrder: Type.Optional(Type.Enum({ asc: 'asc', desc: 'desc' }, { default: 'desc' })),
    action: Type.Optional(Type.String()),
    category: Type.Optional(Type.String()),
    userId: Type.Optional(Type.String()),
    resourceType: Type.Optional(Type.String()),
    resourceId: Type.Optional(Type.String()),
    status: Type.Optional(Type.Enum({ success: 'success', failure: 'failure', warning: 'warning', info: 'info' })),
    severity: Type.Optional(Type.Enum({ low: 'low', medium: 'medium', high: 'high', critical: 'critical' })),
    startDate: Type.Optional(Type.String({ format: 'date-time' })),
    endDate: Type.Optional(Type.String({ format: 'date-time' })),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      logs: Type.Array(
        Type.Object({
          id: Type.String(),
          action: Type.String(),
          category: Type.String(),
          userId: Type.String(),
          userIp: Type.String(),
          userAgent: Type.String(),
          resourceType: Type.String(),
          resourceId: Type.Optional(Type.String()),
          status: Type.String(),
          severity: Type.String(),
          details: Type.Optional(Type.String()),
          createdAt: Type.String(),
        })
      ),
      pagination: Type.Object({
        total: Type.Number(),
        page: Type.Number(),
        limit: Type.Number(),
        pages: Type.Number(),
      }),
    }),
  },
};

/**
 * Get log by ID schema
 */
export const getLogByIdSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      log: Type.Object({
        id: Type.String(),
        action: Type.String(),
        category: Type.String(),
        userId: Type.String(),
        userIp: Type.String(),
        userAgent: Type.String(),
        resourceType: Type.String(),
        resourceId: Type.Optional(Type.String()),
        previousState: Type.Optional(Type.Record(Type.String(), Type.Any())),
        newState: Type.Optional(Type.Record(Type.String(), Type.Any())),
        status: Type.String(),
        details: Type.Optional(Type.String()),
        severity: Type.String(),
        createdAt: Type.String(),
        updatedAt: Type.String(),
      }),
    }),
  },
};

/**
 * Delete log schema
 */
export const deleteLogSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};
