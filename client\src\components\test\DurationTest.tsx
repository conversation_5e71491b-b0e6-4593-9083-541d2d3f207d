import React, { useState, useEffect } from 'react';

/**
 * Test component to verify duration parsing is working correctly
 */
export default function DurationTest() {
  const [testResults, setTestResults] = useState<any[]>([]);

  // Parse duration string in format "MM:SS" or "HH:MM:SS" to seconds
  const parseDurationToSeconds = (durationStr: string | number): number => {
    if (!durationStr) return 0;
    
    // If it's already a number, return it
    if (typeof durationStr === 'number') {
      return durationStr;
    }
    
    // If it's a string that doesn't contain colons, try to parse as number
    if (typeof durationStr === 'string' && !durationStr.includes(':')) {
      const parsed = parseInt(durationStr, 10);
      return isNaN(parsed) ? 0 : parsed;
    }
    
    // Handle MM:SS or HH:MM:SS format
    if (typeof durationStr === 'string') {
      const cleanDuration = durationStr.trim();
      const parts = cleanDuration.split(':');
      
      if (parts.length === 2) {
        // MM:SS format
        const minutes = parseInt(parts[0], 10) || 0;
        const seconds = parseInt(parts[1], 10) || 0;
        return (minutes * 60) + seconds;
      } else if (parts.length === 3) {
        // HH:MM:SS format
        const hours = parseInt(parts[0], 10) || 0;
        const minutes = parseInt(parts[1], 10) || 0;
        const seconds = parseInt(parts[2], 10) || 0;
        return (hours * 3600) + (minutes * 60) + seconds;
      }
    }
    
    return 0;
  };

  // Format duration from seconds to MM:SS or HH:MM:SS
  const formatDuration = (duration: string | number): string => {
    const seconds = parseDurationToSeconds(duration);
    if (!seconds || seconds === 0) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  };

  useEffect(() => {
    // Test cases
    const testCases = [
      { input: '5:30', expected: 330, description: 'MM:SS format' },
      { input: '1:26:27', expected: 5187, description: 'HH:MM:SS format' },
      { input: '0:45', expected: 45, description: 'Zero minutes' },
      { input: '10:00', expected: 600, description: 'Exact minutes' },
      { input: '754', expected: 754, description: 'Number as string' },
      { input: 754, expected: 754, description: 'Number' },
      { input: '', expected: 0, description: 'Empty string' },
      { input: null, expected: 0, description: 'Null' },
      { input: undefined, expected: 0, description: 'Undefined' },
    ];
    
    const results = testCases.map((testCase, index) => {
      const result = parseDurationToSeconds(testCase.input as any);
      const formatted = formatDuration(testCase.input as any);
      const success = result === testCase.expected;
      
      return {
        id: index + 1,
        description: testCase.description,
        input: JSON.stringify(testCase.input),
        expected: testCase.expected,
        result: result,
        formatted: formatted,
        success: success
      };
    });
    
    setTestResults(results);
  }, []);

  const passedTests = testResults.filter(test => test.success).length;
  const totalTests = testResults.length;

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Duration Parsing Test Results</h2>
      
      <div className="mb-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <p className="text-lg">
          <span className="font-semibold">Test Results: </span>
          <span className={passedTests === totalTests ? 'text-green-600' : 'text-red-600'}>
            {passedTests}/{totalTests} passed
          </span>
        </p>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Test</th>
              <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Description</th>
              <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Input</th>
              <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Expected</th>
              <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Result</th>
              <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Formatted</th>
              <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Status</th>
            </tr>
          </thead>
          <tbody>
            {testResults.map((test) => (
              <tr key={test.id} className={test.success ? 'bg-green-50 dark:bg-green-900/20' : 'bg-red-50 dark:bg-red-900/20'}>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{test.id}</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{test.description}</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 font-mono">{test.input}</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{test.expected}s</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{test.result}s</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 font-mono">{test.formatted}</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  <span className={`px-2 py-1 rounded text-sm ${test.success ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}`}>
                    {test.success ? '✅ PASS' : '❌ FAIL'}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Summary</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          This test verifies that the duration parsing function correctly handles various input formats:
        </p>
        <ul className="list-disc list-inside mt-2 text-sm text-gray-600 dark:text-gray-400">
          <li>MM:SS format (e.g., "5:30" → 330 seconds)</li>
          <li>HH:MM:SS format (e.g., "1:26:27" → 5187 seconds)</li>
          <li>Number strings (e.g., "754" → 754 seconds)</li>
          <li>Actual numbers (e.g., 754 → 754 seconds)</li>
          <li>Edge cases (empty, null, undefined)</li>
        </ul>
      </div>
    </div>
  );
}
