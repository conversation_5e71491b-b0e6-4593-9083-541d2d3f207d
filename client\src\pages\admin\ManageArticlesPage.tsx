import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { FileText, Home, Pencil, Plus, Search, Trash2, ChevronUp, ChevronDown, ChevronRight, ArrowLeft, ArrowRight, Filter, X, Check, SlidersHorizontal, Calendar } from 'lucide-react';

// Mock data for articles
const mockArticles = [
  {
    id: '1',
    title: 'Getting Started with React',
    category: 'Education',
    author: 'John Doe',
    date: '2023-05-15',
    status: 'Published'
  },
  {
    id: '2',
    title: 'Advanced TypeScript Techniques',
    category: 'Programming',
    author: 'Jane Smith',
    date: '2023-06-22',
    status: 'Draft'
  },
  {
    id: '3',
    title: 'Building Responsive UIs with Tailwind',
    category: 'Design',
    author: 'Alex Johnson',
    date: '2023-07-10',
    status: 'Published'
  },
  {
    id: '4',
    title: 'State Management in Modern Web Apps',
    category: 'Programming',
    author: 'Sarah Williams',
    date: '2023-08-05',
    status: 'Published'
  },
  {
    id: '5',
    title: 'Optimizing Performance in React Applications',
    category: 'Performance',
    author: 'Mike Brown',
    date: '2023-09-18',
    status: 'Draft'
  }
];

export default function ManageArticlesPage() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [articles, setArticles] = useState(mockArticles);
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);
  const [actionType, setActionType] = useState('activate');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortColumn, setSortColumn] = useState('id');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Filter states
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterDateRange, setFilterDateRange] = useState('all');
  const [resultsPerPage, setResultsPerPage] = useState('10');
  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  // Update active filters based on current filter states
  useEffect(() => {
    updateActiveFilters();
  }, [filterStatus, filterCategory, filterDateRange]);

  const updateActiveFilters = () => {
    const filters: string[] = [];

    if (filterStatus !== 'all') {
      filters.push(`Status: ${filterStatus}`);
    }

    if (filterCategory !== 'all') {
      filters.push(`Category: ${filterCategory}`);
    }

    if (filterDateRange !== 'all') {
      filters.push(`Date: ${filterDateRange}`);
    }

    setActiveFilters(filters);
  };

  // Handle filter changes
  const handleFilterChange = (type: string, value: string) => {
    switch (type) {
      case 'status':
        setFilterStatus(value);
        break;
      case 'category':
        setFilterCategory(value);
        break;
      case 'dateRange':
        setFilterDateRange(value);
        break;
      case 'perPage':
        setResultsPerPage(value);
        break;
    }
  };

  // Clear all filters
  const clearFilters = () => {
    setFilterStatus('all');
    setFilterCategory('all');
    setFilterDateRange('all');
    setActiveFilters([]);
  };

  // Apply filters to articles
  const filteredArticles = articles.filter(article => {
    const matchesSearch =
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.author.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === 'all' || article.status.toLowerCase() === filterStatus.toLowerCase();
    const matchesCategory = filterCategory === 'all' || article.category.toLowerCase() === filterCategory.toLowerCase();

    // Simple date filtering for demo purposes
    let matchesDate = true;
    if (filterDateRange !== 'all') {
      const articleDate = new Date(article.date);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      if (filterDateRange === 'today') {
        matchesDate = articleDate.toDateString() === today.toDateString();
      } else if (filterDateRange === 'yesterday') {
        matchesDate = articleDate.toDateString() === yesterday.toDateString();
      } else if (filterDateRange === 'this-week') {
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        matchesDate = articleDate >= startOfWeek;
      } else if (filterDateRange === 'this-month') {
        matchesDate =
          articleDate.getMonth() === today.getMonth() &&
          articleDate.getFullYear() === today.getFullYear();
      }
    }

    return matchesSearch && matchesStatus && matchesCategory && matchesDate;
  });

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedArticles(filteredArticles.map(article => article.id));
    } else {
      setSelectedArticles([]);
    }
  };

  const handleSelectArticle = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedArticles([...selectedArticles, id]);
    } else {
      setSelectedArticles(selectedArticles.filter(articleId => articleId !== id));
    }
  };

  const handleBulkAction = () => {
    if (selectedArticles.length === 0) return;

    if (actionType === 'delete' && !window.confirm('Are you sure you want to delete the selected articles?')) {
      return;
    }

    // Handle the action based on actionType
    if (actionType === 'delete') {
      setArticles(articles.filter(article => !selectedArticles.includes(article.id)));
    } else {
      setArticles(articles.map(article => {
        if (selectedArticles.includes(article.id)) {
          return {
            ...article,
            status: actionType === 'activate' ? 'Published' : 'Draft'
          };
        }
        return article;
      }));
    }

    // Clear selection after action
    setSelectedArticles([]);
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Manage Articles</h1>
              <Button variant="outline" onClick={() => {}} className="gap-2">
                <Plus className="h-4 w-4" />
                New Article
              </Button>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Content
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Articles
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Manage Articles</span>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Manage & Edit Articles</CardTitle>
                <CardDescription>View, edit, and manage all articles in your library</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col md:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by title, category, or author..."
                      className="pl-10 pr-20"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button
                      className="absolute right-0 top-0 h-full rounded-l-none"
                      size="sm"
                    >
                      Search
                    </Button>
                  </div>

                  <div className="flex gap-2">
                    <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant={activeFilters.length > 0 ? "default" : "outline"}
                          className="min-w-[120px]"
                        >
                          <Filter className="mr-2 h-4 w-4" />
                          Filter {activeFilters.length > 0 && (
                            <Badge variant="secondary" className="ml-2 bg-white text-primary">{activeFilters.length}</Badge>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[340px] p-0" align="end">
                        <div className="bg-primary text-primary-foreground px-4 py-3 font-medium">
                          Filter Articles
                        </div>
                        <div className="p-4 space-y-4">
                          <div className="space-y-3">
                            <div className="space-y-1">
                              <Label htmlFor="status" className="text-sm font-medium">Status</Label>
                              <Select value={filterStatus} onValueChange={(value) => handleFilterChange('status', value)}>
                                <SelectTrigger id="status" className="w-full">
                                  <SelectValue placeholder="Filter status" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">All Statuses</SelectItem>
                                  <SelectItem value="published">Published</SelectItem>
                                  <SelectItem value="draft">Draft</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-1">
                              <Label htmlFor="category" className="text-sm font-medium">Category</Label>
                              <Select value={filterCategory} onValueChange={(value) => handleFilterChange('category', value)}>
                                <SelectTrigger id="category" className="w-full">
                                  <SelectValue placeholder="Filter category" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">All Categories</SelectItem>
                                  <SelectItem value="education">Education</SelectItem>
                                  <SelectItem value="programming">Programming</SelectItem>
                                  <SelectItem value="design">Design</SelectItem>
                                  <SelectItem value="performance">Performance</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-1">
                              <Label htmlFor="dateRange" className="text-sm font-medium">Date Range</Label>
                              <Select value={filterDateRange} onValueChange={(value) => handleFilterChange('dateRange', value)}>
                                <SelectTrigger id="dateRange" className="w-full">
                                  <SelectValue placeholder="Filter date range" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">All Time</SelectItem>
                                  <SelectItem value="today">Today</SelectItem>
                                  <SelectItem value="yesterday">Yesterday</SelectItem>
                                  <SelectItem value="this-week">This Week</SelectItem>
                                  <SelectItem value="this-month">This Month</SelectItem>
                                  <SelectItem value="last-month">Last Month</SelectItem>
                                  <SelectItem value="this-year">This Year</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-1">
                              <Label htmlFor="perPage" className="text-sm font-medium">Results Per Page</Label>
                              <Select value={resultsPerPage} onValueChange={(value) => handleFilterChange('perPage', value)}>
                                <SelectTrigger id="perPage" className="w-full">
                                  <SelectValue placeholder="Results per page" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="10">10 per page</SelectItem>
                                  <SelectItem value="25">25 per page</SelectItem>
                                  <SelectItem value="50">50 per page</SelectItem>
                                  <SelectItem value="100">100 per page</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center justify-between p-4 bg-muted/30 border-t">
                          {activeFilters.length > 0 && (
                            <Button variant="ghost" size="sm" onClick={clearFilters}>
                              <X className="mr-2 h-4 w-4" />
                              Clear all
                            </Button>
                          )}
                          {!activeFilters.length && <div></div>}
                          <Button
                            className="bg-primary text-primary-foreground hover:bg-primary/90"
                            onClick={() => setIsFilterOpen(false)}
                          >
                            <Check className="mr-2 h-4 w-4" />
                            Apply Filters
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>

                    <Select value={resultsPerPage} onValueChange={(value) => handleFilterChange('perPage', value)}>
                      <SelectTrigger className="w-[130px]">
                        <SelectValue placeholder="Results per page" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10 per page</SelectItem>
                        <SelectItem value="25">25 per page</SelectItem>
                        <SelectItem value="50">50 per page</SelectItem>
                        <SelectItem value="100">100 per page</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {activeFilters.length > 0 && (
                  <div className="bg-muted/30 border rounded-md p-3 mb-6 flex items-center">
                    <div className="text-sm font-medium mr-3">Filters:</div>
                    <div className="flex flex-wrap gap-2 flex-1">
                      {activeFilters.map((filter, index) => {
                        const [type, value] = filter.split(': ');
                        return (
                          <Badge key={index} variant="outline" className="px-3 py-1 bg-background flex items-center gap-1">
                            <span className="font-medium">{type}:</span>
                            <span>{value}</span>
                            <X
                              className="ml-1 h-3 w-3 cursor-pointer hover:text-destructive"
                              onClick={() => {
                                if (type === 'Status') handleFilterChange('status', 'all');
                                if (type === 'Category') handleFilterChange('category', 'all');
                                if (type === 'Date') handleFilterChange('dateRange', 'all');
                              }}
                            />
                          </Badge>
                        );
                      })}
                    </div>
                    <Button variant="ghost" size="sm" onClick={clearFilters} className="ml-auto">
                      Clear all filters
                    </Button>
                  </div>
                )}

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-10">
                          <Checkbox
                            checked={selectedArticles.length === filteredArticles.length && filteredArticles.length > 0}
                            onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                          />
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('id')}>
                          <div className="flex items-center">
                            ID
                            {sortColumn === 'id' ? (
                              sortDirection === 'asc' ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />
                            ) : (
                              <ChevronDown className="ml-1 h-4 w-4 opacity-30" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('title')}>
                          <div className="flex items-center">
                            TITLE
                            {sortColumn === 'title' ? (
                              sortDirection === 'asc' ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />
                            ) : (
                              <ChevronDown className="ml-1 h-4 w-4 opacity-30" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('category')}>
                          <div className="flex items-center">
                            CATEGORY
                            {sortColumn === 'category' ? (
                              sortDirection === 'asc' ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />
                            ) : (
                              <ChevronDown className="ml-1 h-4 w-4 opacity-30" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('date')}>
                          <div className="flex items-center">
                            DATE
                            {sortColumn === 'date' ? (
                              sortDirection === 'asc' ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />
                            ) : (
                              <ChevronDown className="ml-1 h-4 w-4 opacity-30" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('status')}>
                          <div className="flex items-center">
                            STATUS
                            {sortColumn === 'status' ? (
                              sortDirection === 'asc' ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />
                            ) : (
                              <ChevronDown className="ml-1 h-4 w-4 opacity-30" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead>ACTION</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredArticles.length > 0 ? (
                        filteredArticles.map((article) => (
                          <TableRow key={article.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedArticles.includes(article.id)}
                                onCheckedChange={(checked) => handleSelectArticle(article.id, checked as boolean)}
                              />
                            </TableCell>
                            <TableCell>{article.id}</TableCell>
                            <TableCell className="font-medium">{article.title}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{article.category}</Badge>
                            </TableCell>
                            <TableCell>{new Date(article.date).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <Badge className={article.status === 'Published' ? 'bg-green-100 text-green-800 hover:bg-green-100' : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100'}>
                                {article.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                                  onClick={() => navigate(`/admin/articles/edit/${article.id}`)}
                                >
                                  <Pencil className="h-4 w-4" />
                                  <span className="sr-only">Edit</span>
                                </Button>
                                <Button variant="ghost" size="icon" className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50">
                                  <Trash2 className="h-4 w-4" />
                                  <span className="sr-only">Delete</span>
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                            <div className="flex flex-col items-center justify-center py-4">
                              <FileText className="h-10 w-10 text-muted-foreground mb-2" />
                              <p>No articles found</p>
                              <p className="text-sm text-muted-foreground">Try adjusting your filters or search term</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>

                <div className="mt-6 flex flex-col md:flex-row justify-between items-center gap-4">
                  <div className="text-sm text-muted-foreground">
                    Showing <span className="font-medium">1</span> to <span className="font-medium">{filteredArticles.length}</span> of <span className="font-medium">{filteredArticles.length}</span> articles
                  </div>

                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="icon" disabled={currentPage === 1}>
                      <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="bg-primary text-primary-foreground hover:bg-primary/90">
                      1
                    </Button>
                    <Button variant="outline" size="icon" disabled={true}>
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <Separator className="my-6" />

                <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                  <div className="space-y-2 w-full md:w-auto">
                    <Label htmlFor="bulk-action" className="text-sm font-medium">Bulk Action</Label>
                    <div className="flex items-center gap-4">
                      <Select value={actionType} onValueChange={setActionType}>
                        <SelectTrigger id="bulk-action" className="w-[180px]">
                          <SelectValue placeholder="Select action" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="activate">Publish</SelectItem>
                          <SelectItem value="deactivate">Set to Draft</SelectItem>
                          <SelectItem value="delete">Delete</SelectItem>
                        </SelectContent>
                      </Select>

                      <Button
                        onClick={handleBulkAction}
                        disabled={selectedArticles.length === 0}
                      >
                        Apply to {selectedArticles.length} selected
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button variant="outline" className="gap-2">
                      <FileText className="h-4 w-4" />
                      Export Articles
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
