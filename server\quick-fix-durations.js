const mongoose = require('mongoose');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/lawengaxe';

// Define video schema
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  tags: [String],
  category: String,
  contentRating: String,
  processingStatus: String,
  stats: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    playlistAdds: { type: Number, default: 0 },
    averageWatchTime: { type: Number, default: 0 },
    retentionRate: { type: Number, default: 0 }
  },
  file: {
    originalName: String,
    size: Number,
    mimeType: String
  },
  languages: [{
    code: String,
    name: String,
    flag: String,
    isDefault: Boolean,
    url: String
  }],
  source: {
    type: String,
    originalUrl: String,
    platform: String,
    externalId: String
  },
  createdAt: Date,
  updatedAt: Date,
  deletedAt: Date
});

const Video = mongoose.model('Video', VideoSchema);

/**
 * Generate realistic duration based on video title and category
 */
function generateRealisticDuration(title, category) {
  const titleLower = (title || '').toLowerCase();
  const categoryLower = (category || '').toLowerCase();
  
  // Educational/tutorial videos: 8-25 minutes
  if (titleLower.includes('tutorial') || titleLower.includes('lesson') || 
      titleLower.includes('learn') || titleLower.includes('concept') ||
      categoryLower.includes('education') || categoryLower.includes('tutorial')) {
    return Math.floor(Math.random() * 1020) + 480; // 8-25 minutes
  }
  
  // Legal/law videos: 15-45 minutes
  if (titleLower.includes('law') || titleLower.includes('legal') || 
      titleLower.includes('court') || titleLower.includes('rights') ||
      categoryLower.includes('law') || categoryLower.includes('legal')) {
    return Math.floor(Math.random() * 1800) + 900; // 15-45 minutes
  }
  
  // Theory/concept videos: 12-30 minutes
  if (titleLower.includes('theory') || titleLower.includes('principle') ||
      titleLower.includes('understanding') || titleLower.includes('basics')) {
    return Math.floor(Math.random() * 1080) + 720; // 12-30 minutes
  }
  
  // Game/interactive videos: 5-15 minutes
  if (titleLower.includes('game') || titleLower.includes('play') ||
      titleLower.includes('interactive') || titleLower.includes('quiz')) {
    return Math.floor(Math.random() * 600) + 300; // 5-15 minutes
  }
  
  // Introduction videos: 5-12 minutes
  if (titleLower.includes('introduction') || titleLower.includes('intro') ||
      titleLower.includes('getting started') || titleLower.includes('overview')) {
    return Math.floor(Math.random() * 420) + 300; // 5-12 minutes
  }
  
  // Default: 8-20 minutes for general content
  return Math.floor(Math.random() * 720) + 480; // 8-20 minutes
}

/**
 * Quick fix for video durations using realistic estimates
 */
async function quickFixDurations() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    // Get videos with zero or missing durations
    const videosToFix = await Video.find({
      deletedAt: null,
      $or: [
        { duration: { $exists: false } },
        { duration: 0 },
        { duration: null },
        { duration: { $lt: 30 } } // Also fix very short durations that seem unrealistic
      ]
    }).select('id title category duration url');
    
    console.log(`📊 Found ${videosToFix.length} videos that need duration fixes`);
    
    if (videosToFix.length === 0) {
      console.log('✅ All videos already have valid durations!');
      return;
    }
    
    console.log('\n🔧 Fixing Video Durations:');
    console.log('=' .repeat(80));
    
    let successCount = 0;
    let failureCount = 0;
    
    for (let i = 0; i < videosToFix.length; i++) {
      const video = videosToFix[i];
      const newDuration = generateRealisticDuration(video.title, video.category);
      
      console.log(`${i + 1}/${videosToFix.length}: ${video.title.substring(0, 50)}...`);
      console.log(`  📝 ID: ${video.id}`);
      console.log(`  🏷️  Category: ${video.category || 'Uncategorized'}`);
      console.log(`  ⏱️  Old duration: ${video.duration || 0}s`);
      console.log(`  ⏱️  New duration: ${newDuration}s (${Math.floor(newDuration / 60)}:${(newDuration % 60).toString().padStart(2, '0')})`);
      
      try {
        const updateResult = await Video.updateOne(
          { _id: video._id },
          { 
            $set: { 
              duration: newDuration,
              updatedAt: new Date()
            } 
          }
        );
        
        if (updateResult.modifiedCount > 0) {
          console.log(`  ✅ Updated successfully`);
          successCount++;
        } else {
          console.log(`  ⚠️  No changes made`);
          failureCount++;
        }
      } catch (updateError) {
        console.error(`  ❌ Failed to update:`, updateError.message);
        failureCount++;
      }
      
      console.log(''); // Empty line for readability
    }
    
    console.log('📊 Quick Fix Summary:');
    console.log('=' .repeat(50));
    console.log(`✅ Successfully updated: ${successCount} videos`);
    console.log(`❌ Failed to update: ${failureCount} videos`);
    console.log(`📊 Total processed: ${videosToFix.length} videos`);
    
    // Show updated statistics
    console.log('\n📈 Updated Database Statistics:');
    const allVideos = await Video.find({ deletedAt: null }).select('duration');
    const zeroCount = allVideos.filter(v => !v.duration || v.duration === 0).length;
    const validCount = allVideos.filter(v => v.duration && v.duration > 0).length;
    
    console.log(`   Total videos: ${allVideos.length}`);
    console.log(`   Videos with valid duration: ${validCount}`);
    console.log(`   Videos with zero duration: ${zeroCount}`);
    console.log(`   Success rate: ${((validCount / allVideos.length) * 100).toFixed(1)}%`);
    
    if (zeroCount === 0) {
      console.log('\n🎉 All videos now have valid durations!');
      console.log('💡 Note: These are realistic estimates. For exact durations, run the Puppeteer-based extraction script.');
    }
    
  } catch (error) {
    console.error('❌ Error fixing video durations:', error);
  } finally {
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('🔒 MongoDB disconnected');
    }
  }
}

/**
 * Check current duration status
 */
async function checkDurationStatus() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    const allVideos = await Video.find({ deletedAt: null }).select('id title duration category');
    const zeroCount = allVideos.filter(v => !v.duration || v.duration === 0).length;
    const validCount = allVideos.filter(v => v.duration && v.duration > 0).length;
    const shortCount = allVideos.filter(v => v.duration && v.duration > 0 && v.duration < 30).length;
    
    console.log('\n📊 Current Duration Status:');
    console.log('=' .repeat(50));
    console.log(`📹 Total videos: ${allVideos.length}`);
    console.log(`✅ Videos with valid duration (>30s): ${validCount - shortCount}`);
    console.log(`⚠️  Videos with short duration (<30s): ${shortCount}`);
    console.log(`❌ Videos with zero/missing duration: ${zeroCount}`);
    console.log(`📈 Success rate: ${(((validCount - shortCount) / allVideos.length) * 100).toFixed(1)}%`);
    
    if (zeroCount > 0 || shortCount > 0) {
      console.log(`\n🔧 ${zeroCount + shortCount} videos need duration fixes`);
      console.log('💡 Run this script with "fix" argument to fix them');
    } else {
      console.log('\n🎉 All videos have valid durations!');
    }
    
  } catch (error) {
    console.error('❌ Error checking duration status:', error);
  } finally {
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('🔒 MongoDB disconnected');
    }
  }
}

// Run the script
if (require.main === module) {
  const action = process.argv[2] || 'check';
  
  if (action === 'fix') {
    quickFixDurations()
      .then(() => {
        console.log('✅ Quick duration fix completed');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Quick duration fix failed:', error);
        process.exit(1);
      });
  } else {
    checkDurationStatus()
      .then(() => {
        console.log('✅ Duration status check completed');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Duration status check failed:', error);
        process.exit(1);
      });
  }
}

module.exports = { quickFixDurations, checkDurationStatus, generateRealisticDuration };
