import { RoleModel, UserModel, PermissionModel } from '../models';
import { IRole } from '../models/role.model';
import { IUser } from '../models/user.model';

/**
 * Role service for handling role-related operations
 */
export class RoleService {
  /**
   * Create a new role
   */
  async createRole(roleData: Partial<IRole>, createdBy: string): Promise<IRole> {
    try {
      // Check if role code already exists
      const existingRole = await RoleModel.findOne({
        code: roleData.code,
        deletedAt: { $exists: false },
      });

      if (existingRole) {
        throw new Error(`Role with code '${roleData.code}' already exists`);
      }

      // Validate permissions
      if (roleData.permissions && roleData.permissions.length > 0) {
        const permissions = await PermissionModel.find({
          code: { $in: roleData.permissions },
          deletedAt: { $exists: false },
        });

        if (permissions.length !== roleData.permissions.length) {
          throw new Error('One or more permissions do not exist');
        }
      }

      // Create new role
      const role = new RoleModel({
        name: roleData.name,
        description: roleData.description,
        code: roleData.code,
        permissions: roleData.permissions || [],
        isSystem: roleData.isSystem || false,
        isActive: roleData.isActive !== undefined ? roleData.isActive : true,
        priority: roleData.priority || 100,
        parentId: roleData.parentId,
        maxUsers: roleData.maxUsers,
        isAssignable: roleData.isAssignable !== undefined ? roleData.isAssignable : true,
        autoAssignConditions: roleData.autoAssignConditions,
        createdBy,
        updatedBy: createdBy,
      });

      await role.save();
      return role;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all roles
   */
  async getAllRoles(options: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
    isSystem?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ roles: IRole[]; total: number; page: number; limit: number; pages: number }> {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        isActive,
        isSystem,
        sortBy = 'priority',
        sortOrder = 'asc',
      } = options;

      // Build query
      const query: any = {
        deletedAt: { $exists: false },
      };

      // Add search filter
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { code: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
        ];
      }

      // Add isActive filter
      if (isActive !== undefined) {
        query.isActive = isActive;
      }

      // Add isSystem filter
      if (isSystem !== undefined) {
        query.isSystem = isSystem;
      }

      // Count total documents
      const total = await RoleModel.countDocuments(query);

      // Calculate pagination
      const pages = Math.ceil(total / limit);
      const skip = (page - 1) * limit;

      // Build sort object
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute query
      const roles = await RoleModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);

      return {
        roles,
        total,
        page,
        limit,
        pages,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get role by ID
   */
  async getRoleById(id: string): Promise<IRole> {
    try {
      const role = await RoleModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!role) {
        throw new Error('Role not found');
      }

      return role;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update role
   */
  async updateRole(id: string, roleData: Partial<IRole>, updatedBy: string): Promise<IRole> {
    try {
      const role = await RoleModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!role) {
        throw new Error('Role not found');
      }

      // Prevent updating system roles
      if (role.isSystem && !roleData.permissions) {
        throw new Error('System roles cannot be modified except for permissions');
      }

      // Validate permissions
      if (roleData.permissions && roleData.permissions.length > 0) {
        const permissions = await PermissionModel.find({
          code: { $in: roleData.permissions },
          deletedAt: { $exists: false },
        });

        if (permissions.length !== roleData.permissions.length) {
          throw new Error('One or more permissions do not exist');
        }
      }

      // Update allowed fields
      if (roleData.name) role.name = roleData.name;
      if (roleData.description) role.description = roleData.description;
      if (roleData.permissions) role.permissions = roleData.permissions;
      if (roleData.isActive !== undefined) role.isActive = roleData.isActive;
      if (roleData.priority !== undefined) role.priority = roleData.priority;
      if (roleData.parentId !== undefined) role.parentId = roleData.parentId;
      if (roleData.maxUsers !== undefined) role.maxUsers = roleData.maxUsers;
      if (roleData.isAssignable !== undefined) role.isAssignable = roleData.isAssignable;
      if (roleData.autoAssignConditions !== undefined) role.autoAssignConditions = roleData.autoAssignConditions;

      role.updatedBy = updatedBy;
      await role.save();

      return role;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete role
   */
  async deleteRole(id: string, deletedBy: string): Promise<boolean> {
    try {
      const role = await RoleModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!role) {
        throw new Error('Role not found');
      }

      // Prevent deleting system roles
      if (role.isSystem) {
        throw new Error('System roles cannot be deleted');
      }

      // Check if role is assigned to any users
      const usersWithRole = await UserModel.countDocuments({
        roles: id,
        deletedAt: { $exists: false },
      });

      if (usersWithRole > 0) {
        throw new Error(`Cannot delete role assigned to ${usersWithRole} users`);
      }

      // Soft delete
      role.deletedAt = new Date();
      role.deletedBy = deletedBy;
      role.isActive = false;
      await role.save();

      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Assign roles to user
   */
  async assignRolesToUser(userId: string, roleIds: string[], updatedBy: string): Promise<IUser> {
    try {
      // Find user
      const user = await UserModel.findOne({
        id: userId,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Validate roles
      const roles = await RoleModel.find({
        id: { $in: roleIds },
        isActive: true,
        isAssignable: true,
        deletedAt: { $exists: false },
      });

      if (roles.length !== roleIds.length) {
        throw new Error('One or more roles do not exist or are not assignable');
      }

      // Update user roles
      user.roles = roleIds;
      user.updatedBy = updatedBy;
      await user.save();

      return user;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get user roles
   */
  async getUserRoles(userId: string): Promise<IRole[]> {
    try {
      // Find user
      const user = await UserModel.findOne({
        id: userId,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Get roles
      const roles = await RoleModel.find({
        id: { $in: user.roles },
        deletedAt: { $exists: false },
      });

      return roles;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if user has role
   */
  async userHasRole(userId: string, roleCode: string): Promise<boolean> {
    try {
      // Find user
      const user = await UserModel.findOne({
        id: userId,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Find role by code
      const role = await RoleModel.findOne({
        code: roleCode,
        deletedAt: { $exists: false },
      });

      if (!role) {
        throw new Error(`Role '${roleCode}' not found`);
      }

      // Check if user has role
      return user.roles.includes(role.id);
    } catch (error) {
      throw error;
    }
  }
}

export default new RoleService();
