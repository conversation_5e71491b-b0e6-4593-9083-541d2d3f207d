import React, { useState, useEffect } from 'react';
import useTranslation from '../hooks/useTranslation';
import { SUPPORTED_LANGUAGES } from '../utils/bhashiniTranslation';

// This is an example of how to integrate the translation functionality
// with your existing MessagesPage component

const MessagesPageWithTranslation = () => {
  // Your existing state and props
  const [messages, setMessages] = useState<any[]>([]);
  const [currentMessage, setCurrentMessage] = useState<string>('');
  
  // Translation hook
  const {
    sourceLang,
    targetLang,
    isTranslating,
    translationEnabled,
    error: translationError,
    translate,
    handleLanguageChange,
    toggleTranslation
  } = useTranslation({
    defaultSourceLang: 'en',
    defaultTargetLang: 'hi',
    autoTranslate: false
  });

  // Translate messages when language changes or translation is toggled
  useEffect(() => {
    if (!translationEnabled || messages.length === 0) return;
    
    const translateMessages = async () => {
      // Create a copy of messages to avoid modifying state directly
      const translatedMessages = await Promise.all(
        messages.map(async (msg) => {
          // Only translate messages that are not from the current user
          if (!msg.isCurrentUser) {
            const translatedContent = await translate(msg.content);
            return { ...msg, content: translatedContent };
          }
          return msg;
        })
      );
      
      setMessages(translatedMessages);
    };
    
    translateMessages();
  }, [targetLang, translationEnabled, translate]);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!currentMessage.trim()) return;
    
    // Add the current user's message
    const userMessage = {
      id: Date.now().toString(),
      content: currentMessage,
      timestamp: new Date(),
      isCurrentUser: true
    };
    
    setMessages(prev => [...prev, userMessage]);
    setCurrentMessage('');
    
    // Simulate receiving a response
    setTimeout(async () => {
      const responseContent = "This is a sample response.";
      
      // Translate the response if translation is enabled
      const content = translationEnabled 
        ? await translate(responseContent)
        : responseContent;
        
      const responseMessage = {
        id: (Date.now() + 1).toString(),
        content,
        timestamp: new Date(),
        isCurrentUser: false
      };
      
      setMessages(prev => [...prev, responseMessage]);
    }, 1000);
  };

  return (
    <div className="messages-page">
      {/* Language selector */}
      <div className="language-selector">
        <select 
          className="language-select" 
          value={targetLang}
          onChange={(e) => handleLanguageChange(e.target.value)}
          data-translating={isTranslating ? "true" : "false"}
        >
          <option value="en">🇺🇸 English</option>
          <optgroup label="Indian Languages">
            <option value="hi">🇮🇳 Hindi</option>
            <option value="mr">🇮🇳 Marathi</option>
            <option value="gu">🇮🇳 Gujarati</option>
            <option value="ta">🇮🇳 Tamil</option>
            <option value="te">🇮🇳 Telugu</option>
            <option value="bn">🇮🇳 Bengali</option>
            <option value="kn">🇮🇳 Kannada</option>
            <option value="ml">🇮🇳 Malayalam</option>
          </optgroup>
        </select>
        
        {/* Translation toggle */}
        <button 
          className={`translation-toggle ${translationEnabled ? 'active' : ''}`}
          onClick={toggleTranslation}
        >
          {translationEnabled ? 'Translation ON' : 'Translation OFF'}
        </button>
        
        {/* Translation status */}
        {isTranslating && <span className="translating-indicator">Translating...</span>}
        {translationError && <span className="translation-error">{translationError}</span>}
      </div>
      
      {/* Messages container */}
      <div className="messages-container">
        {messages.map((message) => (
          <div 
            key={message.id} 
            className={`message ${message.isCurrentUser ? 'user-message' : 'response-message'}`}
          >
            <div className="message-content">{message.content}</div>
            <div className="message-timestamp">
              {message.timestamp.toLocaleTimeString()}
            </div>
          </div>
        ))}
      </div>
      
      {/* Message input */}
      <div className="message-input-container">
        <input
          type="text"
          value={currentMessage}
          onChange={(e) => setCurrentMessage(e.target.value)}
          placeholder="Type your message..."
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
        />
        <button onClick={handleSendMessage}>Send</button>
      </div>
    </div>
  );
};

export default MessagesPageWithTranslation;
