#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Helper function to colorize output
function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// Helper function to create readline interface
function createReadlineInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
}

// Helper function to ask questions
function askQuestion(rl, question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Check if file exists
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Copy file with content replacement
function copyFileWithReplacements(source, destination, replacements = {}) {
  try {
    let content = fs.readFileSync(source, 'utf8');
    
    // Apply replacements
    Object.keys(replacements).forEach(key => {
      const regex = new RegExp(key, 'g');
      content = content.replace(regex, replacements[key]);
    });
    
    fs.writeFileSync(destination, content);
    return true;
  } catch (error) {
    console.error(colorize(`Error copying file: ${error.message}`, 'red'));
    return false;
  }
}

// Main setup function
async function setupEnvironment() {
  console.log(colorize('\n🚀 LawEngaxe Environment Setup', 'cyan'));
  console.log(colorize('=====================================\n', 'cyan'));

  const rl = createReadlineInterface();

  try {
    // Check for existing environment files
    const clientEnvExists = fileExists('client/.env');
    const serverEnvExists = fileExists('server/.env');

    console.log(colorize('📋 Environment Status:', 'blue'));
    console.log(`Client .env: ${clientEnvExists ? colorize('✓ Exists', 'green') : colorize('✗ Missing', 'red')}`);
    console.log(`Server .env: ${serverEnvExists ? colorize('✓ Exists', 'green') : colorize('✗ Missing', 'red')}\n`);

    // Setup client environment
    if (!clientEnvExists || (await askQuestion(rl, 'Recreate client .env file? (y/N): ')).toLowerCase() === 'y') {
      console.log(colorize('\n🔧 Setting up client environment...', 'yellow'));
      
      const apiUrl = await askQuestion(rl, 'API Base URL (http://localhost:3001/api/v1): ') || 'http://localhost:3001/api/v1';
      const wsUrl = await askQuestion(rl, 'WebSocket URL (ws://localhost:3001/ws/chat): ') || 'ws://localhost:3001/ws/chat';
      const bhashiniUserId = await askQuestion(rl, 'Bhashini User ID (optional): ') || 'cee60134c6bb4d179efd3fda48ff32fe';
      const bhashiniApiKey = await askQuestion(rl, 'Bhashini API Key (optional): ') || '13a647c84b-2747-4f0c-afcd-2ac8235f5318';

      const clientEnvContent = `# Client Environment Configuration
# API Configuration
VITE_API_BASE_URL=${apiUrl}
VITE_WS_URL=${wsUrl}

# Bhashini API (for translation)
VITE_BHASHINI_USER_ID=${bhashiniUserId}
VITE_BHASHINI_API_KEY=${bhashiniApiKey}
VITE_BHASHINI_COMPUTE_API_URL=https://bhashini.gov.in/api/v1/inference/translation
VITE_BHASHINI_INFERENCE_URL=https://dhruva-api.bhashini.gov.in/services/inference/pipeline
VITE_BHASHINI_AUTHORIZATION=W9QK_zzb7Lnsc_xYAl30Gk64Z8rJU4r_2NBiguZjiMIdkUI_8p-E38M-zc_VVhun

# App Configuration
VITE_APP_NAME=LawEngaxe
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=development
`;

      fs.writeFileSync('client/.env', clientEnvContent);
      console.log(colorize('✓ Client .env created successfully', 'green'));
    }

    // Setup server environment
    if (!serverEnvExists || (await askQuestion(rl, 'Recreate server .env file? (y/N): ')).toLowerCase() === 'y') {
      console.log(colorize('\n🔧 Setting up server environment...', 'yellow'));
      
      const port = await askQuestion(rl, 'Server Port (3001): ') || '3001';
      const mongoUri = await askQuestion(rl, 'MongoDB URI (mongodb://localhost:27017/lawengaxe): ') || 'mongodb://localhost:27017/lawengaxe';
      const jwtSecret = await askQuestion(rl, 'JWT Secret (generate random): ') || generateRandomString(64);
      const cookieSecret = await askQuestion(rl, 'Cookie Secret (generate random): ') || generateRandomString(32);
      const redisHost = await askQuestion(rl, 'Redis Host (localhost): ') || 'localhost';
      const redisPort = await askQuestion(rl, 'Redis Port (6379): ') || '6379';

      const serverEnvContent = `# Server Environment Configuration
# Basic Configuration
NODE_ENV=development
PORT=${port}

# Database Configuration
MONGODB_URI=${mongoUri}

# Security Configuration
JWT_SECRET=${jwtSecret}
COOKIE_SECRET=${cookieSecret}

# Redis Configuration
REDIS_HOST=${redisHost}
REDIS_PORT=${redisPort}

# Bhashini API Configuration
BHASHINI_USER_ID=${bhashiniUserId || 'cee60134c6bb4d179efd3fda48ff32fe'}
BHASHINI_ULCA_API_KEY=${bhashiniApiKey || '13a647c84b-2747-4f0c-afcd-2ac8235f5318'}

# Typesense Configuration
TYPESENSE_HOST=localhost
TYPESENSE_PORT=8108
TYPESENSE_PROTOCOL=http
TYPESENSE_API_KEY=xyz

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# Email Configuration (optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=LawEngaxe

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads
`;

      fs.writeFileSync('server/.env', serverEnvContent);
      console.log(colorize('✓ Server .env created successfully', 'green'));
    }

    console.log(colorize('\n✅ Environment setup completed!', 'green'));
    console.log(colorize('\n📝 Next Steps:', 'blue'));
    console.log('1. Install dependencies: npm run install');
    console.log('2. Install Python dependencies: npm run install:python');
    console.log('3. Start development: npm run dev');
    console.log('4. Review ENV_SETUP_GUIDE.md for detailed configuration');

  } catch (error) {
    console.error(colorize(`\n❌ Setup failed: ${error.message}`, 'red'));
  } finally {
    rl.close();
  }
}

// Generate random string for secrets
function generateRandomString(length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Create scripts directory if it doesn't exist
if (!fs.existsSync('scripts')) {
  fs.mkdirSync('scripts');
}

// Run setup if this script is executed directly
if (require.main === module) {
  setupEnvironment();
}

module.exports = { setupEnvironment };
