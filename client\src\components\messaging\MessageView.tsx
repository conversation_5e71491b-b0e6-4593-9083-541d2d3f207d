
import { useState, useRef, useEffect, useCallback } from 'react';
import { useMessages } from '@/context/MessageContext';
import { formatDistance } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { Mic, MicOff, Languages, Send, MoreVertical, Circle } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Message } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useInView } from 'react-intersection-observer';

export default function MessageView() {
  const {
    activeConversation,
    messages,
    sendTextMessage,
    sendVoiceMessage,
    translateMessage,
    translateAllMessages,
    isLoading,
    error,
    currentLanguage,
    setCurrentLanguage
  } = useMessages();
  const [messageText, setMessageText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioRecorder, setAudioRecorder] = useState<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const [visibleMessages, setVisibleMessages] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  // Track which messages are currently being translated
  const [translatingMessages, setTranslatingMessages] = useState<Set<string>>(new Set());

  // Languages available for translation
  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'mr', name: 'Marathi', flag: '🇮🇳' },
    { code: 'gu', name: 'Gujarati', flag: '🇮🇳' },
    { code: 'ta', name: 'Tamil', flag: '🇮🇳' },
    { code: 'te', name: 'Telugu', flag: '🇮🇳' },
    { code: 'bn', name: 'Bengali', flag: '🇮🇳' },
    { code: 'kn', name: 'Kannada', flag: '🇮🇳' },
    { code: 'ml', name: 'Malayalam', flag: '🇮🇳' },
    { code: 'pa', name: 'Punjabi', flag: '🇮🇳' },
    { code: 'ur', name: 'Urdu', flag: '🇮🇳' },
    { code: 'or', name: 'Odia', flag: '🇮🇳' },
    { code: 'as', name: 'Assamese', flag: '🇮🇳' },
    { code: 'es', name: 'Spanish', flag: '��' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
    { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
    { code: 'ru', name: 'Russian', flag: '🇷🇺' }
  ];

  // Function to translate only visible messages
  const translateVisibleMessages = useCallback((targetLang: string) => {
    if (targetLang === 'en') return;

    // Get all visible message IDs
    const visibleMessageIds = Array.from(visibleMessages);

    // Find the corresponding messages
    const messagesToTranslate = messages.filter(msg =>
      visibleMessageIds.includes(msg.id)
    );

    // Mark these messages as being translated
    setTranslatingMessages(prev => {
      const newSet = new Set(prev);
      messagesToTranslate.forEach(msg => newSet.add(msg.id));
      return newSet;
    });

    // Translate each visible message
    messagesToTranslate.forEach(msg => {
      // Since translateMessage returns void, we can't chain .then
      translateMessage(msg.id, targetLang);

      // Set a timeout to remove from translating set
      setTimeout(() => {
        setTranslatingMessages(prev => {
          const newSet = new Set(prev);
          newSet.delete(msg.id);
          return newSet;
        });
      }, 1000); // Assume translation takes about 1 second
    });
  }, [messages, visibleMessages, translateMessage]);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Effect to translate visible messages when language changes
  useEffect(() => {
    if (currentLanguage !== 'en' && visibleMessages.size > 0) {
      translateVisibleMessages(currentLanguage);
    }
  }, [currentLanguage, visibleMessages, translateVisibleMessages]);

  const handleSendMessage = () => {
    if (!activeConversation || !messageText.trim()) return;

    sendTextMessage(messageText.trim(), activeConversation.id);
    setMessageText('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);

      audioChunksRef.current = [];
      recorder.ondataavailable = (e) => {
        audioChunksRef.current.push(e.data);
      };

      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/mp3' });
        if (activeConversation) {
          sendVoiceMessage(audioBlob, activeConversation.id);
        }

        // Stop all audio tracks
        stream.getTracks().forEach(track => track.stop());
      };

      setAudioRecorder(recorder);
      recorder.start();
      setIsRecording(true);

      toast({
        title: "Recording started",
        description: "Your voice message is being recorded.",
      });
    } catch (err) {
      console.error("Error accessing microphone:", err);
      toast({
        title: "Microphone access denied",
        description: "Please allow microphone access to send voice messages.",
        variant: "destructive"
      });
    }
  };

  const stopRecording = () => {
    if (audioRecorder) {
      audioRecorder.stop();
      setIsRecording(false);
      setAudioRecorder(null);

      toast({
        title: "Voice message captured",
        description: "Your voice message has been sent.",
      });
    }
  };

  const handleTranslate = (message: Message, targetLang: string) => {
    translateMessage(message.id, targetLang);

    toast({
      title: "Message translated",
      description: `Translated to ${languages.find(lang => lang.code === targetLang)?.name || targetLang}`,
    });
  };

  const handleTranslateAll = (targetLang: string) => {
    translateAllMessages(targetLang);

    toast({
      title: "All messages translated",
      description: `Chat translated to ${languages.find(lang => lang.code === targetLang)?.name || targetLang}`,
    });
  };

  const handleChangeUserLanguage = (langCode: string) => {
    setCurrentLanguage(langCode);

    // Only translate visible messages first
    if (langCode !== 'en') {
      // Translate visible messages immediately
      translateVisibleMessages(langCode);
    } else {
      // If switching to English, reset all translations
      handleTranslateAll('en');
    }

    toast({
      title: "Chat language changed",
      description: `Now using ${languages.find(lang => lang.code === langCode)?.name || langCode}`,
    });
  };

  // Function to translate only visible messages
  const translateVisibleMessages = useCallback((targetLang: string) => {
    if (targetLang === 'en') return;

    // Get all visible message IDs
    const visibleMessageIds = Array.from(visibleMessages);

    // Find the corresponding messages
    const messagesToTranslate = messages.filter(msg =>
      visibleMessageIds.includes(msg.id)
    );

    // Mark these messages as being translated
    setTranslatingMessages(prev => {
      const newSet = new Set(prev);
      messagesToTranslate.forEach(msg => newSet.add(msg.id));
      return newSet;
    });

    // Translate each visible message
    messagesToTranslate.forEach(msg => {
      // Since translateMessage returns void, we can't chain .then
      translateMessage(msg.id, targetLang);

      // Set a timeout to remove from translating set
      setTimeout(() => {
        setTranslatingMessages(prev => {
          const newSet = new Set(prev);
          newSet.delete(msg.id);
          return newSet;
        });
      }, 1000); // Assume translation takes about 1 second
    });
  }, [messages, visibleMessages, translateMessage]);

  if (!activeConversation) {
    return (
      <div className="h-full flex items-center justify-center bg-lingstream-card">
        <div className="text-center p-6">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-700 mb-4">
            <Languages className="h-8 w-8 text-gray-300" />
          </div>
          <h3 className="text-lg font-medium mb-2">Select a conversation</h3>
          <p className="text-sm text-lingstream-muted">
            Choose a conversation from the list to start chatting in multiple languages
          </p>
        </div>
      </div>
    );
  }

  const participant = activeConversation.participants[0];

  // Log the participant information to verify what we're working with
  console.log('Participant in MessageView:', participant);
  console.log('Active conversation:', activeConversation);
  console.log('Messages:', messages);

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-3 border-b border-gray-700 flex items-center justify-between">
        <div className="flex items-center">
          <Avatar className="h-10 w-10 mr-2">
            <AvatarImage src={participant.avatar} />
            <AvatarFallback>{participant.username[0]}</AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium flex items-center">
              {participant.username}
              {participant.isOnline && (
                <Circle className="h-2 w-2 fill-green-500 text-green-500 ml-2" />
              )}
            </div>
            <div className="text-xs text-lingstream-muted flex items-center gap-1">
              <span className="h-2 w-2 rounded-full bg-orange-500"></span>
              <span>Channel Creator</span>
              <span className="mx-1">•</span>
              <span>{participant.isOnline ? 'Online' : 'Offline'}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1 mr-2"
            onClick={() => {
              const dropdown = document.getElementById('language-dropdown-trigger');
              if (dropdown) dropdown.click();
            }}
          >
            <span>
              {languages.find(lang => lang.code === currentLanguage)?.flag || '🌐'}
            </span>
            <span className="text-xs font-medium">
              {languages.find(lang => lang.code === currentLanguage)?.name || 'English'}
            </span>
            <Languages className="h-3 w-3 ml-1" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" id="language-dropdown-trigger">
                <Languages className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Translate Chat</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div className="px-2 py-1.5 text-xs text-lingstream-muted">
                Select a language to translate all messages
              </div>
              {languages.map((lang) => (
                <DropdownMenuItem
                  key={lang.code}
                  className={currentLanguage === lang.code ? "bg-lingstream-hover" : ""}
                  onClick={() => handleChangeUserLanguage(lang.code)}
                >
                  <span className="mr-2">{lang.flag}</span>
                  {lang.name}
                  {currentLanguage === lang.code && (
                    <span className="ml-2 h-2 w-2 rounded-full bg-lingstream-accent" />
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          <Button variant="ghost" size="icon">
            <MoreVertical className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Translation indicator */}
      {currentLanguage !== 'en' && (
        <div className="px-4 py-2 bg-yellow-100 dark:bg-yellow-900 border-b border-yellow-200 dark:border-yellow-800 flex items-center justify-between">
          <div className="flex items-center">
            <Languages className="h-4 w-4 mr-2 text-yellow-600 dark:text-yellow-400" />
            <span className="text-sm text-yellow-800 dark:text-yellow-200">
              Messages are being translated to {languages.find(lang => lang.code === currentLanguage)?.name || 'another language'}
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200"
            onClick={() => handleChangeUserLanguage('en')}
          >
            Reset to English
          </Button>
        </div>
      )}

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {isLoading && messages.length === 0 && (
            <div className="flex justify-center py-10">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-lingstream-accent"></div>
            </div>
          )}

          {error && (
            <div className="flex justify-center py-10">
              <div className="text-red-500 text-center">
                <p className="font-medium">Error loading messages</p>
                <p className="text-sm">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => {
                    // Clear error and try to refetch messages
                    setError(null);
                    if (activeConversation) {
                      fetchMessages();
                    }
                  }}
                >
                  Retry
                </Button>
              </div>
            </div>
          )}

          {!isLoading && (!messages || messages.length === 0) && !error && (
            <div className="flex justify-center py-10 text-center text-lingstream-muted">
              <div>
                <p className="mb-2">No messages yet</p>
                <p className="text-sm">Send a message to start the conversation</p>
              </div>
            </div>
          )}

          {messages && messages.length > 0 && messages.map((message) => {
            const isOwnMessage = message.sender.id === 'self';

            // Create a ref for each message to track visibility
            const { ref, inView } = useInView({
              threshold: 0.1, // Message is considered visible when 10% is in view
              triggerOnce: false, // Keep tracking visibility changes
              onChange: (inView) => {
                // Update the set of visible messages
                setVisibleMessages(prev => {
                  const newSet = new Set(prev);
                  if (inView) {
                    newSet.add(message.id);
                  } else {
                    newSet.delete(message.id);
                  }
                  return newSet;
                });

                // If message becomes visible and we're in a non-English language,
                // translate it if it's not already translated
                if (inView && currentLanguage !== 'en' &&
                    !message.isTranslated &&
                    !translatingMessages.has(message.id)) {
                  translateMessage(message.id, currentLanguage);
                }
              }
            });

            return (
              <div
                ref={ref}
                key={message.id}
                className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
              >
                <div className="flex items-end gap-2 max-w-[80%]">
                  {!isOwnMessage && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={message.sender.avatar} />
                      <AvatarFallback>{message.sender.username[0]}</AvatarFallback>
                    </Avatar>
                  )}

                  <div
                    className={`rounded-lg px-4 py-2 ${
                      isOwnMessage
                        ? 'bg-lingstream-accent text-white'
                        : 'bg-lingstream-card'
                    } ${message.isTranslated ? 'border-l-2 border-yellow-400' : ''}`}
                  >
                    <div className="flex flex-col">
                      {message.isVoice ? (
                        <div className="flex flex-col items-center gap-1 min-w-[150px]">
                          <div className="w-full">
                            <audio src={message.audioUrl} controls className="w-full h-8" />
                          </div>
                          {message.isTranslated && (
                            <div className="text-xs mt-1">
                              {message.content}
                            </div>
                          )}
                        </div>
                      ) : (
                        <>
                          {/* Always show the original message first when translated */}
                          {message.isTranslated && currentLanguage !== 'en' && (
                            <div className="mb-2 text-lingstream-muted">
                              <div className="flex items-center mb-1">
                                <span className="inline-block h-2 w-2 rounded-full bg-gray-400 mr-1"></span>
                                <span className="text-xs font-medium">Original (English)</span>
                              </div>
                              <p className="text-sm">{message.originalContent}</p>
                            </div>
                          )}

                          {/* Show the translated message with a language indicator */}
                          <div className={message.isTranslated && currentLanguage !== 'en' ? "mt-1" : ""}>
                            {message.isTranslated && currentLanguage !== 'en' && (
                              <div className="flex items-center mb-1">
                                <span className="inline-block h-2 w-2 rounded-full bg-yellow-400 mr-1"></span>
                                <span className="text-xs font-medium">
                                  {languages.find(lang => lang.code === currentLanguage)?.name || currentLanguage}
                                </span>
                              </div>
                            )}
                            <p className={message.isTranslated && currentLanguage !== 'en' ? "text-sm font-medium" : ""}>
                              {translatingMessages.has(message.id) ? (
                                <span className="inline-flex items-center">
                                  <span className="animate-pulse mr-2">Translating...</span>
                                  <span className="animate-spin h-3 w-3 border-b-2 border-lingstream-accent rounded-full"></span>
                                </span>
                              ) : message.content}
                            </p>
                          </div>
                        </>
                      )}

                      <div className="text-xs text-lingstream-muted mt-1">
                        {formatDistance(new Date(message.timestamp), new Date(), { addSuffix: true })}
                      </div>
                    </div>
                  </div>

                  {/* Translation dropdown for all messages */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-7 w-7">
                        <Languages className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuLabel>Translate message</DropdownMenuLabel>
                      {languages.map((lang) => (
                        <DropdownMenuItem
                          key={lang.code}
                          onClick={() => handleTranslate(message, lang.code)}
                          className={message.isTranslated && message.originalLanguage === lang.code ? "bg-lingstream-hover" : ""}
                        >
                          <span className="mr-2">{lang.flag}</span> {lang.name}
                          {message.isTranslated && message.originalLanguage === lang.code && (
                            <span className="ml-2 h-2 w-2 rounded-full bg-lingstream-accent" />
                          )}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input box */}
      <div className="p-3 border-t border-gray-700">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className={isRecording ? "text-red-500 bg-red-500 bg-opacity-20" : ""}
            onClick={isRecording ? stopRecording : startRecording}
            title={isRecording ? "Stop recording" : "Start voice recording"}
          >
            {isRecording ? <MicOff className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
          </Button>

          <Input
            value={messageText}
            onChange={(e) => setMessageText(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder={isRecording ? "Recording..." : `Type a message in ${languages.find(lang => lang.code === currentLanguage)?.name || "your language"}...`}
            disabled={isRecording}
            className="flex-1"
          />

          <Button
            onClick={handleSendMessage}
            disabled={!messageText.trim() || isRecording}
          >
            <Send className="h-4 w-4 mr-2" />
            Send
          </Button>
        </div>
      </div>
    </div>
  );
}
