import React, { useState, useEffect } from 'react';
import { useN<PERSON><PERSON>, use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { Home, ChevronRight, Save, ArrowLeft, Trash2, Upload, Globe, Link as LinkIcon, AlertCircle } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface VideoData {
  id: number;
  videoId: string;
  title: string;
  description?: string;
  category: string;
  source: string;
  privacy: string;
  addedBy: string;
  approved: string;
  thumbnail?: string;
  languages?: { code: string; name: string; flag: string; url?: string }[];
  tags?: string[];
  duration?: string;
  views?: number;
  likes?: number;
  createdAt?: string;
}

export default function EditVideoPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  // Check if we're creating a new video or editing an existing one
  const isNewVideo = id === 'new';

  // Mock data for demonstration
  const [video, setVideo] = useState<VideoData>(isNewVideo ? {
    id: 0,
    videoId: '',
    title: '',
    description: '',
    category: '',
    source: 'YouTube',
    privacy: 'Public',
    addedBy: 'Current User',
    approved: '0',
    thumbnail: '',
    languages: [
      { code: 'en', name: 'English', flag: '🇺🇸', url: '' }
    ],
    tags: [],
    duration: '',
    views: 0,
    likes: 0,
    createdAt: new Date().toISOString()
  } : {
    id: 757,
    videoId: 'h8SN9aB7J91Deb6',
    title: 'UNO: You Play! Interactive Video Game',
    description: 'Learn how to play UNO with this interactive tutorial. This video guides you through the rules and strategies of the popular card game.',
    category: 'Gaming',
    source: 'YouTube',
    privacy: 'Public',
    addedBy: 'avatar_Sharliya',
    approved: '1',
    thumbnail: '/placeholder.svg',
    languages: [
      { code: 'en', name: 'English', flag: '🇺🇸', url: 'https://engaxe.com/videos/en-123' },
      { code: 'es', name: 'Spanish', flag: '🇪🇸', url: 'https://engaxe.com/videos/es-123' },
    ],
    tags: ['gaming', 'card games', 'tutorial', 'interactive'],
    duration: '15:30',
    views: 12500,
    likes: 450,
    createdAt: '2023-10-15T14:30:00Z'
  });

  // Form state
  const [title, setTitle] = useState(video.title);
  const [description, setDescription] = useState(video.description || '');
  const [category, setCategory] = useState(video.category);
  const [privacy, setPrivacy] = useState(video.privacy);
  const [approved, setApproved] = useState(video.approved === '1');
  const [tags, setTags] = useState(video.tags?.join(', ') || '');
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState(video.thumbnail);
  const [languages, setLanguages] = useState(video.languages || []);

  // Categories
  const categories = [
    'Gaming', 'Education', 'Entertainment', 'Music', 'Sports',
    'News', 'Technology', 'Travel', 'Cooking', 'Film & Animation'
  ];

  // Privacy options
  const privacyOptions = ['Public', 'Private', 'Unlisted'];

  // Available languages
  const availableLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'it', name: 'Italian', flag: '🇮🇹' },
    { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
    { code: 'ru', name: 'Russian', flag: '🇷🇺' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
    { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
  ];

  // Load video data and initialize form state
  useEffect(() => {
    // In a real app, you would fetch the video data from an API
    // For now, we're using the mock data initialized above

    // Initialize form state from video data
    setTitle(video.title);
    setDescription(video.description || '');
    setCategory(video.category);
    setPrivacy(video.privacy);
    setApproved(video.approved === '1');
    setTags(video.tags?.join(', ') || '');
    setThumbnailPreview(video.thumbnail);
    setLanguages(video.languages || []);
  }, [video]);

  // Handle thumbnail upload
  const handleThumbnailUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      alert("Please upload an image file");
      return;
    }

    // Set the thumbnail file
    setThumbnailFile(file);

    // Create a URL for the thumbnail preview
    const objectUrl = URL.createObjectURL(file);
    setThumbnailPreview(objectUrl);
  };

  // Add a new language
  const addLanguage = () => {
    // Find a language that's not already in the list
    const unusedLanguages = availableLanguages.filter(
      lang => !languages.some(l => l.code === lang.code)
    );

    if (unusedLanguages.length === 0) {
      alert("All available languages have been added");
      return;
    }

    // Add the first unused language
    setLanguages([...languages, { ...unusedLanguages[0], url: '' }]);
  };

  // Update a language
  const updateLanguage = (index: number, field: 'code' | 'url', value: string) => {
    const updatedLanguages = [...languages];

    if (field === 'code') {
      // Find the language details
      const langDetails = availableLanguages.find(l => l.code === value);
      if (langDetails) {
        updatedLanguages[index] = { ...langDetails, url: updatedLanguages[index].url || '' };
      }
    } else {
      updatedLanguages[index] = { ...updatedLanguages[index], [field]: value };
    }

    setLanguages(updatedLanguages);
  };

  // Remove a language
  const removeLanguage = (index: number) => {
    const updatedLanguages = [...languages];
    updatedLanguages.splice(index, 1);
    setLanguages(updatedLanguages);
  };

  // Handle save
  const handleSave = () => {
    // Validate form
    if (!title) {
      alert('Please enter a title');
      return;
    }

    if (!category) {
      alert('Please select a category');
      return;
    }

    setIsLoading(true);

    // Create updated video object
    const updatedVideo: VideoData = {
      ...video,
      title,
      description,
      category,
      privacy,
      approved: approved ? '1' : '0',
      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
      languages
    };

    // For new videos, generate a unique ID and videoId
    if (isNewVideo) {
      updatedVideo.id = Date.now();
      updatedVideo.videoId = `v-${Math.random().toString(36).substring(2, 10)}`;
      updatedVideo.createdAt = new Date().toISOString();
    }

    // In a real app, you would send this data to an API
    console.log(isNewVideo ? 'Creating new video:' : 'Updating video:', updatedVideo);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      // Navigate back to the videos list
      navigate('/admin/videos/manage');
    }, 1000);
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <div>
                <Button
                  variant="ghost"
                  className="mb-2"
                  onClick={() => navigate('/admin/videos/manage')}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Videos
                </Button>
                <h1 className="text-2xl font-bold">{isNewVideo ? 'Add New Video' : 'Edit Video'}</h1>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={() => navigate('/admin/videos/manage')}>
                  Cancel
                </Button>
                <Button onClick={handleSave} disabled={isLoading}>
                  {isLoading ? (
                    <>Saving...</>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {isNewVideo ? 'Create Video' : 'Save Changes'}
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="/admin/videos" className="text-muted-foreground hover:text-foreground">
                Videos
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="/admin/videos/manage" className="text-muted-foreground hover:text-foreground">
                Manage Videos
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">{isNewVideo ? 'Add New Video' : 'Edit Video'}</span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Main content - 2/3 width on desktop */}
              <div className="md:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>{isNewVideo ? 'New Video Information' : 'Video Information'}</CardTitle>
                    <CardDescription>{isNewVideo ? 'Enter the basic information for your new video' : 'Edit the basic information for this video'}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                      <TabsList className="grid grid-cols-3 mb-6">
                        <TabsTrigger value="basic">Basic Info</TabsTrigger>
                        <TabsTrigger value="languages">Languages</TabsTrigger>
                        <TabsTrigger value="advanced">Advanced</TabsTrigger>
                      </TabsList>

                      <TabsContent value="basic" className="space-y-4">
                        {/* Title */}
                        <div className="space-y-2">
                          <Label htmlFor="title" className="flex items-center">
                            Title <span className="text-red-500 ml-1">*</span>
                          </Label>
                          <Input
                            id="title"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                            placeholder="Enter video title"
                          />
                        </div>

                        {/* Description */}
                        <div className="space-y-2">
                          <Label htmlFor="description">Description</Label>
                          <Textarea
                            id="description"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            placeholder="Enter video description"
                            rows={5}
                          />
                        </div>

                        {/* Category */}
                        <div className="space-y-2">
                          <Label htmlFor="category" className="flex items-center">
                            Category <span className="text-red-500 ml-1">*</span>
                          </Label>
                          <Select value={category} onValueChange={setCategory}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              {categories.map((cat) => (
                                <SelectItem key={cat} value={cat}>
                                  {cat}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Tags */}
                        <div className="space-y-2">
                          <Label htmlFor="tags">Tags</Label>
                          <Input
                            id="tags"
                            value={tags}
                            onChange={(e) => setTags(e.target.value)}
                            placeholder="Enter tags separated by commas"
                          />
                          <p className="text-xs text-muted-foreground">
                            Separate tags with commas (e.g., gaming, tutorial, card games)
                          </p>
                        </div>

                        {/* Thumbnail */}
                        <div className="space-y-2">
                          <Label htmlFor="thumbnail">Thumbnail</Label>
                          <div className="flex flex-col gap-4">
                            {thumbnailPreview && (
                              <div className="relative aspect-video w-full max-w-md overflow-hidden rounded-md border">
                                <img
                                  src={thumbnailPreview}
                                  alt="Video thumbnail"
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            )}
                            <div>
                              <Input
                                id="thumbnail"
                                type="file"
                                accept="image/*"
                                onChange={handleThumbnailUpload}
                                className="cursor-pointer"
                              />
                              <p className="text-xs text-muted-foreground mt-1">
                                Recommended size: 1280x720 pixels (16:9 aspect ratio)
                              </p>
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="languages" className="space-y-4">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-lg font-medium">Available Languages</h3>
                          <Button onClick={addLanguage} size="sm">
                            <Globe className="mr-2 h-4 w-4" />
                            Add Language
                          </Button>
                        </div>

                        {languages.length === 0 ? (
                          <Alert>
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>No languages added</AlertTitle>
                            <AlertDescription>
                              Add at least one language to make this video accessible to more viewers.
                            </AlertDescription>
                          </Alert>
                        ) : (
                          <div className="space-y-4">
                            {languages.map((lang, index) => (
                              <div key={index} className="flex items-end gap-4 p-4 border rounded-md">
                                <div className="w-1/3">
                                  <Label className="mb-2 block">Language</Label>
                                  <Select
                                    value={lang.code}
                                    onValueChange={(value) => updateLanguage(index, 'code', value)}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select language" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {availableLanguages.map((l) => (
                                        <SelectItem
                                          key={l.code}
                                          value={l.code}
                                          disabled={languages.some(
                                            (existingLang, i) => existingLang.code === l.code && i !== index
                                          )}
                                        >
                                          <div className="flex items-center gap-2">
                                            <span>{l.flag}</span>
                                            <span>{l.name}</span>
                                          </div>
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div className="flex-1">
                                  <Label htmlFor={`lang-url-${index}`} className="mb-2 block">Video URL</Label>
                                  <Input
                                    id={`lang-url-${index}`}
                                    value={lang.url || ''}
                                    onChange={(e) => updateLanguage(index, 'url', e.target.value)}
                                    placeholder="https://engaxe.com/videos/123"
                                  />
                                </div>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => removeLanguage(index)}
                                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </TabsContent>

                      <TabsContent value="advanced" className="space-y-4">
                        {/* Privacy */}
                        <div className="space-y-2">
                          <Label htmlFor="privacy">Privacy</Label>
                          <Select value={privacy} onValueChange={setPrivacy}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select privacy setting" />
                            </SelectTrigger>
                            <SelectContent>
                              {privacyOptions.map((option) => (
                                <SelectItem key={option} value={option}>
                                  {option}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Approval Status */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="approved">Approved</Label>
                            <Switch
                              id="approved"
                              checked={approved}
                              onCheckedChange={setApproved}
                            />
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Approved videos are visible to all users. Unapproved videos are only visible to admins.
                          </p>
                        </div>

                        {/* Video ID - Only show for existing videos */}
                        {!isNewVideo && (
                          <div className="space-y-2">
                            <Label htmlFor="videoId">Video ID</Label>
                            <div className="flex">
                              <Input
                                id="videoId"
                                value={video.videoId}
                                readOnly
                                className="bg-muted"
                              />
                              <Button variant="ghost" size="icon" className="ml-2" onClick={() => {
                                navigator.clipboard.writeText(video.videoId);
                                alert('Video ID copied to clipboard');
                              }}>
                                <LinkIcon className="h-4 w-4" />
                              </Button>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              This is a unique identifier for this video and cannot be changed.
                            </p>
                          </div>
                        )}

                        {/* Danger Zone - Only show for existing videos */}
                        {!isNewVideo && (
                          <div className="mt-8 pt-6 border-t">
                            <h3 className="text-lg font-medium text-red-600 mb-4">Danger Zone</h3>
                            <Button variant="destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Video
                            </Button>
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar - 1/3 width on desktop */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Video Preview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="aspect-video w-full overflow-hidden rounded-md bg-muted mb-4">
                      {thumbnailPreview ? (
                        <img
                          src={thumbnailPreview}
                          alt="Video thumbnail"
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <p className="text-muted-foreground">No thumbnail</p>
                        </div>
                      )}
                    </div>
                    <h3 className="font-medium mb-2">{title || 'Untitled Video'}</h3>
                    <p className="text-sm text-muted-foreground line-clamp-3 mb-4">
                      {description || 'No description'}
                    </p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {tags.split(',').map((tag, index) => (
                        tag.trim() && (
                          <Badge key={index} variant="secondary">
                            {tag.trim()}
                          </Badge>
                        )
                      ))}
                    </div>
                    <Separator className="my-4" />
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Category:</span>
                        <span className="text-sm font-medium">{category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Privacy:</span>
                        <span className="text-sm font-medium">{privacy}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Status:</span>
                        <span className="text-sm font-medium">
                          {approved ? (
                            <Badge variant="success" className="bg-green-100 text-green-800">Approved</Badge>
                          ) : (
                            <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Languages:</span>
                        <div className="flex space-x-1">
                          {languages.map((lang, index) => (
                            <span key={index} title={lang.name}>{lang.flag}</span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Only show stats for existing videos */}
                {!isNewVideo && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Video Stats</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Views:</span>
                          <span className="text-sm font-medium">{video.views?.toLocaleString() || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Likes:</span>
                          <span className="text-sm font-medium">{video.likes?.toLocaleString() || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Duration:</span>
                          <span className="text-sm font-medium">{video.duration || 'Unknown'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Added by:</span>
                          <div className="flex items-center">
                            <Avatar className="h-5 w-5 mr-1">
                              <AvatarImage src="/avatar.jpg" alt={video.addedBy} />
                              <AvatarFallback>{video.addedBy.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <span className="text-sm font-medium">
                              {video.addedBy.split('_')[1] || video.addedBy}
                            </span>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Added on:</span>
                          <span className="text-sm font-medium">
                            {video.createdAt
                              ? new Date(video.createdAt).toLocaleDateString()
                              : 'Unknown'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Help card for new videos */}
                {isNewVideo && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Adding a New Video</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4 text-sm">
                        <p>Follow these steps to add a new video:</p>
                        <ol className="list-decimal pl-5 space-y-2">
                          <li>Fill in the basic information (title, description, category)</li>
                          <li>Add at least one language version</li>
                          <li>Upload a thumbnail image</li>
                          <li>Set privacy and approval settings</li>
                          <li>Click the "Create Video" button to save</li>
                        </ol>
                        <p className="text-muted-foreground mt-4">
                          All fields marked with an asterisk (*) are required.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
