
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Light theme (default) */
  :root {
    --background: 0 0% 100%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;


    --primary: 24 95% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;


    --accent: 24 95% 50%;

    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --ring: 24 95% 50%;


    --radius: 0.5rem;
  }


  /* Dark theme */

  .dark {
    --background: 217 33% 17%;
    --foreground: 222 47% 95%;

    --card: 217 33% 17%;
    --card-foreground: 222 47% 95%;

    --popover: 217 33% 17%;
    --popover-foreground: 222 47% 95%;

    --primary: 24 95% 55%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217 32% 17%;
    --secondary-foreground: 222 47% 95%;

    --muted: 217 32% 17%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 24 95% 55%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;

    --ring: 24 95% 55%;

  }
}

@layer base {
  body {
    @apply bg-lingstream-background text-lingstream-text;
  }

  /* Hide scrollbar by default but show when scrolling */
  .scrollbar-hide {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  /* Show scrollbar only when scrolling */
  .scrollbar-auto-hide {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    transition: all 0.3s ease;
  }

  .scrollbar-auto-hide::-webkit-scrollbar {
    width: 8px;
    background: transparent;
    display: none;
  }

  /* Show scrollbar when the is-scrolling class is added via JS */
  .scrollbar-auto-hide.is-scrolling::-webkit-scrollbar {
    display: block;
  }

  .scrollbar-auto-hide::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 4px;
  }

  .scrollbar-auto-hide::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.7);
  }

  /* Firefox scrollbar styles */
  .scrollbar-auto-hide.is-scrolling {
    scrollbar-width: thin;
  }

  .video-category-card {
    @apply bg-gradient-to-br transition-transform hover:scale-105 cursor-pointer flex flex-col items-center justify-center gap-2 rounded-xl p-4;
  }

  .message-bubble {
    @apply rounded-2xl px-4 py-2 max-w-[80%];
  }

  .message-bubble-sent {
    @apply bg-lingstream-accent text-white ml-auto;
  }

  .message-bubble-received {
    @apply bg-lingstream-card mr-auto;
  }

  .dropdown-language {
    @apply absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-lingstream-card text-lingstream-text ring-1 ring-border ring-opacity-5 z-50;
  }
}
