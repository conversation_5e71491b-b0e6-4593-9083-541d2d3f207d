import { FastifyInstance, FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { ChannelController } from '../controllers/channel.controller';
import {
  CreateChannelSchema,
  UpdateChannelSchema,
  GetChannelSchema,
  GetChannelsSchema,
  GetChannelByNameSchema,
  GetUserChannelsSchema,
  DeleteChannelSchema,
  SubscribeChannelSchema
} from '../schemas/channel.schema';
import { authenticate } from '../plugins/authenticate';

const channelRoutes: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  const channelController = new ChannelController();

  // Create a new channel (authenticated)
  fastify.post<{
    Body: any
  }>(
    '/',
    {
      schema: CreateChannelSchema,
      preHandler: authenticate,
    },
    (request, reply) => channelController.createChannel(request as any, reply)
  );

  // Update an existing channel (authenticated)
  fastify.put<{
    Params: { id: string },
    Body: any
  }>(
    '/:id',
    {
      schema: UpdateChannelSchema,
      preHandler: authenticate,
    },
    (request, reply) => channelController.updateChannel(request as any, reply)
  );

  // Get a channel by ID
  fastify.get<{
    Params: { id: string }
  }>(
    '/:id',
    {
      schema: GetChannelSchema,
    },
    (request, reply) => channelController.getChannelById(request as any, reply)
  );

  // Get a channel by name
  fastify.get<{
    Params: { name: string }
  }>(
    '/by-name/:name',
    {
      schema: GetChannelByNameSchema,
    },
    (request, reply) => channelController.getChannelByName(request as any, reply)
  );

  // Get channels with pagination and filtering
  fastify.get<{
    Querystring: any
  }>(
    '/',
    {
      schema: GetChannelsSchema,
    },
    (request, reply) => channelController.getChannels(request as any, reply)
  );

  // Get channels for the current authenticated user
  // This route must come before the '/user/:userId' route to avoid conflicts
  fastify.get<{}>(
    '/user/me',
    {
      schema: GetUserChannelsSchema,
      preHandler: authenticate,
    },
    (request, reply) => channelController.getCurrentUserChannels(request as any, reply)
  );

  // Get channels by user ID
  fastify.get<{
    Params: { userId: string }
  }>(
    '/user/:userId',
    {
      schema: GetUserChannelsSchema,
    },
    (request, reply) => channelController.getUserChannels(request as any, reply)
  );

  // Delete a channel (authenticated)
  fastify.delete<{
    Params: { id: string }
  }>(
    '/:id',
    {
      schema: DeleteChannelSchema,
      preHandler: authenticate,
    },
    (request, reply) => channelController.deleteChannel(request as any, reply)
  );

  // Subscribe or unsubscribe from a channel (authenticated)
  fastify.post<{
    Params: { id: string }
  }>(
    '/:id/subscribe',
    {
      schema: SubscribeChannelSchema,
      preHandler: authenticate,
    },
    (request, reply) => channelController.toggleSubscription(request as any, reply)
  );
};

export default channelRoutes;
