import React from 'react';
import Layout from '@/components/layout/Layout';
import GuaranteedVideoPlayer from '@/components/video/GuaranteedVideoPlayer';

/**
 * A test page that displays multiple videos to verify that each video plays its own content.
 */
const VideoTestPage: React.FC = () => {
  // List of video IDs to test
  const videoIds = [
    'XLcMq2', // C++ Basics
    'xW36l7', // Basic Math Test Quiz
    'suZKhW', // Game Theory
    'wollzl', // Avyay Hindi Grammar
    'axHkJa', // Karak Hindi Grammar
    'KxyzuN', // AI Animation Tutorial
    '4OE4QR', // FREE 3D Cartoon Animation
    'gu99XD', // AI Video Generator
    'fgp97y', // Sample Video
    'X4eW1I', // Another Sample Video
    'c1AObf', // New Video
    '99BXqa', // Another New Video
  ];

  return (
    <Layout>
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-8 text-center">Video Test Page</h1>
        <p className="text-center mb-8">
          This page displays multiple videos to verify that each video plays its own content.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {videoIds.map((videoId) => (
            <div key={videoId} className="bg-card rounded-lg overflow-hidden shadow-lg">
              <div className="aspect-video">
                <GuaranteedVideoPlayer videoId={videoId} />
              </div>
              <div className="p-4">
                <h2 className="text-xl font-semibold mb-2">Video ID: {videoId}</h2>
                <p className="text-sm text-muted-foreground">
                  This video should play its own content.
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Layout>
  );
};

export default VideoTestPage;
