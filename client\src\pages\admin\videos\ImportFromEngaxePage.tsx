import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Home, Database, Search, ChevronRight, Download, RefreshCw, ArrowLeft, Globe, Filter, Languages, Info } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';

interface EngaxeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail?: string;
  duration: string;
  views: number;
  category: string;
  languages: { code: string; name: string; flag: string }[];
  date: string;
  author: string;
}

export default function ImportFromEngaxePage() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedVideos, setSelectedVideos] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [activeTab, setActiveTab] = useState('browse');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLanguage, setSelectedLanguage] = useState('all');
  const [autoTranslate, setAutoTranslate] = useState(true);

  // Sample data for Engaxe videos
  const engaxeVideos: EngaxeVideo[] = [
    {
      id: 'eng-001',
      title: 'Understanding Legal Rights in Employment',
      description: 'A comprehensive guide to understanding your legal rights in the workplace, including discrimination, harassment, and wrongful termination.',
      thumbnail: '/placeholder.svg',
      duration: '18:45',
      views: 12500,
      category: 'Employment Law',
      languages: [
        { code: 'en', name: 'English', flag: '🇺🇸' },
        { code: 'es', name: 'Spanish', flag: '🇪🇸' }
      ],
      date: '2023-10-15',
      author: 'Legal Expert Network'
    },
    {
      id: 'eng-002',
      title: 'Family Law Basics: Divorce Proceedings',
      description: 'Learn about the divorce process, child custody, alimony, and property division in this informative video.',
      thumbnail: '/placeholder.svg',
      duration: '22:10',
      views: 8750,
      category: 'Family Law',
      languages: [
        { code: 'en', name: 'English', flag: '🇺🇸' },
        { code: 'fr', name: 'French', flag: '🇫🇷' }
      ],
      date: '2023-09-22',
      author: 'Family Law Associates'
    },
    {
      id: 'eng-003',
      title: 'Immigration Law: Visa Application Process',
      description: 'A step-by-step guide to the visa application process, including required documentation and common pitfalls to avoid.',
      thumbnail: '/placeholder.svg',
      duration: '25:30',
      views: 15200,
      category: 'Immigration Law',
      languages: [
        { code: 'en', name: 'English', flag: '🇺🇸' },
        { code: 'es', name: 'Spanish', flag: '🇪🇸' },
        { code: 'zh', name: 'Chinese', flag: '🇨🇳' }
      ],
      date: '2023-11-05',
      author: 'Global Immigration Consultants'
    },
    {
      id: 'eng-004',
      title: 'Criminal Defense: Know Your Rights',
      description: 'Essential information about your rights when interacting with law enforcement and navigating the criminal justice system.',
      thumbnail: '/placeholder.svg',
      duration: '19:15',
      views: 10800,
      category: 'Criminal Law',
      languages: [
        { code: 'en', name: 'English', flag: '🇺🇸' }
      ],
      date: '2023-08-18',
      author: 'Criminal Defense Network'
    }
  ];

  // Categories from the videos
  const categories = ['all', ...Array.from(new Set(engaxeVideos.map(video => video.category)))];

  // Languages from the videos
  const languages = [
    { code: 'all', name: 'All Languages', flag: '🌐' },
    ...Array.from(
      new Set(
        engaxeVideos.flatMap(video => video.languages)
          .map(lang => JSON.stringify(lang))
      )
    ).map(lang => JSON.parse(lang))
  ];

  // Filter videos based on search term, category, and language
  const filteredVideos = engaxeVideos.filter(video => {
    const matchesSearch = video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         video.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || video.category === selectedCategory;
    const matchesLanguage = selectedLanguage === 'all' ||
                           video.languages.some(lang => lang.code === selectedLanguage);

    return matchesSearch && matchesCategory && matchesLanguage;
  });

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedVideos(filteredVideos.map(video => video.id));
    } else {
      setSelectedVideos([]);
    }
    setSelectAll(checked);
  };

  const handleSelectVideo = (id: string) => {
    const isSelected = selectedVideos.includes(id);
    let newSelected: string[];

    if (isSelected) {
      newSelected = selectedVideos.filter(videoId => videoId !== id);
    } else {
      newSelected = [...selectedVideos, id];
    }

    setSelectedVideos(newSelected);
    setSelectAll(newSelected.length === filteredVideos.length && filteredVideos.length > 0);
  };

  const handleImportSelected = () => {
    if (selectedVideos.length === 0) {
      alert('Please select at least one video to import');
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    // Simulate import progress
    const interval = setInterval(() => {
      setImportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsImporting(false);

          // Show success message or redirect
          alert(`Successfully imported ${selectedVideos.length} videos from Engaxe`);
          setSelectedVideos([]);
          setSelectAll(false);

          return 100;
        }
        return prev + 5;
      });
    }, 200);
  };

  const handleRefresh = () => {
    setIsLoading(true);

    // Simulate API call to refresh videos
    setTimeout(() => {
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <div>
                <Button
                  variant="ghost"
                  className="mb-2"
                  onClick={() => navigate('/admin/videos/import')}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Import Options
                </Button>
                <h1 className="text-2xl font-bold">Import from Engaxe</h1>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
                  {isLoading ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4" />
                  )}
                  <span className="ml-2">Refresh</span>
                </Button>
                <Button
                  onClick={handleImportSelected}
                  disabled={selectedVideos.length === 0 || isImporting}
                >
                  {isImporting ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Importing...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Import Selected ({selectedVideos.length})
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="/admin/videos" className="text-muted-foreground hover:text-foreground">
                Videos
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="/admin/videos/import" className="text-muted-foreground hover:text-foreground">
                Import Videos
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Import from Engaxe</span>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Import Videos from Engaxe</CardTitle>
                <CardDescription>
                  Browse and import videos from the Engaxe legal video library
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid grid-cols-2 mb-6">
                    <TabsTrigger value="browse">Browse Videos</TabsTrigger>
                    <TabsTrigger value="settings">Import Settings</TabsTrigger>
                  </TabsList>

                  <TabsContent value="browse" className="space-y-6">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="relative flex-1">
                        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search videos by title or description..."
                          className="pl-10"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                      </div>

                      <div className="flex gap-2">
                        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                          <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Categories</SelectItem>
                            {categories.filter(cat => cat !== 'all').map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                          <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Select language" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Languages</SelectItem>
                            {languages.filter(lang => lang.code !== 'all').map((language) => (
                              <SelectItem key={language.code} value={language.code}>
                                <div className="flex items-center">
                                  <span className="mr-2">{language.flag}</span>
                                  <span>{language.name}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="select-all"
                          checked={selectAll && filteredVideos.length > 0}
                          onCheckedChange={handleSelectAll}
                          disabled={filteredVideos.length === 0}
                        />
                        <Label htmlFor="select-all">Select All</Label>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {filteredVideos.length} videos found
                      </div>
                    </div>

                    {filteredVideos.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-12 text-center">
                        <Database className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">No videos found</h3>
                        <p className="text-muted-foreground">
                          Try adjusting your search or filters to find videos
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {filteredVideos.map((video) => (
                          <div
                            key={video.id}
                            className={`border rounded-lg overflow-hidden transition-all ${
                              selectedVideos.includes(video.id)
                                ? 'border-primary ring-2 ring-primary ring-opacity-50'
                                : 'border-border hover:border-primary/50'
                            }`}
                          >
                            <div className="relative aspect-video bg-muted flex items-center justify-center">
                              {video.thumbnail ? (
                                <img
                                  src={video.thumbnail}
                                  alt={video.title}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <Database size={40} className="text-muted-foreground" />
                              )}
                              <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                                {video.duration}
                              </div>
                              <div className="absolute top-2 left-2 flex space-x-1">
                                {video.languages.map((lang) => (
                                  <span
                                    key={lang.code}
                                    title={lang.name}
                                    className="w-6 h-6 flex items-center justify-center bg-black bg-opacity-70 rounded-full text-xs"
                                  >
                                    {lang.flag}
                                  </span>
                                ))}
                              </div>
                            </div>
                            <div className="p-4">
                              <div className="flex items-start gap-3">
                                <Checkbox
                                  checked={selectedVideos.includes(video.id)}
                                  onCheckedChange={() => handleSelectVideo(video.id)}
                                  className="mt-1"
                                />
                                <div>
                                  <h3 className="font-medium line-clamp-2 mb-1">{video.title}</h3>
                                  <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                                    {video.description}
                                  </p>
                                  <div className="flex flex-wrap gap-2 mb-2">
                                    <Badge variant="secondary">{video.category}</Badge>
                                    <Badge variant="outline" className="text-xs">
                                      {video.views.toLocaleString()} views
                                    </Badge>
                                  </div>
                                  <div className="flex items-center text-xs text-muted-foreground">
                                    <span>Added {new Date(video.date).toLocaleDateString()}</span>
                                    <span className="mx-2">•</span>
                                    <span>{video.author}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="settings" className="space-y-6">
                    <div className="grid gap-6">
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">Language Settings</h3>
                        <Separator />

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="auto-translate">Auto-translate missing languages</Label>
                            <p className="text-sm text-muted-foreground">
                              Automatically translate videos to all supported languages
                            </p>
                          </div>
                          <Switch
                            id="auto-translate"
                            checked={autoTranslate}
                            onCheckedChange={setAutoTranslate}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Target Languages</Label>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {languages.filter(lang => lang.code !== 'all').map((language) => (
                              <div key={language.code} className="flex items-center space-x-2">
                                <Checkbox id={`lang-${language.code}`} defaultChecked={language.code === 'en'} />
                                <Label htmlFor={`lang-${language.code}`} className="flex items-center">
                                  <span className="mr-2">{language.flag}</span>
                                  <span>{language.name}</span>
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">Import Options</h3>
                        <Separator />

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="auto-approve">Auto-approve imported videos</Label>
                            <p className="text-sm text-muted-foreground">
                              Automatically approve videos after import
                            </p>
                          </div>
                          <Switch id="auto-approve" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="keep-stats">Import view statistics</Label>
                            <p className="text-sm text-muted-foreground">
                              Keep original view counts and engagement metrics
                            </p>
                          </div>
                          <Switch id="keep-stats" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="download-thumbnails">Download thumbnails</Label>
                            <p className="text-sm text-muted-foreground">
                              Download and store thumbnail images locally
                            </p>
                          </div>
                          <Switch id="download-thumbnails" defaultChecked />
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">API Configuration</h3>
                        <Separator />

                        <div className="space-y-2">
                          <Label htmlFor="api-key">Engaxe API Key</Label>
                          <Input id="api-key" type="password" value="••••••••••••••••" />
                          <p className="text-xs text-muted-foreground">
                            Your Engaxe API key is used to authenticate requests to the Engaxe platform
                          </p>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="api-endpoint">API Endpoint</Label>
                          <Input id="api-endpoint" defaultValue="https://api.engaxe.com/v1" />
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                {isImporting && (
                  <div className="mt-6 space-y-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>Importing videos...</span>
                      <span>{importProgress}%</span>
                    </div>
                    <Progress value={importProgress} className="h-2" />
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Videos imported: {Math.floor((selectedVideos.length * importProgress) / 100)}</span>
                      <span>Remaining: {selectedVideos.length - Math.floor((selectedVideos.length * importProgress) / 100)}</span>
                    </div>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-6">
                <Alert className="w-full">
                  <Info className="h-4 w-4" />
                  <AlertTitle>About Engaxe Import</AlertTitle>
                  <AlertDescription className="text-xs">
                    Importing videos from Engaxe will maintain all metadata, including titles, descriptions,
                    and categories. Videos will be available in all languages that are supported by the original content.
                  </AlertDescription>
                </Alert>
              </CardFooter>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
