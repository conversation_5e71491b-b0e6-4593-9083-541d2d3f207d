import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { ThumbsDown, ArrowUpDown, Download, Filter, Search, Calendar, User } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Sample video dislikes data
const videoDislikesData = [
  {
    id: 1,
    videoId: 'qRt7vBnH3jK4mP9',
    title: 'How to Cook Perfect Pasta Every Time',
    dislikes: 5,
    dislikePercentage: '9%',
    category: 'Food',
    creator: 'CookingPro',
    date: '2023-12-11'
  },
  {
    id: 2,
    videoId: 'kLm9pQrT8jD2nS5',
    title: 'Learn JavaScript in 30 Minutes',
    dislikes: 4,
    dislikePercentage: '11%',
    category: 'Education',
    creator: 'CodeMaster',
    date: '2023-12-12'
  },
  {
    id: 3,
    videoId: 'vPwWsQ4fBCT7hF3',
    title: 'PhD | All of Would in Detail..',
    dislikes: 3,
    dislikePercentage: '13%',
    category: 'Education',
    creator: 'Muskan Dindoriya',
    date: '2023-12-14'
  },
  {
    id: 4,
    videoId: 'h8SN9aB7J91Deb6',
    title: 'UNO: You Play! Interactive Video Game',
    dislikes: 2,
    dislikePercentage: '8%',
    category: 'Gaming',
    creator: 'Bhariya',
    date: '2023-12-15'
  },
  {
    id: 5,
    videoId: 'xStzlCwln6yMMnLM',
    title: 'PLAY UNO WITH YOUR KEYBOARD..',
    dislikes: 1,
    dislikePercentage: '6%',
    category: 'Gaming',
    creator: 'Bhariya',
    date: '2023-12-13'
  }
];

// Summary stats
const summaryStats = {
  totalDislikes: 18,
  avgDislikesPerVideo: 3.6,
  mostDislikedVideo: 'How to Cook Perfect Pasta Every Time',
  mostDislikedCategory: 'Food',
  dislikesReduction: 2
};

export default function VideoDislikesPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [sortColumn, setSortColumn] = useState('dislikes');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // Get unique categories for filter
  const categories = ['all', ...new Set(videoDislikesData.map(video => video.category))];

  // Filter and sort data
  const filteredData = videoDislikesData.filter(video => {
    const matchesSearch = 
      video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      video.creator.toLowerCase().includes(searchTerm.toLowerCase()) ||
      video.videoId.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = 
      categoryFilter === 'all' || 
      video.category === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  const sortedData = [...filteredData].sort((a, b) => {
    const aValue = a[sortColumn as keyof typeof a];
    const bValue = b[sortColumn as keyof typeof b];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      if (sortDirection === 'asc') {
        return aValue.localeCompare(bValue);
      } else {
        return bValue.localeCompare(aValue);
      }
    } else {
      if (sortDirection === 'asc') {
        return Number(aValue) - Number(bValue);
      } else {
        return Number(bValue) - Number(aValue);
      }
    }
  });

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('desc');
    }
  };

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <main className="flex-1 p-6 overflow-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold">Total Video Dislikes</h1>
              <p className="text-muted-foreground">Track and analyze negative feedback through video dislikes</p>
            </div>
            <Button className="flex items-center gap-2">
              <Download size={16} />
              Export Data
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Dislikes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{summaryStats.totalDislikes.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground mt-1">Across all videos</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Avg. Dislikes Per Video</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{summaryStats.avgDislikesPerVideo.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground mt-1">Platform average</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Most Disliked</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold truncate">{summaryStats.mostDislikedVideo}</div>
                <p className="text-xs text-muted-foreground mt-1">Highest negative feedback</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Dislikes Reduction</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-500">-{summaryStats.dislikesReduction}%</div>
                <p className="text-xs text-muted-foreground mt-1">Compared to last period</p>
              </CardContent>
            </Card>
          </div>

          <div className="bg-card rounded-lg shadow-sm border mb-6">
            <div className="p-6">
              <div className="flex flex-col md:flex-row gap-4 mb-6 justify-between">
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search videos..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-[180px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Filter by Category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>
                          {category === 'all' ? 'All Categories' : category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[100px]">Video ID</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('dislikes')}
                      >
                        <div className="flex items-center">
                          Dislikes
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('dislikePercentage')}
                      >
                        <div className="flex items-center">
                          Dislike Percentage
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('category')}
                      >
                        <div className="flex items-center">
                          Category
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('creator')}
                      >
                        <div className="flex items-center">
                          Creator
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('date')}
                      >
                        <div className="flex items-center">
                          Date
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedData.map((video) => (
                      <TableRow key={video.id} className="hover:bg-muted/50">
                        <TableCell className="font-medium">{video.videoId.substring(0, 8)}...</TableCell>
                        <TableCell>
                          <div className="font-medium truncate max-w-[250px]">{video.title}</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <ThumbsDown className="mr-2 h-4 w-4 text-red-500" />
                            {video.dislikes.toLocaleString()}
                          </div>
                        </TableCell>
                        <TableCell>{video.dislikePercentage}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{video.category}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <User className="mr-2 h-4 w-4 text-muted-foreground" />
                            {video.creator}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                            {video.date}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
