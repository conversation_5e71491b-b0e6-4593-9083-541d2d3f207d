import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Home, ChevronRight, Mail, Send, AlertCircle, Code, Eye, EyeOff, Users, Calendar, Clock, FileText, Copy, LayoutTemplate, Trash2, Edit, CheckCircle } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

// Newsletter templates
const newsletterTemplates = [
  {
    id: '1',
    name: 'Welcome Template',
    subject: 'Welcome to Our Platform!',
    content: '<h2>Welcome to Our Platform!</h2><p>We are excited to have you join our community. Here are some tips to get started:</p><ul><li>Complete your profile</li><li>Explore our features</li><li>Connect with others</li></ul><p>If you have any questions, feel free to contact our support team.</p>'
  },
  {
    id: '2',
    name: 'Monthly Update Template',
    subject: 'Monthly Update - [Month] [Year]',
    content: '<h2>Monthly Update</h2><p>Here\'s what\'s new this month:</p><ul><li>New feature: [Feature Name]</li><li>Improved performance</li><li>Bug fixes</li></ul><p>Thank you for being a valued subscriber!</p>'
  },
  {
    id: '3',
    name: 'Announcement Template',
    subject: 'Important Announcement',
    content: '<h2>Important Announcement</h2><p>We have some exciting news to share with you!</p><p>[Announcement details go here]</p><p>Stay tuned for more updates.</p>'
  }
];

// Mock data for previous newsletters
const mockPreviousNewsletters = [
  { id: '1', subject: 'New Features Announcement', sentDate: '2023-07-15', recipients: 1250 },
  { id: '2', subject: 'Summer Sale', sentDate: '2023-06-01', recipients: 1500 },
  { id: '3', subject: 'Platform Update', sentDate: '2023-05-10', recipients: 1300 },
];

export default function NewslettersPage() {
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [activeTab, setActiveTab] = useState('compose');
  const [scheduleNewsletter, setScheduleNewsletter] = useState(false);
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [previousNewsletters, setPreviousNewsletters] = useState(mockPreviousNewsletters);

  // Handle template selection
  const handleApplyTemplate = () => {
    if (!selectedTemplate) return;

    const template = newsletterTemplates.find(t => t.id === selectedTemplate);
    if (template) {
      setSubject(template.subject);
      setMessage(template.content);
      setIsTemplateDialogOpen(false);
      setShowPreview(true);
    }
  };

  const handleSendNewsletter = () => {
    // In a real app, this would send the newsletter to all subscribers
    if (!subject.trim()) {
      alert('Please enter a subject for the newsletter');
      return;
    }
    if (!message.trim()) {
      alert('Please enter a message for the newsletter');
      return;
    }

    // Add to newsletter history
    const now = new Date();
    const newNewsletter = {
      id: (Math.max(...previousNewsletters.map(n => parseInt(n.id)), 0) + 1).toString(),
      subject: subject,
      sentDate: now.toISOString().split('T')[0],
      recipients: Math.floor(Math.random() * 1000) + 1000 // Random number for demo
    };

    setPreviousNewsletters([newNewsletter, ...previousNewsletters]);

    // Show success message
    setShowSuccessAlert(true);
    setTimeout(() => setShowSuccessAlert(false), 3000);

    // Reset form
    setSubject('');
    setMessage('');
    setScheduleNewsletter(false);
    setScheduledDate('');
    setScheduledTime('');
    setSelectedTemplate('');

    // Switch to history tab
    setActiveTab('history');
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Newsletters</h1>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Tools
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Newsletters</span>
            </div>

            {showSuccessAlert && (
              <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>
                  {scheduleNewsletter
                    ? `Newsletter scheduled for ${scheduledDate} at ${scheduledTime}.`
                    : 'Newsletter has been sent successfully to all subscribers.'}
                </AlertDescription>
              </Alert>
            )}

            <Tabs defaultValue="compose" className="w-full" onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-2 mb-6">
                <TabsTrigger value="compose">Compose Newsletter</TabsTrigger>
                <TabsTrigger value="history">Newsletter History</TabsTrigger>
              </TabsList>

              <TabsContent value="compose">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="md:col-span-2">
                    <Card>
                      <CardHeader>
                        <CardTitle>Compose Newsletter</CardTitle>
                        <CardDescription>Create and send newsletters to all subscribers</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="flex justify-between items-center mb-4">
                          <Button
                            variant="outline"
                            className="gap-2"
                            onClick={() => setIsTemplateDialogOpen(true)}
                          >
                            <LayoutTemplate className="h-4 w-4" />
                            Use Template
                          </Button>

                          <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
                            <DialogContent className="sm:max-w-[500px]">
                              <DialogHeader>
                                <DialogTitle>Choose a Template</DialogTitle>
                                <DialogDescription>
                                  Select a template to use as a starting point for your newsletter.
                                </DialogDescription>
                              </DialogHeader>
                              <div className="py-4">
                                <RadioGroup value={selectedTemplate} onValueChange={setSelectedTemplate}>
                                  {newsletterTemplates.map((template) => (
                                    <div key={template.id} className="flex items-center space-x-2 border rounded-md p-4 mb-2 hover:bg-muted/50 cursor-pointer">
                                      <RadioGroupItem value={template.id} id={`template-${template.id}`} />
                                      <div className="grid gap-1.5 w-full">
                                        <Label htmlFor={`template-${template.id}`} className="font-medium">{template.name}</Label>
                                        <p className="text-sm text-muted-foreground truncate">{template.subject}</p>
                                      </div>
                                    </div>
                                  ))}
                                </RadioGroup>
                              </div>
                              <DialogFooter>
                                <Button variant="outline" onClick={() => setIsTemplateDialogOpen(false)}>
                                  Cancel
                                </Button>
                                <Button onClick={handleApplyTemplate} className="gap-2">
                                  <CheckCircle className="h-4 w-4" />
                                  Apply Template
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="subject">Subject</Label>
                          <div className="flex items-center space-x-2">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            <Input
                              id="subject"
                              value={subject}
                              onChange={(e) => setSubject(e.target.value)}
                              placeholder="Enter newsletter subject"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="message">Message (HTML Allowed)</Label>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 gap-1"
                              onClick={() => setShowPreview(!showPreview)}
                            >
                              {showPreview ? (
                                <>
                                  <EyeOff className="h-4 w-4" />
                                  Hide Preview
                                </>
                              ) : (
                                <>
                                  <Eye className="h-4 w-4" />
                                  Show Preview
                                </>
                              )}
                            </Button>
                          </div>
                          <Textarea
                            id="message"
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            placeholder="Enter newsletter content (HTML tags are supported)"
                            className="min-h-[200px] font-mono"
                          />
                          <div className="flex items-center text-xs text-muted-foreground">
                            <Code className="h-3 w-3 mr-1" />
                            <span>HTML tags are supported</span>
                          </div>
                        </div>

                        <Separator />

                        <div className="space-y-4">
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="scheduleNewsletter"
                              checked={scheduleNewsletter}
                              onCheckedChange={setScheduleNewsletter}
                            />
                            <Label htmlFor="scheduleNewsletter">Schedule for later</Label>
                          </div>

                          {scheduleNewsletter && (
                            <div className="grid grid-cols-2 gap-4 pl-6 pt-2">
                              <div className="space-y-2">
                                <Label htmlFor="scheduledDate">Date</Label>
                                <div className="flex items-center space-x-2">
                                  <Calendar className="h-4 w-4 text-muted-foreground" />
                                  <Input
                                    id="scheduledDate"
                                    type="date"
                                    value={scheduledDate}
                                    onChange={(e) => setScheduledDate(e.target.value)}
                                  />
                                </div>
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="scheduledTime">Time</Label>
                                <div className="flex items-center space-x-2">
                                  <Clock className="h-4 w-4 text-muted-foreground" />
                                  <Input
                                    id="scheduledTime"
                                    type="time"
                                    value={scheduledTime}
                                    onChange={(e) => setScheduledTime(e.target.value)}
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-end">
                        <Button onClick={handleSendNewsletter} className="gap-2">
                          <Send className="h-4 w-4" />
                          {scheduleNewsletter ? 'Schedule Newsletter' : 'Send Newsletter'}
                        </Button>
                      </CardFooter>
                    </Card>
                  </div>

                  <div>
                    {showPreview ? (
                      <Card>
                        <CardHeader>
                          <CardTitle>Preview</CardTitle>
                          <CardDescription>How your newsletter will appear to subscribers</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="border rounded-lg p-4 bg-white">
                            <h3 className="text-lg font-bold mb-4">{subject || 'Newsletter Subject'}</h3>
                            <div className="prose prose-sm max-w-none">
                              {message ? (
                                <div dangerouslySetInnerHTML={{ __html: message }} />
                              ) : (
                                <p className="text-muted-foreground">Your newsletter content will appear here...</p>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ) : (
                      <Card>
                        <CardHeader>
                          <CardTitle>Newsletter Info</CardTitle>
                          <CardDescription>Information about your newsletter</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <h3 className="text-sm font-medium mb-2">Recipients</h3>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span>All subscribers (approximately 1,500)</span>
                            </div>
                          </div>
                          <Separator />
                          <div>
                            <h3 className="text-sm font-medium mb-2">Status</h3>
                            <Badge variant="outline" className="bg-muted">
                              {scheduleNewsletter ? 'Scheduled' : 'Draft'}
                            </Badge>
                          </div>
                          {scheduleNewsletter && scheduledDate && scheduledTime && (
                            <>
                              <Separator />
                              <div>
                                <h3 className="text-sm font-medium mb-2">Scheduled For</h3>
                                <div className="flex items-center gap-2">
                                  <Calendar className="h-4 w-4 text-muted-foreground" />
                                  <span>{scheduledDate}</span>
                                  <Clock className="h-4 w-4 text-muted-foreground ml-2" />
                                  <span>{scheduledTime}</span>
                                </div>
                              </div>
                            </>
                          )}
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <CardTitle>Newsletter History</CardTitle>
                    <CardDescription>View previously sent newsletters</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {previousNewsletters.length > 0 ? (
                      <div className="space-y-4">
                        {previousNewsletters.map((newsletter) => (
                          <Card key={newsletter.id} className="overflow-hidden">
                            <div className="p-4 border-b bg-muted/30 flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                <Mail className="h-4 w-4 text-primary" />
                                <h3 className="font-medium">{newsletter.subject}</h3>
                              </div>
                              <Button variant="outline" size="sm" className="gap-2">
                                <FileText className="h-4 w-4" />
                                View
                              </Button>
                            </div>
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between text-sm text-muted-foreground">
                                <div className="flex items-center gap-4">
                                  <div className="flex items-center">
                                    <Calendar className="h-3 w-3 mr-1" />
                                    <span>{newsletter.sentDate}</span>
                                  </div>
                                  <div className="flex items-center">
                                    <Users className="h-3 w-3 mr-1" />
                                    <span>{newsletter.recipients} recipients</span>
                                  </div>
                                </div>
                                <Badge variant="outline">Sent</Badge>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <Mail className="h-12 w-12 mx-auto mb-2 opacity-20" />
                        <p>No newsletters have been sent yet</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  );
}
