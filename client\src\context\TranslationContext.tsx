import React, { createContext, useContext, useState, useEffect } from 'react';
import { translateText as bhashiniTranslate } from '@/services/bhashiniTranslationService';
import { translateText as simpleTranslate } from '@/utils/simpleTranslator';
import { translateText as apiTranslate } from '@/services/translationApi';

// Define translation provider types
export type TranslationProvider = 'simple' | 'bhashini' | 'google' | 'custom';

// Define translation provider configuration interface
export interface TranslationProviderConfig {
  enabled: boolean;
  apiKey: string;
  userId?: string;
  ulcaApiKey?: string;
  endpoint?: string; // Only needed for custom provider
}

// Define translation providers configuration map
export interface TranslationProvidersConfig {
  simple: TranslationProviderConfig;
  bhashini: TranslationProviderConfig;
  google: TranslationProviderConfig;
  custom: TranslationProviderConfig;
}

// Define translation context interface
interface TranslationContextType {
  isTranslationEnabled: boolean;
  enableTranslation: () => void;
  disableTranslation: () => void;
  translationProvider: TranslationProvider;
  setTranslationProvider: (provider: TranslationProvider) => void;
  translationProviders: TranslationProvidersConfig;
  updateTranslationProvider: (provider: TranslationProvider, config: Partial<TranslationProviderConfig>) => void;
  translateText: (text: string, targetLanguage: string, sourceLanguage?: string) => Promise<string>;
  getEnabledProviders: () => TranslationProvider[];
  currentLanguage: string;
  setCurrentLanguage: (language: string) => void;
  availableLanguages: { code: string; name: string }[];
  clearTranslationCache: () => void;
  getCacheStats: () => { size: number, entries: number };
}

// Create the translation context
const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

// Available languages
const availableLanguages = [
  { code: 'en', name: 'English' },
  { code: 'hi', name: 'Hindi' },
  { code: 'mr', name: 'Marathi' },
  { code: 'gu', name: 'Gujarati' },
  { code: 'ta', name: 'Tamil' },
  { code: 'te', name: 'Telugu' },
  { code: 'bn', name: 'Bengali' },
  { code: 'kn', name: 'Kannada' },
  { code: 'ml', name: 'Malayalam' },
  { code: 'pa', name: 'Punjabi' },
  { code: 'ur', name: 'Urdu' },
  { code: 'or', name: 'Odia' },
  { code: 'as', name: 'Assamese' },
  { code: 'brx', name: 'Bodo' },
  { code: 'doi', name: 'Dogri' },
  { code: 'ks', name: 'Kashmiri' },
  { code: 'kok', name: 'Konkani' },
  { code: 'mai', name: 'Maithili' },
  { code: 'mni', name: 'Manipuri' },
  { code: 'ne', name: 'Nepali' },
  { code: 'sa', name: 'Sanskrit' },
  { code: 'sat', name: 'Santali' },
  { code: 'sd', name: 'Sindhi' },
  { code: 'lus', name: 'Mizo' },
];

// Translation provider component
export function TranslationProvider({ children }: { children: React.ReactNode }) {
  // Translation states
  const [isTranslationEnabled, setIsTranslationEnabled] = useState(true);
  const [translationProvider, setTranslationProvider] = useState<TranslationProvider>('bhashini');
  const [currentLanguage, setCurrentLanguage] = useState('en');

  // Translation providers configuration
  const [translationProviders, setTranslationProviders] = useState<TranslationProvidersConfig>({
    simple: { enabled: false, apiKey: '' },
    bhashini: {
      enabled: true,
      apiKey: 'cee60134c6bb4d179efd3fda48ff32fe', // User ID provided by the user
      userId: 'cee60134c6bb4d179efd3fda48ff32fe', // User ID provided by the user
      ulcaApiKey: '13a647c84b-2747-4f0c-afcd-2ac8235f5318' // Udyat Key provided by the user
    },
    google: { enabled: false, apiKey: '' },
    custom: { enabled: false, apiKey: '', endpoint: '' }
  });

  // Define updateTranslationProvider function before using it in useEffect
  const updateTranslationProvider = (provider: TranslationProvider, config: Partial<TranslationProviderConfig>) => {
    setTranslationProviders(prev => {
      const updatedProviders = {
        ...prev,
        [provider]: {
          ...prev[provider],
          ...config
        }
      };

      return updatedProviders;
    });

    // If any provider is enabled, enable translation
    if (config.enabled) {
      setIsTranslationEnabled(true);
      setTranslationProvider(provider);
    } else if (provider === translationProvider) {
      // Check if any other provider is enabled
      const anyProviderEnabled = Object.entries(translationProviders)
        .filter(([key]) => key !== provider)
        .some(([, config]) => config.enabled);

      if (anyProviderEnabled) {
        // Find the first enabled provider and set it as active
        const firstEnabledProvider = Object.entries(translationProviders)
          .filter(([key, config]) => key !== provider && config.enabled)
          .map(([key]) => key)[0] as TranslationProvider;

        setTranslationProvider(firstEnabledProvider);
      } else {
        setIsTranslationEnabled(false);
      }
    }
  };

  // Force enable Bhashini translation by default
  useEffect(() => {
    console.log("Forcing Bhashini translation to be enabled by default");
    setIsTranslationEnabled(true);
    setTranslationProvider('bhashini');
    setTranslationProviders(prev => ({
      ...prev,
      bhashini: {
        ...prev.bhashini,
        enabled: true,
        apiKey: 'cee60134c6bb4d179efd3fda48ff32fe',
        userId: 'cee60134c6bb4d179efd3fda48ff32fe',
        ulcaApiKey: '13a647c84b-2747-4f0c-afcd-2ac8235f5318'
      }
    }));

    // Save to localStorage
    localStorage.setItem('isTranslationEnabled', 'true');
    localStorage.setItem('translationProvider', 'bhashini');
    localStorage.setItem('translationProviders', JSON.stringify({
      ...translationProviders,
      bhashini: {
        ...translationProviders.bhashini,
        enabled: true,
        apiKey: 'cee60134c6bb4d179efd3fda48ff32fe',
        userId: 'cee60134c6bb4d179efd3fda48ff32fe',
        ulcaApiKey: '13a647c84b-2747-4f0c-afcd-2ac8235f5318'
      }
    }));
  }, []);

  // Load translation settings from localStorage on component mount
  useEffect(() => {
    const storedIsTranslationEnabled = localStorage.getItem('isTranslationEnabled');
    const storedTranslationProvider = localStorage.getItem('translationProvider');
    const storedTranslationProviders = localStorage.getItem('translationProviders');
    const storedCurrentLanguage = localStorage.getItem('currentLanguage');

    if (storedIsTranslationEnabled) {
      setIsTranslationEnabled(storedIsTranslationEnabled === 'true');
    }

    if (storedTranslationProvider) {
      setTranslationProvider(storedTranslationProvider as TranslationProvider);
    }

    if (storedTranslationProviders) {
      try {
        setTranslationProviders(JSON.parse(storedTranslationProviders));
      } catch (error) {
        console.error('Error parsing stored translation providers:', error);
      }
    }

    if (storedCurrentLanguage) {
      setCurrentLanguage(storedCurrentLanguage);
    }
  }, []);

  // Save translation settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem('isTranslationEnabled', isTranslationEnabled.toString());
    localStorage.setItem('translationProvider', translationProvider);
    localStorage.setItem('translationProviders', JSON.stringify(translationProviders));
    localStorage.setItem('currentLanguage', currentLanguage);
  }, [isTranslationEnabled, translationProvider, translationProviders, currentLanguage]);

  // Enable translation
  const enableTranslation = () => {
    setIsTranslationEnabled(true);
  };

  // Disable translation
  const disableTranslation = () => {
    setIsTranslationEnabled(false);
  };



  // Get enabled providers
  const getEnabledProviders = (): TranslationProvider[] => {
    return Object.entries(translationProviders)
      .filter(([, config]) => config.enabled)
      .map(([key]) => key as TranslationProvider);
  };

  // Clear the translation cache
  const clearTranslationCache = () => {
    setTranslationCache({});
    localStorage.removeItem('translationCache');
    console.log('Translation cache cleared');
  };

  // Get cache statistics
  const getCacheStats = () => {
    const entries = Object.keys(translationCache).length;

    // Calculate approximate size in KB
    let size = 0;
    try {
      const cacheString = JSON.stringify(translationCache);
      size = Math.round(cacheString.length / 1024);
    } catch (error) {
      console.error('Error calculating cache size:', error);
    }

    return { size, entries };
  };

  // Translation cache to avoid re-translating the same content
  const [translationCache, setTranslationCache] = useState<Record<string, Record<string, string>>>({});

  // Load cache from localStorage on component mount
  useEffect(() => {
    try {
      const cachedTranslations = localStorage.getItem('translationCache');
      if (cachedTranslations) {
        setTranslationCache(JSON.parse(cachedTranslations));
        console.log('Loaded translation cache from localStorage');
      }
    } catch (error) {
      console.error('Error loading translation cache:', error);
    }
  }, []);

  // Save cache to localStorage when it changes and manage cache size
  useEffect(() => {
    try {
      if (Object.keys(translationCache).length > 0) {
        // Limit cache size to prevent localStorage from getting too large
        const MAX_CACHE_ENTRIES = 500;
        let trimmedCache = { ...translationCache };

        if (Object.keys(trimmedCache).length > MAX_CACHE_ENTRIES) {
          console.log('Trimming translation cache to prevent overflow');
          // Keep only the most recent entries
          const entries = Object.entries(trimmedCache);
          const sortedEntries = entries.sort((a, b) => {
            // If we had timestamps, we could sort by them
            // For now, just keep the shorter texts (likely more common phrases)
            return a[0].length - b[0].length;
          });

          // Keep only the MAX_CACHE_ENTRIES most recent entries
          trimmedCache = Object.fromEntries(sortedEntries.slice(0, MAX_CACHE_ENTRIES));

          // Update the cache state
          setTranslationCache(trimmedCache);
        }

        localStorage.setItem('translationCache', JSON.stringify(trimmedCache));
      }
    } catch (error) {
      console.error('Error saving translation cache:', error);

      // If we get a quota exceeded error, clear the cache
      if (error instanceof DOMException && error.name === 'QuotaExceededError') {
        console.warn('LocalStorage quota exceeded, clearing translation cache');
        setTranslationCache({});
        localStorage.removeItem('translationCache');
      }
    }
  }, [translationCache]);

  // Translate text using the active provider
  const translateText = async (text: string, targetLanguage: string, sourceLanguage: string = 'en'): Promise<string> => {
    if (!isTranslationEnabled || targetLanguage === 'en') {
      return text;
    }

    // Check if we already have this translation in cache
    const cacheKey = `${text}_${sourceLanguage}`;
    if (translationCache[cacheKey] && translationCache[cacheKey][targetLanguage]) {
      console.log('Using cached translation');
      return translationCache[cacheKey][targetLanguage];
    }

    try {
      const activeProvider = translationProvider;
      const providerConfig = translationProviders[activeProvider];

      if (!providerConfig.enabled) {
        console.warn(`Translation provider ${activeProvider} is not enabled`);
        return text;
      }

      switch (activeProvider) {
        case 'bhashini':
          if (!providerConfig.apiKey) {
            console.warn('Bhashini API key is not set');
            return text;
          }

          try {
            console.log(`Translating text using Bhashini API: "${text.substring(0, 30)}..." from ${sourceLanguage} to ${targetLanguage}`);

            // First try the server API
            const result = await apiTranslate(
              text,
              sourceLanguage,
              targetLanguage,
              providerConfig.userId,
              providerConfig.ulcaApiKey
            );

            console.log(`Server API translation successful: "${result.substring(0, 30)}..."`);

            // Save to cache
            const cacheKey = `${text}_${sourceLanguage}`;
            setTranslationCache(prev => ({
              ...prev,
              [cacheKey]: {
                ...(prev[cacheKey] || {}),
                [targetLanguage]: result
              }
            }));

            return result;
          } catch (error) {
            console.warn('Server API translation failed, falling back to client-side implementation:', error);

            // Fall back to client-side implementation
            console.log('Trying client-side Bhashini API implementation...');
            const result = await bhashiniTranslate(
              text,
              sourceLanguage,
              targetLanguage,
              {
                apiKey: providerConfig.apiKey,
                userId: providerConfig.userId,
                ulcaApiKey: providerConfig.ulcaApiKey
              }
            );

            if (result.includes('[Translation Error]')) {
              console.error('Client-side Bhashini API translation failed');
              throw new Error('Both server and client-side Bhashini API translation failed');
            }

            console.log(`Client-side translation successful: "${result.substring(0, 30)}..."`);

            // Save to cache
            const cacheKey = `${text}_${sourceLanguage}`;
            setTranslationCache(prev => ({
              ...prev,
              [cacheKey]: {
                ...(prev[cacheKey] || {}),
                [targetLanguage]: result
              }
            }));

            return result;
          }

        case 'simple':
        default:
          const simpleResult = simpleTranslate(text, targetLanguage);

          // Save to cache
          const cacheKey = `${text}_${sourceLanguage}`;
          setTranslationCache(prev => ({
            ...prev,
            [cacheKey]: {
              ...(prev[cacheKey] || {}),
              [targetLanguage]: simpleResult
            }
          }));

          return simpleResult;
      }
    } catch (error) {
      console.error('Error translating text:', error);

      // Add more detailed error logging
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }

      // Return original text with error indicator for debugging
      return `[Translation Error: ${error instanceof Error ? error.message : 'Unknown error'}] ${text}`;
    }
  };

  // Context value
  const contextValue: TranslationContextType = {
    isTranslationEnabled,
    enableTranslation,
    disableTranslation,
    translationProvider,
    setTranslationProvider,
    translationProviders,
    updateTranslationProvider,
    translateText,
    getEnabledProviders,
    currentLanguage,
    setCurrentLanguage,
    availableLanguages,
    clearTranslationCache,
    getCacheStats
  };

  // Make the context available globally for direct access
  useEffect(() => {
    (window as any).__TRANSLATION_CONTEXT__ = contextValue;
    return () => {
      delete (window as any).__TRANSLATION_CONTEXT__;
    };
  }, [contextValue]);

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  );
}

// Hook to use the translation context
export function useTranslation() {
  const context = useContext(TranslationContext);
  if (context === undefined) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  return context;
}
