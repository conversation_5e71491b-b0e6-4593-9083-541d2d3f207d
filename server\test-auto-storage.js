/**
 * Test automatic video storage functionality
 * This script simulates adding a new video to show automatic storage
 */

const fs = require('fs');
const path = require('path');

// JSON storage paths
const JSON_DIR = path.join(__dirname, 'json', 'videos');
const ALL_VIDEOS_FILE = path.join(JSON_DIR, 'all-videos.json');

/**
 * Simulate adding a new video (like the system does automatically)
 */
function simulateNewVideoUpload() {
  console.log('🎬 Simulating New Video Upload');
  console.log('==============================');
  
  try {
    // Read current JSON data
    const data = fs.readFileSync(ALL_VIDEOS_FILE, 'utf8');
    const jsonData = JSON.parse(data);
    
    console.log(`📊 Current videos before upload: ${jsonData.videos.length}`);
    
    // Create a new test video (simulating what happens when user uploads)
    const newVideo = {
      id: `new_video_${Date.now()}`,
      title: `New Test Video - ${new Date().toLocaleString()}`,
      description: 'This is a new video that was automatically added to demonstrate the automatic JSON storage system. Every time you upload a video, it gets automatically stored here!',
      url: 'XLcMq2',
      thumbnailUrl: 'https://via.placeholder.com/480x360/4CAF50/FFFFFF?text=New+Video',
      duration: 240,
      userId: 'test_user_123',
      channelId: 'test_channel_123',
      visibility: 'public',
      tags: ['new', 'automatic', 'storage', 'test', 'demo'],
      category: 'Technology',
      contentRating: 'general',
      processingStatus: 'ready',
      stats: {
        views: 0,
        likes: 0,
        dislikes: 0,
        comments: 0,
        shares: 0,
        playlistAdds: 0,
        averageWatchTime: 0,
        retentionRate: 0
      },
      file: {
        originalName: 'new-test-video.mp4',
        size: 25165824,
        mimeType: 'video/mp4'
      },
      languages: [
        {
          code: 'en',
          name: 'English',
          flag: '🇺🇸',
          url: 'XLcMq2',
          isDefault: true
        }
      ],
      source: {
        type: 'upload',
        originalUrl: '',
        platform: 'lawengaxe'
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // Auto-generated search keywords
      searchKeywords: ['new', 'test', 'video', 'automatic', 'storage', 'demo', 'technology'],
      watchUrl: `http://localhost:5173/watch?id=new_video_${Date.now()}`
    };
    
    // Create backup (like the system does)
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(JSON_DIR, 'backups', `all-videos-backup-${timestamp}.json`);
    fs.copyFileSync(ALL_VIDEOS_FILE, backupFile);
    console.log(`📦 Created backup: ${path.basename(backupFile)}`);
    
    // Add new video to array
    jsonData.videos.push(newVideo);
    
    // Update metadata
    jsonData.metadata.totalVideos = jsonData.videos.length;
    jsonData.metadata.lastUpdated = new Date().toISOString();
    
    // Write back to file (like the system does automatically)
    fs.writeFileSync(ALL_VIDEOS_FILE, JSON.stringify(jsonData, null, 2));
    
    console.log('✅ New video automatically added to JSON storage!');
    console.log(`📊 Total videos after upload: ${jsonData.videos.length}`);
    console.log(`📝 New video details:`);
    console.log(`   Title: ${newVideo.title}`);
    console.log(`   ID: ${newVideo.id}`);
    console.log(`   Watch URL: ${newVideo.watchUrl}`);
    console.log(`   Keywords: ${newVideo.searchKeywords.join(', ')}`);
    console.log(`   Created: ${newVideo.createdAt}`);
    
    return newVideo;
    
  } catch (error) {
    console.error('❌ Error simulating video upload:', error.message);
  }
}

/**
 * Test search functionality with new video
 */
function testSearchWithNewVideo() {
  console.log('\n🔍 Testing Search with New Video');
  console.log('================================');
  
  try {
    const data = fs.readFileSync(ALL_VIDEOS_FILE, 'utf8');
    const jsonData = JSON.parse(data);
    
    // Test search for "new" keyword
    const searchTerm = 'new';
    const results = jsonData.videos.filter(video => {
      const titleMatch = video.title?.toLowerCase().includes(searchTerm);
      const descMatch = video.description?.toLowerCase().includes(searchTerm);
      const tagsMatch = video.tags?.some(tag => tag.toLowerCase().includes(searchTerm));
      const keywordsMatch = video.searchKeywords?.some(keyword => keyword.includes(searchTerm));
      
      return titleMatch || descMatch || tagsMatch || keywordsMatch;
    });
    
    console.log(`Search for "${searchTerm}": ${results.length} results found`);
    results.forEach((video, index) => {
      console.log(`   ${index + 1}. ${video.title}`);
      console.log(`      URL: ${video.watchUrl}`);
    });
    
  } catch (error) {
    console.error('❌ Error testing search:', error.message);
  }
}

/**
 * Show current status
 */
function showCurrentStatus() {
  console.log('\n📊 Current JSON Storage Status');
  console.log('==============================');
  
  try {
    const data = fs.readFileSync(ALL_VIDEOS_FILE, 'utf8');
    const jsonData = JSON.parse(data);
    
    console.log(`Total Videos: ${jsonData.metadata.totalVideos}`);
    console.log(`Last Updated: ${jsonData.metadata.lastUpdated}`);
    
    // Show latest 3 videos
    console.log('\n📋 Latest Videos:');
    const latestVideos = jsonData.videos
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 3);
      
    latestVideos.forEach((video, index) => {
      console.log(`   ${index + 1}. ${video.title}`);
      console.log(`      Created: ${new Date(video.createdAt).toLocaleString()}`);
      console.log(`      Watch: ${video.watchUrl}`);
    });
    
  } catch (error) {
    console.error('❌ Error showing status:', error.message);
  }
}

/**
 * Show how automatic storage works
 */
function explainAutomaticStorage() {
  console.log('\n🔄 How Automatic Storage Works');
  console.log('==============================');
  console.log('1. User uploads video through platform');
  console.log('2. Video gets saved to MongoDB database');
  console.log('3. 🤖 AUTOMATICALLY: Video also gets added to JSON file');
  console.log('4. Search keywords are auto-generated');
  console.log('5. Watch URL is auto-created');
  console.log('6. Backup is auto-created');
  console.log('7. ✅ Video is immediately searchable!');
  
  console.log('\n📍 Integration Points:');
  console.log('- Video Upload API: /api/v1/videos/upload');
  console.log('- Video Import API: /api/v1/videos/import');
  console.log('- Video Update API: /api/v1/videos/:id');
  console.log('- Video Delete API: /api/v1/videos/:id');
  
  console.log('\n🎯 All these APIs automatically update JSON storage!');
}

/**
 * Main function
 */
function main() {
  console.log('🚀 Automatic Video Storage Test');
  console.log('================================');
  
  showCurrentStatus();
  simulateNewVideoUpload();
  testSearchWithNewVideo();
  explainAutomaticStorage();
  
  console.log('\n✨ Test completed! New video automatically stored.');
  console.log('💡 Real videos will be stored the same way when uploaded through platform.');
}

// Run if executed directly
if (require.main === module) {
  main();
}

module.exports = { simulateNewVideoUpload, testSearchWithNewVideo };
