import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Search, Link } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Language } from '@/types';

interface BasicInfoStepProps {
  engaxeUrl: string;
  setEngaxeUrl: (value: string) => void;
  title: string;
  setTitle: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  thumbnail: string | null;
  setThumbnail: (value: string | null) => void;
  setThumbnailFile: (value: File | null) => void;
  category: string;
  setCategory: (value: string) => void;
  categories: string[];
  defaultLanguage: string;
  setDefaultLanguage: (value: string) => void;
  languages: Language[];
  videoPreviewUrl: string;
  handleFetchVideo: () => void;
  isFetching: boolean;
  videoMetadata: any;
}

export default function BasicInfoStep({
  engaxeUrl,
  setEngaxeUrl,
  title,
  setTitle,
  description,
  setDescription,
  thumbnail,
  setThumbnail,
  setThumbnailFile,
  category,
  setCategory,
  categories,
  defaultLanguage,
  setDefaultLanguage,
  languages,
  videoPreviewUrl,
  handleFetchVideo,
  isFetching,
  videoMetadata
}: BasicInfoStepProps) {
  // State to track if URL has changed since last fetch
  const [urlChanged, setUrlChanged] = useState(false);

  // Utility function to parse duration string to seconds
  const parseDurationToSeconds = (durationStr: any): number => {
    if (!durationStr) return 0;

    // If it's already a number, return it
    if (typeof durationStr === 'number') {
      return durationStr;
    }

    // If it's a string that doesn't contain colons, try to parse as number
    if (typeof durationStr === 'string' && !durationStr.includes(':')) {
      const parsed = parseInt(durationStr, 10);
      return isNaN(parsed) ? 0 : parsed;
    }

    // Handle MM:SS or HH:MM:SS format
    if (typeof durationStr === 'string') {
      const cleanDuration = durationStr.trim();
      const parts = cleanDuration.split(':');

      if (parts.length === 2) {
        // MM:SS format
        const minutes = parseInt(parts[0], 10) || 0;
        const seconds = parseInt(parts[1], 10) || 0;
        return (minutes * 60) + seconds;
      } else if (parts.length === 3) {
        // HH:MM:SS format
        const hours = parseInt(parts[0], 10) || 0;
        const minutes = parseInt(parts[1], 10) || 0;
        const seconds = parseInt(parts[2], 10) || 0;
        return (hours * 3600) + (minutes * 60) + seconds;
      }
    }

    return 0;
  };

  // Utility function to format duration from seconds to display format
  const formatDuration = (duration: any): string => {
    const seconds = parseDurationToSeconds(duration);
    if (!seconds || seconds === 0) return '0:00';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  };

  // Utility function to format view count
  const formatViews = (views: number): string => {
    if (!views || views === 0) return '0 views';

    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M views`;
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K views`;
    } else {
      return `${views} views`;
    }
  };

  // Handle URL change
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setEngaxeUrl(newUrl);
    setUrlChanged(true);
  };

  // Auto-fetch metadata when URL is pasted
  const handleUrlPaste = async (e: React.ClipboardEvent<HTMLInputElement>) => {
    const pastedUrl = e.clipboardData.getData('text');
    if (pastedUrl && pastedUrl.includes('engaxe.com')) {
      // Small delay to ensure the input value is updated
      setTimeout(() => {
        handleFetchVideo();
      }, 100);
    }
  };
  // Function to handle thumbnail upload
  const handleThumbnailUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      alert("Please upload an image file");
      return;
    }

    // Set the thumbnail file
    setThumbnailFile(file);

    // Create a URL for the thumbnail preview
    const objectUrl = URL.createObjectURL(file);
    setThumbnail(objectUrl);
  };

  return (
    <div className="space-y-6">
      {/* Engaxe URL for default language */}
      <div className="space-y-2">
        <label htmlFor="engaxeUrl" className="text-sm font-medium">
          Engaxe URL <span className="text-xs text-lingstream-muted">(Default Language: {languages.find(l => l.code === defaultLanguage)?.name || 'English'})</span>
        </label>
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Input
              id="engaxeUrl"
              value={engaxeUrl}
              onChange={handleUrlChange}
              onPaste={handleUrlPaste}
              placeholder="https://engaxe.com/videos/VIDEOID"
              className="pr-10"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              <Link className="h-4 w-4" />
            </div>
          </div>

          <Button
            type="button"
            onClick={handleFetchVideo}
            disabled={!engaxeUrl || isFetching}
            className="whitespace-nowrap"
          >
            {isFetching ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Fetching...
              </>
            ) : (
              <>
                <Search className="h-4 w-4 mr-2" />
                Fetch
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Video Preview (if available) */}
      {videoPreviewUrl && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Video Preview</label>
          <div className="flex rounded-md overflow-hidden bg-card">
            {/* Left side - Video thumbnail */}
            <div className="w-2/5 relative">
              {thumbnail ? (
                <div className="relative aspect-video w-full group cursor-pointer">
                  <img
                    src={thumbnail}
                    alt="Video thumbnail"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      // If image fails to load, replace with a placeholder
                      const target = e.target as HTMLImageElement;
                      target.onerror = null; // Prevent infinite loop
                      target.src = `https://placehold.co/480x360/333333/FFFFFF?text=Video+Preview`;
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="w-12 h-12 rounded-full bg-white bg-opacity-80 flex items-center justify-center">
                      <div className="w-0 h-0 border-t-6 border-t-transparent border-l-12 border-l-lingstream-accent border-b-6 border-b-transparent ml-1"></div>
                    </div>
                  </div>
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-1 py-0.5 rounded">
                    {videoMetadata?.duration ? formatDuration(videoMetadata.duration) : '0:00'}
                  </div>
                </div>
              ) : (
                <div className="aspect-video w-full">
                  <iframe
                    src={videoPreviewUrl}
                    className="h-full w-full"
                    allowFullScreen
                    title="Video preview"
                  ></iframe>
                </div>
              )}
            </div>

            {/* Right side - Video information */}
            <div className="w-3/5 p-4 flex flex-col justify-between">
              <div>
                <h3 className="font-medium text-lg line-clamp-2">{title || 'Mission: Impossible - Dead Reckoning | The Biggest Stunt...'}</h3>
                <div className="flex items-center mt-2">
                  <span className="text-sm">{videoMetadata?.author || 'Engaxe Creator'}</span>
                  <span className="ml-1 text-xs bg-gray-200 dark:bg-gray-700 rounded-full px-1">✓</span>
                </div>
                <div className="flex items-center mt-2">
                  <span className="text-sm">{videoMetadata?.views ? formatViews(videoMetadata.views) : '0 views'}</span>
                </div>
              </div>
              <div className="text-sm">
                <span className="text-muted-foreground">Category:</span> <span className="font-medium">{category || 'Entertainment'}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Title */}
      <div className="space-y-2">
        <label htmlFor="title" className="text-sm font-medium">Video Title</label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter video title"
        />
      </div>

      {/* Description */}
      <div className="space-y-2">
        <label htmlFor="description" className="text-sm font-medium">Description</label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Enter video description"
          rows={4}
        />
      </div>

      {/* Thumbnail */}
      <div className="space-y-2">
        <label htmlFor="thumbnail" className="text-sm font-medium">Thumbnail</label>
        <div className="flex flex-col gap-2">
          {thumbnail && (
            <div className="relative aspect-video w-full overflow-hidden rounded-md border border-border">
              <img
                src={thumbnail}
                alt="Video thumbnail"
                className="h-full w-full object-cover"
                onError={(e) => {
                  // If image fails to load, replace with a placeholder
                  const target = e.target as HTMLImageElement;
                  target.onerror = null; // Prevent infinite loop
                  target.src = `https://placehold.co/480x360/333333/FFFFFF?text=Video+Thumbnail`;
                }}
              />
            </div>
          )}
          <Input
            id="thumbnail"
            type="file"
            accept="image/*"
            onChange={handleThumbnailUpload}
            className="cursor-pointer"
          />
          <p className="text-xs text-lingstream-muted">
            Upload a thumbnail for your video
          </p>
        </div>
      </div>

      {/* Category */}
      <div className="space-y-2">
        <label htmlFor="category" className="text-sm font-medium">Category</label>
        <Select value={category} onValueChange={setCategory}>
          <SelectTrigger id="category">
            <SelectValue placeholder="Select a category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((cat) => (
              <SelectItem key={cat} value={cat}>
                {cat}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Default Language */}
      <div className="space-y-2">
        <label htmlFor="defaultLanguage" className="text-sm font-medium">Default Language</label>
        <Select value={defaultLanguage} onValueChange={setDefaultLanguage}>
          <SelectTrigger id="defaultLanguage">
            <SelectValue placeholder="Select a language" />
          </SelectTrigger>
          <SelectContent>
            {languages.map((lang) => (
              <SelectItem key={lang.code} value={lang.code}>
                <div className="flex items-center">
                  <span className="mr-2">{lang.flag}</span>
                  {lang.name}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
