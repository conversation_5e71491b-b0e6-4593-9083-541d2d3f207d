import React, { useState } from 'react';
import {
  Facebook,
  Mail,
  Twitter,
  Linkedin,
  Instagram,
  Github,
  Info,
  Home,
  ChevronRight,
  Search,
  Key,
  Globe,
  Settings
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

// Social platform data with icons and colors
const socialPlatforms = [
  {
    id: 'facebook',
    name: 'Facebook',
    icon: <Facebook className="h-5 w-5" />,
    color: 'bg-blue-500',
    enabled: true,
    description: 'Enable the ability for users to login to your site using their Facebook account.'
  },
  {
    id: 'google',
    name: 'Google',
    icon: <Mail className="h-5 w-5" />,
    color: 'bg-red-500',
    enabled: true,
    description: 'Enable the ability for users to login to your site using their Google account, (App requires reviewing)'
  },
  {
    id: 'twitter',
    name: 'Twitter',
    icon: <Twitter className="h-5 w-5" />,
    color: 'bg-blue-400',
    enabled: false,
    description: 'Enable the ability for users to login to your site using their Twitter account.'
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: <Linkedin className="h-5 w-5" />,
    color: 'bg-blue-700',
    enabled: false,
    description: 'Enable the ability for users to login to your site using their LinkedIn account.'
  },
  {
    id: 'instagram',
    name: 'Instagram',
    icon: <Instagram className="h-5 w-5" />,
    color: 'bg-purple-600',
    enabled: false,
    description: 'Enable the ability for users to login to your site using their Instagram account.'
  },
  {
    id: 'github',
    name: 'GitHub',
    icon: <Github className="h-5 w-5" />,
    color: 'bg-gray-800',
    enabled: true,
    description: 'Enable the ability for users to login to your site using their GitHub account.'
  }
];

export default function SocialLoginSettings() {
  const [platforms, setPlatforms] = useState(socialPlatforms);
  const [activeTab, setActiveTab] = useState('popular');
  const [expandedPlatform, setExpandedPlatform] = useState<string | null>(null);

  const togglePlatform = (id: string) => {
    setPlatforms(platforms.map(platform =>
      platform.id === id ? { ...platform, enabled: !platform.enabled } : platform
    ));
  };

  const handleSave = () => {
    alert('Social login settings saved successfully!');
  };

  return (
    <div className="space-y-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="bg-blue-50 p-2 rounded">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin" className="flex items-center gap-1 text-black font-medium">
              <Home className="h-4 w-4" />
              Admin Panel
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator>
            <ChevronRight className="h-4 w-4" />
          </BreadcrumbSeparator>
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings" className="text-black">
              Settings
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator>
            <ChevronRight className="h-4 w-4" />
          </BreadcrumbSeparator>
          <BreadcrumbItem>
            <span className="text-blue-600 font-medium">Social Login Settings</span>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Info Alert */}
      <Alert className="bg-blue-50 border-blue-200">
        <Info className="h-4 w-4 text-blue-500" />
        <AlertDescription className="text-blue-800">
          For more information on how to setup social login, please visit our <a href="#" className="text-blue-600 hover:underline font-medium">Documentation page</a>
        </AlertDescription>
      </Alert>

      {/* Main Content */}
      <div className="grid grid-cols-1 gap-6">
        {/* Social Login Platforms Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5 text-primary" />
              Social Login Platforms
            </CardTitle>
            <CardDescription>
              Enable or disable social login options for your users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="popular" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3 mb-6">
                <TabsTrigger value="popular">
                  Popular
                </TabsTrigger>
                <TabsTrigger value="all">
                  All Platforms
                </TabsTrigger>
                <TabsTrigger value="enabled">
                  Enabled ({platforms.filter(p => p.enabled).length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="popular" className="space-y-4">
                <div className="grid gap-4">
                  {platforms.slice(0, 4).map(platform => (
                    <div key={platform.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center gap-3">
                        <div className={`${platform.color} p-2 rounded-full text-white`}>
                          {platform.icon}
                        </div>
                        <div>
                          <h3 className="font-medium text-black">{platform.name}</h3>
                          <p className="text-sm text-gray-500">{platform.description}</p>
                        </div>
                      </div>
                      <Switch
                        checked={platform.enabled}
                        onCheckedChange={() => togglePlatform(platform.id)}
                      />
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="all" className="space-y-4">
                <div className="relative mb-4">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search platforms..."
                    className="pl-10 border-gray-300"
                  />
                </div>

                <Accordion type="single" collapsible className="w-full">
                  {platforms.map(platform => (
                    <AccordionItem value={platform.id} key={platform.id}>
                      <AccordionTrigger className="hover:bg-gray-50 px-4 py-2 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`${platform.color} p-2 rounded-full text-white`}>
                            {platform.icon}
                          </div>
                          <span>{platform.name}</span>
                        </div>
                        <div className="flex items-center gap-4 mr-4">
                          <Switch
                            checked={platform.enabled}
                            onCheckedChange={() => togglePlatform(platform.id)}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="px-4 pb-4">
                        <div className="space-y-4 pt-2">
                          <div className="grid gap-2">
                            <Label htmlFor={`${platform.id}-app-id`}>Application ID</Label>
                            <Input
                              id={`${platform.id}-app-id`}
                              placeholder="Enter application ID"
                              className="border-gray-300"
                            />
                          </div>
                          <div className="grid gap-2">
                            <Label htmlFor={`${platform.id}-app-secret`}>Application Secret</Label>
                            <Input
                              id={`${platform.id}-app-secret`}
                              placeholder="Enter application secret"
                              className="border-gray-300"
                              type="password"
                            />
                          </div>
                          {platform.id === 'github' && (
                            <div className="grid gap-2">
                              <Label htmlFor="github-redirect">Redirect URI</Label>
                              <Input
                                id="github-redirect"
                                placeholder="Enter redirect URI"
                                className="border-gray-300"
                              />
                            </div>
                          )}
                          <Button
                            className={`${platform.color} hover:opacity-90 text-white w-full`}
                          >
                            Save {platform.name} Configuration
                          </Button>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </TabsContent>

              <TabsContent value="enabled" className="space-y-4">
                {platforms.filter(p => p.enabled).length > 0 ? (
                  <div className="grid gap-4">
                    {platforms.filter(p => p.enabled).map(platform => (
                      <div key={platform.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-center gap-3">
                          <div className={`${platform.color} p-2 rounded-full text-white`}>
                            {platform.icon}
                          </div>
                          <div>
                            <h3 className="font-medium text-black">{platform.name}</h3>
                            <p className="text-sm text-gray-500">{platform.description}</p>
                          </div>
                        </div>
                        <Switch
                          checked={platform.enabled}
                          onCheckedChange={() => togglePlatform(platform.id)}
                        />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Globe className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                    <h3 className="text-lg font-medium mb-1">No platforms enabled</h3>
                    <p>Enable social login platforms to see them here</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* API Configuration Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5 text-primary" />
              API Configuration
            </CardTitle>
            <CardDescription>
              Configure API keys for your enabled social login platforms
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="bg-amber-50 border-amber-200 mb-6">
              <Info className="h-4 w-4 text-amber-500" />
              <AlertDescription className="text-amber-800">
                Please note that some platforms may require app verification before they can be used in production.
              </AlertDescription>
            </Alert>

            <div className="space-y-6">
              {platforms.filter(p => p.enabled).map(platform => (
                <div key={platform.id} className="border rounded-lg p-5">
                  <div className="flex items-center gap-3 mb-4">
                    <div className={`${platform.color} p-2 rounded-full text-white`}>
                      {platform.icon}
                    </div>
                    <h3 className="font-medium text-lg">{platform.name} Configuration</h3>
                  </div>

                  <div className="space-y-4">
                    <div className="grid gap-2">
                      <Label htmlFor={`${platform.id}-config-app-id`}>
                        {platform.id === 'twitter' ? 'Consumer Key' : 'Application ID'}
                      </Label>
                      <Input
                        id={`${platform.id}-config-app-id`}
                        placeholder={platform.id === 'twitter' ? 'Enter consumer key' : 'Enter application ID'}
                        className="border-gray-300"
                      />
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor={`${platform.id}-config-app-secret`}>
                        {platform.id === 'twitter' ? 'Consumer Secret' : 'Application Secret'}
                      </Label>
                      <Input
                        id={`${platform.id}-config-app-secret`}
                        placeholder={platform.id === 'twitter' ? 'Enter consumer secret' : 'Enter application secret'}
                        className="border-gray-300"
                        type="password"
                      />
                    </div>

                    {(platform.id === 'github' || platform.id === 'google') && (
                      <div className="grid gap-2">
                        <Label htmlFor={`${platform.id}-redirect`}>Redirect URI</Label>
                        <Input
                          id={`${platform.id}-redirect`}
                          placeholder="Enter redirect URI"
                          className="border-gray-300"
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Advanced Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-primary" />
              Advanced Settings
            </CardTitle>
            <CardDescription>
              Configure additional settings for social login
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium text-black">Auto-create user accounts</h3>
                <p className="text-sm text-gray-500">Automatically create user accounts when users sign in with social login</p>
              </div>
              <Switch defaultChecked />
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium text-black">Link existing accounts</h3>
                <p className="text-sm text-gray-500">Allow users to link social accounts to their existing account</p>
              </div>
              <Switch defaultChecked />
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium text-black">Force email verification</h3>
                <p className="text-sm text-gray-500">Require email verification for accounts created through social login</p>
              </div>
              <Switch />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          className="bg-blue-600 hover:bg-blue-700"
        >
          Save Changes
        </Button>
      </div>
    </div>
  );
}
