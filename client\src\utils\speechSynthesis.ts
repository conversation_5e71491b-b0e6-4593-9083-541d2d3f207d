/**
 * Speech Synthesis Utility
 *
 * This utility provides functions for text-to-speech conversion using the Web Speech API.
 */

// Check if speech synthesis is supported
const isSpeechSynthesisSupported = typeof window !== 'undefined' && 'speechSynthesis' in window;

// Store the current utterance to be able to cancel it
let currentUtterance: SpeechSynthesisUtterance | null = null;

/**
 * Speak text using the Web Speech API
 * @param text Text to speak
 * @param options Options for speech synthesis
 * @returns Promise that resolves when speech is complete or rejects on error
 */
export function speak(
  text: string,
  options: {
    lang?: string;
    voice?: SpeechSynthesisVoice;
    rate?: number;
    pitch?: number;
    volume?: number;
    onStart?: () => void;
    onEnd?: () => void;
    onError?: (error: any) => void;
  } = {}
): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!isSpeechSynthesisSupported) {
      const error = new Error('Speech synthesis is not supported in this browser');
      console.error(error);
      if (options.onError) options.onError(error);
      reject(error);
      return;
    }

    try {
      // Cancel any ongoing speech
      stop();

      // Create a new utterance
      const utterance = new SpeechSynthesisUtterance(text);

      // Set options with defaults - now supporting Hindi (hi-IN) as well
      utterance.lang = options.lang || 'en-US';
      utterance.rate = options.rate || 1.0;
      utterance.pitch = options.pitch || 1.0;
      utterance.volume = options.volume || 1.0;

      // Try to find a suitable voice if not provided
      if (options.voice) {
        utterance.voice = options.voice;
      } else {
        // Get available voices
        let voices = window.speechSynthesis.getVoices();

        // If no voices available yet, wait for them to load
        if (voices.length === 0) {
          console.log('No voices available yet, waiting...');

          // Some browsers need a small delay to load voices
          setTimeout(() => {
            try {
              voices = window.speechSynthesis.getVoices();
              console.log(`Found ${voices.length} voices after waiting`);

              // Try to find a suitable voice based on the language
              let preferredVoice;

              // Check if we need a Hindi voice
              if (utterance.lang.startsWith('hi')) {
                console.log('Looking for Hindi voice');
                preferredVoice = voices.find(voice =>
                  voice.lang.includes('hi') || voice.lang.includes('HI')
                );
              }

              // If no Hindi voice found or not Hindi language, fall back to default
              if (!preferredVoice) {
                preferredVoice = voices.find(voice =>
                  voice.lang.includes(utterance.lang.split('-')[0]) && voice.localService
                ) || voices[0];
              }

              if (preferredVoice) {
                console.log('Using voice:', preferredVoice.name);
                utterance.voice = preferredVoice;
              }

              // Set event handlers
              setupEventHandlers(utterance, options, resolve, reject);

              // Store the current utterance
              currentUtterance = utterance;

              // Start speaking
              window.speechSynthesis.speak(utterance);
            } catch (delayedError) {
              console.error('Error setting up delayed speech:', delayedError);
              if (options.onError) options.onError(delayedError);
              if (options.onEnd) options.onEnd();
              reject(delayedError);
            }
          }, 500);

          return;
        } else {
          // Try to find a suitable voice based on the language
          let preferredVoice;

          // Check if we need a Hindi voice
          if (utterance.lang.startsWith('hi')) {
            console.log('Looking for Hindi voice');
            preferredVoice = voices.find(voice =>
              voice.lang.includes('hi') || voice.lang.includes('HI')
            );
          }

          // If no Hindi voice found or not Hindi language, fall back to default
          if (!preferredVoice) {
            preferredVoice = voices.find(voice =>
              voice.lang.includes(utterance.lang.split('-')[0]) && voice.localService
            ) || voices[0];
          }

          if (preferredVoice) {
            console.log('Using voice:', preferredVoice.name);
            utterance.voice = preferredVoice;
          }
        }
      }

      // Set event handlers
      setupEventHandlers(utterance, options, resolve, reject);

      // Store the current utterance
      currentUtterance = utterance;

      // Start speaking
      window.speechSynthesis.speak(utterance);

      // Chrome bug workaround: sometimes utterances don't start
      // This forces the speech synthesis to restart if it gets stuck
      setTimeout(() => {
        if (window.speechSynthesis.speaking && !window.speechSynthesis.paused) {
          console.log('Applying Chrome speech synthesis fix');
          window.speechSynthesis.pause();
          window.speechSynthesis.resume();
        }
      }, 1000);

    } catch (error) {
      console.error('Error setting up speech synthesis:', error);
      if (options.onError) options.onError(error);
      if (options.onEnd) options.onEnd();
      reject(error);
    }
  });
}

// Helper function to set up event handlers for utterances
function setupEventHandlers(
  utterance: SpeechSynthesisUtterance,
  options: any,
  resolve: (value: void | PromiseLike<void>) => void,
  reject: (reason?: any) => void
): void {
  utterance.onstart = () => {
    console.log('Speech started');
    if (options.onStart) options.onStart();
  };

  utterance.onend = () => {
    console.log('Speech ended normally');
    currentUtterance = null;
    if (options.onEnd) options.onEnd();
    resolve();
  };

  utterance.onerror = (event) => {
    console.error('Speech synthesis error:', event);
    currentUtterance = null;
    if (options.onError) options.onError(event);
    reject(event);
  };
}

/**
 * Stop any ongoing speech
 */
export function stop(): void {
  if (isSpeechSynthesisSupported) {
    try {
      window.speechSynthesis.cancel();
      currentUtterance = null;
      console.log('Speech synthesis stopped');
    } catch (error) {
      console.error('Error stopping speech synthesis:', error);
    }
  }
}

/**
 * Pause any ongoing speech
 */
export function pause(): void {
  if (isSpeechSynthesisSupported) {
    try {
      window.speechSynthesis.pause();
      console.log('Speech synthesis paused');
    } catch (error) {
      console.error('Error pausing speech synthesis:', error);
    }
  }
}

/**
 * Resume any paused speech
 */
export function resume(): void {
  if (isSpeechSynthesisSupported) {
    try {
      window.speechSynthesis.resume();
      console.log('Speech synthesis resumed');
    } catch (error) {
      console.error('Error resuming speech synthesis:', error);
    }
  }
}

/**
 * Get all available voices
 * @returns Array of available voices
 */
export function getVoices(): SpeechSynthesisVoice[] {
  if (!isSpeechSynthesisSupported) {
    return [];
  }

  try {
    const voices = window.speechSynthesis.getVoices();
    console.log(`Found ${voices.length} voices`);
    return voices;
  } catch (error) {
    console.error('Error getting voices:', error);
    return [];
  }
}

/**
 * Get a voice by language
 * @param lang Language code (e.g., 'en-US', 'hi-IN')
 * @returns The first voice that matches the language, or null if none found
 */
export function getVoiceByLang(lang: string): SpeechSynthesisVoice | null {
  try {
    const voices = getVoices();

    // First try to find an exact match
    let voice = voices.find(voice => voice.lang.toLowerCase() === lang.toLowerCase());

    // If no exact match, try to find a partial match
    if (!voice) {
      voice = voices.find(voice => voice.lang.toLowerCase().startsWith(lang.toLowerCase().split('-')[0]));
    }

    // If still no match, try to find any voice
    if (!voice && voices.length > 0) {
      // Prefer local voices
      voice = voices.find(voice => voice.localService) || voices[0];
    }

    if (voice) {
      console.log(`Found voice for language ${lang}:`, voice.name);
    } else {
      console.log(`No voice found for language ${lang}`);
    }

    return voice || null;
  } catch (error) {
    console.error(`Error finding voice for language ${lang}:`, error);
    return null;
  }
}

/**
 * Check if speech synthesis is currently speaking
 * @returns True if speaking, false otherwise
 */
export function isSpeaking(): boolean {
  if (!isSpeechSynthesisSupported) {
    return false;
  }

  try {
    return window.speechSynthesis.speaking;
  } catch (error) {
    console.error('Error checking if speaking:', error);
    return false;
  }
}

/**
 * Check if speech synthesis is currently paused
 * @returns True if paused, false otherwise
 */
export function isPaused(): boolean {
  if (!isSpeechSynthesisSupported) {
    return false;
  }

  try {
    return window.speechSynthesis.paused;
  } catch (error) {
    console.error('Error checking if paused:', error);
    return false;
  }
}

/**
 * Check if speech synthesis is supported
 * @returns True if supported, false otherwise
 */
export function isSupported(): boolean {
  return isSpeechSynthesisSupported;
}

/**
 * Initialize speech synthesis
 * This can be called to preload voices
 */
export function initialize(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!isSpeechSynthesisSupported) {
      console.warn('Speech synthesis is not supported in this browser');
      resolve();
      return;
    }

    try {
      // Try to get voices
      const voices = window.speechSynthesis.getVoices();

      if (voices.length > 0) {
        console.log('Speech synthesis initialized with', voices.length, 'voices');
        resolve();
      } else {
        // Some browsers need a small delay to load voices
        console.log('No voices available yet, waiting...');

        // Set up the voiceschanged event
        window.speechSynthesis.onvoiceschanged = () => {
          const updatedVoices = window.speechSynthesis.getVoices();
          console.log('Voices loaded:', updatedVoices.length);
          resolve();
        };

        // Fallback in case the event doesn't fire
        setTimeout(() => {
          const delayedVoices = window.speechSynthesis.getVoices();
          console.log('Voices after timeout:', delayedVoices.length);
          resolve();
        }, 1000);
      }
    } catch (error) {
      console.error('Error initializing speech synthesis:', error);
      reject(error);
    }
  });
}

export default {
  speak,
  stop,
  pause,
  resume,
  getVoices,
  getVoiceByLang,
  isSpeaking,
  isPaused,
  isSupported,
  initialize
};
