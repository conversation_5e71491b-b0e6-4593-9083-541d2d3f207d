import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { videoAPI } from '@/services/video-api';
import { Video } from '@/types';
import Layout from '@/components/layout/Layout';
import VideoPlayer from '@/components/video/VideoPlayer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

export default function LanguageTestPage() {
  const { videoId } = useParams<{ videoId: string }>();
  const [video, setVideo] = useState<Video | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVideo = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!videoId) {
          setError('No video ID provided');
          setIsLoading(false);
          return;
        }

        const response = await videoAPI.getVideoById(videoId);

        if (response.success && response.video) {
          console.log('Fetched video:', response.video);
          setVideo(response.video);
        } else {
          setError('Failed to fetch video');
        }
      } catch (error) {
        console.error('Error fetching video:', error);
        setError('Error fetching video');
      } finally {
        setIsLoading(false);
      }
    };

    fetchVideo();
  }, [videoId]);

  const handleFixVideo = async () => {
    try {
      setIsLoading(true);

      if (!video) {
        setError('No video to fix');
        setIsLoading(false);
        return;
      }

      // Call the API to fix all video languages
      const response = await videoAPI.fixAllVideoLanguages();
      console.log('Fix response:', response);

      // Refetch the video to get the updated data
      const videoResponse = await videoAPI.getVideoById(videoId!);
      if (videoResponse.success && videoResponse.video) {
        console.log('Fetched updated video:', videoResponse.video);
        setVideo(videoResponse.video);
      }
    } catch (error) {
      console.error('Error fixing video:', error);

      // Extract error message
      let errorMessage = "Error fixing video";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (error.response && error.response.data) {
        errorMessage = error.response.data.message || error.response.data.error?.message || errorMessage;
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout>
      <div className="container py-8">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Language Test Page</CardTitle>
            <CardDescription>
              This page is for testing video language functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Button onClick={handleFixVideo} disabled={isLoading || !video}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Fix This Video Languages'
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setError(null);
                    setVideo(null);
                    // Re-fetch video data
                    window.location.hash = Date.now().toString();
                  }}
                >
                  Refresh Data
                </Button>
              </div>

              {error && (
                <div className="p-4 bg-red-900/20 border border-red-800 rounded-md text-red-300">
                  {error}
                </div>
              )}

              {video && (
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Video Details:</h3>
                  <p><strong>ID:</strong> {video.id}</p>
                  <p><strong>Title:</strong> {video.title}</p>
                  <p><strong>URL:</strong> {video.url}</p>
                  <p><strong>Languages:</strong> {video.languages?.length || 0}</p>

                  {video.languages && video.languages.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-md font-medium">Available Languages:</h4>
                      <ul className="space-y-1">
                        {video.languages.map((lang, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <span>{lang.flag}</span>
                            <span>{lang.name}</span>
                            <span className="text-xs text-lingstream-muted">({lang.code})</span>
                            {lang.isDefault && (
                              <span className="text-xs bg-lingstream-accent/20 px-1 rounded">Default</span>
                            )}
                            <span className="text-xs text-lingstream-muted">URL: {lang.url}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {video && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold">{video.title}</h2>
            <VideoPlayer video={video} />
          </div>
        )}

        {isLoading && !video && (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-lingstream-accent" />
          </div>
        )}
      </div>
    </Layout>
  );
}
