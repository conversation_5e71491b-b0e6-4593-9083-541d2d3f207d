import React, { ReactNode } from 'react';
import { useTheme } from '@/context/ThemeContext';
import { useNavigate } from 'react-router-dom';
import { TrendingUp } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: ReactNode;
  iconBgColor: string;
  trend?: number;
  linkTo?: string;
  description?: string;
}

export default function StatCard({
  title,
  value,
  icon,
  iconBgColor,
  trend,
  linkTo,
  description
}: StatCardProps) {
  const { theme } = useTheme();
  const navigate = useNavigate();

  const handleClick = () => {
    if (linkTo) {
      navigate(linkTo);
    }
  };

  return (
    <div
      className={`${theme === 'dark' ? 'bg-gray-800 hover:bg-gray-750' : 'bg-white hover:bg-gray-50'}
        rounded-lg shadow-sm p-6 transition-all duration-200 transform hover:scale-[1.02] hover:shadow-md
        ${linkTo ? 'cursor-pointer' : ''}`}
      onClick={handleClick}
    >
      <div className="flex justify-between items-start mb-4">
        <h3 className={`${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'} text-sm font-medium`}>{title}</h3>
        <div className={`${iconBgColor} w-10 h-10 rounded-full flex items-center justify-center`}>
          {icon}
        </div>
      </div>

      <div className="flex flex-col">
        <span className={`text-3xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-1`}>{value}</span>

        {description && (
          <p className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} mb-2`}>{description}</p>
        )}

        {trend !== undefined && (
          <div className="flex items-center mt-1">
            <div className={`text-sm flex items-center ${trend >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              <TrendingUp size={14} className={`mr-1 ${trend < 0 ? 'transform rotate-180' : ''}`} />
              <span>{Math.abs(trend)}%</span>
            </div>
            <span className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} ml-2`}>vs last period</span>
          </div>
        )}
      </div>

      {linkTo && (
        <div className={`mt-3 text-xs ${theme === 'dark' ? 'text-orange-400' : 'text-orange-600'} font-medium`}>
          View details →
        </div>
      )}
    </div>
  );
}
