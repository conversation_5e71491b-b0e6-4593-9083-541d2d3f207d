// Bhashini Translation API integration

// API endpoints and credentials
const INFERENCE_URL = "https://dhruva-api.bhashini.gov.in/services/inference/pipeline";
const USER_ID = "cee60134c6bb4d179efd3fda48ff32fe";
const API_KEY = "13a647c84b-2747-4f0c-afcd-2ac8235f5318";
const AUTHORIZATION_VALUE = "W9QK_zzb7Lnsc_xYAl30Gk64Z8rJU4r_2NBiguZjiMIdkUI_8p-E38M-zc_VVhun";

// Service and model IDs from the pipeline configuration
const NMT_SERVICE_ID = "ai4bharat/indictrans-v2-all-gpu--t4";
const NMT_MODEL_ID = "641d1d7892a6a31751ff1f5a";

// Supported languages with their names
export const SUPPORTED_LANGUAGES: Record<string, string> = {
  "en": "English",
  "hi": "हिंदी (Hindi)",
  "mr": "मराठी (Marathi)",
  "gu": "ગુજરાતી (Gujarati)",
  "ta": "தமிழ் (Tamil)",
  "te": "తెలుగు (Telugu)",
  "bn": "বাংলা (Bengali)",
  "kn": "ಕನ್ನಡ (Kannada)",
  "ml": "മലയാളം (Malayalam)",
  "pa": "ਪੰਜਾਬੀ (Punjabi)",
  "or": "ଓଡ଼ିଆ (Odia)",
  "as": "অসমীয়া (Assamese)",
  "ur": "اردو (Urdu)"
};

// Script code mapping for Indian languages
function getScriptCode(languageCode: string): string {
  const scriptMapping: Record<string, string> = {
    "hi": "Deva",  // Hindi - Devanagari
    "en": "Latn",  // English - Latin
    "gu": "Gujr",  // Gujarati
    "bn": "Beng",  // Bengali
    "ta": "Taml",  // Tamil
    "te": "Telu",  // Telugu
    "mr": "Deva",  // Marathi - Devanagari
    "kn": "Knda",  // Kannada
    "ml": "Mlym",  // Malayalam
    "pa": "Guru",  // Punjabi - Gurmukhi
    "or": "Orya",  // Odia
    "as": "Beng",  // Assamese - Bengali
    "ur": "Arab",  // Urdu - Arabic
  };
  return scriptMapping[languageCode] || "Deva";  // Default to Devanagari if not found
}

// Interface for translation response
interface TranslationResponse {
  pipelineResponse: Array<{
    output: Array<{
      target: string;
    }>;
  }>;
}

/**
 * Translates text from source language to target language using Bhashini API
 * @param text Text to translate
 * @param sourceLang Source language code (default: "en")
 * @param targetLang Target language code
 * @returns Promise with translated text or error message
 */
export async function translateText(
  text: string,
  sourceLang: string = "en",
  targetLang: string
): Promise<string> {
  // Headers for the API request
  const headers = {
    "Content-Type": "application/json",
    "userID": USER_ID,
    "ulcaApiKey": API_KEY,
    "Authorization": AUTHORIZATION_VALUE
  };

  // Payload for the translation request
  const payload = {
    "pipelineTasks": [
      {
        "taskType": "translation",
        "config": {
          "language": {
            "sourceLanguage": sourceLang,
            "targetLanguage": targetLang,
            "sourceScriptCode": getScriptCode(sourceLang),
            "targetScriptCode": getScriptCode(targetLang)
          },
          "serviceId": NMT_SERVICE_ID,
          "modelId": NMT_MODEL_ID
        }
      }
    ],
    "inputData": {
      "input": [
        {
          "source": text
        }
      ]
    }
  };

  try {
    // Make the API request
    const response = await fetch(INFERENCE_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(payload)
    });

    // Process the response
    if (response.ok) {
      const result = await response.json() as TranslationResponse;
      try {
        const outputText = result.pipelineResponse[0].output[0].target;
        return outputText;
      } catch (e) {
        console.error("Error parsing response:", e);
        return `Error parsing response: ${e}`;
      }
    } else {
      const errorText = await response.text();
      console.error(`Error: ${response.status}, ${errorText}`);
      return `Error: ${response.status}, ${errorText}`;
    }
  } catch (e) {
    console.error("Exception occurred:", e);
    return `Error: ${e}`;
  }
}

/**
 * Get list of supported languages (excluding a specific language if provided)
 * @param excludeLang Language code to exclude from the list (optional)
 * @returns Object with language codes and names
 */
export function getSupportedLanguages(excludeLang?: string): Record<string, string> {
  if (!excludeLang) return SUPPORTED_LANGUAGES;
  
  const filteredLanguages: Record<string, string> = {};
  Object.entries(SUPPORTED_LANGUAGES).forEach(([code, name]) => {
    if (code !== excludeLang) {
      filteredLanguages[code] = name;
    }
  });
  
  return filteredLanguages;
}

/**
 * Check if a language is supported
 * @param langCode Language code to check
 * @returns Boolean indicating if language is supported
 */
export function isLanguageSupported(langCode: string): boolean {
  return Object.keys(SUPPORTED_LANGUAGES).includes(langCode);
}
