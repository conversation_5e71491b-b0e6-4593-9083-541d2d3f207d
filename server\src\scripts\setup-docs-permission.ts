import { config } from 'dotenv';
import permissionService from '../services/permission.service';
import roleService from '../services/role.service';
import { connectDB } from '../config/database';

// Load environment variables
config();

/**
 * Set up documentation permissions
 */
async function setupDocsPermission() {
  try {
    console.log('🔍 Setting up documentation permissions...');
    
    // Connect to database
    await connectDB();
    
    // Create docs:read permission
    console.log('Creating docs:read permission...');
    const docsReadPermission = await permissionService.createPermission({
      name: 'Read API Documentation',
      code: 'docs:read',
      description: 'Allows access to the API documentation',
      category: 'Documentation',
      isActive: true,
      resourceType: 'documentation',
      action: 'read',
      scope: 'all',
    }, 'system');
    
    console.log(`✅ Created permission: ${docsReadPermission.name} (${docsReadPermission.code})`);
    
    // Add permission to admin role
    console.log('Adding permission to admin role...');
    const adminRole = await roleService.getRoleByCode('admin');
    
    if (adminRole) {
      // Add permission to admin role
      const updatedRole = await roleService.updateRole(
        adminRole.id,
        {
          permissions: [...adminRole.permissions, docsReadPermission.code],
        },
        'system'
      );
      
      console.log(`✅ Added permission to role: ${updatedRole.name}`);
    } else {
      console.log('❌ Admin role not found');
    }
    
    // Add permission to developer role if it exists
    try {
      const developerRole = await roleService.getRoleByCode('developer');
      
      if (developerRole) {
        // Add permission to developer role
        const updatedRole = await roleService.updateRole(
          developerRole.id,
          {
            permissions: [...developerRole.permissions, docsReadPermission.code],
          },
          'system'
        );
        
        console.log(`✅ Added permission to role: ${updatedRole.name}`);
      }
    } catch (error) {
      console.log('Developer role not found, skipping...');
    }
    
    console.log('\n✅ Documentation permissions setup completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error setting up documentation permissions:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupDocsPermission();
