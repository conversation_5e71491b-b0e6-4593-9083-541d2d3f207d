const mongoose = require('mongoose');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/lawengaxe';

// Define video schema
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  tags: [String],
  category: String,
  contentRating: String,
  processingStatus: String,
  stats: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    playlistAdds: { type: Number, default: 0 },
    averageWatchTime: { type: Number, default: 0 },
    retentionRate: { type: Number, default: 0 }
  },
  file: {
    originalName: String,
    size: Number,
    mimeType: String
  },
  languages: [{
    code: String,
    name: String,
    flag: String,
    isDefault: Boolean,
    url: String
  }],
  source: {
    type: String,
    originalUrl: String,
    platform: String,
    externalId: String
  },
  createdAt: Date,
  updatedAt: Date,
  deletedAt: Date
});

const Video = mongoose.model('Video', VideoSchema);

/**
 * Format seconds to readable time
 */
function formatDuration(seconds) {
  if (seconds <= 0) return '0:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

/**
 * Check actual video durations in detail
 */
async function checkActualDurations() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    const videos = await Video.find({ deletedAt: null })
      .select('id title duration url category')
      .sort({ createdAt: -1 });
    
    console.log(`\n📊 Detailed Duration Analysis (${videos.length} videos):`);
    console.log('=' .repeat(100));
    
    videos.forEach((video, index) => {
      const durationFormatted = formatDuration(video.duration || 0);
      console.log(`${(index + 1).toString().padStart(2, ' ')}. ${video.title.substring(0, 40).padEnd(40, ' ')} | ${durationFormatted.padStart(8, ' ')} | ${video.id}`);
    });
    
    // Statistics
    const durations = videos.map(v => v.duration || 0);
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);
    
    console.log('\n📈 Duration Statistics:');
    console.log('=' .repeat(50));
    console.log(`Average duration: ${formatDuration(avgDuration)} (${Math.round(avgDuration)}s)`);
    console.log(`Minimum duration: ${formatDuration(minDuration)} (${minDuration}s)`);
    console.log(`Maximum duration: ${formatDuration(maxDuration)} (${maxDuration}s)`);
    
    // Check if durations look realistic
    const suspiciousVideos = videos.filter(v => {
      const duration = v.duration || 0;
      return duration < 60 || duration > 3600; // Less than 1 minute or more than 1 hour
    });
    
    if (suspiciousVideos.length > 0) {
      console.log(`\n⚠️  Potentially Unrealistic Durations (${suspiciousVideos.length} videos):`);
      console.log('=' .repeat(80));
      suspiciousVideos.forEach((video, index) => {
        const durationFormatted = formatDuration(video.duration || 0);
        console.log(`${index + 1}. ${video.title.substring(0, 50)} | ${durationFormatted} | ${video.id}`);
      });
    } else {
      console.log('\n✅ All durations appear realistic (between 1 minute and 1 hour)');
    }
    
    // Check for duplicate durations (might indicate random generation)
    const durationCounts = {};
    durations.forEach(d => {
      durationCounts[d] = (durationCounts[d] || 0) + 1;
    });
    
    const duplicateDurations = Object.entries(durationCounts)
      .filter(([duration, count]) => count > 1)
      .sort((a, b) => b[1] - a[1]);
    
    if (duplicateDurations.length > 0) {
      console.log(`\n🔍 Duplicate Durations (might indicate random generation):`);
      console.log('=' .repeat(60));
      duplicateDurations.slice(0, 10).forEach(([duration, count]) => {
        console.log(`${formatDuration(parseInt(duration))} appears ${count} times`);
      });
    } else {
      console.log('\n✅ No duplicate durations found - good sign!');
    }
    
  } catch (error) {
    console.error('❌ Error checking durations:', error);
  } finally {
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('\n🔒 MongoDB disconnected');
    }
  }
}

// Run the script
if (require.main === module) {
  checkActualDurations()
    .then(() => {
      console.log('✅ Duration analysis completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Duration analysis failed:', error);
      process.exit(1);
    });
}

module.exports = { checkActualDurations };
