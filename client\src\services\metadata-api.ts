import api from './api';

/**
 * Metadata API service
 */
export const metadataAPI = {
  /**
   * Fetch video metadata from a URL
   */
  fetchVideoMetadata: async (url: string) => {
    try {
      console.log(`Fetching metadata for URL: ${url}`);
      const response = await api.get(`/metadata/video?url=${encodeURIComponent(url)}`);
      console.log('Metadata API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching metadata:', error?.message || error);

      // Extract video ID from URL for fallback data
      const urlParts = url.split('/');
      const videoId = urlParts[urlParts.length - 1] || 'unknown';

      // Return a fallback response with mock data
      return {
        success: false,
        message: error?.message || 'Failed to fetch video metadata',
        metadata: {
          title: `Engaxe Video ${videoId}`,
          description: 'No description available',
          thumbnailUrl: `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`,
          embedUrl: `https://engaxe.com/embed/${videoId}`,
          category: 'Education'
        }
      };
    }
  },
};
