/**
 * Simple script to fix all videos in the database
 * This script directly connects to the database and fixes all videos
 */

const mongoose = require('mongoose');
const { exec } = require('child_process');
const path = require('path');

// MongoDB connection string
const MONGODB_URI = 'mongodb://localhost:27017/lawengaxe';

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('Connected to MongoDB');
    
    // Run the TypeScript script using ts-node
    const scriptPath = path.join(__dirname, 'src', 'scripts', 'fix-videos.ts');
    console.log(`Running script: ${scriptPath}`);
    
    const child = exec(`npx ts-node "${scriptPath}"`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        return;
      }
      
      if (stderr) {
        console.error(`stderr: ${stderr}`);
      }
      
      console.log(`stdout: ${stdout}`);
      
      // Disconnect from MongoDB
      mongoose.disconnect()
        .then(() => {
          console.log('Disconnected from MongoDB');
          process.exit(0);
        })
        .catch(err => {
          console.error('Error disconnecting from MongoDB:', err);
          process.exit(1);
        });
    });
    
    // Forward stdout and stderr to console
    child.stdout.pipe(process.stdout);
    child.stderr.pipe(process.stderr);
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });
