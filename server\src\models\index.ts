// Base model
import BaseSchema, { IBaseEntity } from './base.model';

// User management models
import UserModel, { IUser } from './user.model';
import PermissionModel, { IPermission } from './permission.model';
import RoleModel, { IRole } from './role.model';

// Content models
import VideoModel, { IVideo } from './video.model';
import ChannelModel, { IChannel } from './channel.model';
import { VideoIdMappingModel, VideoIdMapping } from './video-id-mapping.model';
import LikeModel, { ILike } from './like.model';

// Messaging models
import ConversationModel, { IConversation } from './conversation.model';
import MessageModel, { IMessage } from './message.model';

// Notification models
import NotificationTemplateModel, { INotificationTemplate } from './notification-template.model';
import NotificationModel, { INotification } from './notification.model';

// System models
import SystemConfigModel, { ISystemConfig } from './system-config.model';
import LogModel, { ILog } from './log.model';
import ReportModel, { IReport } from './report.model';

// Export all models and interfaces
export {
  // Base
  BaseSchema,
  IBaseEntity,

  // User management
  UserModel,
  IUser,
  PermissionModel,
  IPermission,
  RoleModel,
  IRole,

  // Content
  VideoModel,
  IVideo,
  ChannelModel,
  IChannel,
  VideoIdMappingModel,
  VideoIdMapping,
  LikeModel,
  ILike,

  // Notifications
  NotificationTemplateModel,
  INotificationTemplate,
  NotificationModel,
  INotification,

  // Messaging
  ConversationModel,
  IConversation,
  MessageModel,
  IMessage,

  // System
  SystemConfigModel,
  ISystemConfig,
  LogModel,
  ILog,
  ReportModel,
  IReport,
};
