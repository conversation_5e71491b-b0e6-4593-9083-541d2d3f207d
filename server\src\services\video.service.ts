import { VideoModel, ChannelModel, UserModel } from '../models';
import { createBadRequestError, createNotFoundError, createForbiddenError } from '../utils/errors/AppError';
import { ErrorCodes } from '../utils/errors/errorCodes';
import { generateId, generateEngaxeId, isValidEngaxeId, isHashId, getRandomValidEngaxeId, validEngaxeIds, getLanguageFlag } from '../utils/id';
import { engaxeApiService } from './engaxe-api.service';
import { videoIdMappingService } from './index';
import { extractVideoInfo, extractEngaxeVideoId } from '../utils/video.utils';
import mongoose from 'mongoose';
import { VideoJsonStorageService } from './video-json-storage.service';

/**
 * Service for video-related operations
 */
export class VideoService {
  private jsonStorageService: VideoJsonStorageService;

  constructor() {
    this.jsonStorageService = new VideoJsonStorageService();
    // Initialize JSON storage
    this.jsonStorageService.initialize().catch(error => {
      console.error('Failed to initialize JSON storage:', error);
    });
  }
  /**
   * Upload a new video
   */
  async uploadVideo(userId: string, videoData: {
    title: string;
    description: string;
    channelId: string;
    category: string;
    tags: string[];
    visibility: 'public' | 'unlisted' | 'private' | 'scheduled';
    scheduledPublishTime?: string;
    contentRating: 'general' | 'teen' | 'mature' | 'explicit';
    commentsEnabled?: boolean;
    ratingsEnabled?: boolean;
    embeddingEnabled?: boolean;
    chapters?: Array<{
      title: string;
      startTime: number;
      endTime: number;
    }>;
    location?: {
      latitude: number;
      longitude: number;
      name: string;
    };
    copyright?: {
      owner: string;
      license: string;
      allowReuse: boolean;
      allowCommercialUse: boolean;
      allowModification: boolean;
      attributionRequired: boolean;
    };
    file: {
      originalName: string;
      size: number;
      mimeType: string;
    };
    thumbnailUrl: string;
    url?: string;
  }) {
    // Check if channel exists and user has permission
    const channel = await ChannelModel.findOne({ id: videoData.channelId, deletedAt: null });
    if (!channel) {
      throw createNotFoundError('Channel not found', ErrorCodes.CHANNEL_NOT_FOUND);
    }

    if (channel.ownerId !== userId && !channel.moderators.includes(userId)) {
      throw createForbiddenError('You do not have permission to upload to this channel', ErrorCodes.INSUFFICIENT_PERMISSIONS);
    }

    // Generate a single Engaxe ID to use as both the ID and URL
    const engaxeId = (videoData.url && isValidEngaxeId(videoData.url)) ? videoData.url : generateEngaxeId(); // Ensure URL is a valid Engaxe ID
    console.log(`Using Engaxe ID for new video: ${engaxeId}`);

    // Create new video
    const video = new VideoModel({
      id: engaxeId, // Use the Engaxe ID as the primary identifier
      title: videoData.title,
      description: videoData.description,
      url: engaxeId,
      thumbnailUrl: videoData.thumbnailUrl,
      duration: 0, // Will be updated after processing
      userId,
      channelId: videoData.channelId,
      visibility: videoData.visibility,
      scheduledPublishTime: videoData.scheduledPublishTime ? new Date(videoData.scheduledPublishTime) : undefined,
      tags: videoData.tags || [],
      category: videoData.category,
      contentRating: videoData.contentRating,
      processingStatus: 'uploading',
      commentsEnabled: videoData.commentsEnabled ?? true,
      ratingsEnabled: videoData.ratingsEnabled ?? true,
      embeddingEnabled: videoData.embeddingEnabled ?? true,
      // Add required fields from BaseSchema
      createdBy: userId,
      updatedBy: userId,
      stats: {
        views: 0,
        likes: 0,
        dislikes: 0,
        comments: 0,
        shares: 0,
        playlistAdds: 0,
        averageWatchTime: 0,
        retentionRate: 0,
      },
      file: {
        originalName: videoData.file.originalName,
        size: videoData.file.size,
        mimeType: videoData.file.mimeType,
        codec: '',
        resolution: '',
        bitrate: 0,
        frameRate: 0,
      },
      variants: [],
      chapters: videoData.chapters || [],
      location: videoData.location,
      copyright: videoData.copyright || {
        owner: '',
        license: 'All Rights Reserved',
        allowReuse: false,
        allowCommercialUse: false,
        allowModification: false,
        attributionRequired: true,
      },
    });

    try {
      await video.save();
    } catch (error) {
      console.error('Error saving video:', error);
      throw error;
    }

    // Add video to JSON storage
    try {
      await this.jsonStorageService.addVideo(video);
    } catch (error) {
      console.error('Error adding video to JSON storage:', error);
      // Don't throw error here as the video was successfully saved to database
    }

    // Update channel video count
    channel.stats.videoCount += 1;
    await channel.save();

    // In a real implementation, we would start a background job to process the video
    // For now, we'll simulate a successful upload
    setTimeout(async () => {
      try {
        video.processingStatus = 'ready';
        // Don't set random duration - let the client extract the real duration
        // video.duration will be updated by the client when the video player loads
        await video.save();

        // Update video in JSON storage after processing
        try {
          await this.jsonStorageService.updateVideo(video);
        } catch (error) {
          console.error('Error updating video in JSON storage after processing:', error);
        }
      } catch (error) {
        console.error('Error updating video status:', error);
      }
    }, 5000);

    return video;
  }

  /**
   * Import a video from an external source
   */
  async importVideo(userId: string, videoData: {
    title: string;
    description: string;
    channelId: string;
    category: string;
    tags: string[];
    visibility: 'public' | 'unlisted' | 'private' | 'scheduled';
    scheduledPublishTime?: string;
    contentRating: 'general' | 'teen' | 'mature' | 'explicit';
    commentsEnabled?: boolean;
    ratingsEnabled?: boolean;
    embeddingEnabled?: boolean;
    chapters?: Array<{
      title: string;
      startTime: number;
      endTime: number;
    }>;
    source: {
      type: 'import' | 'embed';
      originalUrl: string;
      platform: string;
      externalId?: string;
    };
    thumbnailUrl?: string;
    duration?: number;
    url?: string;
    languages?: Array<{
      code: string;
      name: string;
      flag?: string;
      url?: string;
      isDefault?: boolean;
    }>;
  }) {
    // Check if channel exists and user has permission
    console.log(`Looking for channel with ID: ${videoData.channelId}`);

    let channel;
    try {
      channel = await ChannelModel.findOne({ id: videoData.channelId, deletedAt: null });

      if (!channel) {
        console.error(`Channel not found with ID: ${videoData.channelId}`);

        // For development purposes, let's try to find any channel owned by the user
        console.log(`Trying to find any channel owned by user: ${userId}`);
        channel = await ChannelModel.findOne({
          ownerId: userId,
          deletedAt: null,
        });

        if (channel) {
          console.log(`Found alternative channel with ID: ${channel.id}`);
          // Update the video data to use this channel
          videoData.channelId = channel.id;
        } else {
          console.error(`No channels found for user: ${userId}`);
          throw createNotFoundError('Channel not found. Please create a channel first.', ErrorCodes.CHANNEL_NOT_FOUND);
        }
      } else {
        console.log(`Found channel: ${channel.name} (${channel.id})`);
      }
    } catch (error) {
      console.error('Error finding channel:', error);
      throw error;
    }

    if (channel.ownerId !== userId && !channel.moderators?.includes(userId)) {
      console.error(`User ${userId} does not have permission for channel ${channel.id}`);
      console.log(`Channel owner: ${channel.ownerId}, Moderators: ${channel.moderators}`);
      throw createForbiddenError('You do not have permission to upload to this channel', ErrorCodes.INSUFFICIENT_PERMISSIONS);
    }

    // Validate source URL or check if it's an Engaxe video ID
    const isEngaxeVideoId = /^[a-zA-Z0-9]+$/.test(videoData.source.originalUrl) &&
                          !videoData.source.originalUrl.includes('/') &&
                          !videoData.source.originalUrl.includes('.');

    if (!isEngaxeVideoId) {
      try {
        console.log('Validating source URL:', videoData.source.originalUrl);
        new URL(videoData.source.originalUrl);
      } catch (error) {
        console.error('Invalid source URL:', videoData.source.originalUrl);
        // Check if it might be an Engaxe video ID despite not passing the initial check
        if (/^[a-zA-Z0-9]+$/.test(videoData.source.originalUrl)) {
          console.log('Treating as Engaxe video ID despite validation failure:', videoData.source.originalUrl);
        } else {
          throw createBadRequestError('Invalid source URL or Engaxe video ID', ErrorCodes.INVALID_INPUT);
        }
      }
    } else {
      console.log('Input appears to be a direct Engaxe video ID:', videoData.source.originalUrl);
      // For direct Engaxe video IDs, always use embed type
      videoData.source.type = 'embed';
    }

    // Reject YouTube URLs
    if (videoData.source.originalUrl.includes('youtube.com') || videoData.source.originalUrl.includes('youtu.be')) {
      console.error('YouTube URL detected:', videoData.source.originalUrl);
      throw createBadRequestError('YouTube URLs are not supported. Only Engaxe URLs are allowed.', ErrorCodes.UNSUPPORTED_PLATFORM);
    }

    // Ensure platform is set to 'engaxe'
    if (videoData.source.platform !== 'engaxe') {
      console.error('Unsupported platform:', videoData.source.platform);
      throw createBadRequestError('Only Engaxe platform is supported.', ErrorCodes.UNSUPPORTED_PLATFORM);
    }

    // Extract video ID from URL or use direct ID
    console.log('Extracting video ID from input:', videoData.source.originalUrl);
    let videoInfo;

    // If it's already an Engaxe video ID, use it directly
    if (isEngaxeVideoId || videoData.source.type === 'embed') {
      videoInfo = {
        platform: 'engaxe' as const,
        videoId: videoData.source.originalUrl
      };
      console.log('Using direct Engaxe video ID:', videoInfo.videoId);

      // For embed videos, we can skip the API call
      if (videoData.source.type === 'embed') {
        console.log('Embed video detected - will skip API call');
      }
    } else {
      // Otherwise try to extract from URL
      videoInfo = extractVideoInfo(videoData.source.originalUrl);

      if (!videoInfo) {
        console.error('Failed to extract video ID from URL:', videoData.source.originalUrl);

        // Try to extract video ID directly from the URL
        const urlParts = videoData.source.originalUrl.split('/');
        const potentialVideoId = urlParts[urlParts.length - 1];

        if (potentialVideoId && potentialVideoId.length > 0) {
          console.log('Using last URL segment as video ID:', potentialVideoId);
          videoInfo = {
            platform: 'engaxe' as const,
            videoId: potentialVideoId
          };
        } else {
          throw createBadRequestError('Invalid Engaxe URL format', ErrorCodes.INVALID_URL);
        }
      }
    }

    console.log(`Importing Engaxe video with ID: ${videoInfo.videoId}`);

    // Get user's Engaxe credentials if available
    const user = await UserModel.findOne({ id: userId, deletedAt: null });

    // For development, we'll allow importing without Engaxe credentials
    let engaxeVideo;

    // Try to fetch video details using the AJAX endpoint first
    try {
      console.log('Attempting to fetch video details using AJAX endpoint');
      const ajaxResponse = await engaxeApiService.getVideoDetailsAjax(videoInfo.videoId);

      if (ajaxResponse) {
        console.log('Successfully retrieved video details via AJAX');
        engaxeVideo = ajaxResponse;
      }
    } catch (ajaxError) {
      console.warn('Failed to fetch video details via AJAX, falling back to API:', ajaxError);

      // For embed videos, we can skip the API call and use the provided data
      if (videoData.source.type === 'embed') {
        console.log('Skipping Engaxe API call for embed video');
        engaxeVideo = {
          title: videoData.title || `Engaxe Video ${videoInfo.videoId}`,
          description: videoData.description || '',
          thumbnail: videoData.thumbnailUrl || `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoInfo.videoId}`,
          duration: videoData.duration || 0,
        };
        console.log('Using provided video details for embed video:', JSON.stringify(engaxeVideo, null, 2));
      } else if (user?.engaxe?.sessionId && user?.engaxe?.userId) {
        console.log(`Using Engaxe credentials for user: ${userId}`);
        // Fetch video details from Engaxe
        engaxeVideo = await engaxeApiService.getVideoDetails(videoInfo.videoId);
      } else {
        console.log('No Engaxe credentials found, using unauthenticated request');
        // Fetch video details without authentication
        engaxeVideo = await engaxeApiService.getVideoDetails(videoInfo.videoId);
      }
    }

    console.log('Engaxe video details:', JSON.stringify(engaxeVideo, null, 2));

    // Ensure thumbnail URL is valid
    let thumbnailUrl = videoData.thumbnailUrl || (engaxeVideo ? engaxeVideo.thumbnail : undefined);
    console.log('Initial thumbnail URL:', thumbnailUrl);

    // If thumbnail is missing or invalid, generate a fallback URL
    if (!thumbnailUrl || thumbnailUrl === 'undefined' || thumbnailUrl === 'null') {
      // Use a reliable placeholder service that's guaranteed to work
      thumbnailUrl = `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoInfo.videoId}`;
      console.log(`Using fallback placeholder thumbnail URL: ${thumbnailUrl}`);
    } else {
      // Validate the thumbnail URL
      try {
        new URL(thumbnailUrl);
        console.log(`Using provided thumbnail URL: ${thumbnailUrl}`);
      } catch (error) {
        console.error('Invalid thumbnail URL:', thumbnailUrl);
        thumbnailUrl = `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoInfo.videoId}`;
        console.log(`Using fallback placeholder thumbnail URL after validation failure: ${thumbnailUrl}`);
      }
    }

    // Use fetched metadata or provided data
    // The engaxeVideo.duration should already be parsed to seconds by the engaxe API service
    const duration = videoData.duration ||
                    (engaxeVideo && engaxeVideo.duration ? engaxeVideo.duration : undefined) ||
                    0; // Don't set random duration - let client extract real duration

    console.log(`Video duration for import: ${duration} seconds (from engaxeVideo: ${engaxeVideo?.duration}, from videoData: ${videoData.duration})`);

    // Determine the video URL and processing status
    let videoUrl = '';
    let processingStatus = videoData.source.type === 'embed' ? 'ready' : 'processing';

    // For Engaxe embed videos, store the video ID directly
    if (videoData.source.type === 'embed') {
      // Extract the video ID if it's a URL
      const extractedId = extractEngaxeVideoId(videoInfo.videoId);
      if (extractedId) {
        videoUrl = extractedId;
        console.log(`Extracted video ID from URL: ${extractedId} (original: ${videoInfo.videoId})`);
      } else {
        videoUrl = videoInfo.videoId;
        console.log(`Using Engaxe video ID as URL for embed: ${videoUrl}`);
      }

      // For embed videos, we can skip the API call and set processing status to ready immediately
      console.log('Embed video - skipping API call and setting status to ready');
      processingStatus = 'ready';
    }
    // For import type, try to get a playable URL
    else {
      // First try to use the video ID from the Engaxe API response if available
      if (engaxeVideo && engaxeVideo.video_id && engaxeVideo.video_id !== 'undefined' && engaxeVideo.video_id !== 'null') {
        // Extract the video ID if it's a URL
        const extractedId = extractEngaxeVideoId(engaxeVideo.video_id);
        if (extractedId) {
          videoUrl = extractedId;
          console.log(`Extracted video ID from Engaxe API: ${extractedId} (original: ${engaxeVideo.video_id})`);
        } else {
          videoUrl = engaxeVideo.video_id;
          console.log(`Using video_id from Engaxe API: ${videoUrl}`);
        }
      }
      // Then try to use the URL provided in the request
      else if (videoData.url && videoData.url !== 'undefined' && videoData.url !== 'null') {
        // Extract the video ID if it's a URL
        const extractedId = extractEngaxeVideoId(videoData.url);
        if (extractedId) {
          videoUrl = extractedId;
          console.log(`Extracted video ID from request URL: ${extractedId} (original: ${videoData.url})`);
        } else {
          videoUrl = videoData.url;
          console.log(`Using URL from request data: ${videoUrl}`);
        }
      }
      // Finally, fall back to using just the video ID
      else {
        videoUrl = videoInfo.videoId;
        console.log(`Using video ID directly: ${videoUrl}`);
      }
    }

    // List of known valid Engaxe video IDs
    const validEngaxeIds = [
      'XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', 'KxyzuN', '4OE4QR', 'gu99XD',
      // Add any new valid IDs here
      'fgp97y', 'X4eW1I', 'c1AObf', '7mXOfb'
    ];

    // Extract the ID if it's a URL
    const extractedId = extractEngaxeVideoId(videoUrl);
    if (extractedId) {
      console.log(`Extracted video ID from input: ${extractedId} (original: ${videoUrl})`);
      videoUrl = extractedId;
    }

    // Check if the ID is a valid Engaxe ID (6-7 alphanumeric characters)
    const isValidEngaxeIdFormat = /^[a-zA-Z0-9]{6,7}$/.test(videoUrl);

    if (isValidEngaxeIdFormat) {
      console.log(`Using provided Engaxe ID which has valid format: ${videoUrl}`);

      // Add this ID to our list of known valid IDs for future reference
      if (!validEngaxeIds.includes(videoUrl)) {
        validEngaxeIds.push(videoUrl);
        console.log(`Added new ID to valid Engaxe IDs list: ${videoUrl}`);
      }
    }
    // For any other format, use a known valid ID from the list
    else {
      console.log(`Invalid Engaxe ID format: ${videoUrl}, using a known valid ID`);

      // Use a random valid ID from the list
      const randomIndex = Math.floor(Math.random() * validEngaxeIds.length);
      const validId = validEngaxeIds[randomIndex];

      console.log(`Using known valid Engaxe ID: ${validId} (original input: ${videoUrl})`);
      videoUrl = validId;
    }

    // Use the original video ID as the external ID for reference
    const externalId = videoInfo.videoId;

    console.log(`Video import data prepared:`);
    console.log(`- Thumbnail URL: ${thumbnailUrl}`);
    console.log(`- Video URL: ${videoUrl}`);
    console.log(`- Duration: ${duration}`);
    console.log(`- External ID: ${externalId}`);

    // Use the Engaxe ID directly as both the ID and URL
    const engaxeId = isValidEngaxeId(videoUrl) ? videoUrl : generateEngaxeId(); // Ensure URL is a valid Engaxe ID
    console.log(`Using Engaxe ID for imported video: ${engaxeId}`);

    // Process languages if provided
    let languages = [];

    // Check if languages are provided in the request
    if (videoData.languages && videoData.languages.length > 0) {
      console.log('Processing languages for new video:', JSON.stringify(videoData.languages, null, 2));

      // Process each language to ensure it has a valid Engaxe ID
      languages = videoData.languages.map((lang: {
        code: string;
        name: string;
        flag?: string;
        url?: string;
        isDefault?: boolean;
      }) => {
        let finalUrl = engaxeId; // Default to video's URL

        // If the language has a URL, ensure it's a valid Engaxe ID
        if (lang.url) {
          // Always try to extract the Engaxe ID from the URL first
          const extractedId = extractEngaxeVideoId(lang.url);
          if (extractedId) {
            console.log(`Extracted Engaxe ID from language URL: ${extractedId} (original: ${lang.url})`);
            finalUrl = extractedId;
          } else {
            // If extraction fails, check if the URL itself is a valid Engaxe ID
            if (isValidEngaxeId(lang.url)) {
              finalUrl = lang.url;
              console.log(`Language URL is already a valid Engaxe ID: ${finalUrl}`);
            } else {
              console.warn(`Invalid Engaxe ID format for language ${lang.code}: ${lang.url}`);
              // Use the video's URL as a fallback
              finalUrl = engaxeId;
              console.log(`Using video's URL as fallback for language ${lang.code}: ${finalUrl}`);
            }
          }
        } else {
          // If no URL is provided, use the video's URL
          console.log(`No URL provided for language ${lang.code}, using video's URL: ${finalUrl}`);
        }

        // Create a new language object with all required fields
        return {
          code: lang.code,
          name: lang.name,
          flag: lang.flag,
          url: finalUrl, // Always a string now
          isDefault: lang.isDefault !== undefined ? lang.isDefault : lang.code === 'en' // Use provided isDefault or default to English
        };
      });

      // Check if we have at least one default language
      const hasDefaultLanguage = languages.some((lang: {
        code: string;
        name: string;
        flag?: string;
        url: string;
        isDefault?: boolean;
      }) => lang.isDefault);

      // If no default language, set the first one as default
      if (!hasDefaultLanguage && languages.length > 0) {
        console.log(`No default language found, setting ${languages[0].code} as default`);
        languages[0].isDefault = true;
      }
    } else {
      // If no languages provided, create a default English language
      languages = [{
        code: 'en',
        name: 'English',
        flag: '🇺🇸',
        url: engaxeId,
        isDefault: true
      }] as any;
      console.log('No languages provided, creating default English language');
    }

    // Create new video - ensure it matches the local video format
    const video = new VideoModel({
      id: engaxeId, // Use the Engaxe ID as the primary identifier
      title: videoData.title,
      description: videoData.description,
      url: engaxeId,
      thumbnailUrl,
      duration,
      userId,
      channelId: videoData.channelId,
      visibility: videoData.visibility,
      scheduledPublishTime: videoData.scheduledPublishTime ? new Date(videoData.scheduledPublishTime) : undefined,
      tags: videoData.tags || [],
      category: videoData.category,
      contentRating: videoData.contentRating,
      processingStatus,
      commentsEnabled: videoData.commentsEnabled ?? true,
      ratingsEnabled: videoData.ratingsEnabled ?? true,
      embeddingEnabled: videoData.embeddingEnabled ?? true,
      // Add languages
      languages,
      // Add required fields from BaseSchema
      createdBy: userId,
      updatedBy: userId,
      stats: {
        views: 0,
        likes: 0,
        dislikes: 0,
        comments: 0,
        shares: 0,
        playlistAdds: 0,
        averageWatchTime: 0,
        retentionRate: 0,
      },
      file: {
        originalName: `imported_${Date.now()}`,
        size: 0,
        mimeType: 'video/mp4',
        codec: '',
        resolution: '',
        bitrate: 0,
        frameRate: 0,
      },
      variants: [],
      chapters: videoData.chapters || [],
      source: {
        type: videoData.source.type,
        originalUrl: videoUrl, // Store just the video ID here too, not the full URL
        platform: videoData.source.platform,
        externalId: externalId,
      },
    });

    try {
      console.log('Attempting to save video to database:', {
        id: video.id,
        title: video.title,
        url: video.url,
        thumbnailUrl: video.thumbnailUrl,
        channelId: video.channelId,
        source: video.source
      });

      // Check MongoDB connection status before saving
      if (mongoose.connection.readyState !== 1) {
        console.error('MongoDB connection is not active. Current state:', mongoose.connection.readyState);
        throw new Error('Database connection is not active. Please check MongoDB connection.');
      }

      console.log('Attempting to save video to database with ID:', video.id);
      await video.save();
      console.log('Video successfully saved to database with ID:', video.id);

      // Verify the video was actually saved
      const savedVideo = await VideoModel.findOne({ id: video.id });
      if (savedVideo) {
        console.log('Successfully verified video in database with ID:', video.id);
      } else {
        console.error('Video was not found in database after save operation!');
        throw new Error('Video save operation did not persist to database');
      }
    } catch (error: any) {
      console.error('Error saving video to database:', error);
      console.error('Error stack:', error.stack);

      // Log more details about the error
      if (error.name === 'ValidationError' && error.errors) {
        console.error('Validation errors:');
        for (const field in error.errors) {
          console.error(`- Field '${field}':`, error.errors[field]?.message || 'Unknown validation error');
        }

        // Log the full video object to help diagnose validation issues
        console.error('Video object with validation errors:', JSON.stringify(video.toObject(), null, 2));
      } else if ((error.name === 'MongoError' || error.name === 'MongoServerError') && error.code === 11000 && error.keyValue) {
        console.error('Duplicate key error:', error.keyValue);
        throw createBadRequestError('Video already exists in database', ErrorCodes.VIDEO_ALREADY_EXISTS);
      }

      throw error;
    }

    // Add video to JSON storage
    try {
      await this.jsonStorageService.addVideo(video);
    } catch (error) {
      console.error('Error adding imported video to JSON storage:', error);
      // Don't throw error here as the video was successfully saved to database
    }

    // Update channel video count
    channel.stats.videoCount += 1;
    await channel.save();

    // If it's an import (not embed), simulate processing
    if (videoData.source.type === 'import') {
      setTimeout(async () => {
        try {
          video.processingStatus = 'ready';
          await video.save();

          // Update video in JSON storage after processing
          try {
            await this.jsonStorageService.updateVideo(video);
          } catch (error) {
            console.error('Error updating imported video in JSON storage after processing:', error);
          }
        } catch (error: any) {
          console.error('Error updating video status:', error?.message || error);
        }
      }, 5000);
    }

    return video;
  }

  /**
   * Update an existing video
   */
  async updateVideo(userId: string, videoId: string, videoData: {
    title?: string;
    description?: string;
    channelId?: string;
    category?: string;
    tags?: string[];
    visibility?: 'public' | 'unlisted' | 'private' | 'scheduled';
    scheduledPublishTime?: string;
    contentRating?: 'general' | 'teen' | 'mature' | 'explicit';
    commentsEnabled?: boolean;
    ratingsEnabled?: boolean;
    embeddingEnabled?: boolean;
    chapters?: Array<{
      title: string;
      startTime: number;
      endTime: number;
    }>;
    location?: {
      latitude: number;
      longitude: number;
      name: string;
    };
    copyright?: {
      owner: string;
      license: string;
      allowReuse: boolean;
      allowCommercialUse: boolean;
      allowModification: boolean;
      attributionRequired: boolean;
    };
    thumbnailUrl?: string;
    languages?: Array<{
      code: string;
      name: string;
      flag?: string;
      url?: string;
      isDefault?: boolean;
    }>;
  }) {
    // Find video
    const video = await VideoModel.findOne({ id: videoId, deletedAt: null });
    if (!video) {
      throw createNotFoundError('Video not found', ErrorCodes.VIDEO_NOT_FOUND);
    }

    // Check if user has permission
    const channel = await ChannelModel.findOne({ id: video.channelId, deletedAt: null });
    if (!channel) {
      throw createNotFoundError('Channel not found', ErrorCodes.CHANNEL_NOT_FOUND);
    }

    if (video.userId !== userId && channel.ownerId !== userId && !channel.moderators.includes(userId)) {
      throw createForbiddenError('You do not have permission to update this video', ErrorCodes.VIDEO_PERMISSION_DENIED);
    }

    // If changing channel, check if user has permission for the new channel
    if (videoData.channelId && videoData.channelId !== video.channelId) {
      const newChannel = await ChannelModel.findOne({ id: videoData.channelId, deletedAt: null });
      if (!newChannel) {
        throw createNotFoundError('New channel not found', ErrorCodes.CHANNEL_NOT_FOUND);
      }

      if (newChannel.ownerId !== userId && !newChannel.moderators.includes(userId)) {
        throw createForbiddenError('You do not have permission to move videos to this channel', ErrorCodes.CHANNEL_PERMISSION_DENIED);
      }

      // Update channel video counts
      channel.stats.videoCount -= 1;
      newChannel.stats.videoCount += 1;
      await channel.save();
      await newChannel.save();

      video.channelId = videoData.channelId;
    }

    // Update video fields
    if (videoData.title) video.title = videoData.title;
    if (videoData.description) video.description = videoData.description;
    if (videoData.category) video.category = videoData.category;
    if (videoData.tags) video.tags = videoData.tags;
    if (videoData.visibility) video.visibility = videoData.visibility;
    if (videoData.scheduledPublishTime) {
      video.scheduledPublishTime = new Date(videoData.scheduledPublishTime);
    }
    if (videoData.contentRating) video.contentRating = videoData.contentRating;
    if (videoData.commentsEnabled !== undefined) video.commentsEnabled = videoData.commentsEnabled;
    if (videoData.ratingsEnabled !== undefined) video.ratingsEnabled = videoData.ratingsEnabled;
    if (videoData.embeddingEnabled !== undefined) video.embeddingEnabled = videoData.embeddingEnabled;
    if (videoData.chapters) video.chapters = videoData.chapters;
    if (videoData.location) video.location = videoData.location;
    if (videoData.copyright) video.copyright = videoData.copyright;
    if (videoData.thumbnailUrl) video.thumbnailUrl = videoData.thumbnailUrl;

    // Update languages if provided
    if (videoData.languages) {
      console.log(`Updating languages for video ${videoId}`);
      console.log('New languages:', JSON.stringify(videoData.languages, null, 2));

      // Process each language to ensure it has a valid Engaxe ID
      const processedLanguages = videoData.languages.map(lang => {
        let finalUrl = video.url; // Default to video's URL

        // If the language has a URL, ensure it's a valid Engaxe ID
        if (lang.url) {
          // Always try to extract the Engaxe ID from the URL first
          const extractedId = extractEngaxeVideoId(lang.url);
          if (extractedId) {
            console.log(`Extracted Engaxe ID from language URL: ${extractedId} (original: ${lang.url})`);
            finalUrl = extractedId;
          } else {
            // If extraction fails, check if the URL itself is a valid Engaxe ID
            if (isValidEngaxeId(lang.url)) {
              finalUrl = lang.url;
              console.log(`Language URL is already a valid Engaxe ID: ${finalUrl}`);
            } else {
              console.warn(`Invalid Engaxe ID format for language ${lang.code}: ${lang.url}`);
              // Use the video's URL as a fallback
              finalUrl = video.url;
              console.log(`Using video's URL as fallback for language ${lang.code}: ${finalUrl}`);
            }
          }
        } else {
          // If no URL is provided, use the video's URL
          console.log(`No URL provided for language ${lang.code}, using video's URL: ${finalUrl}`);
        }

        // Create a new language object with all required fields
        return {
          code: lang.code,
          name: lang.name,
          url: finalUrl, // Always a string now
          isDefault: typeof (lang as any).isDefault !== 'undefined' ? (lang as any).isDefault : lang.code === 'en' // Use provided isDefault or default to English
        };
      });

      // Check if we have at least one default language
      const hasDefaultLanguage = processedLanguages.some(lang => lang.isDefault);

      // If no default language, set the first one as default
      if (!hasDefaultLanguage && processedLanguages.length > 0) {
        console.log(`No default language found, setting ${processedLanguages[0].code} as default`);
        processedLanguages[0].isDefault = true;
      }

      // Update the video's languages
      video.languages = processedLanguages;
      console.log(`Updated languages for video ${videoId}:`, JSON.stringify(processedLanguages, null, 2));

      // Create mappings for all language URLs
      for (const lang of processedLanguages) {
        if (lang.url && isValidEngaxeId(lang.url)) {
          try {
            await videoIdMappingService.createMapping(video.id, lang.url, `${video.title} (${lang.name})`);
            console.log(`Created mapping for language ${lang.code}: ${video.id} -> ${lang.url}`);
          } catch (error) {
            console.error(`Error creating mapping for language ${lang.code}:`, error);
          }
        }
      }
    }

    // Update the updatedBy field
    video.updatedBy = userId;
    await video.save();

    // Update video in JSON storage
    try {
      await this.jsonStorageService.updateVideo(video);
    } catch (error) {
      console.error('Error updating video in JSON storage:', error);
      // Don't throw error here as the video was successfully updated in database
    }

    return video;
  }

  /**
   * Get a video by ID
   * @param videoId Video ID (can be either a hash ID or an Engaxe ID)
   * @param incrementViews Whether to increment the view count
   * @returns Video with channel information
   */
  async getVideoById(videoId: string, incrementViews = false) {
    let video;

    // First try to find the video by hash ID
    video = await VideoModel.findOne({
      id: videoId,
      deletedAt: null,
      $or: [
        { visibility: 'public' },
        { visibility: 'unlisted' },
        { visibility: 'scheduled', scheduledPublishTime: { $lte: new Date() } },
      ],
    });

    // If not found, check if it's an Engaxe ID
    if (!video && isValidEngaxeId(videoId)) {
      console.log(`Video not found by hash ID, trying to find by Engaxe ID: ${videoId}`);
      video = await VideoModel.findOne({
        url: videoId,
        deletedAt: null,
        $or: [
          { visibility: 'public' },
          { visibility: 'unlisted' },
          { visibility: 'scheduled', scheduledPublishTime: { $lte: new Date() } },
        ],
      });

      if (video) {
        console.log(`Found video by Engaxe ID: ${videoId}, hash ID: ${video.id}`);
      }
    }

    // If still not found, try to look up the hash ID from the mapping service
    if (!video) {
      console.log(`Video not found by direct lookup, checking mapping service`);

      // Try to get the hash ID from the Engaxe ID
      const hashId = await videoIdMappingService.getHashIdFromEngaxeId(videoId);

      if (hashId) {
        console.log(`Found hash ID ${hashId} for Engaxe ID ${videoId} in mapping service`);
        video = await VideoModel.findOne({
          id: hashId,
          deletedAt: null,
          $or: [
            { visibility: 'public' },
            { visibility: 'unlisted' },
            { visibility: 'scheduled', scheduledPublishTime: { $lte: new Date() } },
          ],
        });

        if (video) {
          console.log(`Found video using mapping: ${videoId} -> ${hashId}`);
        }
      }

      // If still not found, try to get the Engaxe ID from the hash ID
      if (!video) {
        const engaxeId = await videoIdMappingService.getEngaxeIdFromHashId(videoId);

        if (engaxeId) {
          console.log(`Found Engaxe ID ${engaxeId} for hash ID ${videoId} in mapping service`);
          video = await VideoModel.findOne({
            url: engaxeId,
            deletedAt: null,
            $or: [
              { visibility: 'public' },
              { visibility: 'unlisted' },
              { visibility: 'scheduled', scheduledPublishTime: { $lte: new Date() } },
            ],
          });

          if (video) {
            console.log(`Found video using mapping: ${videoId} -> ${engaxeId}`);
          }
        }
      }
    }

    if (!video) {
      throw createNotFoundError('Video not found', ErrorCodes.VIDEO_NOT_FOUND);
    }

    // Log video languages for debugging
    console.log(`Video ${videoId} found with title: ${video.title}`);
    if (video.languages && video.languages.length > 0) {
      console.log(`Video has ${video.languages.length} languages in database:`);
      video.languages.forEach((lang, idx) => {
        console.log(`Language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url}, isDefault: ${lang.isDefault}, flag: ${lang.flag || 'none'}`);
      });
    } else {
      console.log(`Video has no languages in database`);
    }

    // Ensure the video has a mapping
    if (!await videoIdMappingService.getEngaxeIdFromHashId(video.id)) {
      console.log(`Creating missing mapping for video ${video.id} -> ${video.url}`);
      await videoIdMappingService.createMapping(video.id, video.url, video.title);
    }

    // Increment view count if requested
    if (incrementViews) {
      video.stats.views += 1;
      await video.save();

      // Update channel total views
      await ChannelModel.updateOne(
        { id: video.channelId },
        { $inc: { 'stats.totalViews': 1 } }
      );
    }

    // Get channel details
    const channel = await ChannelModel.findOne({ id: video.channelId });

    // Ensure the video has a valid Engaxe ID before returning it
    await this.ensureValidEngaxeId(video);

    // Process the video object to ensure URLs are properly formatted
    const videoObj = video.toObject();

    // Get the Engaxe ID from the mapping service
    console.log(`Getting Engaxe ID for video ${videoObj.id} from mapping service`);
    const engaxeId = await videoIdMappingService.getEngaxeIdFromHashId(videoObj.id);

    if (engaxeId) {
      console.log(`Found Engaxe ID ${engaxeId} for video ${videoObj.id} in mapping service`);
      videoObj.url = engaxeId;

      // Update the database if needed
      if (video.url !== engaxeId) {
        try {
          await VideoModel.updateOne({ id: video.id }, { $set: { url: engaxeId } });
          console.log(`Updated video ${video.id} in the database with Engaxe ID: ${engaxeId}`);
        } catch (error) {
          console.error(`Error updating video ${video.id} in the database:`, error);
        }
      }
    } else {
      console.log(`No mapping found for video ${videoObj.id}, checking if URL is valid`);

      // Ensure the URL is set - this is critical for the client
      if (!videoObj.url || videoObj.url === '') {
        console.log(`Video ${videoObj.id} has no URL, setting default URL`);

        // Generate a new Engaxe ID
        const newEngaxeId = generateEngaxeId();
        videoObj.url = newEngaxeId;
        console.log(`Generated new Engaxe ID for video ${videoObj.id}: ${newEngaxeId}`);

        // Create a mapping
        await videoIdMappingService.createMapping(videoObj.id, newEngaxeId, videoObj.title || 'Untitled Video');

        // Update the database
        try {
          await VideoModel.updateOne({ id: video.id }, { $set: { url: newEngaxeId } });
          console.log(`Updated video ${video.id} in the database with new Engaxe ID: ${newEngaxeId}`);
        } catch (error) {
          console.error(`Error updating video ${video.id} in the database:`, error);
        }
      }
      // Check if the URL is a hash format (32 hex characters)
      else if (isHashId(videoObj.url)) {
        console.log(`Video ${videoObj.id} has a hash URL: ${videoObj.url}`);

        // Generate a new Engaxe ID
        const newEngaxeId = generateEngaxeId();
        videoObj.url = newEngaxeId;
        console.log(`Generated new Engaxe ID for video ${videoObj.id}: ${newEngaxeId}`);

        // Create a mapping
        await videoIdMappingService.createMapping(videoObj.id, newEngaxeId, videoObj.title || 'Untitled Video');

        // Update the database
        try {
          await VideoModel.updateOne({ id: video.id }, { $set: { url: newEngaxeId } });
          console.log(`Updated video ${video.id} in the database with new Engaxe ID: ${newEngaxeId}`);
        } catch (error) {
          console.error(`Error updating video ${video.id} in the database:`, error);
        }
      }
      // If the URL is already a valid Engaxe ID, create a mapping
      else if (isValidEngaxeId(videoObj.url)) {
        console.log(`Video ${videoObj.id} already has a valid Engaxe ID: ${videoObj.url}`);

        // Create a mapping
        await videoIdMappingService.createMapping(videoObj.id, videoObj.url, videoObj.title || 'Untitled Video');
      }
    }

    // Process language URLs if they exist
    if (videoObj.languages && videoObj.languages.length > 0) {
      console.log(`Processing ${videoObj.languages.length} languages for video ${videoObj.id}`);

      // Check if we have at least one default language
      const hasDefaultLanguage = videoObj.languages.some((lang: any) => lang.isDefault);

      // If no default language, set the first one as default
      if (!hasDefaultLanguage && videoObj.languages.length > 0) {
        console.log(`No default language found, setting ${videoObj.languages[0].code} as default`);
        videoObj.languages[0].isDefault = true;

        // Update the database
        try {
          const updateQuery = { id: video.id, 'languages.code': videoObj.languages[0].code };
          const updateData = { $set: { 'languages.$.isDefault': true } };
          await VideoModel.updateOne(updateQuery, updateData);
          console.log(`Updated language ${videoObj.languages[0].code} for video ${video.id} in the database to be default`);
        } catch (error) {
          console.error(`Error updating default language for video ${video.id} in the database:`, error);
        }
      }

      // Process each language to ensure it has a valid URL
      const updatedLanguages = [];

      for (const lang of videoObj.languages) {
        // Create a copy of the language object to avoid modifying the original
        const processedLang = { ...lang };

        // Ensure each language has a valid URL
        if (!processedLang.url || !isValidEngaxeId(processedLang.url)) {
          // Try to get a mapping for this language
          const langMappingKey = `${video.id}_${processedLang.code}`;
          const mappedUrl = videoIdMappingService.getMappingByKey(langMappingKey);

          if (mappedUrl && isValidEngaxeId(mappedUrl)) {
            console.log(`Found mapped URL for language ${processedLang.code}: ${mappedUrl}`);
            processedLang.url = mappedUrl;
          } else {
            // If no mapping found, use the video URL
            console.log(`Setting language ${processedLang.code} URL to video's Engaxe ID: ${videoObj.url}`);
            processedLang.url = videoObj.url;
          }

          // Update the database
          try {
            const updateQuery = { id: video.id, 'languages.code': processedLang.code };
            const updateData = { $set: { 'languages.$.url': processedLang.url } };
            await VideoModel.updateOne(updateQuery, updateData);
            console.log(`Updated language ${processedLang.code} for video ${video.id} in the database with URL: ${processedLang.url}`);
          } catch (error) {
            console.error(`Error updating language ${processedLang.code} for video ${video.id} in the database:`, error);
          }
        } else {
          console.log(`Language ${processedLang.code} already has a valid URL: ${processedLang.url}`);
        }

        // Add the processed language to the updated languages array
        updatedLanguages.push(processedLang);

        // Create a mapping for this language URL
        if (processedLang.url && isValidEngaxeId(processedLang.url)) {
          try {
            const langMappingKey = `${video.id}_${processedLang.code}`;
            await videoIdMappingService.createMappingWithKey(
              langMappingKey,
              processedLang.url,
              `${video.title} (${processedLang.name})`
            );
            console.log(`Created mapping for language ${processedLang.code}: ${langMappingKey} -> ${processedLang.url}`);
          } catch (error) {
            console.error(`Error creating mapping for language ${processedLang.code}:`, error);
          }
        }
      }

      // Update the video object with the processed languages
      videoObj.languages = updatedLanguages;
    } else {
      // If no languages, add a default one
      videoObj.languages = [{
        code: 'en',
        name: 'English',
        isDefault: true,
        url: videoObj.url
      }];
      console.log(`No languages found for video ${videoObj.id}, added default language with URL: ${videoObj.url}`);

      // Also update the database with the new language
      try {
        await VideoModel.updateOne({ id: video.id }, { $set: { languages: videoObj.languages } });
        console.log(`Updated video ${video.id} in the database with new languages`);
      } catch (error) {
        console.error(`Error updating video ${video.id} in the database:`, error);
      }
    }

    return {
      ...videoObj,
      channel: channel ? {
        id: channel.id,
        name: channel.name,
        displayName: channel.displayName,
        avatar: channel.avatar,
        isVerified: channel.isVerified,
        subscriberCount: channel.stats.subscribers,
      } : null,
    };
  }

  /**
   * Get videos with pagination and filtering
   */
  async getVideos(options: {
    page?: number | string;
    limit?: number | string;
    sort?: 'newest' | 'popular' | 'trending';
    category?: string;
    channelId?: string;
    userId?: string;
    search?: string;
    tags?: string[] | string;
    contentRating?: 'general' | 'teen' | 'mature' | 'explicit';
  }) {
    console.log('Getting videos with options:', JSON.stringify(options, null, 2));

    // Convert string values to numbers
    const page = typeof options.page === 'string' ? parseInt(options.page) : (options.page || 1);
    const limit = typeof options.limit === 'string' ? parseInt(options.limit) : (options.limit || 20);
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {
      deletedAt: null,
      visibility: 'public',
      processingStatus: 'ready',
    };

    console.log('Base query:', JSON.stringify(query, null, 2));

    if (options.category) {
      query.category = options.category;
    }

    if (options.channelId) {
      query.channelId = options.channelId;
    }

    if (options.userId) {
      query.userId = options.userId;
    }

    if (options.search) {
      query.$or = [
        { title: { $regex: options.search, $options: 'i' } },
      ];
    }

    if (options.tags && options.tags.length > 0) {
      query.tags = { $in: options.tags };
    }

    if (options.contentRating) {
      // If content rating is specified, show videos with that rating or lower
      const ratings = ['general', 'teen', 'mature', 'explicit'];
      const maxIndex = ratings.indexOf(options.contentRating);
      query.contentRating = { $in: ratings.slice(0, maxIndex + 1) };
    }

    // Determine sort order
    let sortOptions: any = { createdAt: -1 }; // Default to newest
    if (options.sort === 'popular') {
      sortOptions = { 'stats.views': -1 };
    } else if (options.sort === 'trending') {
      // For trending, we would ideally use a more complex algorithm
      // For now, we'll use a simple proxy of recent videos with high view counts
      sortOptions = { 'stats.views': -1, createdAt: -1 };
    }

    // Get total count
    const total = await VideoModel.countDocuments(query);

    // Get videos
    const videos = await VideoModel.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(limit);

    // Get channel details for each video
    const channelIds = [...new Set(videos.map(video => video.channelId))];
    const channels = await ChannelModel.find({ id: { $in: channelIds } });
    const channelMap = channels.reduce((map, channel) => {
      map[channel.id] = channel;
      return map;
    }, {} as Record<string, any>);

    const videosWithChannels = videos.map(video => {
      const channel = channelMap[video.channelId];

      // Process the video object to ensure URLs are properly formatted
      const videoObj = video.toObject();

      // Ensure the URL is set - this is critical for the client
      if (!videoObj.url || videoObj.url === '') {
        console.log(`Video ${videoObj.id} has no URL, setting default URL`);

        // If it's an Engaxe video, use the externalId
        if (videoObj.source && videoObj.source.platform === 'engaxe' && videoObj.source.externalId) {
          videoObj.url = videoObj.source.externalId;
          console.log(`Set URL to Engaxe externalId: ${videoObj.url}`);
        } else {
          // Otherwise use a guaranteed working ID
          videoObj.url = getRandomValidEngaxeId();
          console.log(`Set URL to guaranteed working ID: ${videoObj.url}`);
        }
      }

      // Check if the URL is not a valid 6-7 character Engaxe ID
      if (!isValidEngaxeId(videoObj.url)) {
        console.log(`Video ${videoObj.id} has an invalid URL: ${videoObj.url}`);

        // Try to get a valid Engaxe ID from the source
        let validId = null;

        // Check if we have a valid Engaxe ID in source.externalId
        if (videoObj.source && videoObj.source.externalId && isValidEngaxeId(videoObj.source.externalId)) {
          validId = videoObj.source.externalId;
          console.log(`Found valid Engaxe ID in source.externalId: ${validId}`);
        }
        // Check if we have a valid Engaxe ID in source.originalUrl
        else if (videoObj.source && videoObj.source.originalUrl) {
          // If it's a URL, try to extract the ID
          if (videoObj.source.originalUrl.includes('engaxe.com')) {
            const extractedId = extractEngaxeVideoId(videoObj.source.originalUrl);
            if (extractedId && isValidEngaxeId(extractedId)) {
              validId = extractedId;
              console.log(`Extracted valid Engaxe ID from source.originalUrl: ${validId}`);
            }
          }
          // If it's already a valid ID, use it directly
          else if (isValidEngaxeId(videoObj.source.originalUrl)) {
            validId = videoObj.source.originalUrl;
            console.log(`Found valid Engaxe ID in source.originalUrl: ${validId}`);
          }
        }

        // If we couldn't find a valid Engaxe ID, use a guaranteed working ID
        if (!validId) {
          console.log(`Could not find a valid Engaxe ID for video ${videoObj.id}, using a guaranteed working ID`);
          validId = getRandomValidEngaxeId();
          console.log(`Using random valid Engaxe ID: ${validId}`);
        }

        // Update the URL with the valid Engaxe ID
        videoObj.url = validId;
        console.log(`Using valid Engaxe ID for video ${videoObj.id}: ${validId}`);

        // Also update the database with the new URL
        try {
          VideoModel.updateOne({ id: video.id }, { $set: { url: validId } }).then(() => {
            console.log(`Updated video ${video.id} in the database with valid Engaxe ID: ${validId}`);
          }).catch((error) => {
            console.error(`Error updating video ${video.id} in the database:`, error);
          });
        } catch (error) {
          console.error(`Error updating video ${video.id} in the database:`, error);
        }
      }

      // Process language URLs if they exist
      if (videoObj.languages && videoObj.languages.length > 0) {
        videoObj.languages = videoObj.languages.map((lang: any) => {
          // If language URL is missing, use the video URL
          if (!lang.url || lang.url === '') {
            lang.url = videoObj.url;
            console.log(`Language URL missing for video ${videoObj.id}, using video URL: ${videoObj.url}`);
          }

          // Check if the language URL is not a valid 6-7 character Engaxe ID
          if (!isValidEngaxeId(lang.url)) {
            console.log(`Language ${lang.code} for video ${videoObj.id} has an invalid URL: ${lang.url}`);

            // Use the video URL (which is now guaranteed to be a valid Engaxe ID)
            console.log(`Replacing invalid language URL ${lang.url} with video's Engaxe ID: ${videoObj.url}`);
            lang.url = videoObj.url;

            // Also update the database with the new URL
            try {
              const updateQuery = { id: video.id, 'languages.code': lang.code };
              const updateData = { $set: { 'languages.$.url': videoObj.url } };
              VideoModel.updateOne(updateQuery, updateData).then(() => {
                console.log(`Updated language ${lang.code} for video ${video.id} in the database with video's Engaxe ID: ${videoObj.url}`);
              }).catch((error) => {
                console.error(`Error updating language ${lang.code} for video ${video.id} in the database:`, error);
              });
            } catch (error) {
              console.error(`Error updating language ${lang.code} for video ${video.id} in the database:`, error);
            }
          }

          return lang;
        });
      } else {
        // If no languages, add a default one
        videoObj.languages = [{
          code: 'en',
          name: 'English',
          isDefault: true,
          url: videoObj.url
        }];
        console.log(`No languages found for video ${videoObj.id}, added default language with URL: ${videoObj.url}`);

        // Also update the database with the new language
        try {
          VideoModel.updateOne({ id: video.id }, { $set: { languages: videoObj.languages } }).then(() => {
            console.log(`Updated video ${video.id} in the database with new languages`);
          }).catch((error) => {
            console.error(`Error updating video ${video.id} in the database:`, error);
          });
        } catch (error) {
          console.error(`Error updating video ${video.id} in the database:`, error);
        }
      }

      return {
        ...videoObj,
        channel: channel ? {
          id: channel.id,
          name: channel.name,
          displayName: channel.displayName,
          avatar: channel.avatar,
          isVerified: channel.isVerified,
        } : null,
      };
    });

    // Log the number of videos found and their IDs for debugging
    console.log(`Found ${videosWithChannels.length} videos matching the query`);
    if (videosWithChannels.length > 0) {
      console.log('Video IDs:', videosWithChannels.map(v => v.id).join(', '));
      console.log('First video details:', {
        id: videosWithChannels[0].id,
        title: videosWithChannels[0].title,
        url: videosWithChannels[0].url,
        processingStatus: videosWithChannels[0].processingStatus,
        visibility: videosWithChannels[0].visibility,
        channelId: videosWithChannels[0].channelId,
        channel: videosWithChannels[0].channel ? videosWithChannels[0].channel.name : 'No channel'
      });
    } else {
      console.log('No videos found matching the query');
    }

    return {
      videos: videosWithChannels,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get videos by channel
   */
  async getChannelVideos(channelId: string, options: {
    page?: number | string;
    limit?: number | string;
    sort?: 'newest' | 'popular' | 'oldest';
  }) {
    // Convert string values to numbers
    const page = typeof options.page === 'string' ? parseInt(options.page) : (options.page || 1);
    const limit = typeof options.limit === 'string' ? parseInt(options.limit) : (options.limit || 20);
    const skip = (page - 1) * limit;

    // Check if channel exists
    const channel = await ChannelModel.findOne({ id: channelId, deletedAt: null });
    if (!channel) {
      throw createNotFoundError('Channel not found', ErrorCodes.CHANNEL_NOT_FOUND);
    }

    // Build query
    const query: any = {
      channelId,
      deletedAt: null,
      visibility: 'public',
      processingStatus: 'ready',
    };

    // Determine sort order
    let sortOptions: any = { createdAt: -1 }; // Default to newest
    if (options.sort === 'popular') {
      sortOptions = { 'stats.views': -1 };
    } else if (options.sort === 'oldest') {
      sortOptions = { createdAt: 1 };
    }

    // Get total count
    const total = await VideoModel.countDocuments(query);

    // Get videos
    const videos = await VideoModel.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(limit);

    return {
      videos,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
      channel: {
        id: channel.id,
        name: channel.name,
        displayName: channel.displayName,
        avatar: channel.avatar,
        isVerified: channel.isVerified,
        subscriberCount: channel.stats.subscribers,
      },
    };
  }

  /**
   * Get Engaxe video details using AJAX endpoint
   */
  async getEngaxeVideoDetails(videoId: string) {
    try {
      console.log(`Fetching Engaxe video details for ID: ${videoId}`);

      // Extract video ID if a full URL was provided
      const extractedId = extractEngaxeVideoId(videoId);

      if (extractedId) {
        console.log(`Extracted video ID from input: ${extractedId} (original: ${videoId})`);
        videoId = extractedId;
      } else {
        console.log(`Using original video ID: ${videoId}`);
      }

      console.log(`Using AJAX endpoint to fetch video details for ID: ${videoId}`);
      const videoDetails = await engaxeApiService.getVideoDetailsAjax(videoId);
      console.log('Successfully retrieved video details:', JSON.stringify(videoDetails, null, 2));
      return videoDetails;
    } catch (error: any) {
      console.error('Error fetching Engaxe video details:', error?.message || error);

      // Generate fallback data for development
      console.warn('Generating fallback video details');

      // Customize mock data based on video ID
      let mockTitle, mockDescription, mockCategory;

      if (videoId === 'XLcMq2') {
        mockTitle = 'Understanding Legal Rights in Employment';
        mockDescription = 'A comprehensive guide to understanding your legal rights in the workplace, including discrimination, harassment, and wrongful termination.';
        mockCategory = 'Employment Law';
      } else if (videoId === 'a71tuY') {
        mockTitle = 'Contract Law Fundamentals';
        mockDescription = 'Learn the basics of contract law including formation, terms, conditions, and remedies for breach of contract.';
        mockCategory = 'Contract Law';
      } else {
        // Default mock data
        mockTitle = `Engaxe Video ${videoId}`;
        mockDescription = 'This is a sample description for an Engaxe video.';
        mockCategory = 'Education';
      }

      const fallbackDetails = {
        id: videoId,
        video_id: videoId,
        title: mockTitle,
        description: mockDescription,
        thumbnail: `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`,
        duration: '330',
        views: '1000',
        likes: '50',
        dislikes: '5',
        category_name: mockCategory,
        tags: 'video,sample,engaxe',
        owner: {
          id: '456',
          username: 'Engaxe Creator',
          avatar: 'https://placehold.co/100x100/333333/FFFFFF?text=EC'
        }
      };

      console.log('Using fallback video details:', JSON.stringify(fallbackDetails, null, 2));
      return fallbackDetails;
    }
  }



  /**
   * Save Engaxe video to database
   */
  async saveEngaxeVideoToDatabase(
    userId: string,
    channelId: string,
    videoId: string,
    videoDetails: any,
    languages?: Array<{
      code: string;
      name: string;
      flag?: string;
      url?: string;
      isDefault?: boolean;
    }>
  ) {
    try {
      console.log('=== SAVE ENGAXE VIDEO TO DATABASE DEBUG ===');
      console.log(`Saving Engaxe video ${videoId} to database for user ${userId} in channel ${channelId}`);
      console.log('Video details:', JSON.stringify(videoDetails, null, 2));
      console.log('Languages:', languages ? JSON.stringify(languages, null, 2) : 'None provided');

      // List of known valid Engaxe video IDs (6-7 digit format only)
      const validEngaxeIds = [
        'XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', 'KxyzuN', '4OE4QR', 'gu99XD',
        'fgp97y', 'X4eW1I', 'c1AObf', '99BXqa', 'Hs7Qzd', '7mXOfb'
      ];

      // Extract the ID if it's a URL
      const extractedId = extractEngaxeVideoId(videoId);
      if (extractedId) {
        console.log(`Extracted video ID from input: ${extractedId} (original: ${videoId})`);
        videoId = extractedId;
      }

      // Check if the ID is a valid Engaxe ID (6-7 alphanumeric characters)
      const isValidEngaxeIdFormat = /^[a-zA-Z0-9]{6,7}$/.test(videoId);

      if (isValidEngaxeIdFormat) {
        console.log(`Using provided Engaxe ID which has valid format: ${videoId}`);

        // Add this ID to our list of known valid IDs for future reference
        if (!validEngaxeIds.includes(videoId)) {
          validEngaxeIds.push(videoId);
          console.log(`Added new ID to valid Engaxe IDs list: ${videoId}`);
        }
      }
      // For any other format, reject the request
      else {
        console.error(`Invalid Engaxe ID format: ${videoId}. Must be 6-7 alphanumeric characters.`);
        throw createBadRequestError('Invalid Engaxe ID. Must be 6-7 alphanumeric characters.', ErrorCodes.INVALID_INPUT);
      }

      console.log(`Using Engaxe ID: ${videoId}`);

      // Always use the extracted/provided ID - no more random IDs
      // This ensures the video plays correctly

      // Check if channel exists and user has permission
      console.log(`Looking for channel with ID: ${channelId}`);

      // Try multiple ways to find the channel
      let channel;

      // First try by ID
      try {
        channel = await ChannelModel.findOne({ id: channelId, deletedAt: null });
        if (channel) {
          console.log(`Found channel by ID: ${channel.name} (${channel.id})`);
        }
      } catch (err) {
        console.error('Error finding channel by ID:', err);
      }

      // If not found, try by MongoDB _id
      if (!channel && channelId.match(/^[0-9a-fA-F]{24}$/)) {
        try {
          console.log(`Channel not found by ID, trying to find by MongoDB _id: ${channelId}`);
          channel = await ChannelModel.findOne({ _id: channelId, deletedAt: null });
          if (channel) {
            console.log(`Found channel by MongoDB _id: ${channel.name} (${channel.id})`);
          }
        } catch (err) {
          console.error('Error finding channel by MongoDB _id:', err);
        }
      }

      // If still not found, try a more flexible query
      if (!channel) {
        try {
          console.log('Trying more flexible channel query');
          channel = await ChannelModel.findOne({
            $or: [
              { id: { $regex: new RegExp(channelId, 'i') }, deletedAt: null },
              { name: { $regex: new RegExp(channelId, 'i') }, deletedAt: null }
            ]
          });
          if (channel) {
            console.log(`Found channel using flexible query: ${channel.name} (${channel.id})`);
          }
        } catch (err) {
          console.error('Error with flexible channel query:', err);
        }
      }

      // If still not found, try to find any channel owned by the user
      if (!channel) {
        console.error(`Channel not found with ID: ${channelId}`);
        console.log(`Trying to find any channel owned by user: ${userId}`);

        const userChannel = await ChannelModel.findOne({
          ownerId: userId,
          deletedAt: null,
        });

        if (userChannel) {
          console.log(`Found alternative channel with ID: ${userChannel.id}`);
          channelId = userChannel.id;
          channel = userChannel;
        } else {
          // Create a default channel for the user
          console.log(`No channels found for user ${userId}, creating a default channel`);
          try {
            // Get the user's username first
            const user = await UserModel.findOne({ id: userId });
            if (!user) {
              throw new Error(`User not found with ID: ${userId}`);
            }

            const defaultChannel = new ChannelModel({
              id: generateId(),
              name: `${user.username}-channel`,
              displayName: `${user.username}-channel`,
              description: 'My default channel created automatically',
              ownerId: userId,
              moderators: [],
              tags: [],
              isVerified: false,
              isFeatured: false,
              visibility: 'public',
              status: 'active',
              stats: {
                subscribers: 0,
                totalViews: 0,
                videoCount: 0
              }
            });

            await defaultChannel.save();
            console.log(`Created default channel with ID: ${defaultChannel.id}`);
            channel = defaultChannel;
            channelId = defaultChannel.id;
          } catch (createError) {
            console.error('Error creating default channel:', createError);
            throw createNotFoundError('Channel not found and could not create a default channel. Please create a channel first.', ErrorCodes.CHANNEL_NOT_FOUND);
          }
        }
      } else {
        console.log(`Found channel: ${channel.name} (${channel.id})`);
      }

      // At this point, channel should never be null because we either found it, found an alternative, or threw an error
      if (channel.ownerId !== userId && !channel.moderators?.includes(userId)) {
        console.error(`User ${userId} does not have permission for channel ${channel.id}`);
        throw createForbiddenError('You do not have permission to add videos to this channel', ErrorCodes.INSUFFICIENT_PERMISSIONS);
      }

      // Check if video already exists - use a more flexible query to avoid duplicates
      const existingVideo = await VideoModel.findOne({
        $or: [
          { 'source.externalId': videoId, 'source.platform': 'engaxe', deletedAt: null },
          { url: videoId, deletedAt: null }
        ]
      });

      if (existingVideo) {
        console.log(`Video with Engaxe ID ${videoId} already exists in database with ID ${existingVideo.id}`);
        return existingVideo;
      }

      // Ensure we have valid video details
      if (!videoDetails) {
        console.error('No video details provided');
        throw createBadRequestError('No video details provided', ErrorCodes.INVALID_INPUT);
      }

      // Ensure we have a valid title
      const title = videoDetails.title || `Engaxe Video ${videoId}`;

      // Ensure we have a valid description
      const description = videoDetails.description || '';

      // Ensure we have a valid thumbnail URL
      let thumbnailUrl = videoDetails.thumbnail || `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`;

      // Validate the thumbnail URL
      try {
        if (thumbnailUrl && thumbnailUrl !== 'undefined' && thumbnailUrl !== 'null') {
          new URL(thumbnailUrl);
        } else {
          thumbnailUrl = `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`;
        }
      } catch (error) {
        console.warn('Invalid thumbnail URL, using fallback:', error);
        thumbnailUrl = `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`;
      }

      // Create new video document directly - ensure we're using the same format as local videos
      // Use generateId for the internal database ID (this is not exposed to the client)
      const newVideoId = generateId();
      console.log(`Generated new internal database ID: ${newVideoId}`);

      // Ensure we have valid data for required fields
      const videoTitle = title || `Engaxe Video ${newVideoId.substring(0, 8)}`;

      // Get description, clean it, and truncate if too long
      let videoDescription = description || '';

      // Clean numbered tags like #[150], #[153], etc. and br tags
      videoDescription = videoDescription
        .replace(/#\[\d+\]/g, '')
        .replace(/<br\s*\/?>/gi, ' ')
        .replace(/&lt;br\s*\/?&gt;/gi, ' ')
        .replace(/\s+/g, ' ')
        .trim();

      if (videoDescription.length > 5000) {
        console.log(`Video description is too long (${videoDescription.length} characters). Truncating to 5000 characters.`);
        videoDescription = videoDescription.substring(0, 4997) + '...';
      }

      const videoDuration = parseInt(videoDetails.duration) || 0;
      const videoTags = videoDetails.tags ? videoDetails.tags.split(',') : [];
      const videoCategory = videoDetails.category_name || 'Uncategorized';

      console.log(`Creating new video with title: "${videoTitle}"`);

      // CRITICAL FIX: Always ensure videoId is a valid 6-7 character Engaxe ID
      // This is crucial to prevent hash IDs from being used
      if (!isValidEngaxeId(videoId)) {
        console.error(`Invalid Engaxe ID: ${videoId}. Must be 6-7 alphanumeric characters.`);

        // Always replace invalid IDs with a valid Engaxe ID
        console.log(`Replacing invalid ID: ${videoId} with a valid Engaxe ID`);
        videoId = getRandomValidEngaxeId();
        console.log(`Using valid Engaxe ID instead: ${videoId}`);
      }

      // Add to the list of valid Engaxe IDs
      if (!validEngaxeIds.includes(videoId)) {
        console.log(`Adding new Engaxe ID to valid list: ${videoId}`);
        validEngaxeIds.push(videoId);
      }

      // CRITICAL FIX: Always ensure videoId is a valid 6-7 character Engaxe ID
      // This is crucial to prevent hash IDs from being used
      if (!isValidEngaxeId(videoId)) {
        console.error(`Invalid Engaxe ID: ${videoId}. Must be 6-7 alphanumeric characters.`);
        console.log(`Replacing invalid ID: ${videoId} with a valid Engaxe ID`);
        videoId = getRandomValidEngaxeId();
        console.log(`Using valid Engaxe ID instead: ${videoId}`);
      }

      // Create a clean, consistent video object
      const video = new VideoModel({
        id: newVideoId,
        title: videoTitle,
        description: videoDescription,
        url: videoId, // CRITICAL: Store the 6-7 character Engaxe ID for direct embedding with ngxEmbed
        thumbnailUrl: thumbnailUrl || 'https://via.placeholder.com/480x360?text=Video',
        duration: videoDuration,
        userId,
        channelId: channel.id, // Use the channel ID from the found channel
        visibility: 'public', // Ensure it's public
        tags: videoTags,
        category: videoCategory,
        contentRating: 'general',
        processingStatus: 'ready', // Ensure it's ready
        commentsEnabled: true,
        ratingsEnabled: true,
        embeddingEnabled: true,
        createdBy: userId,
        updatedBy: userId,
        stats: {
          views: parseInt(videoDetails.views) || 0,
          likes: parseInt(videoDetails.likes) || 0,
          dislikes: parseInt(videoDetails.dislikes) || 0,
          comments: 0,
          shares: 0,
          playlistAdds: 0,
          averageWatchTime: 0,
          retentionRate: 0
        },
        file: {
          originalName: `${videoTitle}.mp4`,
          size: 1024, // Placeholder size
          mimeType: 'video/mp4',
          codec: '',
          resolution: '',
          bitrate: 0,
          frameRate: 0
        },
        source: {
          type: 'embed',
          originalUrl: videoId, // CRITICAL: Store the 6-7 character Engaxe ID for direct embedding
          platform: 'engaxe',
          externalId: videoId
        },
        languages: languages && languages.length > 0 ?
          // Process provided languages to ensure they have valid Engaxe IDs
          (() => {
            console.log(`Processing ${languages.length} languages for video ${videoId}:`);

            // Check if we have at least one default language
            const hasDefaultLanguage = languages.some(lang => lang.isDefault);
            if (!hasDefaultLanguage && languages.length > 0) {
              console.log(`No default language found, setting ${languages[0].code} as default`);
              languages[0].isDefault = true;
            }

            // Process each language
            return languages.map((lang, idx) => {
              console.log(`Processing language ${idx + 1}/${languages.length}: ${lang.name} (${lang.code}), URL: ${lang.url || 'none'}, isDefault: ${lang.isDefault || false}, flag: ${lang.flag || 'none'}`);

              // Ensure each language has a unique URL if provided
              const processedLang = {
                code: lang.code,
                name: lang.name,
                flag: lang.flag || getLanguageFlag(lang.code),
                // IMPORTANT: Use the language-specific URL if provided and valid, otherwise use the video ID
                url: lang.url && isValidEngaxeId(lang.url) ? lang.url : videoId,
                isDefault: lang.isDefault !== undefined ? lang.isDefault : lang.code === 'en' // Default to English if not specified
              };

              console.log(`Processed language ${lang.code}: URL=${processedLang.url} (original: ${lang.url}), isDefault=${processedLang.isDefault}, flag=${processedLang.flag}`);
              return processedLang;
            });
          })() :
          // Default language if none provided
          [
            {
              code: 'en',
              name: 'English',
              flag: '🇺🇸',
              isDefault: true,
              url: videoId // CRITICAL: Store the 6-7 character Engaxe ID for direct embedding
            }
          ]
      });

      // Log the created video object for debugging
      console.log('Created new video object:', {
        id: video.id,
        title: video.title,
        url: video.url,
        source: video.source,
        languages: video.languages
      });

      // Save the video to database
      try {
        console.log('Attempting to save video to database with the following data:', {
          id: video.id,
          title: video.title,
          url: video.url,
          channelId: video.channelId,
          userId: video.userId,
          visibility: video.visibility,
          processingStatus: video.processingStatus,
          source: video.source,
          file: video.file
        });

        // Ensure all required fields are set
        if (!video.file || !video.file.originalName) {
          console.log('Setting default file properties');
          video.file = {
            originalName: `engaxe_video_${videoId}.mp4`,
            size: 1024, // Placeholder size
            mimeType: 'video/mp4',
            codec: '',
            resolution: '',
            bitrate: 0,
            frameRate: 0
          };
        }

        if (!video.category) {
          console.log('Setting default category');
          video.category = 'Uncategorized';
        }

        if (!video.languages || video.languages.length === 0) {
          console.log('Setting default language');
          video.languages = [
            {
              code: 'en',
              name: 'English',
              flag: '🇺🇸',
              isDefault: true,
              url: videoId // Store just the video ID, not the full URL
            }
          ] as any;
        } else if (video.languages) {
          console.log(`Video has ${video.languages.length} languages:`, JSON.stringify(video.languages, null, 2));

          // Ensure at least one language is marked as default
          if (Array.isArray(video.languages) && video.languages.length > 0) {
            const hasDefaultLanguage = video.languages.some((lang: any) => lang.isDefault);
            if (!hasDefaultLanguage) {
              console.log(`No default language found, setting ${video.languages[0].code || 'first language'} as default`);
              video.languages[0].isDefault = true;
            }
          }

          // Ensure all language URLs are valid Engaxe IDs
          if (Array.isArray(video.languages) && video.languages.length > 0) {
            console.log(`Validating ${video.languages.length} languages before saving:`);
            video.languages = video.languages.map((lang: any, idx: number) => {
              // Create a safe reference to languages length
              const languagesLength = Array.isArray(video.languages) ? video.languages.length : 0;

              console.log(`Validating language ${idx + 1}/${languagesLength}: ${lang.name || 'Unknown'} (${lang.code || 'unknown'}), URL: ${lang.url || 'none'}, isDefault: ${lang.isDefault || false}, flag: ${lang.flag || 'none'}`);

              const validatedLang = {
                ...lang,
                url: lang.url && isValidEngaxeId(lang.url) ? lang.url : videoId,
                flag: lang.flag || getLanguageFlag(lang.code || 'en')
              };

              console.log(`Validated language ${lang.code || 'unknown'}: URL=${validatedLang.url}, isDefault=${validatedLang.isDefault || false}, flag=${validatedLang.flag}`);
              return validatedLang;
            }) as any;
          } else {
            console.log('No languages to validate');
          }
        } else {
          console.log('Video has no languages defined');
        }

        // Double-check critical fields
        if (video.visibility !== 'public') {
          console.log('Ensuring visibility is set to public');
          video.visibility = 'public';
        }

        if (video.processingStatus !== 'ready') {
          console.log('Ensuring processingStatus is set to ready');
          video.processingStatus = 'ready';
        }

        // Ensure the video has a valid Engaxe ID
        await this.ensureValidEngaxeId(video);
        console.log(`Ensured video ${video.id} has a valid Engaxe ID: ${video.url}`);

        // Save the video
        console.log('About to save video with ID:', video.id);
        const savedVideo = await video.save();
        console.log(`Successfully saved Engaxe video to database with ID: ${video.id}`);
        console.log('Saved video document:', JSON.stringify({
          id: savedVideo.id,
          title: savedVideo.title,
          url: savedVideo.url,
          visibility: savedVideo.visibility,
          processingStatus: savedVideo.processingStatus
        }, null, 2));

        // Verify the video was saved
        const verifyVideo = await VideoModel.findOne({ id: video.id });
        if (verifyVideo) {
          console.log('Verified: Video exists in database with ID:', video.id);
          console.log('Verified video details:', {
            visibility: verifyVideo.visibility,
            processingStatus: verifyVideo.processingStatus
          });
        } else {
          console.error('ERROR: Video not found in database immediately after save!');

          // Try to find by _id
          const verifyById = await VideoModel.findById(savedVideo._id);
          if (verifyById) {
            console.log('Found video by _id but not by id field. This suggests an issue with the id field.');
            // Try to fix the issue
            verifyById.id = video.id;
            await verifyById.save();
            console.log('Fixed video ID field');
          } else {
            console.error('Video not found by _id either. This suggests the save operation failed completely.');

            // Try one more time with a new internal database ID
            video.id = generateId();
            console.log('Trying again with a new internal database ID:', video.id);
            await video.save();

            // Verify again
            const secondVerify = await VideoModel.findOne({ id: video.id });
            if (secondVerify) {
              console.log('Second attempt successful! Video saved with ID:', video.id);
            } else {
              console.error('Second attempt also failed. This is a critical database issue.');
              throw new Error('Failed to save video to database after multiple attempts');
            }
          }
        }

        // Update channel video count
        channel.stats.videoCount += 1;
        await channel.save();
      } catch (saveError: any) {
        console.error('Error saving video to database:', saveError);

        // Log more details about the error
        if (saveError.name === 'ValidationError' && saveError.errors) {
          console.error('Validation errors:');
          for (const field in saveError.errors) {
            console.error(`- Field '${field}':`, saveError.errors[field]?.message || 'Unknown validation error');
          }

          // Try to fix common validation errors
          if (saveError.errors.file) {
            console.log('Attempting to fix file validation error');
            video.file = {
              originalName: `engaxe_video_${videoId}.mp4`,
              size: 1024, // Placeholder size
              mimeType: 'video/mp4',
              codec: '',
              resolution: '',
              bitrate: 0,
              frameRate: 0
            };
          }

          if (saveError.errors.category) {
            console.log('Attempting to fix category validation error');
            video.category = 'Uncategorized';
          }

          if (saveError.errors.languages) {
            console.log('Attempting to fix languages validation error');
            video.languages = [
              {
                code: 'en',
                name: 'English',
                flag: '🇺🇸',
                isDefault: true,
                url: videoId // Store just the video ID, not the full URL
              }
            ] as any;

            // If languages were provided but had validation errors, try to fix them
            if (languages && languages.length > 0) {
              console.log('Attempting to fix provided languages');
              try {
                // Create valid language objects
                const validLanguages = languages.map((lang: any) => ({
                  code: lang.code || 'en',
                  name: lang.name || 'English',
                  flag: lang.flag || '🇺🇸',
                  url: lang.url && isValidEngaxeId(lang.url) ? lang.url : videoId,
                  isDefault: lang.isDefault !== undefined ? lang.isDefault : lang.code === 'en'
                })) as any;

                // Ensure at least one language is marked as default
                const hasDefaultLanguage = validLanguages.some((lang: any) => lang.isDefault);
                if (!hasDefaultLanguage && validLanguages.length > 0) {
                  validLanguages[0].isDefault = true;
                }

                video.languages = validLanguages;
                console.log('Fixed languages:', JSON.stringify(validLanguages, null, 2));
              } catch (langFixError) {
                console.error('Error fixing languages:', langFixError);
                // Fall back to default English
              }
            }
          }

          // Try saving again with fixes
          console.log('Attempting to save video again with fixes');
          await video.save();
          console.log(`Successfully saved Engaxe video to database with ID: ${video.id} after fixes`);

          // Update channel video count
          channel.stats.videoCount += 1;
          await channel.save();
        } else if ((saveError.name === 'MongoError' || saveError.name === 'MongoServerError') && saveError.code === 11000 && saveError.keyValue) {
          console.error('Duplicate key error:', saveError.keyValue);
          throw createBadRequestError('Video already exists in database', ErrorCodes.VIDEO_ALREADY_EXISTS);
        } else {
          // Re-throw the error if we couldn't fix it
          throw saveError;
        }
      }

      return video;
    } catch (error) {
      console.error('Error saving Engaxe video to database:', error);
      throw error;
    }
  }

  /**
   * Delete a video
   */
  async deleteVideo(userId: string, videoId: string) {
    // Find video
    const video = await VideoModel.findOne({ id: videoId, deletedAt: null });
    if (!video) {
      throw createNotFoundError('Video not found', ErrorCodes.VIDEO_NOT_FOUND);
    }

    // Check if user has permission
    const channel = await ChannelModel.findOne({ id: video.channelId, deletedAt: null });
    if (!channel) {
      throw createNotFoundError('Channel not found', ErrorCodes.CHANNEL_NOT_FOUND);
    }

    if (video.userId !== userId && channel.ownerId !== userId && !channel.moderators.includes(userId)) {
      throw createForbiddenError('You do not have permission to delete this video', ErrorCodes.VIDEO_PERMISSION_DENIED);
    }

    // Soft delete video
    video.deletedAt = new Date();
    await video.save();

    // Remove video from JSON storage
    try {
      await this.jsonStorageService.removeVideo(videoId);
    } catch (error) {
      console.error('Error removing video from JSON storage:', error);
      // Don't throw error here as the video was successfully deleted from database
    }

    // Update channel video count
    channel.stats.videoCount -= 1;
    await channel.save();

    return { success: true };
  }

  /**
   * Fix video URLs in the database
   * This method finds videos with hash IDs and replaces them with valid Engaxe IDs
   * @param forceUpdate If true, update all videos regardless of whether they already have valid IDs
   */
  async fixVideoUrls(forceUpdate = false) {
    console.log(`Starting to fix video URLs in the database (forceUpdate: ${forceUpdate})`);

    // We're using the validEngaxeIds list imported from utils/id.ts

    // Find all videos
    const videos = await VideoModel.find({ deletedAt: null });
    console.log(`Found ${videos.length} videos in the database`);

    let fixedCount = 0;

    // Process each video
    for (const video of videos) {
      let needsUpdate = false;

      // ALWAYS update the URL to a valid Engaxe ID
      // This ensures that all videos have valid Engaxe IDs
      console.log(`Processing video ${video.id} with URL: ${video.url}`);

      // Try to get a valid Engaxe ID from the source
      let validEngaxeId = null;

      // Check if the current URL is already a valid Engaxe ID
      if (isValidEngaxeId(video.url) && !forceUpdate) {
        validEngaxeId = video.url;
        console.log(`Current URL is already a valid Engaxe ID: ${validEngaxeId}`);
      }
      // Check if we have a valid Engaxe ID in source.externalId
      else if (video.source && video.source.externalId && isValidEngaxeId(video.source.externalId)) {
        validEngaxeId = video.source.externalId;
        console.log(`Found valid Engaxe ID in source.externalId: ${validEngaxeId}`);
        needsUpdate = true;
      }
      // Check if we have a valid Engaxe ID in source.originalUrl
      else if (video.source && video.source.originalUrl) {
        // If it's a URL, try to extract the ID
        if (video.source.originalUrl.includes('engaxe.com')) {
          const extractedId = extractEngaxeVideoId(video.source.originalUrl);
          if (extractedId && isValidEngaxeId(extractedId)) {
            validEngaxeId = extractedId;
            console.log(`Extracted valid Engaxe ID from source.originalUrl: ${validEngaxeId}`);
            needsUpdate = true;
          }
        }
        // If it's already a valid ID, use it directly
        else if (isValidEngaxeId(video.source.originalUrl)) {
          validEngaxeId = video.source.originalUrl;
          console.log(`Found valid Engaxe ID in source.originalUrl: ${validEngaxeId}`);
          needsUpdate = true;
        }
      }

      // If we couldn't find a valid Engaxe ID, use a guaranteed working ID
      if (!validEngaxeId) {
        console.log(`Could not find a valid Engaxe ID for video ${video.id}, using a guaranteed working ID`);
        validEngaxeId = getRandomValidEngaxeId();
        console.log(`Using random valid Engaxe ID: ${validEngaxeId}`);
        needsUpdate = true;
      }

      // If forceUpdate is true, always update the URL
      if (forceUpdate) {
        console.log(`Force updating URL for video ${video.id} to valid Engaxe ID: ${validEngaxeId}`);
        video.url = validEngaxeId;
        needsUpdate = true;
      }
      // Otherwise, only update if needed
      else if (needsUpdate) {
        console.log(`Setting URL for video ${video.id} to valid Engaxe ID: ${validEngaxeId}`);
        video.url = validEngaxeId;
      }

      // Update language URLs only if they're not valid Engaxe IDs
      // This ensures that all languages have valid Engaxe IDs while preserving unique URLs
      if (video.languages && video.languages.length > 0) {
        for (let i = 0; i < video.languages.length; i++) {
          console.log(`Processing language ${video.languages[i].code} for video ${video.id} with URL: ${video.languages[i].url}`);

          // Only update the language URL if it's not a valid Engaxe ID
          if (!isValidEngaxeId(video.languages[i].url)) {
            console.log(`Language ${video.languages[i].code} has invalid URL: ${video.languages[i].url}`);
            console.log(`Setting language ${video.languages[i].code} URL to video's Engaxe ID: ${video.url}`);
            video.languages[i].url = video.url;
            needsUpdate = true;
          } else {
            console.log(`Language ${video.languages[i].code} already has valid Engaxe ID: ${video.languages[i].url}, preserving it`);
          }
        }
      }

      // Check if source.originalUrl is not a valid 6-7 character Engaxe ID
      if (video.source && video.source.originalUrl && !isValidEngaxeId(video.source.originalUrl)) {
        console.log(`Video ${video.id} has an invalid source.originalUrl: ${video.source.originalUrl}`);

        // Use the video URL (which is now guaranteed to be a valid Engaxe ID)
        console.log(`Replacing invalid source.originalUrl ${video.source.originalUrl} with video's Engaxe ID: ${video.url}`);
        video.source.originalUrl = video.url;
        needsUpdate = true;
      }

      // Check if source.externalId is not a valid 6-7 character Engaxe ID
      if (video.source && video.source.externalId && !isValidEngaxeId(video.source.externalId)) {
        console.log(`Video ${video.id} has an invalid source.externalId: ${video.source.externalId}`);

        // Use the video URL (which is now guaranteed to be a valid Engaxe ID)
        console.log(`Replacing invalid source.externalId ${video.source.externalId} with video's Engaxe ID: ${video.url}`);
        video.source.externalId = video.url;
        needsUpdate = true;
      }

      // Save the video if it needs an update
      if (needsUpdate) {
        try {
          await video.save();
          console.log(`Successfully updated video ${video.id}`);
          fixedCount++;
        } catch (error) {
          console.error(`Error updating video ${video.id}:`, error);
        }
      }
    }

    return {
      success: true,
      totalCount: videos.length,
      fixedCount
    };
  }

  /**
   * Verify that all videos in the database have valid Engaxe IDs
   * @returns Object with counts of valid and invalid videos
   */
  async verifyAllVideosHaveValidEngaxeIds() {
    console.log('Verifying that all videos have valid Engaxe IDs');

    // Find all videos
    const videos = await VideoModel.find({ deletedAt: null });
    console.log(`Found ${videos.length} videos in the database`);

    let validCount = 0;
    let invalidCount = 0;
    const invalidIds: string[] = [];

    // Check each video
    for (const video of videos) {
      if (isValidEngaxeId(video.url)) {
        validCount++;
      } else {
        invalidCount++;
        invalidIds.push(video.id);
        console.error(`Video ${video.id} has an invalid URL: ${video.url}`);
      }
    }

    return {
      totalCount: videos.length,
      validCount,
      invalidCount,
      invalidIds
    };
  }

  /**
   * Ensure all videos have mappings between hash IDs and Engaxe IDs
   * @returns Object with counts of created and existing mappings
   */
  async ensureAllVideosMapped() {
    console.log('Ensuring all videos have mappings between hash IDs and Engaxe IDs');

    // Find all videos
    const videos = await VideoModel.find({ deletedAt: null });
    console.log(`Found ${videos.length} videos in the database`);

    let existingCount = 0;
    let createdCount = 0;

    // Check each video
    for (const video of videos) {
      // Check if a mapping already exists
      const existingMapping = await videoIdMappingService.getEngaxeIdFromHashId(video.id);

      if (existingMapping) {
        console.log(`Mapping already exists for video ${video.id}: ${existingMapping}`);
        existingCount++;

        // If the existing mapping doesn't match the video URL, update it
        if (existingMapping !== video.url && isValidEngaxeId(video.url)) {
          console.log(`Updating mapping for video ${video.id}: ${existingMapping} -> ${video.url}`);
          await videoIdMappingService.updateMapping(video.id, video.url, video.title);
        }
      } else {
        // Create a new mapping
        console.log(`Creating mapping for video ${video.id}: ${video.url}`);

        // If the video URL is not a valid Engaxe ID, generate a new one
        const engaxeId = isValidEngaxeId(video.url) ? video.url : generateEngaxeId();

        // Create the mapping
        await videoIdMappingService.createMapping(video.id, engaxeId, video.title);

        // If we generated a new Engaxe ID, update the video
        if (engaxeId !== video.url) {
          console.log(`Updating video ${video.id} with new Engaxe ID: ${engaxeId}`);
          video.url = engaxeId;
          await video.save();
        }

        createdCount++;
      }
    }

    return {
      totalCount: videos.length,
      existingCount,
      createdCount
    };
  }

  /**
   * Ensure a video has a valid Engaxe ID
   * @param video The video to check
   * @returns The video with a valid Engaxe ID
   */
  async ensureValidEngaxeId(video: any) {
    // If the video URL is not a valid Engaxe ID, generate a new one
    if (!isValidEngaxeId(video.url)) {
      console.log(`Video ${video.id} has an invalid URL: ${video.url}`);

      // Generate a new Engaxe ID
      const engaxeId = generateEngaxeId();
      console.log(`Generated new Engaxe ID for video ${video.id}: ${engaxeId}`);

      // Update the video
      video.url = engaxeId;
      await video.save();

      // Create a mapping
      await videoIdMappingService.createMapping(video.id, engaxeId, video.title);
    }

    // Ensure the video has a mapping
    if (!await videoIdMappingService.getEngaxeIdFromHashId(video.id)) {
      console.log(`Creating missing mapping for video ${video.id} -> ${video.url}`);
      await videoIdMappingService.createMapping(video.id, video.url, video.title);
    }

    return video;
  }
}

// Export a singleton instance
export const videoService = new VideoService();
