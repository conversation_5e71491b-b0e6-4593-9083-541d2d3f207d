import { FastifyRequest } from 'fastify';
import { AppError } from './AppError';
import fs from 'fs';
import path from 'path';
import { format } from 'date-fns';

/**
 * Error logger for the application
 * Logs errors to a file and/or console
 */
export class ErrorLogger {
  private logDir: string;
  private logToFile: boolean;
  private logToConsole: boolean;

  /**
   * Create a new ErrorLogger
   * @param options Logger options
   */
  constructor(options: {
    logDir?: string;
    logToFile?: boolean;
    logToConsole?: boolean;
  } = {}) {
    this.logDir = options.logDir || path.join(process.cwd(), 'logs');
    this.logToFile = options.logToFile !== undefined ? options.logToFile : true;
    this.logToConsole = options.logToConsole !== undefined ? options.logToConsole : true;

    // Create log directory if it doesn't exist
    if (this.logToFile && !fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * Log an error
   * @param error The error to log
   * @param request The request that caused the error
   */
  public logError(error: Error | AppError, request?: FastifyRequest): void {
    const timestamp = new Date();
    const formattedDate = format(timestamp, 'yyyy-MM-dd');
    const formattedTime = format(timestamp, 'HH:mm:ss.SSS');

    // Create error log entry
    const logEntry = {
      timestamp: timestamp.toISOString(),
      level: 'error',
      message: error.message,
      stack: error.stack,
      name: error.name,
      ...(error instanceof AppError && {
        statusCode: error.statusCode,
        errorCode: error.errorCode,
        isOperational: error.isOperational,
        data: error.data,
      }),
      ...(request && {
        request: {
          id: request.id,
          method: request.method,
          url: request.url,
          params: request.params,
          query: request.query,
          headers: this.sanitizeHeaders(request.headers),
          body: this.sanitizeBody(request.body),
          ip: request.ip,
        },
      }),
    };

    // Log to console
    if (this.logToConsole) {
      console.error(`[${formattedTime}] ERROR:`, error.message);
      console.error(error.stack);
      
      if (error instanceof AppError) {
        console.error('Status Code:', error.statusCode);
        console.error('Error Code:', error.errorCode);
        console.error('Is Operational:', error.isOperational);
        
        if (error.data) {
          console.error('Additional Data:', error.data);
        }
      }
      
      if (request) {
        console.error('Request:', {
          method: request.method,
          url: request.url,
          params: request.params,
          query: request.query,
        });
      }
    }

    // Log to file
    if (this.logToFile) {
      const logFilePath = path.join(this.logDir, `error-${formattedDate}.log`);
      const logMessage = JSON.stringify(logEntry) + '\n';
      
      fs.appendFile(logFilePath, logMessage, (err) => {
        if (err) {
          console.error('Failed to write to error log file:', err);
        }
      });
    }
  }

  /**
   * Sanitize request headers to remove sensitive information
   * @param headers Request headers
   * @returns Sanitized headers
   */
  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    
    // Remove sensitive headers
    const sensitiveHeaders = ['authorization', 'cookie', 'set-cookie'];
    
    for (const header of sensitiveHeaders) {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }

  /**
   * Sanitize request body to remove sensitive information
   * @param body Request body
   * @returns Sanitized body
   */
  private sanitizeBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }
    
    const sanitized = { ...body };
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'passwordConfirmation', 'currentPassword', 'newPassword', 'token', 'refreshToken', 'accessToken', 'secret', 'apiKey'];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }
}

// Create a singleton instance
export const errorLogger = new ErrorLogger();
