import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import path from 'path';
import userRoutes from './user.routes';
import roleRoutes from './role.routes';
import permissionRoutes from './permission.routes';
import authRoutes from './auth.routes';
import docsRoutes from './docs.routes';
import logRoutes from './log.routes';
import reportRoutes from './report.routes';

import aiAssistantRoutes from './ai-assistant.routes';
import translationRoutes from './translation.routes';

import videoRoutes from './video.routes';
import videoJsonRoutes from './video-json.routes';
import channelRoutes from './channel.routes';
import metadataRoutes from './metadata.routes';
import testVideoRoutes from './test-video.routes';
import fixLanguagesRoutes from './fix-languages.routes';
import simpleFixRoutes from './simple-fix.routes';
import likeRoutes from './like.routes';

// Messaging routes
import conversationRoutes from './conversation.routes';
import messageRoutes from './message.routes';
import userSearchRoutes from './user-search.routes';

/**
 * Register all routes
 */
export default async function routes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // Health check route
  fastify.get('/health', async () => {
    return { status: 'ok', timestamp: new Date() };
  });

  // API routes
  const apiPrefix = process.env.API_PREFIX || '/api/v1';

  // Register routes with prefixes
  fastify.register(authRoutes, { prefix: `${apiPrefix}/auth` });
  fastify.register(userRoutes, { prefix: `${apiPrefix}/users` });
  fastify.register(roleRoutes, { prefix: `${apiPrefix}/roles` });
  fastify.register(permissionRoutes, { prefix: `${apiPrefix}/permissions` });
  fastify.register(videoRoutes, { prefix: `${apiPrefix}/videos` });
  fastify.register(videoJsonRoutes, { prefix: `${apiPrefix}/videos` });
  fastify.register(channelRoutes, { prefix: `${apiPrefix}/channels` });
  fastify.register(metadataRoutes, { prefix: `${apiPrefix}/metadata` });
  fastify.register(logRoutes, { prefix: `${apiPrefix}/logs` });
  fastify.register(reportRoutes, { prefix: `${apiPrefix}/reports` });
  fastify.register(docsRoutes, { prefix: `${apiPrefix}/auth` });
  fastify.register(aiAssistantRoutes, { prefix: `${apiPrefix}/ai-assistant` });
  fastify.register(translationRoutes, { prefix: `${apiPrefix}/translation` });
  fastify.register(fixLanguagesRoutes, { prefix: `${apiPrefix}/fix-languages` });
  fastify.register(simpleFixRoutes, { prefix: `${apiPrefix}/simple-fix` });
  fastify.register(likeRoutes, { prefix: `${apiPrefix}/likes` });

  // Register messaging routes
  fastify.register(conversationRoutes, { prefix: `${apiPrefix}/conversations` });
  fastify.register(messageRoutes, { prefix: `${apiPrefix}/messages` });
  fastify.register(userSearchRoutes, { prefix: `${apiPrefix}/users` });

  // Register test routes - these are public and don't require authentication
  fastify.register(testVideoRoutes, { prefix: '/test' });

  // Serve chat test HTML file
  fastify.get('/chat-test', async (request, reply) => {
    return reply.sendFile('chat-test.html', path.join(__dirname, '../../src/public'));
  });
}
