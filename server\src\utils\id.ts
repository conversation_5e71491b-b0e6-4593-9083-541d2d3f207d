import { randomBytes } from 'crypto';

/**
 * Generate a unique ID
 * @param length Length of the ID (default: 16)
 * @returns A unique ID string
 */
export function generateId(length: number = 16): string {
  return randomBytes(length).toString('hex');
}

/**
 * Generate a short unique ID
 * @param length Length of the ID (default: 8)
 * @returns A short unique ID string
 */
export function generateShortId(length: number = 8): string {
  return randomBytes(length).toString('hex');
}

/**
 * Generate an Engaxe-compatible ID (6 alphanumeric characters)
 * @returns A 6 character alphanumeric ID compatible with Engaxe
 */
export function generateEngaxeId(): string {
  // Define the character set (alphanumeric)
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

  // Always use 6 characters for consistency
  const length = 6;

  // Generate the ID
  let id = '';
  for (let i = 0; i < length; i++) {
    id += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return id;
}

/**
 * List of known valid Engaxe IDs
 */
export const validEngaxeIds = [
  'XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', 'KxyzuN', '4OE4QR', 'gu99XD',
  'fgp97y', 'X4eW1I', 'c1AObf', '99BXqa', 'Hs7Qzd', '7mXOfb', 'dBVslb'
];

/**
 * Get a random valid Engaxe ID from the list
 * @returns A valid Engaxe ID
 */
export function getRandomValidEngaxeId(): string {
  const randomIndex = Math.floor(Math.random() * validEngaxeIds.length);
  return validEngaxeIds[randomIndex];
}

/**
 * Get a flag emoji for a language code
 * @param code The language code
 * @returns The flag emoji for the language
 */
export function getLanguageFlag(code: string): string {
  if (!code) return '🌐';

  switch (code.toLowerCase()) {
    case 'en': return '🇺🇸';
    case 'hi': return '🇮🇳';
    case 'es': return '🇪🇸';
    case 'fr': return '🇫🇷';
    case 'de': return '🇩🇪';
    case 'ja': return '🇯🇵';
    case 'zh': return '🇨🇳';
    case 'ru': return '🇷🇺';
    case 'ar': return '🇸🇦';
    case 'pt': return '🇵🇹';
    case 'it': return '🇮🇹';
    case 'nl': return '🇳🇱';
    case 'ko': return '🇰🇷';
    case 'tr': return '🇹🇷';
    // Add more Indian languages
    case 'bn': return '🇮🇳'; // Bengali
    case 'ta': return '🇮🇳'; // Tamil
    case 'te': return '🇮🇳'; // Telugu
    case 'mr': return '🇮🇳'; // Marathi
    case 'gu': return '🇮🇳'; // Gujarati
    case 'kn': return '🇮🇳'; // Kannada
    case 'ml': return '🇮🇳'; // Malayalam
    case 'pa': return '🇮🇳'; // Punjabi
    case 'or': return '🇮🇳'; // Odia
    case 'as': return '🇮🇳'; // Assamese
    case 'ur': return '🇵🇰'; // Urdu
    default: return '🌐';
  }
}

/**
 * Check if a string is a valid Engaxe ID (6-7 alphanumeric characters)
 * @param id The ID to check
 * @returns True if the ID is a valid Engaxe ID, false otherwise
 */
export function isValidEngaxeId(id: string): boolean {
  // Strict validation: must be 6-7 alphanumeric characters
  return /^[a-zA-Z0-9]{6,7}$/.test(id);
}

/**
 * Check if a string is a hash ID (32 hex characters)
 * @param id The ID to check
 * @returns True if the ID is a hash ID, false otherwise
 */
export function isHashId(id: string): boolean {
  return /^[0-9a-f]{32}$/i.test(id);
}

/**
 * Generate a random string
 * @param length Length of the string (default: 32)
 * @returns A random string
 */
export function generateRandomString(length: number = 32): string {
  return randomBytes(length).toString('base64').replace(/[+/=]/g, '').substring(0, length);
}

/**
 * Generate a random number within a range
 * @param min Minimum value (inclusive)
 * @param max Maximum value (exclusive)
 * @returns A random number within the specified range
 */
export function generateRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min) + min);
}

/**
 * Generate a random boolean
 * @param probability Probability of returning true (0-1, default: 0.5)
 * @returns A random boolean
 */
export function generateRandomBoolean(probability: number = 0.5): boolean {
  return Math.random() < probability;
}

/**
 * Generate a random element from an array
 * @param array Array to pick from
 * @returns A random element from the array
 */
export function generateRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Generate a random subset of an array
 * @param array Array to pick from
 * @param count Number of elements to pick
 * @returns A random subset of the array
 */
export function generateRandomSubset<T>(array: T[], count: number): T[] {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

/**
 * Generate a random date within a range
 * @param start Start date
 * @param end End date
 * @returns A random date within the specified range
 */
export function generateRandomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

/**
 * Generate a random color in hex format
 * @returns A random color in hex format
 */
export function generateRandomColor(): string {
  return `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`;
}

/**
 * Generate a random IP address
 * @returns A random IP address
 */
export function generateRandomIp(): string {
  return Array.from({ length: 4 }, () => Math.floor(Math.random() * 256)).join('.');
}

/**
 * Generate a random user agent
 * @returns A random user agent
 */
export function generateRandomUserAgent(): string {
  const browsers = [
    'Chrome',
    'Firefox',
    'Safari',
    'Edge',
    'Opera',
  ];

  const os = [
    'Windows NT 10.0',
    'Macintosh; Intel Mac OS X 10_15_7',
    'X11; Linux x86_64',
    'iPhone; CPU iPhone OS 14_0 like Mac OS X',
    'Android 11',
  ];

  const browser = generateRandomElement(browsers);
  const osString = generateRandomElement(os);
  const version = `${generateRandomNumber(70, 110)}.0.${generateRandomNumber(1000, 5000)}.${generateRandomNumber(10, 200)}`;

  return `Mozilla/5.0 (${osString}) AppleWebKit/537.36 (KHTML, like Gecko) ${browser}/${version} Safari/537.36`;
}
