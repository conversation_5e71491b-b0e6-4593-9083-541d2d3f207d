<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bhashini Python-Style Translation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, textarea, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .loading {
            display: none;
            margin-top: 20px;
            text-align: center;
            color: #666;
        }
        .error {
            color: #D8000C;
            background-color: #FFD2D2;
            padding: 10px;
            margin-top: 20px;
            border-radius: 4px;
        }
        .request-details, .response-details {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f5f5f5;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .request-details h3, .response-details h3 {
            margin-top: 0;
        }
        .language-list {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f5f5f5;
        }
        .language-list h3 {
            margin-top: 0;
        }
        .language-option {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>Bhashini Python-Style Translation Test</h1>
    
    <div class="language-list">
        <h3>Available Target Languages:</h3>
        <div id="languageOptions"></div>
    </div>
    
    <div class="form-group">
        <label for="targetLanguage">Select Target Language:</label>
        <select id="targetLanguage"></select>
    </div>
    
    <div class="form-group">
        <label for="textToTranslate">Text to Translate (from English):</label>
        <textarea id="textToTranslate" rows="5" placeholder="Enter text to translate...">Hello, how are you? This is a test of the Bhashini translation API.</textarea>
    </div>
    
    <button id="translateBtn">Translate</button>
    
    <div id="loading" class="loading">Translating... Please wait.</div>
    
    <div id="result" class="result" style="display: none;">
        <h3>Translation Result:</h3>
        <div>
            <strong>Original (English):</strong>
            <div id="originalText"></div>
        </div>
        <div style="margin-top: 10px;">
            <strong>Translation:</strong>
            <div id="translatedText"></div>
        </div>
    </div>
    
    <div id="request" class="request-details" style="display: none;">
        <h3>Request Details:</h3>
        <div id="requestDetails"></div>
    </div>
    
    <div id="response" class="response-details" style="display: none;">
        <h3>Response Details:</h3>
        <div id="responseDetails"></div>
    </div>
    
    <div id="error" class="error" style="display: none;"></div>
    
    <script>
        // API endpoints and credentials - Matching the Python code
        const INFERENCE_URL = "https://dhruva-api.bhashini.gov.in/services/inference/pipeline";
        const USER_ID = "cee60134c6bb4d179efd3fda48ff32fe";
        const API_KEY = "13a647c84b-2747-4f0c-afcd-2ac8235f5318";
        const AUTHORIZATION_VALUE = "W9QK_zzb7Lnsc_xYAl30Gk64Z8rJU4r_2NBiguZjiMIdkUI_8p-E38M-zc_VVhun";
        
        // Service and model IDs
        const NMT_SERVICE_ID = "ai4bharat/indictrans-v2-all-gpu--t4";
        const NMT_MODEL_ID = "641d1d7892a6a31751ff1f5a";
        
        // Supported languages with their names
        const SUPPORTED_LANGUAGES = {
            "hi": "हिंदी (Hindi)",
            "en": "अंग्रेज़ी (English)",
            "gu": "गुजराती (Gujarati)",
            "bn": "बंगाली (Bengali)",
            "ta": "तमिल (Tamil)",
            "te": "तेलुगु (Telugu)",
            "mr": "मराठी (Marathi)",
            "kn": "कन्नड़ (Kannada)",
            "ml": "मलयालम (Malayalam)",
            "pa": "पंजाबी (Punjabi)",
            "or": "उड़िया (Odia)",
            "as": "असमिया (Assamese)",
            "ur": "उर्दू (Urdu)"
        };
        
        // Script code mapping for Indian languages
        function getScriptCode(languageCode) {
            const scriptMapping = {
                "hi": "Deva",  // Hindi - Devanagari
                "en": "Latn",  // English - Latin
                "gu": "Gujr",  // Gujarati
                "bn": "Beng",  // Bengali
                "ta": "Taml",  // Tamil
                "te": "Telu",  // Telugu
                "mr": "Deva",  // Marathi - Devanagari
                "kn": "Knda",  // Kannada
                "ml": "Mlym",  // Malayalam
                "pa": "Guru",  // Punjabi - Gurmukhi
                "or": "Orya",  // Odia
                "as": "Beng",  // Assamese - Bengali
                "ur": "Arab",  // Urdu - Arabic
            };
            return scriptMapping[languageCode] || "Deva";  // Default to Devanagari if not found
        }
        
        // Translate text function - Matching the Python implementation
        async function translateText(text, sourceLang = "en", targetLang = "hi") {
            // Headers for the API request
            const headers = {
                "Content-Type": "application/json",
                "userID": USER_ID,
                "ulcaApiKey": API_KEY,
                "Authorization": AUTHORIZATION_VALUE
            };
            
            // Payload for the translation request
            const payload = {
                "pipelineTasks": [
                    {
                        "taskType": "translation",
                        "config": {
                            "language": {
                                "sourceLanguage": sourceLang,
                                "targetLanguage": targetLang,
                                "sourceScriptCode": getScriptCode(sourceLang),
                                "targetScriptCode": getScriptCode(targetLang)
                            },
                            "serviceId": NMT_SERVICE_ID,
                            "modelId": NMT_MODEL_ID
                        }
                    }
                ],
                "inputData": {
                    "input": [
                        {
                            "source": text
                        }
                    ]
                }
            };
            
            // Display request details
            document.getElementById('request').style.display = 'block';
            document.getElementById('requestDetails').textContent = JSON.stringify(payload, null, 2);
            
            // Make the API request
            const response = await fetch(INFERENCE_URL, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(payload)
            });
            
            const data = await response.json();
            
            // Display response details
            document.getElementById('response').style.display = 'block';
            document.getElementById('responseDetails').textContent = JSON.stringify(data, null, 2);
            
            // Process the response
            if (response.status === 200) {
                try {
                    const outputText = data.pipelineResponse[0].output[0].target;
                    return outputText;
                } catch (e) {
                    throw new Error(`Error parsing response: ${e}`);
                }
            } else {
                throw new Error(`Error: ${response.status}`);
            }
        }
        
        // Initialize the page
        function initPage() {
            const sourceLang = "en";
            const languageOptionsDiv = document.getElementById('languageOptions');
            const targetLanguageSelect = document.getElementById('targetLanguage');
            
            // Populate language options
            let i = 1;
            for (const [code, name] of Object.entries(SUPPORTED_LANGUAGES)) {
                if (code !== sourceLang) {
                    // Add to the language options div
                    const option = document.createElement('div');
                    option.className = 'language-option';
                    option.textContent = `${i}. ${name}`;
                    languageOptionsDiv.appendChild(option);
                    
                    // Add to the select dropdown
                    const selectOption = document.createElement('option');
                    selectOption.value = code;
                    selectOption.textContent = name;
                    targetLanguageSelect.appendChild(selectOption);
                    
                    i++;
                }
            }
            
            // Set up the translate button
            document.getElementById('translateBtn').addEventListener('click', async function() {
                const targetLang = document.getElementById('targetLanguage').value;
                const text = document.getElementById('textToTranslate').value;
                
                // Show loading indicator
                document.getElementById('loading').style.display = 'block';
                document.getElementById('result').style.display = 'none';
                document.getElementById('error').style.display = 'none';
                
                try {
                    // Perform translation
                    const translation = await translateText(text, "en", targetLang);
                    
                    // Hide loading indicator
                    document.getElementById('loading').style.display = 'none';
                    
                    // Show result
                    document.getElementById('result').style.display = 'block';
                    document.getElementById('originalText').textContent = text;
                    document.getElementById('translatedText').textContent = translation;
                } catch (error) {
                    // Hide loading indicator and show error
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('error').style.display = 'block';
                    document.getElementById('error').textContent = error.message;
                }
            });
        }
        
        // Initialize the page when loaded
        window.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html>
