import { FastifyInstance, FastifyPluginAsync } from 'fastify';
import { LikeController } from '../controllers/like.controller';
import { ToggleLikeSchema, HasUserLikedSchema, GetLikeCountSchema } from '../schemas/like.schema';
import { authenticate } from '../plugins/authenticate';

const likeRoutes: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  const likeController = new LikeController();

  // Toggle like for a video (authenticated)
  fastify.post<{
    Params: { videoId: string }
  }>(
    '/:videoId',
    {
      schema: ToggleLikeSchema,
      preHandler: authenticate,
    },
    (request, reply) => likeController.toggleLike(request as any, reply)
  );

  // Check if a user has liked a video (authenticated)
  fastify.get<{
    Params: { videoId: string }
  }>(
    '/:videoId/check',
    {
      schema: HasUserLikedSchema,
      preHandler: authenticate,
    },
    (request, reply) => likeController.hasUserLiked(request as any, reply)
  );

  // Get like count for a video (public)
  fastify.get<{
    Params: { videoId: string }
  }>(
    '/:videoId/count',
    {
      schema: GetLikeCountSchema,
    },
    (request, reply) => likeController.getLikeCount(request as any, reply)
  );
};

export default likeRoutes;
