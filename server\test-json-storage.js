/**
 * Test script for JSON storage functionality
 * This script tests the video JSON storage system
 */

const { JsonStorageUtil } = require('./src/utils/json-storage.util');
const { VideoJsonStorageService } = require('./src/services/video-json-storage.service');

/**
 * Test the JSON storage utility
 */
async function testJsonStorageUtil() {
  console.log('🧪 Testing JsonStorageUtil...');
  
  try {
    // Initialize storage
    await JsonStorageUtil.initializeStorage();
    console.log('✅ Storage initialized successfully');
    
    // Read all videos
    const videosData = await JsonStorageUtil.readAllVideos();
    console.log(`📊 Current videos in storage: ${videosData.videos.length}`);
    
    // Test video formatting
    const testVideo = {
      id: 'test123',
      title: 'Test Video',
      description: 'This is a test video for JSON storage',
      url: 'XLcMq2',
      thumbnailUrl: 'https://example.com/thumb.jpg',
      duration: 120,
      userId: 'user123',
      channelId: 'channel123',
      visibility: 'public',
      tags: ['test', 'json', 'storage'],
      category: 'Education',
      contentRating: 'general',
      processingStatus: 'ready',
      stats: {
        views: 100,
        likes: 10,
        dislikes: 1,
        comments: 5
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const formattedVideo = JsonStorageUtil.formatVideoForStorage(testVideo);
    console.log('✅ Video formatted successfully');
    console.log(`   Search keywords: ${formattedVideo.searchKeywords.join(', ')}`);
    console.log(`   Watch URL: ${formattedVideo.watchUrl}`);
    
  } catch (error) {
    console.error('❌ JsonStorageUtil test failed:', error);
  }
}

/**
 * Test the video JSON storage service
 */
async function testVideoJsonStorageService() {
  console.log('\n🧪 Testing VideoJsonStorageService...');
  
  try {
    const service = new VideoJsonStorageService();
    
    // Initialize service
    await service.initialize();
    console.log('✅ Service initialized successfully');
    
    // Get storage stats
    const stats = await service.getStorageStats();
    console.log('📈 Storage Statistics:');
    console.log(`   Total Videos: ${stats.totalVideos}`);
    console.log(`   Last Updated: ${stats.lastUpdated}`);
    
    // Test search functionality
    if (stats.totalVideos > 0) {
      console.log('\n🔍 Testing search functionality...');
      
      // Search for common terms
      const searchTerms = ['video', 'test', 'education', 'logarithm'];
      
      for (const term of searchTerms) {
        const results = await service.searchVideos(term);
        console.log(`   Search "${term}": ${results.length} results`);
      }
    } else {
      console.log('ℹ️ No videos in storage to test search functionality');
    }
    
  } catch (error) {
    console.error('❌ VideoJsonStorageService test failed:', error);
  }
}

/**
 * Test adding a sample video
 */
async function testAddSampleVideo() {
  console.log('\n🧪 Testing adding sample video...');
  
  try {
    const service = new VideoJsonStorageService();
    
    const sampleVideo = {
      id: `test_${Date.now()}`,
      title: 'Sample Test Video',
      description: 'This is a sample video added by the test script to demonstrate JSON storage functionality',
      url: 'XLcMq2',
      thumbnailUrl: 'https://via.placeholder.com/480x360?text=Test+Video',
      duration: 300,
      userId: 'test_user',
      channelId: 'test_channel',
      visibility: 'public',
      tags: ['test', 'sample', 'json-storage', 'demo'],
      category: 'Technology',
      contentRating: 'general',
      processingStatus: 'ready',
      stats: {
        views: 0,
        likes: 0,
        dislikes: 0,
        comments: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Add the video
    await service.addVideo(sampleVideo);
    console.log('✅ Sample video added successfully');
    
    // Verify it was added
    const addedVideo = await service.getVideoById(sampleVideo.id);
    if (addedVideo) {
      console.log('✅ Sample video retrieved successfully');
      console.log(`   Title: ${addedVideo.title}`);
      console.log(`   Watch URL: ${addedVideo.watchUrl}`);
    } else {
      console.log('❌ Sample video not found after adding');
    }
    
    // Test search for the new video
    const searchResults = await service.searchVideos('sample');
    console.log(`🔍 Search for "sample": ${searchResults.length} results`);
    
    // Get updated stats
    const updatedStats = await service.getStorageStats();
    console.log(`📊 Updated total videos: ${updatedStats.totalVideos}`);
    
  } catch (error) {
    console.error('❌ Add sample video test failed:', error);
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 JSON Storage Test Suite');
  console.log('===========================');
  
  try {
    await testJsonStorageUtil();
    await testVideoJsonStorageService();
    await testAddSampleVideo();
    
    console.log('\n✅ All tests completed successfully!');
    console.log('\n📁 JSON files location: server/json/videos/');
    console.log('   - all-videos.json (main storage file)');
    console.log('   - backups/ (backup files)');
    
  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
