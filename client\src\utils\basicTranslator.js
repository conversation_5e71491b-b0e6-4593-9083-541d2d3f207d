/**
 * Basic Translator - Provides simple translations for common phrases
 */

// Dictionary of translations for common phrases
const translations = {
  // Greetings
  'hello': {
    'hi': 'नमस्ते',
    'mr': 'नमस्कार',
    'gu': 'નમસ્તે',
    'ta': 'வணக்கம்',
    'te': 'హలో',
    'bn': 'হ্যালো',
    'kn': 'ಹಲೋ',
    'ml': 'ഹലോ',
    'es': 'Hola',
    'fr': 'Bonjour',
    'de': 'Hallo',
    'zh': '你好',
    'ja': 'こんにちは',
    'ru': 'Привет',
    'ar': 'مرحبا'
  },
  'hi': {
    'hi': 'नमस्ते',
    'mr': 'नमस्कार',
    'gu': 'નમસ્તે',
    'ta': 'வணக்கம்',
    'te': 'హలో',
    'bn': 'হ্যালো',
    'kn': 'ಹಲೋ',
    'ml': 'ഹലോ',
    'es': 'Hola',
    'fr': 'Bonjour',
    'de': 'Hallo',
    'zh': '你好',
    'ja': 'こんにちは',
    'ru': 'Привет',
    'ar': 'مرحبا'
  },
  'hii': {
    'hi': 'नमस्ते',
    'mr': 'नमस्कार',
    'gu': 'નમસ્તે',
    'ta': 'வணக்கம்',
    'te': 'హలో',
    'bn': 'হ্যালো',
    'kn': 'ಹಲೋ',
    'ml': 'ഹലോ',
    'es': 'Hola',
    'fr': 'Bonjour',
    'de': 'Hallo',
    'zh': '你好',
    'ja': 'こんにちは',
    'ru': 'Привет',
    'ar': 'مرحبا'
  },
  'good morning': {
    'hi': 'सुप्रभात',
    'mr': 'सुप्रभात',
    'gu': 'સુપ્રભાત',
    'ta': 'காலை வணக்கம்',
    'te': 'శుభోదయం',
    'bn': 'সুপ্রভাত',
    'kn': 'ಶುಭೋದಯ',
    'ml': 'സുപ്രഭാതം',
    'es': 'Buenos días',
    'fr': 'Bonjour',
    'de': 'Guten Morgen',
    'zh': '早上好',
    'ja': 'おはようございます',
    'ru': 'Доброе утро',
    'ar': 'صباح الخير'
  },
  
  // Questions
  'how are you': {
    'hi': 'आप कैसे हैं?',
    'mr': 'तुम्ही कसे आहात?',
    'gu': 'તમે કેમ છો?',
    'ta': 'நீங்கள் எப்படி இருக்கிறீர்கள்?',
    'te': 'మీరు ఎలా ఉన్నారు?',
    'bn': 'আপনি কেমন আছেন?',
    'kn': 'ನೀವು ಹೇಗಿದ್ದೀರಿ?',
    'ml': 'സുഖമാണോ?',
    'es': '¿Cómo estás?',
    'fr': 'Comment allez-vous?',
    'de': 'Wie geht es dir?',
    'zh': '你好吗？',
    'ja': 'お元気ですか？',
    'ru': 'Как дела?',
    'ar': 'كيف حالك؟'
  },
  'how can i help you': {
    'hi': 'मैं आपकी कैसे मदद कर सकता हूँ?',
    'mr': 'मी आपली कशी मदत करू शकतो?',
    'gu': 'હું તમને કેવી રીતે મદદ કરી શકું?',
    'ta': 'நான் உங்களுக்கு எப்படி உதவ முடியும்?',
    'te': 'నేను మీకు ఎలా సహాయం చేయగలను?',
    'bn': 'আমি আপনাকে কিভাবে সাহায্য করতে পারি?',
    'kn': 'ನಾನು ನಿಮಗೆ ಹೇಗೆ ಸಹಾಯ ಮಾಡಬಹುದು?',
    'ml': 'എനിക്ക് നിങ്ങളെ എങ്ങനെ സഹായിക്കാൻ കഴിയും?',
    'es': '¿Cómo puedo ayudarte?',
    'fr': 'Comment puis-je vous aider?',
    'de': 'Wie kann ich Ihnen helfen?',
    'zh': '我能帮你什么?',
    'ja': 'どのようにお手伝いできますか?',
    'ru': 'Как я могу вам помочь?',
    'ar': 'كيف يمكنني مساعدتك؟'
  },
  'may i help you': {
    'hi': 'क्या मैं आपकी मदद कर सकता हूँ?',
    'mr': 'मी आपली मदत करू शकतो का?',
    'gu': 'શું હું તમને મદદ કરી શકું?',
    'ta': 'நான் உங்களுக்கு உதவலாமா?',
    'te': 'నేను మీకు సహాయం చేయవచ్చా?',
    'bn': 'আমি কি আপনাকে সাহায্য করতে পারি?',
    'kn': 'ನಾನು ನಿಮಗೆ ಸಹಾಯ ಮಾಡಬಹುದೇ?',
    'ml': 'എനിക്ക് നിങ്ങളെ സഹായിക്കാമോ?',
    'es': '¿Puedo ayudarte?',
    'fr': 'Puis-je vous aider?',
    'de': 'Kann ich Ihnen helfen?',
    'zh': '我能帮你吗?',
    'ja': 'お手伝いしましょうか?',
    'ru': 'Могу я вам помочь?',
    'ar': 'هل يمكنني مساعدتك؟'
  },
  'why are you happy': {
    'hi': 'आप खुश क्यों हैं?',
    'mr': 'तुम्ही आनंदी का आहात?',
    'gu': 'તમે શા માટે ખુશ છો?',
    'ta': 'நீங்கள் ஏன் மகிழ்ச்சியாக இருக்கிறீர்கள்?',
    'te': 'మీరు ఎందుకు సంతోషంగా ఉన్నారు?',
    'bn': 'আপনি কেন খুশি?',
    'kn': 'ನೀವು ಏಕೆ ಸಂತೋಷವಾಗಿದ್ದೀರಿ?',
    'ml': 'നിങ്ങൾ എന്തിനാണ് സന്തോഷവാനായിരിക്കുന്നത്?',
    'es': '¿Por qué estás feliz?',
    'fr': 'Pourquoi êtes-vous heureux?',
    'de': 'Warum bist du glücklich?',
    'zh': '你为什么开心？',
    'ja': 'なぜ嬉しいのですか？',
    'ru': 'Почему вы счастливы?',
    'ar': 'لماذا أنت سعيد؟'
  },
  
  // Statements
  'i am happy': {
    'hi': 'मैं खुश हूँ',
    'mr': 'मी आनंदी आहे',
    'gu': 'હું ખુશ છું',
    'ta': 'நான் மகிழ்ச்சியாக இருக்கிறேன்',
    'te': 'నేను సంతోషంగా ఉన్నాను',
    'bn': 'আমি খুশি',
    'kn': 'ನಾನು ಸಂತೋಷವಾಗಿದ್ದೇನೆ',
    'ml': 'ഞാൻ സന്തോഷവാനാണ്',
    'es': 'Estoy feliz',
    'fr': 'Je suis heureux',
    'de': 'Ich bin glücklich',
    'zh': '我很开心',
    'ja': '私は幸せです',
    'ru': 'Я счастлив',
    'ar': 'أنا سعيد'
  },
  'because my classes are ending': {
    'hi': 'क्योंकि मेरी कक्षाएं समाप्त हो रही हैं',
    'mr': 'कारण माझे वर्ग संपत आहेत',
    'gu': 'કારણ કે મારા વર્ગો સમાપ્ત થઈ રહ્યા છે',
    'ta': 'ஏனென்றால் என் வகுப்புகள் முடிவடைகின்றன',
    'te': 'ఎందుకంటే నా తరగతులు ముగుస్తున్నాయి',
    'bn': 'কারণ আমার ক্লাসগুলি শেষ হচ্ছে',
    'kn': 'ಏಕೆಂದರೆ ನನ್ನ ತರಗತಿಗಳು ಕೊನೆಗೊಳ್ಳುತ್ತಿವೆ',
    'ml': 'കാരണം എന്റെ ക്ലാസുകൾ അവസാനിക്കുന്നു',
    'es': 'porque mis clases están terminando',
    'fr': 'parce que mes cours se terminent',
    'de': 'weil meine Klassen enden',
    'zh': '因为我的课程正在结束',
    'ja': '私のクラスが終わるからです',
    'ru': 'потому что мои занятия заканчиваются',
    'ar': 'لأن فصولي تنتهي'
  },
  'do want to play game': {
    'hi': 'क्या आप गेम खेलना चाहते हैं?',
    'mr': 'तुम्हाला गेम खेळायचा आहे का?',
    'gu': 'શું તમે રમત રમવા માંગો છો?',
    'ta': 'விளையாட்டு விளையாட விரும்புகிறீர்களா?',
    'te': 'ఆట ఆడాలనుకుంటున్నారా?',
    'bn': 'আপনি কি গেম খেলতে চান?',
    'kn': 'ಆಟ ಆಡಲು ಬಯಸುತ್ತೀರಾ?',
    'ml': 'കളി കളിക്കാൻ ആഗ്രഹിക്കുന്നുണ്ടോ?',
    'es': '¿Quieres jugar un juego?',
    'fr': 'Voulez-vous jouer à un jeu?',
    'de': 'Willst du ein Spiel spielen?',
    'zh': '你想玩游戏吗？',
    'ja': 'ゲームをしたいですか？',
    'ru': 'Хотите поиграть в игру?',
    'ar': 'هل تريد أن تلعب لعبة؟'
  },
  'it is ok': {
    'hi': 'यह ठीक है',
    'mr': 'हे ठीक आहे',
    'gu': 'તે બરાબર છે',
    'ta': 'அது சரி',
    'te': 'అది సరే',
    'bn': 'এটা ঠিক আছে',
    'kn': 'ಅದು ಸರಿ',
    'ml': 'അത് ശരിയാണ്',
    'es': 'Está bien',
    'fr': 'C\'est bon',
    'de': 'Es ist in Ordnung',
    'zh': '没关系',
    'ja': '大丈夫です',
    'ru': 'Это нормально',
    'ar': 'هذا جيد'
  }
};

/**
 * Translates text to the target language
 * 
 * @param {string} text - Text to translate
 * @param {string} targetLanguage - Target language code
 * @returns {string} - Translated text
 */
export function translateText(text, targetLanguage) {
  if (!text || targetLanguage === 'en') {
    return text;
  }

  // Convert to lowercase and trim for better matching
  const normalizedText = text.toLowerCase().trim();
  
  // Remove punctuation for better matching
  const cleanText = normalizedText.replace(/[?!.,]/g, '');
  
  // Check if we have a direct translation
  if (translations[cleanText] && translations[cleanText][targetLanguage]) {
    // Add back question mark if original had one
    return text.includes('?') 
      ? translations[cleanText][targetLanguage] + (translations[cleanText][targetLanguage].includes('?') ? '' : '?')
      : translations[cleanText][targetLanguage];
  }
  
  // If no direct translation, add a language prefix
  const prefixes = {
    'hi': 'हिंदी: ',
    'mr': 'मराठी: ',
    'gu': 'ગુજરાતી: ',
    'ta': 'தமிழ்: ',
    'te': 'తెలుగు: ',
    'bn': 'বাংলা: ',
    'kn': 'ಕನ್ನಡ: ',
    'ml': 'മലയാളം: ',
    'es': 'Español: ',
    'fr': 'Français: ',
    'de': 'Deutsch: ',
    'zh': '中文: ',
    'ja': '日本語: ',
    'ru': 'Русский: ',
    'ar': 'العربية: '
  };
  
  return prefixes[targetLanguage] + text;
}

export default {
  translateText
};
