// Test script for Bhashini API integration
import { translateText } from '../services/bhashiniDirectApi';

// Sample text to translate
const sampleText = "Hello, how are you? This is a test of the Bhashini translation API.";

// Test translation to Hindi
async function testHindiTranslation() {
  console.log("Testing translation to Hindi...");
  try {
    const result = await translateText(sampleText, 'en', 'hi');
    console.log("Original text:", sampleText);
    console.log("Hindi translation:", result);
    return result;
  } catch (error) {
    console.error("Error translating to Hindi:", error);
    return null;
  }
}

// Test translation to multiple Indian languages
async function testMultipleLanguages() {
  const languages = ['hi', 'mr', 'gu', 'ta', 'te', 'bn', 'kn', 'ml', 'pa', 'or', 'as', 'ur'];
  
  console.log("Testing translation to multiple Indian languages...");
  
  for (const lang of languages) {
    try {
      console.log(`\nTranslating to ${lang}...`);
      const result = await translateText(sampleText, 'en', lang);
      console.log("Original text:", sampleText);
      console.log(`${lang} translation:`, result);
    } catch (error) {
      console.error(`Error translating to ${lang}:`, error);
    }
  }
}

// Run the tests
async function runTests() {
  console.log("=== Bhashini API Integration Test ===\n");
  
  // Test Hindi translation
  const hindiResult = await testHindiTranslation();
  
  if (hindiResult) {
    console.log("\nHindi translation successful!");
    
    // If Hindi translation works, test other languages
    await testMultipleLanguages();
  }
  
  console.log("\n=== Test Complete ===");
}

// Execute tests
runTests().catch(error => {
  console.error("Test failed with error:", error);
});

// Export the test functions
export {
  testHindiTranslation,
  testMultipleLanguages,
  runTests
};
