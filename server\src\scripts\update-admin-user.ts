import { config } from 'dotenv';
import mongoose from 'mongoose';
import { connectDB } from '../config/database';
import { UserModel } from '../models';
import bcrypt from 'bcrypt';

// Load environment variables
config();

/**
 * <PERSON><PERSON><PERSON> to update the admin user with enhanced information
 */
async function updateAdminUser() {
  try {
    console.log('🔄 Starting admin user update...');

    // Connect to the database
    await connectDB();
    console.log('MongoDB connected successfully');

    // Find the admin user
    const adminUser = await UserModel.findOne({ email: '<EMAIL>' });

    if (!adminUser) {
      console.log('❌ Admin user not found. Please run the seed script first.');
      await mongoose.disconnect();
      return;
    }

    console.log(`Found admin user: ${adminUser.username} (${adminUser.email})`);

    // Hash a new password if needed
    // const salt = await bcrypt.genSalt(10);
    // const newPassword = await bcrypt.hash('new_admin_password', salt);

    // Update the admin user with enhanced information
    // Update using the found document instead of findOneAndUpdate

    // Update personal information
    adminUser.firstName = 'Super';
    adminUser.lastName = 'Administrator';
    adminUser.displayName = 'System Super Admin';

    // Add profile information
    adminUser.avatar = 'https://ui-avatars.com/api/?name=Super+Admin&background=0D8ABC&color=fff';
    adminUser.phone = '+91 **********';
    adminUser.language = 'en';
    adminUser.timezone = 'Asia/Kolkata';

    // Ensure account is fully verified and active
    adminUser.status = 'active';
    adminUser.emailVerified = true;
    adminUser.phoneVerified = true;

    // Add preferences
    adminUser.preferences = {
      theme: 'system',
      emailNotifications: {
        marketing: true,
        account: true,
        security: true,
        social: true
      },
      pushNotifications: {
        messages: true,
        comments: true,
        followers: true,
        videoUploads: true
      }
    };

    // Update stats
    adminUser.stats = {
      videosUploaded: 0,
      totalViews: 0,
      followers: 0,
      following: 0,
      reputation: 1000 // Give admin high reputation
    };

    // Set resource limits
    adminUser.limits = {
      storageUsed: 0,
      storageLimit: ***********, // 10GB in bytes
      videoUploadLimit: 1000
    };

    // Update metadata
    adminUser.updatedAt = new Date();
    adminUser.updatedBy = 'system';

    // Save the updated admin user
    await adminUser.save();

    // Get the updated admin user
    const updatedAdmin = await UserModel.findOne({ email: '<EMAIL>' });

    if (updatedAdmin) {
      console.log('✅ Admin user updated successfully!');
      console.log('Updated admin details:');
      console.log(`  Name: ${updatedAdmin.firstName} ${updatedAdmin.lastName}`);
      console.log(`  Display Name: ${updatedAdmin.displayName}`);
      console.log(`  Email: ${updatedAdmin.email}`);
      console.log(`  Status: ${updatedAdmin.status}`);
      console.log(`  Roles: ${updatedAdmin.roles.join(', ')}`);
    } else {
      console.log('❌ Failed to update admin user.');
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('MongoDB disconnected');

  } catch (error) {
    console.error('❌ Error updating admin user:', error);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('MongoDB disconnected');

    process.exit(1);
  }
}

// Run the update script
updateAdminUser();
