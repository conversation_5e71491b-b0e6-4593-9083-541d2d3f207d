import mongoose from 'mongoose';
import { AppError, createBadRequestError, createConflictError, createInternalServerError } from './AppError';
import { ErrorCodes } from './errorCodes';
import { logger } from '../logger';

/**
 * Handle Mongoose validation errors
 * @param error Mongoose validation error
 * @returns AppError
 */
export function handleValidationError(error: mongoose.Error.ValidationError): AppError {
  const validationErrors = Object.keys(error.errors).map(field => ({
    field,
    message: error.errors[field].message,
  }));

  return createBadRequestError(
    'Validation error',
    ErrorCodes.DATABASE_VALIDATION_ERROR,
    validationErrors
  );
}

/**
 * Handle Mongoose duplicate key errors
 * @param error MongoDB duplicate key error
 * @returns AppError
 */
export function handleDuplicateKeyError(error: any): AppError {
  const field = Object.keys(error.keyValue)[0];
  const value = error.keyValue[field];

  return createConflictError(
    `Duplicate value for ${field}: ${value}`,
    ErrorCodes.CONFLICT,
    {
      field,
      value,
    }
  );
}

/**
 * Handle Mongoose cast errors
 * @param error Mongoose cast error
 * @returns AppError
 */
export function handleCastError(error: mongoose.Error.CastError): AppError {
  return createBadRequestError(
    `Invalid ${error.path}: ${error.value}`,
    ErrorCodes.INVALID_INPUT,
    {
      field: error.path,
      value: error.value,
    }
  );
}

/**
 * Handle database errors
 * @param error Error from database operation
 * @returns AppError
 */
export function handleDatabaseError(error: any): AppError {
  // Log the original error
  logger.error('Database error', error);

  // Handle specific error types
  if (error instanceof mongoose.Error.ValidationError) {
    return handleValidationError(error);
  }

  if (error.name === 'MongoError' && error.code === 11000) {
    return handleDuplicateKeyError(error);
  }

  if (error instanceof mongoose.Error.CastError) {
    return handleCastError(error);
  }

  // Handle connection errors
  if (error.name === 'MongoNetworkError') {
    return createInternalServerError(
      'Database connection error',
      ErrorCodes.DATABASE_CONNECTION_ERROR
    );
  }

  // Handle other database errors
  return createInternalServerError(
    'Database error',
    ErrorCodes.DATABASE_ERROR
  );
}
