import { FastifyInstance } from 'fastify';
import axios from 'axios';

/**
 * Routes for testing API connections
 */
export default async function apiTestRoutes(fastify: FastifyInstance) {
  // Test route for Engaxe API
  fastify.get('/api-test/engaxe/:videoId', async (request, reply) => {
    const { videoId } = request.params as { videoId: string };
    const serverKey = '1312a113c58715637a94437389326a49';
    
    console.log(`Testing Engaxe API for video ID: ${videoId}`);
    
    const results: any = {
      attempts: [],
      success: false,
      data: null,
      error: null
    };

    // Attempt 1: API URL with GET
    try {
      console.log('Attempt 1: API URL with GET');
      const apiUrl = 'https://api.engaxe.com/api/v1.0';
      const response = await axios.get(`${apiUrl}/platform/mobile/get_video_details`, {
        params: {
          video_id: videoId,
          server_key: server<PERSON>ey
        },
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'LawEngaxe/1.0'
        },
        timeout: 10000
      });
      
      results.attempts.push({
        method: 'GET',
        url: `${apiUrl}/platform/mobile/get_video_details`,
        status: response.status,
        success: response.data.api_status === '200',
        response: response.data
      });
      
      if (response.data.api_status === '200') {
        results.success = true;
        results.data = response.data.data;
      }
    } catch (error: any) {
      results.attempts.push({
        method: 'GET',
        url: 'https://api.engaxe.com/api/v1.0/platform/mobile/get_video_details',
        error: error.message,
        response: error.response?.data
      });
    }
    
    // Attempt 2: API URL with POST
    if (!results.success) {
      try {
        console.log('Attempt 2: API URL with POST');
        const apiUrl = 'https://api.engaxe.com/api/v1.0';
        const response = await axios.post(`${apiUrl}/platform/mobile/get_video_details`, {
          video_id: videoId,
          server_key: serverKey
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'LawEngaxe/1.0'
          },
          timeout: 10000
        });
        
        results.attempts.push({
          method: 'POST',
          url: `${apiUrl}/platform/mobile/get_video_details`,
          status: response.status,
          success: response.data.api_status === '200',
          response: response.data
        });
        
        if (response.data.api_status === '200') {
          results.success = true;
          results.data = response.data.data;
        }
      } catch (error: any) {
        results.attempts.push({
          method: 'POST',
          url: 'https://api.engaxe.com/api/v1.0/platform/mobile/get_video_details',
          error: error.message,
          response: error.response?.data
        });
      }
    }
    
    // Attempt 3: Base URL with GET
    if (!results.success) {
      try {
        console.log('Attempt 3: Base URL with GET');
        const baseUrl = 'https://engaxe.com/api/v1.0';
        const response = await axios.get(`${baseUrl}/platform/mobile/get_video_details`, {
          params: {
            video_id: videoId,
            server_key: serverKey
          },
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'LawEngaxe/1.0'
          },
          timeout: 10000
        });
        
        results.attempts.push({
          method: 'GET',
          url: `${baseUrl}/platform/mobile/get_video_details`,
          status: response.status,
          success: response.data.api_status === '200',
          response: response.data
        });
        
        if (response.data.api_status === '200') {
          results.success = true;
          results.data = response.data.data;
        }
      } catch (error: any) {
        results.attempts.push({
          method: 'GET',
          url: 'https://engaxe.com/api/v1.0/platform/mobile/get_video_details',
          error: error.message,
          response: error.response?.data
        });
      }
    }
    
    // Attempt 4: Base URL with POST
    if (!results.success) {
      try {
        console.log('Attempt 4: Base URL with POST');
        const baseUrl = 'https://engaxe.com/api/v1.0';
        const response = await axios.post(`${baseUrl}/platform/mobile/get_video_details`, {
          video_id: videoId,
          server_key: serverKey
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'LawEngaxe/1.0'
          },
          timeout: 10000
        });
        
        results.attempts.push({
          method: 'POST',
          url: `${baseUrl}/platform/mobile/get_video_details`,
          status: response.status,
          success: response.data.api_status === '200',
          response: response.data
        });
        
        if (response.data.api_status === '200') {
          results.success = true;
          results.data = response.data.data;
        }
      } catch (error: any) {
        results.attempts.push({
          method: 'POST',
          url: 'https://engaxe.com/api/v1.0/platform/mobile/get_video_details',
          error: error.message,
          response: error.response?.data
        });
      }
    }
    
    // Attempt 5: Direct URL with GET
    if (!results.success) {
      try {
        console.log('Attempt 5: Direct URL with GET');
        const directUrl = `https://engaxe.com/api/v1.0/platform/mobile/get_video_details?video_id=${videoId}&server_key=${serverKey}`;
        const response = await axios.get(directUrl, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'LawEngaxe/1.0'
          },
          timeout: 10000
        });
        
        results.attempts.push({
          method: 'GET',
          url: directUrl,
          status: response.status,
          success: response.data.api_status === '200',
          response: response.data
        });
        
        if (response.data.api_status === '200') {
          results.success = true;
          results.data = response.data.data;
        }
      } catch (error: any) {
        results.attempts.push({
          method: 'GET',
          url: `https://engaxe.com/api/v1.0/platform/mobile/get_video_details?video_id=${videoId}&server_key=${serverKey}`,
          error: error.message,
          response: error.response?.data
        });
      }
    }
    
    if (!results.success) {
      results.error = 'All API attempts failed';
    }
    
    return reply.code(200).send(results);
  });
}
