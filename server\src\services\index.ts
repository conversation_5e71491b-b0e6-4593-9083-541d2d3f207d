import { videoService, VideoService } from './video.service';
import { engaxeApiService, EngaxeApiService } from './engaxe-api.service';
import { VideoIdMappingService } from './video-id-mapping.service';

// Create instances of services
export const videoIdMappingService = new VideoIdMappingService();

// Re-export the singletons
export { videoService, engaxeApiService };

// Export service classes
export { VideoService, EngaxeApiService, VideoIdMappingService };
