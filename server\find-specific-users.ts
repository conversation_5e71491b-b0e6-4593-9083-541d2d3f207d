import { UserModel } from './src/models';
import { connectDB } from './src/config/database';

async function findSpecificUsers() {
  try {
    await connectDB();
    
    // Find users by username
    const shreeUser = await UserModel.findOne({ username: 'shree' });
    const sejalUser = await UserModel.findOne({ username: 'Seja<PERSON>' });
    
    console.log('Results:');
    console.log('User with username "shree":', shreeUser ? 'Found' : 'Not found');
    console.log('User with username "Sejal":', sejalUser ? 'Found' : 'Not found');
    
    // Try case-insensitive search
    console.log('\nTrying case-insensitive search:');
    const shreeUserInsensitive = await UserModel.findOne({ 
      username: { $regex: new RegExp('^shree$', 'i') } 
    });
    const sejalUserInsensitive = await UserModel.findOne({ 
      username: { $regex: new RegExp('^sejal$', 'i') } 
    });
    
    console.log('User with username like "shree":', shreeUserInsensitive ? 'Found' : 'Not found');
    console.log('User with username like "Sejal":', sejalUserInsensitive ? 'Found' : 'Not found');
    
    // List all usernames for reference
    const allUsers = await UserModel.find({});
    console.log('\nAll usernames in the database:');
    allUsers.forEach(user => {
      console.log(` - ${user.username}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

findSpecificUsers();
