/**
 * Special fix for H5P fullscreen button
 * This file contains a direct fix for the specific H5P fullscreen button
 */

/**
 * Initialize a direct fix for the H5P fullscreen button
 * This function adds a direct event handler for the specific button
 */
export const initH5PFullscreenFix = (): void => {
  console.log('Initializing H5P fullscreen fix');

  // Function to handle the specific fullscreen button
  const setupSpecificButton = (): void => {
    // Try to find the specific button using multiple selectors to ensure we catch it
    const specificButtons = [
      ...document.querySelectorAll('div[role="button"][tabindex="0"].h5p-control.h5p-fullscreen'),
      ...document.querySelectorAll('div[role="button"][tabindex="0"][aria-label="Fullscreen"]'),
      ...document.querySelectorAll('.h5p-control.h5p-fullscreen'),
      ...document.querySelectorAll('div[role="button"].h5p-fullscreen')
    ];

    if (specificButtons.length > 0) {
      console.log(`Found ${specificButtons.length} H5P fullscreen buttons to fix`);

      // Process each button
      specificButtons.forEach((button, index) => {
        console.log(`Processing button ${index + 1}:`, button);

        // Apply direct styles to make it more clickable
        const buttonEl = button as HTMLElement;

        // Force these styles with !important to override any conflicting styles
        buttonEl.style.setProperty('pointer-events', 'auto', 'important');
        buttonEl.style.setProperty('cursor', 'pointer', 'important');
        buttonEl.style.setProperty('z-index', '99999', 'important'); // Higher z-index
        buttonEl.style.setProperty('position', 'relative', 'important');
        buttonEl.style.setProperty('background-color', 'rgba(0, 0, 0, 0.5)', 'important');
        buttonEl.style.setProperty('border-radius', '4px', 'important');
        buttonEl.style.setProperty('padding', '8px', 'important');
        buttonEl.style.setProperty('display', 'flex', 'important');
        buttonEl.style.setProperty('align-items', 'center', 'important');
        buttonEl.style.setProperty('justify-content', 'center', 'important');
        buttonEl.style.setProperty('width', '32px', 'important');
        buttonEl.style.setProperty('height', '32px', 'important');
        buttonEl.style.setProperty('min-width', '32px', 'important');
        buttonEl.style.setProperty('min-height', '32px', 'important');

        // Add a data attribute to mark this as fixed
        buttonEl.setAttribute('data-fullscreen-fixed', 'true');

        // Create a transparent overlay to capture clicks if needed
        const overlay = document.createElement('div');
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.zIndex = '1';
        overlay.style.cursor = 'pointer';
        overlay.className = 'h5p-fullscreen-overlay';

        // Add the overlay as a sibling to the button
        if (buttonEl.parentNode) {
          buttonEl.parentNode.insertBefore(overlay, buttonEl.nextSibling);

          // Add click handler to the overlay
          overlay.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            handleButtonClick(e);
          });
        }

        // Remove any existing click listeners to avoid duplicates
        buttonEl.removeEventListener('click', handleButtonClick);

        // Add our click handler with capture phase to ensure it runs first
        buttonEl.addEventListener('click', handleButtonClick, true);
      });
    } else {
      // If not found, try again after a short delay
      setTimeout(setupSpecificButton, 300);
    }
  };

  // Function to handle button click
  const handleButtonClick = (event: Event): void => {
    console.log('H5P fullscreen button clicked (from fix)');

    // Prevent default behavior
    event.preventDefault();
    event.stopPropagation();

    // Find the container using multiple strategies
    // First try to find specific containers by ID
    let container = document.getElementById('engaxe-player-container');

    // If not found, try other common container IDs
    if (!container) {
      container = document.getElementById('video-container');
    }

    // If still not found, try to find by class names
    if (!container) {
      container = document.querySelector('.h5p-iframe-wrapper') as HTMLElement;
    }

    // If still not found, try to find the closest container from the event target
    if (!container && event.target) {
      const target = event.target as HTMLElement;
      container = target.closest('.h5p-iframe-wrapper') ||
                 target.closest('.h5p-content') ||
                 target.closest('.video-player') ||
                 target.closest('.h5p-video-container') as HTMLElement;
    }

    // If still not found, try to find any iframe parent
    if (!container && event.target) {
      const target = event.target as HTMLElement;
      const iframe = target.closest('iframe');
      if (iframe && iframe.parentElement) {
        container = iframe.parentElement;
      }
    }

    // Last resort: find any video container on the page
    if (!container) {
      container = document.querySelector('.video-player') ||
                 document.querySelector('.h5p-iframe-wrapper') ||
                 document.querySelector('.h5p-content') ||
                 document.querySelector('#video-container') as HTMLElement;
    }

    if (container) {
      console.log('Found container for fullscreen:', container);

      try {
        if (document.fullscreenElement) {
          console.log('Exiting fullscreen');
          // Exit fullscreen
          document.exitFullscreen().catch(err => {
            console.error('Error exiting fullscreen:', err);

            // Try alternative methods
            try {
              if ((document as any).mozCancelFullScreen) {
                (document as any).mozCancelFullScreen();
              } else if ((document as any).webkitExitFullscreen) {
                (document as any).webkitExitFullscreen();
              } else if ((document as any).msExitFullscreen) {
                (document as any).msExitFullscreen();
              }
            } catch (altErr) {
              console.error('Alternative exit fullscreen methods failed:', altErr);
            }
          });
        } else {
          console.log('Entering fullscreen');
          // Prepare container for fullscreen
          container.style.setProperty('position', 'relative', 'important');
          container.style.setProperty('width', '100%', 'important');
          container.style.setProperty('height', '100%', 'important');

          // Enter fullscreen with all possible methods
          try {
            // Standard method
            if (container.requestFullscreen) {
              container.requestFullscreen().catch(err => {
                console.error('Error with standard fullscreen:', err);
                tryAlternativeMethods();
              });
            } else {
              tryAlternativeMethods();
            }

            // Function to try alternative fullscreen methods
            function tryAlternativeMethods() {
              if ((container as any).mozRequestFullScreen) {
                (container as any).mozRequestFullScreen();
              } else if ((container as any).webkitRequestFullscreen) {
                (container as any).webkitRequestFullscreen();
              } else if ((container as any).msRequestFullscreen) {
                (container as any).msRequestFullscreen();
              } else {
                console.error('Fullscreen API is not supported in this browser');
              }
            }
          } catch (error) {
            console.error('All fullscreen methods failed:', error);
          }
        }
      } catch (error) {
        console.error('Error toggling fullscreen:', error);
      }
    } else {
      console.error('No suitable container found for fullscreen');

      // Last resort: try to make the document body fullscreen
      try {
        if (!document.fullscreenElement) {
          document.documentElement.requestFullscreen().catch(err => {
            console.error('Error making document fullscreen:', err);
          });
        } else {
          document.exitFullscreen();
        }
      } catch (error) {
        console.error('Error with document fullscreen:', error);
      }
    }
  };

  // Add a direct document-level event listener for the specific button
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;

    // Check if the clicked element matches any of our fullscreen button selectors
    if (
      target && (
        // Check for standard H5P fullscreen button
        (target.getAttribute('role') === 'button' &&
         target.getAttribute('tabindex') === '0' &&
         target.classList.contains('h5p-control') &&
         target.classList.contains('h5p-fullscreen')) ||

        // Check for fullscreen button by aria-label
        (target.getAttribute('role') === 'button' &&
         target.getAttribute('aria-label') === 'Fullscreen') ||

        // Check for any element with h5p-fullscreen class
        target.classList.contains('h5p-fullscreen') ||

        // Check for any element with fullscreen in the aria-label
        (target.hasAttribute('aria-label') &&
         target.getAttribute('aria-label')?.toLowerCase().includes('fullscreen'))
      )
    ) {
      console.log('Direct click on H5P fullscreen button detected (from fix)');

      // Always handle the click, even if the button has been processed
      // This ensures maximum compatibility
      event.preventDefault();
      event.stopPropagation();

      // Call our handler directly
      handleButtonClick(event);

      // Return false to prevent any other handlers from running
      return false;
    }
  }, true); // Use capture phase to ensure our handler runs first

  // Add a mousedown event listener to catch events before they're processed
  document.addEventListener('mousedown', (event) => {
    const target = event.target as HTMLElement;

    // Check if the clicked element matches any of our fullscreen button selectors
    if (
      target && (
        // Check for standard H5P fullscreen button
        (target.getAttribute('role') === 'button' &&
         target.getAttribute('tabindex') === '0' &&
         target.classList.contains('h5p-control') &&
         target.classList.contains('h5p-fullscreen')) ||

        // Check for fullscreen button by aria-label
        (target.getAttribute('role') === 'button' &&
         target.getAttribute('aria-label') === 'Fullscreen') ||

        // Check for any element with h5p-fullscreen class
        target.classList.contains('h5p-fullscreen') ||

        // Check for any element with fullscreen in the aria-label
        (target.hasAttribute('aria-label') &&
         target.getAttribute('aria-label')?.toLowerCase().includes('fullscreen'))
      )
    ) {
      console.log('Mousedown on H5P fullscreen button detected (from fix)');

      // Mark this element to be handled on click
      target.setAttribute('data-fullscreen-pending', 'true');

      // Don't prevent default here to allow focus
    }
  }, true);

  // Initial setup
  setupSpecificButton();

  // Set up a mutation observer to detect when the button is added to the DOM
  const observer = new MutationObserver((mutations) => {
    let shouldSetup = false;

    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any of the added nodes contain the specific button
        for (let i = 0; i < mutation.addedNodes.length; i++) {
          const node = mutation.addedNodes[i];
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;

            // Check for any of our fullscreen button selectors
            if (
              element.querySelector('div[role="button"][tabindex="0"].h5p-control.h5p-fullscreen') ||
              element.querySelector('div[role="button"][aria-label="Fullscreen"]') ||
              element.querySelector('.h5p-fullscreen')
            ) {
              shouldSetup = true;
              break;
            }
          }
        }
      }

      if (shouldSetup) {
        break;
      }
    }

    if (shouldSetup) {
      // If found, set it up
      setupSpecificButton();
    }
  });

  // Start observing the document body for changes
  observer.observe(document.body, { childList: true, subtree: true });

  // Removed frequent interval checking to prevent performance issues and unwanted reloads
  // The mutation observer should handle dynamic content changes efficiently

  // Add a special handler for iframes that might contain H5P content
  const setupIframeHandlers = () => {
    const iframes = document.querySelectorAll('iframe');

    iframes.forEach(iframe => {
      try {
        // Try to access the iframe's document
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

        if (iframeDoc) {
          // Add event listener to the iframe document
          iframeDoc.addEventListener('click', (event) => {
            const target = event.target as HTMLElement;

            if (
              target && (
                target.classList.contains('h5p-fullscreen') ||
                target.classList.contains('h5p-control') ||
                (target.hasAttribute('aria-label') &&
                 target.getAttribute('aria-label')?.toLowerCase().includes('fullscreen'))
              )
            ) {
              // Use the iframe's parent element as the container
              const container = iframe.parentElement || iframe;

              // Prevent default behavior
              event.preventDefault();
              event.stopPropagation();

              // Toggle fullscreen
              if (document.fullscreenElement) {
                document.exitFullscreen();
              } else if (container) {
                container.requestFullscreen();
              }
            }
          }, true);
        }
      } catch (error) {
        // Ignore cross-origin errors
        // This is expected for iframes from different domains
      }
    });
  };

  // Set up iframe handlers initially and periodically
  setupIframeHandlers();
  setInterval(setupIframeHandlers, 2000);
};
