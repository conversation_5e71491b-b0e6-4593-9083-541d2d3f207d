const mongoose = require('mongoose');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/lawengaxe';

// Define the Video schema
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  tags: [String],
  category: String,
  contentRating: String,
  processingStatus: String,
  stats: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    playlistAdds: { type: Number, default: 0 },
    averageWatchTime: { type: Number, default: 0 },
    retentionRate: { type: Number, default: 0 }
  },
  file: {
    originalName: String,
    size: Number,
    mimeType: String,
    codec: String,
    resolution: String,
    bitrate: Number,
    frameRate: Number
  },
  languages: [{
    code: String,
    name: String,
    flag: String,
    isDefault: Boolean,
    url: String
  }],
  source: {
    type: String,
    originalUrl: String,
    platform: String,
    externalId: String
  },
  createdAt: Date,
  updatedAt: Date,
  deletedAt: Date
});

// Create the Video model
const Video = mongoose.model('Video', VideoSchema);

async function checkVideoDurations() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    // Get all videos
    const videos = await Video.find({ deletedAt: null }).select('id title duration processingStatus createdAt');
    console.log(`📊 Found ${videos.length} videos in database`);
    
    console.log('\n📋 Video Duration Report:');
    console.log('=' .repeat(80));
    
    let zeroDurationCount = 0;
    let validDurationCount = 0;
    
    videos.forEach((video, index) => {
      const duration = video.duration || 0;
      const status = duration === 0 ? '❌ ZERO' : '✅ VALID';
      
      if (duration === 0) {
        zeroDurationCount++;
      } else {
        validDurationCount++;
      }
      
      console.log(`${index + 1}. ${status} | Duration: ${duration}s | Status: ${video.processingStatus} | Title: ${video.title.substring(0, 50)}...`);
    });
    
    console.log('=' .repeat(80));
    console.log(`📈 Summary:`);
    console.log(`   Total Videos: ${videos.length}`);
    console.log(`   Videos with ZERO duration: ${zeroDurationCount}`);
    console.log(`   Videos with VALID duration: ${validDurationCount}`);
    console.log(`   Percentage with zero duration: ${((zeroDurationCount / videos.length) * 100).toFixed(1)}%`);
    
    if (zeroDurationCount > 0) {
      console.log('\n🔧 Issue Found: Videos with zero duration detected!');
      console.log('   This explains why durations are not showing in the JSON file.');
    } else {
      console.log('\n✅ All videos have valid durations in the database.');
    }
    
  } catch (error) {
    console.error('❌ Error checking video durations:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the check
checkVideoDurations();
