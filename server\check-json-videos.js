/**
 * Check current videos in JSON storage
 */

const fs = require('fs');
const path = require('path');

// JSON storage paths
const JSON_DIR = path.join(__dirname, 'json', 'videos');
const ALL_VIDEOS_FILE = path.join(JSON_DIR, 'all-videos.json');

/**
 * Check current JSON storage status
 */
function checkJsonStorage() {
  console.log('📊 LawEngaxe Video JSON Storage Status');
  console.log('=====================================');
  
  try {
    // Check if file exists
    if (!fs.existsSync(ALL_VIDEOS_FILE)) {
      console.log('❌ JSON storage file not found!');
      console.log(`   Expected location: ${ALL_VIDEOS_FILE}`);
      return;
    }
    
    // Read and parse JSON file
    const data = fs.readFileSync(ALL_VIDEOS_FILE, 'utf8');
    const jsonData = JSON.parse(data);
    
    console.log('✅ JSON storage file found and readable');
    console.log(`📁 File location: ${ALL_VIDEOS_FILE}`);
    console.log(`📊 Total videos: ${jsonData.metadata?.totalVideos || 0}`);
    console.log(`📅 Last updated: ${jsonData.metadata?.lastUpdated || 'Unknown'}`);
    console.log(`🔢 Version: ${jsonData.metadata?.version || 'Unknown'}`);
    
    if (jsonData.videos && jsonData.videos.length > 0) {
      console.log('\n📋 Videos in storage:');
      console.log('=====================');
      
      jsonData.videos.forEach((video, index) => {
        console.log(`\n${index + 1}. ${video.title || 'Untitled'}`);
        console.log(`   ID: ${video.id}`);
        console.log(`   URL: ${video.url}`);
        console.log(`   Watch URL: ${video.watchUrl}`);
        console.log(`   Category: ${video.category || 'Unknown'}`);
        console.log(`   Visibility: ${video.visibility || 'Unknown'}`);
        console.log(`   Duration: ${video.duration || 0} seconds`);
        console.log(`   Views: ${video.stats?.views || 0}`);
        console.log(`   Tags: ${video.tags?.join(', ') || 'None'}`);
        console.log(`   Keywords: ${video.searchKeywords?.slice(0, 5).join(', ') || 'None'}`);
        console.log(`   Created: ${video.createdAt || 'Unknown'}`);
      });
      
      // Show statistics
      console.log('\n📈 Statistics:');
      console.log('==============');
      
      const categories = {};
      const visibility = {};
      let totalViews = 0;
      let totalDuration = 0;
      
      jsonData.videos.forEach(video => {
        if (video.category) {
          categories[video.category] = (categories[video.category] || 0) + 1;
        }
        if (video.visibility) {
          visibility[video.visibility] = (visibility[video.visibility] || 0) + 1;
        }
        totalViews += video.stats?.views || 0;
        totalDuration += video.duration || 0;
      });
      
      console.log(`Total Views: ${totalViews}`);
      console.log(`Total Duration: ${Math.round(totalDuration / 60)} minutes`);
      console.log(`Categories: ${JSON.stringify(categories, null, 2)}`);
      console.log(`Visibility: ${JSON.stringify(visibility, null, 2)}`);
      
    } else {
      console.log('\n📭 No videos found in storage');
      console.log('   - Upload videos through the platform to see them here');
      console.log('   - Or run sync script to import from database');
    }
    
    // Check backup files
    const backupDir = path.join(JSON_DIR, 'backups');
    if (fs.existsSync(backupDir)) {
      const backupFiles = fs.readdirSync(backupDir).filter(file => file.endsWith('.json'));
      console.log(`\n💾 Backup files: ${backupFiles.length}`);
      if (backupFiles.length > 0) {
        console.log('   Latest backups:');
        backupFiles.slice(-3).forEach(file => {
          console.log(`   - ${file}`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Error reading JSON storage:', error.message);
  }
}

/**
 * Test search functionality
 */
function testSearch() {
  console.log('\n🔍 Testing Search Functionality');
  console.log('===============================');
  
  try {
    const data = fs.readFileSync(ALL_VIDEOS_FILE, 'utf8');
    const jsonData = JSON.parse(data);
    
    if (!jsonData.videos || jsonData.videos.length === 0) {
      console.log('❌ No videos to search');
      return;
    }
    
    const searchTerms = ['test', 'sample', 'video', 'demo', 'technology'];
    
    searchTerms.forEach(term => {
      const results = jsonData.videos.filter(video => {
        const titleMatch = video.title?.toLowerCase().includes(term.toLowerCase());
        const descMatch = video.description?.toLowerCase().includes(term.toLowerCase());
        const tagsMatch = video.tags?.some(tag => tag.toLowerCase().includes(term.toLowerCase()));
        const keywordsMatch = video.searchKeywords?.some(keyword => keyword.includes(term.toLowerCase()));
        const categoryMatch = video.category?.toLowerCase().includes(term.toLowerCase());
        
        return titleMatch || descMatch || tagsMatch || keywordsMatch || categoryMatch;
      });
      
      console.log(`Search "${term}": ${results.length} results`);
      if (results.length > 0) {
        results.forEach(video => {
          console.log(`   - ${video.title} (${video.watchUrl})`);
        });
      }
    });
    
  } catch (error) {
    console.error('❌ Error testing search:', error.message);
  }
}

/**
 * Show API endpoints
 */
function showApiEndpoints() {
  console.log('\n🌐 Available API Endpoints');
  console.log('==========================');
  console.log('GET /api/v1/videos/json/videos - Get all videos');
  console.log('GET /api/v1/videos/json/videos/search?q=query - Search videos');
  console.log('GET /api/v1/videos/json/videos/:id - Get video by ID');
  console.log('GET /api/v1/videos/json/stats - Get statistics');
  console.log('POST /api/v1/videos/json/sync - Sync from database (auth required)');
  console.log('GET /api/v1/videos/json/download - Download JSON file (auth required)');
  
  console.log('\n📝 Example API calls:');
  console.log('curl http://localhost:3000/api/v1/videos/json/videos');
  console.log('curl "http://localhost:3000/api/v1/videos/json/videos/search?q=test"');
  console.log('curl http://localhost:3000/api/v1/videos/json/stats');
}

/**
 * Main function
 */
function main() {
  checkJsonStorage();
  testSearch();
  showApiEndpoints();
  
  console.log('\n✨ JSON Storage System is ready!');
  console.log('   - Videos are automatically stored when uploaded');
  console.log('   - Search functionality is available');
  console.log('   - API endpoints are accessible');
}

// Run if executed directly
if (require.main === module) {
  main();
}

module.exports = { checkJsonStorage, testSearch };
