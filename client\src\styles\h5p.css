/* H5P Custom Styles */

/* Make sure H5P fullscreen button is clickable */
.h5p-fullscreen,
.h5p-control[aria-label="Fullscreen"],
div[role="button"][tabindex="0"][aria-label="Fullscreen"],
.h5p-control.h5p-fullscreen,
div[role="button"][tabindex="0"].h5p-control.h5p-fullscreen,
*[aria-label="Fullscreen"],
*[aria-label="fullscreen"],
.h5p-fullscreen-button,
.fullscreen-button {
  cursor: pointer !important;
  pointer-events: auto !important;
  z-index: 99999 !important; /* Higher z-index to ensure it's above other elements */
  position: relative !important;
  background-color: white !important;
  border: 1px solid black !important;
  color: black !important;
  border-radius: 4px !important;
  padding: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color 0.2s ease !important;
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  min-height: 32px !important;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3) !important; /* Add shadow for better visibility */
  border: 1px solid rgba(255, 255, 255, 0.2) !important; /* Add subtle border */
  opacity: 1 !important; /* Ensure it's visible */
  visibility: visible !important; /* Ensure it's visible */
}

/* Style for H5P fullscreen button when hovered - removed hover effects */

/* Style for H5P fullscreen button when active - removed active effects */

/* Add a special overlay class for the fullscreen button to make it more clickable */
.h5p-fullscreen-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1 !important;
  cursor: pointer !important;
  background-color: transparent !important;
}

/* Make sure H5P content is visible in fullscreen mode */
.h5p-content.h5p-fullscreen,
.h5p-iframe-wrapper.h5p-fullscreen,
.h5p-iframe.h5p-fullscreen,
:fullscreen .h5p-content,
:fullscreen .h5p-iframe-wrapper,
:fullscreen .h5p-iframe {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  background-color: #000 !important;
}

/* Vendor prefixes for fullscreen */
:-webkit-full-screen .h5p-content,
:-webkit-full-screen .h5p-iframe-wrapper,
:-webkit-full-screen .h5p-iframe {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  background-color: #000 !important;
}

:-moz-full-screen .h5p-content,
:-moz-full-screen .h5p-iframe-wrapper,
:-moz-full-screen .h5p-iframe {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  background-color: #000 !important;
}

:-ms-fullscreen .h5p-content,
:-ms-fullscreen .h5p-iframe-wrapper,
:-ms-fullscreen .h5p-iframe {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  background-color: #000 !important;
}
