import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Language } from '@/types';

interface AdditionalLanguagesStepProps {
  additionalLanguages: {
    id: string;
    languageCode: string;
    url: string;
  }[];
  addLanguageEntry: () => void;
  updateLanguageEntry: (id: string, field: 'languageCode' | 'url', value: string) => void;
  removeLanguageEntry: (id: string) => void;
  getAvailableLanguages: () => Language[];
  languages: Language[];
}

export default function AdditionalLanguagesStep({
  additionalLanguages,
  addLanguageEntry,
  updateLanguageEntry,
  removeLanguageEntry,
  getAvailableLanguages,
  languages
}: AdditionalLanguagesStepProps) {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Additional Language Versions</h3>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addLanguageEntry}
            disabled={getAvailableLanguages().length === 0}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Language
          </Button>
        </div>

        <p className="text-sm text-lingstream-muted">
          Add additional language versions of this video to create a multilingual experience.
          Each language version must have its own URL pointing to the video in that language.
          When viewers switch languages, the video will automatically change to the corresponding language version.
        </p>

        {additionalLanguages.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 border border-dashed border-border rounded-md">
            <p className="text-center text-lingstream-muted mb-4">
              No additional languages added yet
            </p>
            <Button
              type="button"
              variant="outline"
              onClick={addLanguageEntry}
              disabled={getAvailableLanguages().length === 0}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Language Version
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {additionalLanguages.map((entry) => (
              <div key={entry.id} className="flex items-end gap-2 p-4 border border-border rounded-md">
                <div className="w-1/3">
                  <label className="text-xs mb-1 block">Language</label>
                  <Select
                    value={entry.languageCode}
                    onValueChange={(value) => updateLanguageEntry(entry.id, 'languageCode', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableLanguages().map((lang) => (
                        <SelectItem key={lang.code} value={lang.code}>
                          <div className="flex items-center">
                            <span className="mr-2">{lang.flag}</span>
                            {lang.name}
                          </div>
                        </SelectItem>
                      ))}
                      {entry.languageCode && !getAvailableLanguages().some(l => l.code === entry.languageCode) && (
                        <SelectItem value={entry.languageCode}>
                          <div className="flex items-center">
                            <span className="mr-2">
                              {languages.find(l => l.code === entry.languageCode)?.flag || '🌐'}
                            </span>
                            {languages.find(l => l.code === entry.languageCode)?.name || entry.languageCode}
                          </div>
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex-1">
                  <label className="text-xs mb-1 block">
                    Engaxe URL for {languages.find(l => l.code === entry.languageCode)?.name || 'this language'}
                  </label>
                  <Input
                    value={entry.url}
                    onChange={(e) => updateLanguageEntry(entry.id, 'url', e.target.value)}
                    placeholder="https://engaxe.com/videos/VIDEOID"
                  />
                  <p className="text-xs text-lingstream-muted mt-1">
                    Must be an Engaxe URL for the video in {languages.find(l => l.code === entry.languageCode)?.name || 'this language'}
                  </p>
                </div>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeLanguageEntry(entry.id)}
                  className="text-red-500 hover:text-red-600 hover:bg-red-100/10"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
