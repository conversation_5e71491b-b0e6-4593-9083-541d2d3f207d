import React, { useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { Home, Upload, Save, Eye, ChevronRight, Globe, Palette, Mail, FileText, Shield, BarChart3 } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { useTheme } from '@/context/ThemeContext';

// Sample tenant data
const tenantData = {
  id: '1',
  name: 'LegalAid Corp',
  domain: 'legalaid.example.com',
  status: 'active',
  branding: {
    logo: '/logo-placeholder.png',
    favicon: '/favicon.ico',
    primaryColor: '#3B82F6',
    secondaryColor: '#1E40AF',
    fontFamily: 'Inter',
    customCss: ''
  },
  contact: {
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Legal Street, Suite 100, New York, NY 10001'
  },
  email: {
    fromName: 'LegalAid Support',
    fromEmail: '<EMAIL>',
    footerText: '© 2023 LegalAid Corp. All rights reserved.',
    enableNotifications: true
  },
  legal: {
    termsUrl: 'https://legalaid.example.com/terms',
    privacyUrl: 'https://legalaid.example.com/privacy',
    footerNotes: 'LegalAid Corp is a registered trademark.'
  },
  features: {
    enableMembership: true,
    enableEvents: true,
    enableGroups: false,
    enableBlog: true
  }
};

export default function TenantConfigurationPage() {
  const { theme } = useTheme();
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState('branding');
  const [tenant, setTenant] = useState(tenantData);
  const [previewMode, setPreviewMode] = useState(false);

  // Tabs for configuration
  const tabs = [
    { id: 'branding', label: 'Branding', icon: <Palette size={18} /> },
    { id: 'domain', label: 'Domain & SSL', icon: <Globe size={18} /> },
    { id: 'email', label: 'Email Settings', icon: <Mail size={18} /> },
    { id: 'legal', label: 'Legal & Footer', icon: <FileText size={18} /> },
    { id: 'features', label: 'Features & Access', icon: <Shield size={18} /> },
    { id: 'analytics', label: 'Analytics', icon: <BarChart3 size={18} /> }
  ];

  const handleSave = () => {
    // In a real app, this would save to the backend
    alert('Configuration saved successfully!');
  };

  const handlePreview = () => {
    setPreviewMode(!previewMode);
  };

  const handleBrandingChange = (field: string, value: string) => {
    setTenant({
      ...tenant,
      branding: {
        ...tenant.branding,
        [field]: value
      }
    });
  };

  const handleEmailChange = (field: string, value: any) => {
    setTenant({
      ...tenant,
      email: {
        ...tenant.email,
        [field]: value
      }
    });
  };

  const handleFeatureToggle = (feature: string) => {
    setTenant({
      ...tenant,
      features: {
        ...tenant.features,
        [feature]: !tenant.features[feature as keyof typeof tenant.features]
      }
    });
  };

  return (
    <div className="flex h-screen bg-[#f5f7fb] overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Tenant Configuration</h1>
              <div className="flex space-x-2">
                <button
                  onClick={handlePreview}
                  className="bg-gray-100 text-gray-700 rounded-md px-4 py-2 flex items-center hover:bg-gray-200 transition-colors"
                >
                  <Eye size={16} className="mr-2" />
                  {previewMode ? 'Exit Preview' : 'Preview'}
                </button>
                <button
                  onClick={handleSave}
                  className="bg-green-600 text-white rounded-md px-4 py-2 flex items-center hover:bg-green-700 transition-colors"
                >
                  <Save size={16} className="mr-2" />
                  Save Changes
                </button>
              </div>
            </div>
            
            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-gray-600 hover:text-gray-900 flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-gray-500">&gt;</span>
              <Link to="/admin/tenants" className="text-gray-600 hover:text-gray-900">
                Tenant Management
              </Link>
              <span className="mx-2 text-gray-500">&gt;</span>
              <span className="text-[#38bdf8]">{tenant.name} Configuration</span>
            </div>

            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="flex border-b">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    className={`px-6 py-4 flex items-center ${
                      activeTab === tab.id
                        ? 'border-b-2 border-[#38bdf8] text-[#38bdf8]'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    {tab.icon}
                    <span className="ml-2">{tab.label}</span>
                  </button>
                ))}
              </div>

              <div className="p-6">
                {activeTab === 'branding' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium">Brand Customization</h3>
                    <p className="text-gray-600">Customize the appearance of the tenant's portal.</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Brand Name
                        </label>
                        <input
                          type="text"
                          className="w-full border border-gray-300 rounded-md px-4 py-2"
                          value={tenant.name}
                          onChange={(e) => setTenant({...tenant, name: e.target.value})}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Logo
                        </label>
                        <div className="flex items-center">
                          <div className="w-16 h-16 bg-gray-100 rounded-md flex items-center justify-center mr-4 overflow-hidden">
                            {tenant.branding.logo ? (
                              <img src={tenant.branding.logo} alt="Logo" className="max-w-full max-h-full" />
                            ) : (
                              <span className="text-gray-400">No logo</span>
                            )}
                          </div>
                          <button className="bg-gray-100 text-gray-700 rounded-md px-4 py-2 flex items-center hover:bg-gray-200 transition-colors">
                            <Upload size={16} className="mr-2" />
                            Upload Logo
                          </button>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Primary Color
                        </label>
                        <div className="flex items-center">
                          <input
                            type="color"
                            className="w-10 h-10 border-0 p-0 mr-2"
                            value={tenant.branding.primaryColor}
                            onChange={(e) => handleBrandingChange('primaryColor', e.target.value)}
                          />
                          <input
                            type="text"
                            className="flex-1 border border-gray-300 rounded-md px-4 py-2"
                            value={tenant.branding.primaryColor}
                            onChange={(e) => handleBrandingChange('primaryColor', e.target.value)}
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Secondary Color
                        </label>
                        <div className="flex items-center">
                          <input
                            type="color"
                            className="w-10 h-10 border-0 p-0 mr-2"
                            value={tenant.branding.secondaryColor}
                            onChange={(e) => handleBrandingChange('secondaryColor', e.target.value)}
                          />
                          <input
                            type="text"
                            className="flex-1 border border-gray-300 rounded-md px-4 py-2"
                            value={tenant.branding.secondaryColor}
                            onChange={(e) => handleBrandingChange('secondaryColor', e.target.value)}
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Font Family
                        </label>
                        <select
                          className="w-full border border-gray-300 rounded-md px-4 py-2"
                          value={tenant.branding.fontFamily}
                          onChange={(e) => handleBrandingChange('fontFamily', e.target.value)}
                        >
                          <option value="Inter">Inter</option>
                          <option value="Roboto">Roboto</option>
                          <option value="Open Sans">Open Sans</option>
                          <option value="Lato">Lato</option>
                          <option value="Poppins">Poppins</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Favicon
                        </label>
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gray-100 rounded-md flex items-center justify-center mr-4 overflow-hidden">
                            {tenant.branding.favicon ? (
                              <img src={tenant.branding.favicon} alt="Favicon" className="max-w-full max-h-full" />
                            ) : (
                              <span className="text-gray-400">No icon</span>
                            )}
                          </div>
                          <button className="bg-gray-100 text-gray-700 rounded-md px-4 py-2 flex items-center hover:bg-gray-200 transition-colors">
                            <Upload size={16} className="mr-2" />
                            Upload Favicon
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Custom CSS (Advanced)
                      </label>
                      <textarea
                        className="w-full border border-gray-300 rounded-md px-4 py-2 font-mono text-sm h-32"
                        value={tenant.branding.customCss}
                        onChange={(e) => handleBrandingChange('customCss', e.target.value)}
                        placeholder="/* Add custom CSS here */"
                      />
                    </div>
                  </div>
                )}

                {activeTab === 'domain' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium">Domain & SSL Configuration</h3>
                    <p className="text-gray-600">Configure custom domain and SSL settings for this tenant.</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Custom Domain
                        </label>
                        <input
                          type="text"
                          className="w-full border border-gray-300 rounded-md px-4 py-2"
                          value={tenant.domain}
                          onChange={(e) => setTenant({...tenant, domain: e.target.value})}
                          placeholder="example.com"
                        />
                        <p className="mt-1 text-sm text-gray-500">
                          Enter the domain without http:// or https://
                        </p>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          SSL Certificate
                        </label>
                        <div className="flex items-center">
                          <button className="bg-gray-100 text-gray-700 rounded-md px-4 py-2 flex items-center hover:bg-gray-200 transition-colors">
                            <Upload size={16} className="mr-2" />
                            Upload Certificate
                          </button>
                        </div>
                        <p className="mt-1 text-sm text-gray-500">
                          Upload a valid SSL certificate for your domain
                        </p>
                      </div>
                    </div>
                    
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                      <h4 className="text-sm font-medium text-blue-800 mb-2">DNS Configuration</h4>
                      <p className="text-sm text-blue-700 mb-2">
                        To connect your custom domain, add the following DNS records:
                      </p>
                      <div className="bg-white rounded-md p-3 font-mono text-xs">
                        <div className="grid grid-cols-3 gap-4 mb-2">
                          <div className="font-medium">Type</div>
                          <div className="font-medium">Name</div>
                          <div className="font-medium">Value</div>
                        </div>
                        <div className="grid grid-cols-3 gap-4">
                          <div>CNAME</div>
                          <div>@</div>
                          <div>tenant-{id}.example.com</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'email' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium">Email Configuration</h3>
                    <p className="text-gray-600">Configure email settings for this tenant.</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          From Name
                        </label>
                        <input
                          type="text"
                          className="w-full border border-gray-300 rounded-md px-4 py-2"
                          value={tenant.email.fromName}
                          onChange={(e) => handleEmailChange('fromName', e.target.value)}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          From Email
                        </label>
                        <input
                          type="email"
                          className="w-full border border-gray-300 rounded-md px-4 py-2"
                          value={tenant.email.fromEmail}
                          onChange={(e) => handleEmailChange('fromEmail', e.target.value)}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email Footer Text
                      </label>
                      <textarea
                        className="w-full border border-gray-300 rounded-md px-4 py-2 h-24"
                        value={tenant.email.footerText}
                        onChange={(e) => handleEmailChange('footerText', e.target.value)}
                      />
                    </div>
                    
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enableNotifications"
                        className="h-4 w-4 text-blue-600 rounded"
                        checked={tenant.email.enableNotifications}
                        onChange={() => handleEmailChange('enableNotifications', !tenant.email.enableNotifications)}
                      />
                      <label htmlFor="enableNotifications" className="ml-2 text-sm text-gray-700">
                        Enable automated email notifications to users
                      </label>
                    </div>
                  </div>
                )}

                {activeTab === 'legal' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium">Legal & Footer Configuration</h3>
                    <p className="text-gray-600">Configure legal documents and footer information.</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Terms of Service URL
                        </label>
                        <input
                          type="url"
                          className="w-full border border-gray-300 rounded-md px-4 py-2"
                          value={tenant.legal.termsUrl}
                          onChange={(e) => setTenant({
                            ...tenant,
                            legal: {
                              ...tenant.legal,
                              termsUrl: e.target.value
                            }
                          })}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Privacy Policy URL
                        </label>
                        <input
                          type="url"
                          className="w-full border border-gray-300 rounded-md px-4 py-2"
                          value={tenant.legal.privacyUrl}
                          onChange={(e) => setTenant({
                            ...tenant,
                            legal: {
                              ...tenant.legal,
                              privacyUrl: e.target.value
                            }
                          })}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Footer Notes
                      </label>
                      <textarea
                        className="w-full border border-gray-300 rounded-md px-4 py-2 h-24"
                        value={tenant.legal.footerNotes}
                        onChange={(e) => setTenant({
                          ...tenant,
                          legal: {
                            ...tenant.legal,
                            footerNotes: e.target.value
                          }
                        })}
                        placeholder="Copyright information, disclaimers, etc."
                      />
                    </div>
                  </div>
                )}

                {activeTab === 'features' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium">Features & Access Control</h3>
                    <p className="text-gray-600">Enable or disable features for this tenant.</p>
                    
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-md">
                        <div>
                          <h4 className="font-medium">Membership Management</h4>
                          <p className="text-sm text-gray-600">Enable user registration and membership features</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input 
                            type="checkbox" 
                            className="sr-only peer"
                            checked={tenant.features.enableMembership}
                            onChange={() => handleFeatureToggle('enableMembership')}
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                      
                      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-md">
                        <div>
                          <h4 className="font-medium">Events Management</h4>
                          <p className="text-sm text-gray-600">Enable event creation and registration</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input 
                            type="checkbox" 
                            className="sr-only peer"
                            checked={tenant.features.enableEvents}
                            onChange={() => handleFeatureToggle('enableEvents')}
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                      
                      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-md">
                        <div>
                          <h4 className="font-medium">Groups & Communities</h4>
                          <p className="text-sm text-gray-600">Enable user groups and community features</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input 
                            type="checkbox" 
                            className="sr-only peer"
                            checked={tenant.features.enableGroups}
                            onChange={() => handleFeatureToggle('enableGroups')}
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                      
                      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-md">
                        <div>
                          <h4 className="font-medium">Blog & Content</h4>
                          <p className="text-sm text-gray-600">Enable blog and content management</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input 
                            type="checkbox" 
                            className="sr-only peer"
                            checked={tenant.features.enableBlog}
                            onChange={() => handleFeatureToggle('enableBlog')}
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'analytics' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium">Analytics & Reporting</h3>
                    <p className="text-gray-600">View analytics and reports for this tenant.</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                        <h4 className="text-sm font-medium text-gray-500 mb-1">Total Users</h4>
                        <p className="text-3xl font-bold">{tenant.users}</p>
                        <div className="mt-2 text-sm text-green-600 flex items-center">
                          <span className="mr-1">+12%</span>
                          <ChevronRight size={14} className="rotate-90" />
                          <span className="ml-1 text-gray-500">vs last month</span>
                        </div>
                      </div>
                      
                      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                        <h4 className="text-sm font-medium text-gray-500 mb-1">Active Subscriptions</h4>
                        <p className="text-3xl font-bold">87</p>
                        <div className="mt-2 text-sm text-green-600 flex items-center">
                          <span className="mr-1">+5%</span>
                          <ChevronRight size={14} className="rotate-90" />
                          <span className="ml-1 text-gray-500">vs last month</span>
                        </div>
                      </div>
                      
                      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                        <h4 className="text-sm font-medium text-gray-500 mb-1">Monthly Revenue</h4>
                        <p className="text-3xl font-bold">$12,450</p>
                        <div className="mt-2 text-sm text-green-600 flex items-center">
                          <span className="mr-1">+8%</span>
                          <ChevronRight size={14} className="rotate-90" />
                          <span className="ml-1 text-gray-500">vs last month</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white p-6 rounded-lg border border-gray-200">
                      <h4 className="font-medium mb-4">User Growth</h4>
                      <div className="h-64 flex items-end space-x-2">
                        {[35, 42, 58, 63, 72, 90, 105, 120, 143, 158, 192, 245].map((value, index) => (
                          <div key={index} className="flex-1 flex flex-col items-center">
                            <div
                              className="w-full bg-blue-500 rounded-t"
                              style={{ height: `${(value / 245) * 100}%` }}
                            ></div>
                            <div className="text-xs text-gray-500 mt-1">
                              {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][index]}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <button className="bg-gray-100 text-gray-700 rounded-md px-4 py-2 flex items-center hover:bg-gray-200 transition-colors">
                        <BarChart3 size={16} className="mr-2" />
                        View Detailed Reports
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
      
      {previewMode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-4/5 h-4/5 flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="font-medium">Preview: {tenant.name}</h3>
              <button
                onClick={() => setPreviewMode(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                Close
              </button>
            </div>
            <div className="flex-1 overflow-auto">
              <iframe
                title="Preview"
                className="w-full h-full"
                src={`https://${tenant.domain}`}
                sandbox="allow-same-origin allow-scripts"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
