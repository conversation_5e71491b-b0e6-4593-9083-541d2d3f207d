const axios = require('axios');

async function testVideoAPI() {
  const baseURL = 'http://localhost:3001'; // Adjust port if needed
  
  console.log('🔍 Testing video search API endpoint...');
  
  try {
    // Test 1: Search for logarithm
    console.log('\n📡 Testing search for "logarithm"...');
    const response1 = await axios.get(`${baseURL}/api/videos?search=logarithm&limit=5`);
    console.log(`✅ Status: ${response1.status}`);
    console.log(`📊 Response data:`, JSON.stringify(response1.data, null, 2));
    
    // Test 2: Search for mathematics
    console.log('\n📡 Testing search for "mathematics"...');
    const response2 = await axios.get(`${baseURL}/api/videos?search=mathematics&limit=5`);
    console.log(`✅ Status: ${response2.status}`);
    console.log(`📊 Found ${response2.data.data?.videos?.length || 0} videos`);
    
    // Test 3: Get all videos
    console.log('\n📡 Testing get all videos...');
    const response3 = await axios.get(`${baseURL}/api/videos?limit=10`);
    console.log(`✅ Status: ${response3.status}`);
    console.log(`📊 Total videos: ${response3.data.data?.videos?.length || 0}`);
    
    if (response3.data.data?.videos?.length > 0) {
      console.log('📋 Sample video titles:');
      response3.data.data.videos.slice(0, 3).forEach((video, index) => {
        console.log(`  ${index + 1}. ${video.title}`);
      });
    }
    
  } catch (error) {
    if (error.response) {
      console.error(`❌ API Error: ${error.response.status} - ${error.response.statusText}`);
      console.error(`📄 Response data:`, error.response.data);
    } else if (error.request) {
      console.error('❌ Network Error: No response received');
      console.error('🔗 Make sure the server is running on http://localhost:3001');
    } else {
      console.error('❌ Error:', error.message);
    }
  }
}

testVideoAPI();
