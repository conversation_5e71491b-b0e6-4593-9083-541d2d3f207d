/**
 * Test script for Bhashini API integration
 *
 * This script tests the Bhashini API integration by:
 * 1. Authenticating with the Bhashini API
 * 2. Executing a simple translation task
 * 3. Executing a language detection task
 *
 * Usage:
 * ts-node src/scripts/test-bhashini-api.ts
 */

import { bhashiniApiService } from '../services/bhashini-api.service';

// Bhashini API credentials
const BHASHINI_USER_ID = 'cee60134c6bb4d179efd3fda48ff32fe'; // User ID provided by the user
const BHASHINI_ULCA_API_KEY = '13a647c84b-2747-4f0c-afcd-2ac8235f5318'; // Udyat Key provided by the user

// Test text
const TEST_TEXT = 'Hello, how are you today?';
const TEST_SOURCE_LANGUAGE = 'en';
const TEST_TARGET_LANGUAGE = 'hi';

/**
 * Test authentication
 */
async function testAuthentication() {
  console.log('Testing Bhashini API authentication...');

  try {
    const authToken = await bhashiniApiService.getAuthToken(BHASHINI_USER_ID, BHASHINI_ULCA_API_KEY);
    console.log('Authentication successful!');
    console.log('Auth token:', authToken.substring(0, 20) + '...');
    return authToken;
  } catch (error) {
    console.error('Authentication failed:', error);
    throw error;
  }
}

/**
 * Test translation
 */
async function testTranslation() {
  console.log(`\nTesting Bhashini API translation...`);
  console.log(`Translating "${TEST_TEXT}" from ${TEST_SOURCE_LANGUAGE} to ${TEST_TARGET_LANGUAGE}`);

  try {
    const translatedText = await bhashiniApiService.translateText(
      TEST_TEXT,
      TEST_SOURCE_LANGUAGE,
      TEST_TARGET_LANGUAGE,
      BHASHINI_USER_ID,
      BHASHINI_ULCA_API_KEY
    );

    console.log('Translation successful!');
    console.log('Translated text:', translatedText);
    return translatedText;
  } catch (error) {
    console.error('Translation failed:', error);
    throw error;
  }
}

/**
 * Test language detection
 */
async function testLanguageDetection() {
  console.log(`\nTesting language detection...`);
  console.log(`Detecting language of "${TEST_TEXT}"`);

  try {
    // Simple language detection function
    const detectLanguageSimple = (text: string): { detectedLanguage: string; confidence: number } => {
      // Define character ranges for different languages
      const langPatterns = {
        hi: /[\u0900-\u097F]/g,  // Hindi
        mr: /[\u0900-\u097F]/g,  // Marathi (shares Devanagari script with Hindi)
        gu: /[\u0A80-\u0AFF]/g,  // Gujarati
        ta: /[\u0B80-\u0BFF]/g,  // Tamil
        te: /[\u0C00-\u0C7F]/g,  // Telugu
        bn: /[\u0980-\u09FF]/g,  // Bengali
        kn: /[\u0C80-\u0CFF]/g,  // Kannada
        ml: /[\u0D00-\u0D7F]/g,  // Malayalam
        pa: /[\u0A00-\u0A7F]/g,  // Punjabi
        or: /[\u0B00-\u0B7F]/g,  // Odia
        en: /[a-zA-Z]/g,         // English
      };

      // Count characters for each language
      const counts: Record<string, number> = {};
      for (const [lang, pattern] of Object.entries(langPatterns)) {
        const matches = text.match(pattern);
        counts[lang] = matches ? matches.length : 0;
      }

      // Find the language with the most matches
      let maxLang = 'en';
      let maxCount = 0;
      for (const [lang, count] of Object.entries(counts)) {
        if (count > maxCount) {
          maxLang = lang;
          maxCount = count;
        }
      }

      // Calculate confidence (simple ratio of matched chars to total)
      const totalChars = text.length;
      const confidence = totalChars > 0 ? maxCount / totalChars : 0;

      return {
        detectedLanguage: maxLang,
        confidence: Math.min(confidence * 1.5, 1.0) // Adjust confidence and cap at 1.0
      };
    };

    // Detect language
    const { detectedLanguage, confidence } = detectLanguageSimple(TEST_TEXT);

    console.log('Language detection successful!');
    console.log('Detected language:', detectedLanguage);
    console.log('Confidence:', confidence);

    return { detectedLanguage, confidence };
  } catch (error) {
    console.error('Language detection failed:', error);
    throw error;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('=== BHASHINI API INTEGRATION TEST ===\n');

  try {
    // Test authentication
    await testAuthentication();

    // Test translation
    await testTranslation();

    // Test language detection
    await testLanguageDetection();

    console.log('\n=== ALL TESTS PASSED ===');
  } catch (error) {
    console.error('\n=== TEST FAILED ===');
    process.exit(1);
  }
}

// Run the tests
runTests();
