import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Home, ChevronUp, ChevronDown, Trash2, Search, ChevronRight, Filter } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface CommentData {
  id: number;
  text: string;
  video: string;
  article: string;
  postedOn: string;
}

export default function ManageCommentsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<keyof CommentData>('id');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedComments, setSelectedComments] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const comments: CommentData[] = [
    {
      id: 7,
      text: 'vary nic vdo mam',
      video: 'LeU5VBb2URXimJP',
      article: '',
      postedOn: 'January-08-2025'
    },
    {
      id: 2,
      text: 'VIDEO HAS BEEN QUEUED',
      video: 'AOZVIUVyiALkR4H',
      article: '',
      postedOn: 'July-18-2024'
    }
  ];

  const handleSort = (column: keyof CommentData) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const SortIcon = ({ column }: { column: keyof CommentData }) => {
    if (sortColumn !== column) return null;
    return sortDirection === 'asc' ?
      <ChevronUp size={14} className="ml-1" /> :
      <ChevronDown size={14} className="ml-1" />;
  };

  const handleSelectAll = (checked: boolean) => {
    if (!checked) {
      setSelectedComments([]);
    } else {
      setSelectedComments(comments.map(comment => comment.id));
    }
    setSelectAll(!!checked);
  };

  const handleSelectComment = (id: number) => {
    const isSelected = selectedComments.includes(id);
    let newSelected: number[];

    if (isSelected) {
      newSelected = selectedComments.filter(commentId => commentId !== id);
    } else {
      newSelected = [...selectedComments, id];
    }

    setSelectedComments(newSelected);
    setSelectAll(newSelected.length === comments.length);
  };

  const handleDeleteSelected = () => {
    // In a real app, this would call an API to delete the selected comments
    console.log('Deleting comments:', selectedComments);
    // Then reset selection
    setSelectedComments([]);
    setSelectAll(false);
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Manage Comments</h1>
              <Button variant="outline" size="sm" className="w-32">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="/admin/videos" className="text-muted-foreground hover:text-foreground">
                Videos
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Manage Comments</span>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Manage Video Comments</CardTitle>
                <CardDescription>View and moderate comments on your videos</CardDescription>
              </CardHeader>
              <CardContent>

                <div className="mb-6 space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="text-sm font-medium">Filter Comments</div>
                    <Select defaultValue="all">
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Comments</SelectItem>
                        <SelectItem value="video">Video Comments</SelectItem>
                        <SelectItem value="article">Article Comments</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by keyword or comment text..."
                      className="pl-10 pr-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button
                      className="absolute right-0 top-0 h-full rounded-l-none"
                      size="sm"
                    >
                      Search
                    </Button>
                  </div>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-10">
                          <Checkbox
                            checked={selectAll}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('id')}>
                          <div className="flex items-center">
                            ID <SortIcon column="id" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('text')}>
                          <div className="flex items-center">
                            COMMENT <SortIcon column="text" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('video')}>
                          <div className="flex items-center">
                            VIDEO <SortIcon column="video" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('article')}>
                          <div className="flex items-center">
                            ARTICLE <SortIcon column="article" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('postedOn')}>
                          <div className="flex items-center">
                            POSTED ON <SortIcon column="postedOn" />
                          </div>
                        </TableHead>
                        <TableHead>ACTION</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {comments.map((comment) => (
                        <TableRow key={comment.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedComments.includes(comment.id)}
                              onCheckedChange={() => handleSelectComment(comment.id)}
                            />
                          </TableCell>
                          <TableCell>{comment.id}</TableCell>
                          <TableCell className="max-w-xs truncate">{comment.text}</TableCell>
                          <TableCell>{comment.video}</TableCell>
                          <TableCell>{comment.article || '—'}</TableCell>
                          <TableCell>{comment.postedOn}</TableCell>
                          <TableCell>
                            <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700 hover:bg-red-50">
                              <Trash2 className="h-4 w-4 mr-1" />
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="mt-6 flex justify-between items-center">
                  <div>
                    <p className="text-sm text-muted-foreground">Showing <span className="font-medium">1</span> to <span className="font-medium">2</span> of <span className="font-medium">2</span> comments</p>
                  </div>
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious href="#" />
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationLink href="#" isActive>1</PaginationLink>
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationNext href="#" />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>

                <div className="mt-6">
                  <Button
                    variant="destructive"
                    onClick={handleDeleteSelected}
                    disabled={selectedComments.length === 0}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Selected ({selectedComments.length})
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
