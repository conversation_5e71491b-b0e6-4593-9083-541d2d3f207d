import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Mi<PERSON>, MicOff, Loader2, Volume2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useChatbot } from '@/context/ChatbotContext';
import { useLanguage } from '@/context/LanguageContext';
import * as speechSynthesis from '@/utils/speechSynthesis';

interface VoiceAssistantButtonProps {
  className?: string;
  size?: 'default' | 'sm' | 'lg' | 'icon';
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
}

export default function VoiceAssistantButton({
  className,
  size = 'icon',
  variant = 'outline'
}: VoiceAssistantButtonProps) {
  const { toast } = useToast();
  const { aiProviders } = useChatbot();
  const { currentLanguage } = useLanguage();

  // States
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState('');

  // References
  const recognition = useRef<SpeechRecognition | null>(null);

  // Get OpenRouter configuration
  const openRouterConfig = aiProviders.openrouter;
  const isOpenRouterEnabled = openRouterConfig.enabled && openRouterConfig.apiKey;

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check if SpeechRecognition is available
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

      if (SpeechRecognition) {
        try {
          recognition.current = new SpeechRecognition();
          recognition.current.continuous = false;
          recognition.current.interimResults = false;

          // Set language based on current language selection
          const langCode = currentLanguage.code === 'hi' ? 'hi-IN' : 'en-US';
          recognition.current.lang = langCode;
          console.log(`Setting speech recognition language to: ${langCode}`);

          // Set up recognition event handlers
          recognition.current.onresult = handleSpeechResult;
          recognition.current.onerror = handleSpeechError;
          recognition.current.onend = () => {
            console.log('Speech recognition ended');
            setIsListening(false);
          };
          recognition.current.onstart = () => {
            console.log('Speech recognition started');
            setIsListening(true);
          };
        } catch (error) {
          console.error('Error initializing speech recognition:', error);
          toast({
            title: "Speech Recognition Error",
            description: "Could not initialize speech recognition. Please try using Chrome or Edge browser.",
            variant: "destructive"
          });
        }
      } else {
        console.error('SpeechRecognition not available in this browser');
        toast({
          title: "Speech Recognition Unavailable",
          description: "Your browser doesn't support speech recognition. Please try using Chrome or Edge.",
          variant: "destructive"
        });
      }
    }

    // Initialize speech synthesis
    speechSynthesis.initialize().catch(error => {
      console.error('Error initializing speech synthesis:', error);
    });

    // Cleanup
    return () => {
      if (recognition.current) {
        try {
          recognition.current.abort();
        } catch (error) {
          console.error('Error cleaning up speech recognition:', error);
        }
      }
      speechSynthesis.stop();
    };
  }, [currentLanguage.code]);

  // Handle speech recognition result
  const handleSpeechResult = (event: SpeechRecognitionEvent) => {
    try {
      console.log('Processing speech result:', event);

      if (event.results && event.results.length > 0 && event.results[0].length > 0) {
        const text = event.results[0][0].transcript;
        console.log('Transcript:', text);

        if (text && text.trim() !== '') {
          setTranscript(text);

          // Process the user's message and get response
          processUserMessage(text);
        } else {
          console.log('Empty transcript received');
          toast({
            title: "No Speech Detected",
            description: "We couldn't detect any speech. Please try again.",
            variant: "destructive"
          });
        }
      } else {
        console.error('Invalid speech recognition results structure:', event.results);
      }
    } catch (error) {
      console.error('Error handling speech result:', error);
      toast({
        title: "Speech Processing Error",
        description: "Could not process your speech. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle speech recognition error
  const handleSpeechError = (event: SpeechRecognitionErrorEvent) => {
    console.error('Speech recognition error:', event.error, event.message);
    setIsListening(false);

    toast({
      title: "Speech Recognition Error",
      description: `Error: ${event.error}. Please try again.`,
      variant: "destructive"
    });
  };

  // Toggle speech recognition
  const toggleListening = () => {
    // If AI is currently speaking, stop the speech
    if (isSpeaking) {
      speechSynthesis.stop();
      setIsSpeaking(false);
      return;
    }

    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // Start speech recognition
  const startListening = () => {
    if (!recognition.current) {
      toast({
        title: "Speech Recognition Unavailable",
        description: "Your browser doesn't support speech recognition. Try Chrome or Edge.",
        variant: "destructive"
      });
      return;
    }

    if (!isOpenRouterEnabled) {
      toast({
        title: "API Not Configured",
        description: "Please enable OpenRouter or OpenAI and add your API key in settings.",
        variant: "destructive"
      });
      return;
    }

    if (isSpeaking) {
      speechSynthesis.stop();
      setIsSpeaking(false);
    }

    try {
      recognition.current.start();
      setIsListening(true);

      toast({
        title: "Listening",
        description: "Speak now...",
      });
    } catch (error) {
      console.error('Error starting speech recognition:', error);
      toast({
        title: "Speech Recognition Error",
        description: "Could not start listening. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Stop speech recognition
  const stopListening = () => {
    if (recognition.current) {
      try {
        recognition.current.stop();
      } catch (error) {
        console.error('Error stopping speech recognition:', error);
      }
    }
    setIsListening(false);
  };

  // Process user message and get response from OpenRouter
  const processUserMessage = async (text: string) => {
    if (!isOpenRouterEnabled) {
      toast({
        title: "API Not Configured",
        description: "Please enable OpenRouter or OpenAI and add your API key in settings.",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);

    try {
      // toast({
      //   title: "Processing",
      //   description: `"${text}" - Sending to AI...`,
      // });

      console.log('Sending request to OpenRouter with API key:', openRouterConfig.apiKey ? `${openRouterConfig.apiKey.substring(0, 10)}...` : 'none');
      console.log('Using model:', openRouterConfig.model || 'anthropic/claude-instant-1');

      // Validate API key format
      if (!openRouterConfig.apiKey.startsWith('sk-or-') && !openRouterConfig.apiKey.startsWith('sk-proj-')) {
        throw new Error('Invalid API key format. API keys should start with "sk-or-" or "sk-proj-".');
      }

      // Determine the API endpoint and model based on the API key format
      const isOpenRouterKey = openRouterConfig.apiKey.startsWith('sk-or-');
      const apiEndpoint = isOpenRouterKey
        ? 'https://openrouter.ai/api/v1/chat/completions'
        : 'https://api.openai.com/v1/chat/completions';

      const apiModel = isOpenRouterKey
        ? (openRouterConfig.model || 'anthropic/claude-instant-1') // OpenRouter model
        : (openRouterConfig.model || 'gpt-3.5-turbo'); // OpenAI model

      console.log(`Using API key (masked): ${openRouterConfig.apiKey.substring(0, 10)}...`);
      console.log(`Using API endpoint: ${apiEndpoint}`);
      console.log(`Using model: ${apiModel}`);

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${openRouterConfig.apiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'LawEngaxe Voice Assistant'
        },
        body: JSON.stringify({
          model: apiModel,
          messages: [
            { role: 'user', content: text }
          ]
        })
      });

      if (!response.ok) {
        let errorMessage = `Failed to get response from API (${apiEndpoint})`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error?.message || errorMessage;
          console.error('API error:', errorData);
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError);
        }

        // Provide a fallback response even when the API fails
        const fallbackResponse = "I'm sorry, I'm having trouble connecting to my AI service right now. Please try again in a moment.";
        speakText(fallbackResponse);

        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('API response:', data);

      // Extract the response text
      const responseText = data.choices?.[0]?.message?.content || 'Sorry, I could not generate a response.';

      // Speak the response
      speakText(responseText);
    } catch (error) {
      console.error('Error processing message:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to process your message",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Speak text using speech synthesis
  const speakText = (text: string) => {
    try {
      console.log('Speaking text:', text);

      // toast({
      //   title: "AI Response",
      //   description: text.length > 100 ? text.substring(0, 100) + '...' : text,
      // });

      // Set speaking state
      setIsSpeaking(true);

      // Get language code for speech synthesis
      const langCode = currentLanguage.code === 'hi' ? 'hi-IN' : 'en-US';
      console.log(`Using speech synthesis language: ${langCode}`);

      // Check if speech synthesis is supported
      if (!speechSynthesis.isSupported()) {
        console.error('Speech synthesis is not supported in this browser');
        toast({
          title: "Speech Synthesis Unavailable",
          description: "Your browser doesn't support text-to-speech. Please try Chrome or Edge.",
          variant: "destructive"
        });
        setIsSpeaking(false);
        return;
      }

      // Make sure any previous speech is stopped
      speechSynthesis.stop();

      // Use our speech synthesis utility
      speechSynthesis.speak(text, {
        lang: langCode,
        rate: 1.0,
        pitch: 1.0,
        onStart: () => {
          console.log('Speech started');
          setIsSpeaking(true);
        },
        onEnd: () => {
          console.log('Speech ended');
          setIsSpeaking(false);
        },
        onError: (error) => {
          console.error('Speech synthesis error:', error);
          setIsSpeaking(false);

          // Display the error message to the user
          toast({
            title: "Speech Synthesis Error",
            description: "Could not speak the response. Please check your audio settings.",
            variant: "destructive"
          });

          // Try to use a fallback method if available
          try {
            // Create a simple utterance directly
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = langCode;
            window.speechSynthesis.speak(utterance);
          } catch (fallbackError) {
            console.error('Fallback speech synthesis also failed:', fallbackError);
          }
        }
      });
    } catch (error) {
      console.error('Error with speech synthesis:', error);
      setIsSpeaking(false);
      toast({
        title: "Speech Synthesis Error",
        description: "Could not speak the response. Please check your audio settings.",
        variant: "destructive"
      });
    }
  };

  return (
    <Button
      className={className}
      size={size}
      variant={isListening ? "destructive" : variant}
      onClick={toggleListening}
      disabled={isProcessing}
      title={
        isSpeaking
          ? "Click to stop AI speech"
          : isListening
            ? "Click to stop voice input"
            : "Click to start voice input"
      }
    >
      {isListening ? (
        <MicOff className="h-4 w-4" />
      ) : isProcessing ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : isSpeaking ? (
        <Volume2 className="h-4 w-4" />
      ) : (
        <Mic className="h-4 w-4" />
      )}
    </Button>
  );
}
