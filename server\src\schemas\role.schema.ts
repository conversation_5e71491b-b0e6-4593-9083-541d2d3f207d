import { FastifySchema } from 'fastify';
import { Type } from '@sinclair/typebox';

/**
 * Create role schema
 */
export const createRoleSchema: FastifySchema = {
  body: Type.Object({
    name: Type.String(),
    description: Type.String(),
    code: Type.String({ pattern: '^[a-z0-9_.-]+$' }),
    permissions: Type.Array(Type.String()),
    isSystem: Type.Optional(Type.Boolean()),
    isActive: Type.Optional(Type.Boolean()),
    priority: Type.Optional(Type.Number()),
    parentId: Type.Optional(Type.String()),
    maxUsers: Type.Optional(Type.Number()),
    isAssignable: Type.Optional(Type.Boolean()),
    autoAssignConditions: Type.Optional(Type.String()),
  }),
  response: {
    201: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      role: Type.Object({
        id: Type.String(),
        name: Type.String(),
        description: Type.String(),
        code: Type.String(),
        permissions: Type.Array(Type.String()),
        isSystem: Type.Boolean(),
        isActive: Type.Boolean(),
        priority: Type.Number(),
        createdAt: Type.String({ format: 'date-time' }),
      }),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      error: Type.Optional(Type.String()),
    }),
  },
};

/**
 * Get all roles schema
 */
export const getAllRolesSchema: FastifySchema = {
  querystring: Type.Object({
    page: Type.Optional(Type.Number({ minimum: 1, default: 1 })),
    limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100, default: 10 })),
    search: Type.Optional(Type.String()),
    isActive: Type.Optional(Type.Boolean()),
    isSystem: Type.Optional(Type.Boolean()),
    sortBy: Type.Optional(Type.String()),
    sortOrder: Type.Optional(Type.Enum({ asc: 'asc', desc: 'desc' })),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      roles: Type.Array(
        Type.Object({
          id: Type.String(),
          name: Type.String(),
          description: Type.String(),
          code: Type.String(),
          permissions: Type.Array(Type.String()),
          isSystem: Type.Boolean(),
          isActive: Type.Boolean(),
          priority: Type.Number(),
          createdAt: Type.String({ format: 'date-time' }),
        })
      ),
      pagination: Type.Object({
        total: Type.Number(),
        page: Type.Number(),
        limit: Type.Number(),
        pages: Type.Number(),
      }),
    }),
  },
};

/**
 * Get role by ID schema
 */
export const getRoleByIdSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      role: Type.Object({
        id: Type.String(),
        name: Type.String(),
        description: Type.String(),
        code: Type.String(),
        permissions: Type.Array(Type.String()),
        isSystem: Type.Boolean(),
        isActive: Type.Boolean(),
        priority: Type.Number(),
        parentId: Type.Optional(Type.String()),
        maxUsers: Type.Optional(Type.Number()),
        isAssignable: Type.Boolean(),
        autoAssignConditions: Type.Optional(Type.String()),
        createdAt: Type.String({ format: 'date-time' }),
        updatedAt: Type.String({ format: 'date-time' }),
      }),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Update role schema
 */
export const updateRoleSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  body: Type.Object({
    name: Type.Optional(Type.String()),
    description: Type.Optional(Type.String()),
    permissions: Type.Optional(Type.Array(Type.String())),
    isActive: Type.Optional(Type.Boolean()),
    priority: Type.Optional(Type.Number()),
    parentId: Type.Optional(Type.String()),
    maxUsers: Type.Optional(Type.Number()),
    isAssignable: Type.Optional(Type.Boolean()),
    autoAssignConditions: Type.Optional(Type.String()),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      role: Type.Object({
        id: Type.String(),
        name: Type.String(),
        description: Type.String(),
        code: Type.String(),
        permissions: Type.Array(Type.String()),
        isActive: Type.Boolean(),
        priority: Type.Number(),
        updatedAt: Type.String({ format: 'date-time' }),
      }),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Delete role schema
 */
export const deleteRoleSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Assign role to user schema
 */
export const assignRoleToUserSchema: FastifySchema = {
  params: Type.Object({
    userId: Type.String(),
  }),
  body: Type.Object({
    roleIds: Type.Array(Type.String()),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      user: Type.Object({
        id: Type.String(),
        username: Type.String(),
        roles: Type.Array(Type.String()),
      }),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};
