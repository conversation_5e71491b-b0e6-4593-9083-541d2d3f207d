<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adding Engaxe Videos by ID - Guide</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2, h3 {
            color: #333;
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #4a90e2;
        }
        .step h3 {
            margin-top: 0;
            color: #4a90e2;
        }
        .note {
            background-color: #fffde7;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffd600;
            margin: 20px 0;
        }
        code {
            background-color: #eee;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        img {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        .button {
            display: inline-block;
            background-color: #4a90e2;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            text-decoration: none;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Adding Engaxe Videos by ID - Guide</h1>
    
    <div class="note">
        <strong>Important:</strong> This guide shows you how to add Engaxe videos to your platform using just the video ID.
    </div>
    
    <div class="step">
        <h3>Step 1: Find the Engaxe Video ID</h3>
        <p>The Engaxe video ID is a unique alphanumeric code that identifies a video. You can find it:</p>
        <ul>
            <li>In the URL of the video (e.g., <code>https://engaxe.com/v/<strong>XLcMq2</strong></code>)</li>
            <li>From the embed code provided by Engaxe</li>
            <li>From the video sharing options</li>
        </ul>
        <p>In this example, the video ID is <code>XLcMq2</code>.</p>
    </div>
    
    <div class="step">
        <h3>Step 2: Add a New Video</h3>
        <p>To add a new video:</p>
        <ol>
            <li>Go to the Creator Studio</li>
            <li>Click the "Add Video" button</li>
            <li>In the URL field, enter <strong>just the video ID</strong> (e.g., <code>XLcMq2</code>)</li>
            <li>Click "Next" to fetch the video metadata</li>
        </ol>
        <p>The system will automatically recognize the ID and fetch the video details.</p>
    </div>
    
    <div class="step">
        <h3>Step 3: Edit Video Details</h3>
        <p>After fetching the metadata:</p>
        <ol>
            <li>Review and edit the video title, description, and category</li>
            <li>Select the default language</li>
            <li>Add additional language versions if needed</li>
            <li>Click "Save Video" to add it to your library</li>
        </ol>
    </div>
    
    <div class="note">
        <h3>Troubleshooting</h3>
        <p>If you see "Using default video information. You can edit the details before saving." message:</p>
        <ul>
            <li>This is normal and means the system couldn't fetch detailed metadata from Engaxe</li>
            <li>You can still proceed and manually enter the video details</li>
            <li>The video will still play correctly when embedded</li>
        </ul>
    </div>
    
    <div class="step">
        <h3>How It Works</h3>
        <p>When you add a video using just the ID:</p>
        <ol>
            <li>The system stores the ID in the database</li>
            <li>When a user views the video, the platform loads the Engaxe embed script</li>
            <li>The script uses the ID to load and play the video directly from Engaxe</li>
        </ol>
        <p>This approach ensures the best compatibility and performance for Engaxe videos.</p>
    </div>
    
    <a href="/engaxe-embed-example.html" class="button">View Embed Example</a>
</body>
</html>
