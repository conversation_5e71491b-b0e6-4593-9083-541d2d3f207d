
import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { formatDistanceToNow } from 'date-fns';

interface Comment {
  id: string;
  user: {
    id: string;
    username: string;
    avatar: string;
  };
  text: string;
  timestamp: string;
  likes: number;
  replies?: Comment[];
}

interface CommentsProps {
  videoId: string;
}

export default function Comments({ videoId }: CommentsProps) {
  const { currentUser } = useAuth();
  const [commentText, setCommentText] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [likedComments, setLikedComments] = useState<Set<string>>(new Set());

  // Mock comments - in a real app this would come from an API
  const [comments, setComments] = useState<Comment[]>([
    {
      id: 'c1',
      user: {
        id: 'u7',
        username: 'PolyglotLearner',
        avatar: '/placeholder.svg'
      },
      text: 'The Spanish version is especially helpful, thanks for providing multiple languages!',
      timestamp: new Date(Date.now() - 2 * 60 * 60000).toISOString(),
      likes: 14,
    },
    {
      id: 'c2',
      user: {
        id: 'u8',
        username: 'GlobalStudent',
        avatar: '/placeholder.svg'
      },
      text: 'I\'ve been switching between languages to improve my vocabulary. Great content!',
      timestamp: new Date(Date.now() - 8 * 60 * 60000).toISOString(),
      likes: 27,
      replies: [
        {
          id: 'c2r1',
          user: {
            id: 'u10',
            username: 'LanguageCoach',
            avatar: '/placeholder.svg'
          },
          text: 'That\'s a great learning technique! I do the same thing.',
          timestamp: new Date(Date.now() - 6 * 60 * 60000).toISOString(),
          likes: 8,
        }
      ]
    },
  ]);

  const handleAddComment = () => {
    if (!commentText.trim() || !currentUser) return;

    const newComment: Comment = {
      id: `c${Date.now()}`,
      user: currentUser,
      text: commentText,
      timestamp: new Date().toISOString(),
      likes: 0
    };

    setComments(prevComments => [newComment, ...prevComments]);
    setCommentText('');
  };

  const handleReply = (commentId: string) => {
    if (!replyText.trim() || !currentUser) return;

    const newReply: Comment = {
      id: `r${Date.now()}`,
      user: currentUser,
      text: replyText,
      timestamp: new Date().toISOString(),
      likes: 0
    };

    setComments(prevComments =>
      prevComments.map(comment => {
        if (comment.id === commentId) {
          return {
            ...comment,
            replies: [...(comment.replies || []), newReply]
          };
        }
        return comment;
      })
    );

    setReplyText('');
    setReplyingTo(null);
  };

  const handleLike = (commentId: string, isReply: boolean = false, parentCommentId?: string) => {
    if (!currentUser) return;

    // Check if already liked
    const isLiked = likedComments.has(commentId);

    // Create a new Set from the current likedComments
    const newLikedComments = new Set(likedComments);

    if (isLiked) {
      // Unlike: remove from liked set and decrease count
      newLikedComments.delete(commentId);
      setLikedComments(newLikedComments);

      setComments(prevComments =>
        prevComments.map(comment => {
          if (!isReply && comment.id === commentId) {
            // Main comment like
            return {
              ...comment,
              likes: Math.max(0, comment.likes - 1)
            };
          } else if (isReply && parentCommentId && comment.id === parentCommentId) {
            // Reply like
            return {
              ...comment,
              replies: comment.replies?.map(reply =>
                reply.id === commentId
                  ? { ...reply, likes: Math.max(0, reply.likes - 1) }
                  : reply
              )
            };
          }
          return comment;
        })
      );
    } else {
      // Like: add to liked set and increase count
      newLikedComments.add(commentId);
      setLikedComments(newLikedComments);

      setComments(prevComments =>
        prevComments.map(comment => {
          if (!isReply && comment.id === commentId) {
            // Main comment like
            return {
              ...comment,
              likes: comment.likes + 1
            };
          } else if (isReply && parentCommentId && comment.id === parentCommentId) {
            // Reply like
            return {
              ...comment,
              replies: comment.replies?.map(reply =>
                reply.id === commentId
                  ? { ...reply, likes: reply.likes + 1 }
                  : reply
              )
            };
          }
          return comment;
        })
      );

      // In a real app, you would save this to the database
      // For example:
      // await saveCommentLike(videoId, commentId, currentUser.id);
    }
  };

  return (
    <div className="mt-6 space-y-6">
      <h3 className="text-xl font-medium">{comments.length} Comments</h3>

      {/* Add Comment */}
      {currentUser ? (
        <div className="flex gap-4">
          <Avatar className="h-10 w-10">
            <AvatarImage src={currentUser.avatar} alt={currentUser.username} />
            <AvatarFallback>{currentUser.username[0]}</AvatarFallback>
          </Avatar>
          <div className="flex-1 space-y-2">
            <Textarea
              placeholder="Add a comment..."
              value={commentText}
              onChange={(e) => setCommentText(e.target.value)}
              className="min-h-[80px]"
            />
            <div className="flex justify-end gap-2">
              <Button variant="ghost" onClick={() => setCommentText('')}>Cancel</Button>
              <Button onClick={handleAddComment}>Comment</Button>
            </div>
          </div>
        </div>
      ) : (
        <p className="text-lingstream-muted">Sign in to add a comment</p>
      )}

      {/* Comments List */}
      <div className="space-y-6 mt-6">
        {comments.map((comment) => (
          <div key={comment.id} className="space-y-4">
            <div className="flex gap-4">
              <Avatar className="h-10 w-10">
                <AvatarImage src={comment.user.avatar} alt={comment.user.username} />
                <AvatarFallback>{comment.user.username[0]}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{comment.user.username}</span>
                  <span className="text-xs text-lingstream-muted">
                    {formatDistanceToNow(new Date(comment.timestamp), { addSuffix: true })}
                  </span>
                </div>
                <p className="mt-1">{comment.text}</p>
                <div className="flex items-center gap-4 mt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`${likedComments.has(comment.id) ? 'text-orange-500' : 'text-lingstream-muted'}`}
                    onClick={() => handleLike(comment.id)}
                  >
                    👍 {comment.likes}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-lingstream-muted"
                    onClick={() => setReplyingTo(comment.id)}
                  >
                    Reply
                  </Button>
                </div>

                {replyingTo === comment.id && currentUser && (
                  <div className="mt-3 ml-4 space-y-2">
                    <div className="flex gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={currentUser.avatar} alt={currentUser.username} />
                        <AvatarFallback>{currentUser.username[0]}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 space-y-2">
                        <Textarea
                          placeholder="Add a reply..."
                          value={replyText}
                          onChange={(e) => setReplyText(e.target.value)}
                          className="min-h-[60px]"
                        />
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setReplyText('');
                              setReplyingTo(null);
                            }}
                          >
                            Cancel
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleReply(comment.id)}
                          >
                            Reply
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Replies */}
            {comment.replies && comment.replies.length > 0 && (
              <div className="ml-14 space-y-4">
                {comment.replies.map((reply) => (
                  <div key={reply.id} className="flex gap-4">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={reply.user.avatar} alt={reply.user.username} />
                      <AvatarFallback>{reply.user.username[0]}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{reply.user.username}</span>
                        <span className="text-xs text-lingstream-muted">
                          {formatDistanceToNow(new Date(reply.timestamp), { addSuffix: true })}
                        </span>
                      </div>
                      <p className="mt-1">{reply.text}</p>
                      <div className="flex items-center gap-4 mt-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`${likedComments.has(reply.id) ? 'text-orange-500' : 'text-lingstream-muted'}`}
                          onClick={() => handleLike(reply.id, true, comment.id)}
                        >
                          👍 {reply.likes}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
