import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Home,
  ChevronRight,
  ArrowUpDown,
  Check,
  Eye,
  Trash2,
  Search,
  Filter,
  AlertCircle,
  Flag,
  Video,
  Calendar,
  User,
  MoreHorizontal,
  Shield,
  ShieldAlert,
  ShieldCheck,
  ShieldX,
  Loader2,
  Bug,
  Lightbulb
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { reportAPI } from '@/services/api';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Interface for report data
interface Report {
  id: string;
  reportType: string;
  targetId: string;
  reporterId: string;
  reporterUsername: string;
  category: string;
  reason: string;
  description: string;
  status: string;
  contentTitle?: string;
  createdAt: string;
  updatedAt: string;
  adminComments?: Array<{
    comment: string;
    addedBy: string;
    addedAt: string;
  }>;
}

export default function ManageVideoReportsPage() {
  const [reports, setReports] = useState<Report[]>([]);
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string | null>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [action, setAction] = useState('Mark Safe');
  const [currentPage, setCurrentPage] = useState(1);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [reportTypeTab, setReportTypeTab] = useState<string>('video');
  const [totalReports, setTotalReports] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [adminComment, setAdminComment] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Show success alert
  const displaySuccessAlert = (message: string) => {
    setSuccessMessage(message);
    setShowSuccessAlert(true);
    setTimeout(() => setShowSuccessAlert(false), 3000);
  };

  // Fetch reports from API
  const fetchReports = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await reportAPI.getAllReports({
        page: currentPage,
        limit: itemsPerPage,
        status: activeTab !== 'all' ? activeTab : undefined,
        reportType: reportTypeTab === 'issue' ? 'issue' : 'video', // Get video or issue reports based on tab
        search: searchTerm || undefined,
        sortBy: sortField || undefined,
        sortDirection: sortDirection,
      });

      if (response.success) {
        setReports(response.data.reports);
        setTotalReports(response.data.pagination.total);
        setTotalPages(response.data.pagination.pages);
      } else {
        setError('Failed to fetch reports');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch reports');
      console.error('Error fetching reports:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load reports when component mounts or when filters change
  useEffect(() => {
    fetchReports();
  }, [currentPage, activeTab, reportTypeTab, sortField, sortDirection]);

  // Debounce search term changes
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchReports();
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Handle report selection
  const handleSelectReport = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedReports([...selectedReports, id]);
    } else {
      setSelectedReports(selectedReports.filter(reportId => reportId !== id));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedReports(reports.map(report => report.id));
    } else {
      setSelectedReports([]);
    }
  };

  // Handle mark safe
  const handleMarkSafe = async (id: string, comment?: string) => {
    try {
      const response = await reportAPI.updateReportStatus(id, {
        status: 'safe',
        adminComment: comment
      });
      if (response.success) {
        displaySuccessAlert('Report marked as safe successfully');
        fetchReports(); // Refresh the reports list
      } else {
        setError('Failed to update report status');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to update report status');
      console.error('Error updating report status:', error);
    }
  };

  // Handle add admin comment
  const handleAddAdminComment = async () => {
    if (!selectedReport || !adminComment.trim()) return;

    try {
      const response = await reportAPI.updateReportStatus(selectedReport.id, {
        status: selectedReport.status,
        adminComment: adminComment
      });

      if (response.success) {
        displaySuccessAlert('Comment added successfully');
        setAdminComment('');
        // Update the selected report with the new comment
        const updatedReport = await reportAPI.getReportById(selectedReport.id);
        if (updatedReport.success) {
          setSelectedReport(updatedReport.data);
        }
        fetchReports(); // Refresh the reports list
      } else {
        setError('Failed to add comment');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to add comment');
      console.error('Error adding comment:', error);
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this report?')) {
      try {
        const response = await reportAPI.deleteReport(id);
        if (response.success) {
          displaySuccessAlert('Report deleted successfully');
          setSelectedReports(selectedReports.filter(reportId => reportId !== id));
          fetchReports(); // Refresh the reports list
        } else {
          setError('Failed to delete report');
        }
      } catch (error: any) {
        setError(error.message || 'Failed to delete report');
        console.error('Error deleting report:', error);
      }
    }
  };

  // Handle bulk action
  const handleBulkAction = async () => {
    if (selectedReports.length === 0) return;

    if (action === 'Mark Safe') {
      try {
        const response = await reportAPI.bulkUpdateReportStatus({
          ids: selectedReports,
          status: 'safe',
        });
        if (response.success) {
          displaySuccessAlert(`${selectedReports.length} reports marked as safe`);
          fetchReports(); // Refresh the reports list
          setSelectedReports([]);
        } else {
          setError('Failed to update report statuses');
        }
      } catch (error: any) {
        setError(error.message || 'Failed to update report statuses');
        console.error('Error updating report statuses:', error);
      }
    } else if (action === 'Delete') {
      if (window.confirm('Are you sure you want to delete the selected reports?')) {
        try {
          const response = await reportAPI.bulkDeleteReports({
            ids: selectedReports,
          });
          if (response.success) {
            displaySuccessAlert(`${selectedReports.length} reports deleted successfully`);
            fetchReports(); // Refresh the reports list
            setSelectedReports([]);
          } else {
            setError('Failed to delete reports');
          }
        } catch (error: any) {
          setError(error.message || 'Failed to delete reports');
          console.error('Error deleting reports:', error);
        }
      }
    }
  };

  // Handle view report
  const handleViewReport = (report: Report) => {
    setSelectedReport(report);
    setIsViewDialogOpen(true);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'safe':
        return <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">Safe</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Manage Video Reports</h1>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Reports
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Manage Video Reports</span>
            </div>

            {showSuccessAlert && (
              <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>
                  {successMessage}
                </AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert className="mb-6 bg-red-50 text-red-800 border-red-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>{reportTypeTab === 'issue' ? 'Issue Reports' : 'Video Reports'}</CardTitle>
                    <CardDescription>
                      {reportTypeTab === 'issue'
                        ? 'Manage bug reports and feature suggestions'
                        : 'Manage reported videos on your platform'}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="video" className="w-full mb-4" value={reportTypeTab} onValueChange={setReportTypeTab}>
                  <TabsList>
                    <TabsTrigger value="video" className="flex items-center gap-2">
                      <Video className="h-4 w-4" />
                      Video Reports
                    </TabsTrigger>
                    <TabsTrigger value="issue" className="flex items-center gap-2">
                      <Bug className="h-4 w-4" />
                      Issue Reports
                    </TabsTrigger>
                  </TabsList>
                </Tabs>

                <Tabs defaultValue="all" className="w-full mb-6" value={activeTab} onValueChange={setActiveTab}>
                  <TabsList>
                    <TabsTrigger value="all" className="flex items-center gap-2">
                      <Flag className="h-4 w-4" />
                      All Reports
                    </TabsTrigger>
                    <TabsTrigger value="pending" className="flex items-center gap-2">
                      <ShieldAlert className="h-4 w-4" />
                      Pending
                    </TabsTrigger>
                    <TabsTrigger value="safe" className="flex items-center gap-2">
                      <ShieldCheck className="h-4 w-4" />
                      Safe
                    </TabsTrigger>
                  </TabsList>
                </Tabs>

                <div className="flex flex-col md:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search reports by username, video, or reason..."
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      disabled={isLoading}
                    />
                  </div>

                  <div className="flex gap-2">
                    <Select
                      value={action}
                      onValueChange={setAction}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select action" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Mark Safe">Mark Safe</SelectItem>
                        <SelectItem value="Delete">Delete</SelectItem>
                      </SelectContent>
                    </Select>

                    <Button
                      onClick={handleBulkAction}
                      disabled={selectedReports.length === 0 || isLoading}
                      className="gap-2"
                    >
                      {isLoading ? 'Processing...' : 'Apply'}
                    </Button>
                  </div>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedReports.length === reports.length && reports.length > 0}
                            onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                          />
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('id')}>
                          <div className="flex items-center">
                            ID
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center">
                            USERNAME
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('reported')}>
                          <div className="flex items-center">
                            REPORTED
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center">
                            {reportTypeTab === 'issue' ? 'TYPE' : 'VIDEO'}
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center">
                            REASON
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center">
                            STATUS
                          </div>
                        </TableHead>
                        <TableHead className="text-right">ACTION</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8">
                            <div className="flex flex-col items-center justify-center py-4">
                              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-2" />
                              <p>Loading reports...</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : reports.length > 0 ? (
                        reports.map((report) => (
                          <TableRow key={report.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedReports.includes(report.id)}
                                onCheckedChange={(checked) => handleSelectReport(report.id, checked as boolean)}
                              />
                            </TableCell>
                            <TableCell className="font-medium">{report.id.substring(0, 8)}...</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-muted-foreground" />
                                {report.reporterUsername}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                {formatDate(report.createdAt)}
                              </div>
                            </TableCell>
                            <TableCell>
                              {reportTypeTab === 'issue' ? (
                                <div className="flex items-center gap-2">
                                  {report.category === 'bug' ? (
                                    <Bug className="h-4 w-4 text-red-500" />
                                  ) : report.category === 'suggestion' ? (
                                    <Lightbulb className="h-4 w-4 text-yellow-500" />
                                  ) : (
                                    <Flag className="h-4 w-4 text-blue-500" />
                                  )}
                                  <span className="capitalize">{report.category}</span>
                                </div>
                              ) : (
                                <div className="flex items-center gap-2">
                                  <Video className="h-4 w-4 text-muted-foreground" />
                                  <span className="truncate max-w-[150px]">{report.contentTitle || 'Unknown Video'}</span>
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              <span className="truncate max-w-[150px]">{report.reason}</span>
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(report.status)}
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    className="flex items-center gap-2 cursor-pointer"
                                    onClick={() => handleViewReport(report)}
                                  >
                                    <Eye className="h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="flex items-center gap-2 text-green-600 cursor-pointer"
                                    onClick={() => handleMarkSafe(report.id)}
                                  >
                                    <Check className="h-4 w-4" />
                                    Mark Safe
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="flex items-center gap-2 text-red-600 cursor-pointer"
                                    onClick={() => handleDelete(report.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                            <div className="flex flex-col items-center justify-center py-4">
                              <Flag className="h-10 w-10 text-muted-foreground mb-2 opacity-20" />
                              <p>No reports found</p>
                              <p className="text-sm text-muted-foreground">Try adjusting your filters or search term</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {reports.length > 0 && (
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-muted-foreground">
                      Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalReports)} of {totalReports} entries
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(1)}
                        disabled={currentPage === 1}
                      >
                        First
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <div className="text-sm">
                        Page <span className="font-medium">{currentPage}</span> of <span className="font-medium">{totalPages}</span>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(totalPages)}
                        disabled={currentPage === totalPages}
                      >
                        Last
                      </Button>
                    </div>
                  </div>
                )}

                {selectedReports.length > 0 && (
                  <div className="flex items-center justify-between bg-muted/30 p-3 rounded-md mt-4">
                    <div className="text-sm">
                      <span className="font-medium">{selectedReports.length}</span> {selectedReports.length === 1 ? 'report' : 'reports'} selected
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-2"
                        disabled={isLoading}
                        onClick={async () => {
                          try {
                            const response = await reportAPI.bulkUpdateReportStatus({
                              ids: selectedReports,
                              status: 'safe',
                            });
                            if (response.success) {
                              displaySuccessAlert(`${selectedReports.length} reports marked as safe`);
                              fetchReports(); // Refresh the reports list
                              setSelectedReports([]);
                            } else {
                              setError('Failed to update report statuses');
                            }
                          } catch (error: any) {
                            setError(error.message || 'Failed to update report statuses');
                            console.error('Error updating report statuses:', error);
                          }
                        }}
                      >
                        <Check className="h-4 w-4" />
                        Mark Selected Safe
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="gap-2"
                        disabled={isLoading}
                        onClick={async () => {
                          if (window.confirm('Are you sure you want to delete the selected reports?')) {
                            try {
                              const response = await reportAPI.bulkDeleteReports({
                                ids: selectedReports,
                              });
                              if (response.success) {
                                displaySuccessAlert(`${selectedReports.length} reports deleted successfully`);
                                fetchReports(); // Refresh the reports list
                                setSelectedReports([]);
                              } else {
                                setError('Failed to delete reports');
                              }
                            } catch (error: any) {
                              setError(error.message || 'Failed to delete reports');
                              console.error('Error deleting reports:', error);
                            }
                          }
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                        Delete Selected
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* View Report Dialog */}
            <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Report Details</DialogTitle>
                  <DialogDescription>
                    {selectedReport?.reportType === 'issue'
                      ? 'Detailed information about the issue report'
                      : 'Detailed information about the reported video'}
                  </DialogDescription>
                </DialogHeader>
                {selectedReport && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Report ID</h3>
                        <p>{selectedReport.id}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Status</h3>
                        <p>{getStatusBadge(selectedReport.status)}</p>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Reported By</h3>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <p>{selectedReport.reporterUsername}</p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Reported On</h3>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <p>{formatDate(selectedReport.createdAt)}</p>
                      </div>
                    </div>

                    <Separator />

                    {selectedReport.reportType === 'issue' ? (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Issue Type</h3>
                        <div className="flex items-center gap-2">
                          {selectedReport.category === 'bug' ? (
                            <Bug className="h-4 w-4 text-red-500" />
                          ) : selectedReport.category === 'suggestion' ? (
                            <Lightbulb className="h-4 w-4 text-yellow-500" />
                          ) : (
                            <Flag className="h-4 w-4 text-blue-500" />
                          )}
                          <p className="capitalize">{selectedReport.category}</p>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground mb-1">Video</h3>
                          <div className="flex items-center gap-2">
                            <Video className="h-4 w-4 text-muted-foreground" />
                            <p>{selectedReport.contentTitle || 'Unknown Video'}</p>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground mb-1">Video ID</h3>
                          <p>{selectedReport.targetId}</p>
                        </div>
                      </>
                    )}

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">
                        {selectedReport.reportType === 'issue' ? 'Issue Title' : 'Reason for Report'}
                      </h3>
                      <p>{selectedReport.reason}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
                      <p className="whitespace-pre-wrap">{selectedReport.description}</p>
                    </div>

                    {/* Admin Comments Section */}
                    <div className="mt-6">
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">Admin Comments</h3>
                      {selectedReport.adminComments && selectedReport.adminComments.length > 0 ? (
                        <div className="space-y-3 max-h-40 overflow-y-auto p-2 border rounded-md">
                          {selectedReport.adminComments.map((comment, index) => (
                            <div key={index} className="p-2 bg-muted/50 rounded-md">
                              <p className="text-sm">{comment.comment}</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Added on {new Date(comment.addedAt).toLocaleDateString()} at {new Date(comment.addedAt).toLocaleTimeString()}
                              </p>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">No admin comments yet.</p>
                      )}

                      <div className="mt-4">
                        <Label htmlFor="admin-comment" className="text-sm font-medium">Add Comment</Label>
                        <div className="flex gap-2 mt-1">
                          <Textarea
                            id="admin-comment"
                            placeholder="Add your comment here..."
                            value={adminComment}
                            onChange={(e) => setAdminComment(e.target.value)}
                            className="flex-1"
                            rows={2}
                          />
                          <Button
                            onClick={handleAddAdminComment}
                            disabled={!adminComment.trim()}
                            className="self-end"
                          >
                            Add
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <DialogFooter className="flex justify-between items-center">
                  <Button
                    variant="destructive"
                    onClick={() => {
                      handleDelete(selectedReport.id);
                      setIsViewDialogOpen(false);
                    }}
                    className="gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete Report
                  </Button>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsViewDialogOpen(false)}
                    >
                      Close
                    </Button>
                    <Button
                      onClick={() => {
                        handleMarkSafe(selectedReport.id, adminComment.trim() ? adminComment : undefined);
                        setIsViewDialogOpen(false);
                      }}
                      className="gap-2"
                    >
                      <Check className="h-4 w-4" />
                      Mark Safe
                    </Button>
                  </div>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </main>
      </div>
    </div>
  );
}
