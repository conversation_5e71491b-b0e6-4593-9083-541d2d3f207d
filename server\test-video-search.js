const mongoose = require('mongoose');
require('dotenv').config();

// Define the Video schema
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  tags: [String],
  category: String,
  contentRating: String,
  processingStatus: String,
  deletedAt: Date
});

const Video = mongoose.model('Video', VideoSchema);

async function testVideoSearch() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/lawengaxe');
    console.log('✅ Connected to MongoDB');
    
    // Test 1: Check total videos
    const totalVideos = await Video.countDocuments();
    console.log(`📊 Total videos in database: ${totalVideos}`);
    
    // Test 2: Check public, ready videos
    const publicVideos = await Video.countDocuments({
      deletedAt: null,
      visibility: 'public',
      processingStatus: 'ready'
    });
    console.log(`📊 Public, ready videos: ${publicVideos}`);
    
    // Test 3: Search for logarithm videos (title only)
    console.log('\n🔍 Searching for logarithm videos...');
    const logarithmVideos = await Video.find({
      deletedAt: null,
      visibility: 'public',
      processingStatus: 'ready',
      $or: [
        { title: { $regex: 'logarithm', $options: 'i' } }
      ]
    });
    
    console.log(`🎥 Found ${logarithmVideos.length} logarithm videos:`);
    logarithmVideos.forEach((video, index) => {
      console.log(`  ${index + 1}. Title: ${video.title}`);
      console.log(`     ID: ${video.id}`);
      console.log(`     URL: ${video.url}`);
      console.log(`     Category: ${video.category}`);
      console.log(`     Tags: ${video.tags?.join(', ') || 'None'}`);
      console.log(`     Visibility: ${video.visibility}`);
      console.log(`     Status: ${video.processingStatus}`);
      console.log('     ---');
    });
    
    // Test 4: Search for mathematics videos (title only)
    console.log('\n🔍 Searching for mathematics videos...');
    const mathVideos = await Video.find({
      deletedAt: null,
      visibility: 'public',
      processingStatus: 'ready',
      $or: [
        { title: { $regex: 'math', $options: 'i' } }
      ]
    });
    
    console.log(`🎥 Found ${mathVideos.length} mathematics videos:`);
    mathVideos.forEach((video, index) => {
      console.log(`  ${index + 1}. ${video.title} (${video.category})`);
    });
    
    // Test 5: List all categories
    console.log('\n📂 All video categories:');
    const categories = await Video.distinct('category', {
      deletedAt: null,
      visibility: 'public',
      processingStatus: 'ready'
    });
    console.log(categories);
    
    // Test 6: Sample of all videos
    console.log('\n📋 Sample of all videos:');
    const sampleVideos = await Video.find({
      deletedAt: null,
      visibility: 'public',
      processingStatus: 'ready'
    }).limit(5);
    
    sampleVideos.forEach((video, index) => {
      console.log(`  ${index + 1}. ${video.title} (${video.category})`);
    });
    
    console.log('\n✅ Video search test completed');
    mongoose.disconnect();
  } catch (error) {
    console.error('❌ Error:', error);
    mongoose.disconnect();
    process.exit(1);
  }
}

testVideoSearch();
