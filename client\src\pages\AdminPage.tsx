import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import UsersChart from '@/components/admin/UsersChart';
import VideosChart from '@/components/admin/VideosChart';
import EngagementChart from '@/components/admin/EngagementChart';
import StatCard from '@/components/admin/StatCard';
import TimeRangeSelector from '@/components/admin/TimeRangeSelector';
import { Video, Eye, Users as UsersIcon, MessageSquare, ThumbsUp, ThumbsDown, Bookmark, UserPlus, FileText, TrendingUp } from 'lucide-react';

export default function AdminPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState<'Today' | 'Yesterday' | 'This Week' | 'This Month' | 'Last Month' | 'This Year'>('This Year');
  const { theme } = useTheme();

  // Redirect non-admin users to home page
  useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // If not admin, don't render the admin page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex overflow-hidden">
      <AdminSidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />

        <div className={`flex-1 ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-100'} p-4 overflow-y-auto`}>
          <div className="mb-3">
            <h1 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>Welcome back, {useAuth().currentUser?.username}</h1>
            <div className="flex justify-between items-center">
              <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>Manage your platform from this dashboard</p>
              <TimeRangeSelector
                selectedRange={timeRange}
                onRangeChange={setTimeRange}
              />
            </div>
          </div>

          {/* Error message removed */}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <StatCard
              title="Total Videos"
              value="465"
              icon={<Video size={20} className="text-white" />}
              iconBgColor="bg-orange-500"
              trend={12}
              linkTo="/admin/videos/manage"
              description="All videos on the platform"
            />

            <StatCard
              title="Total Video Views"
              value="3,822"
              icon={<Eye size={20} className="text-white" />}
              iconBgColor="bg-orange-600"
              trend={8}
              linkTo="/admin/videos/views"
              description="Cumulative video views"
            />

            <StatCard
              title="Total Users"
              value="144"
              icon={<UsersIcon size={20} className="text-white" />}
              iconBgColor="bg-orange-400"
              trend={5}
              linkTo="/admin/users/manage"
              description="Registered platform users"
            />

            <StatCard
              title="Total Posts"
              value="32"
              icon={<FileText size={20} className="text-white" />}
              iconBgColor="bg-orange-300"
              trend={-2}
              linkTo="/admin/posts/manage"
              description="Blog and content posts"
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
            <div className="lg:col-span-2">
              <UsersChart timeRange={timeRange} />
            </div>

            <div className="grid grid-cols-1 gap-4">
              <StatCard
                title="Total Subscriptions"
                value="8"
                icon={<UserPlus size={20} className="text-white" />}
                iconBgColor="bg-orange-500"
                trend={15}
                linkTo="/admin/subscriptions/list"
                description="Active user subscriptions"
              />

              <StatCard
                title="Platform Growth"
                value="+23%"
                icon={<TrendingUp size={20} className="text-white" />}
                iconBgColor="bg-orange-600"
                description="Overall platform growth"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <StatCard
              title="Video Comments"
              value="128"
              icon={<MessageSquare size={20} className="text-white" />}
              iconBgColor="bg-orange-400"
              linkTo="/admin/videos/comments"
              description="Total comments on videos"
            />

            <StatCard
              title="Video Likes"
              value="342"
              icon={<ThumbsUp size={20} className="text-white" />}
              iconBgColor="bg-orange-500"
              linkTo="/admin/videos/likes"
              description="Total likes on videos"
            />

            <StatCard
              title="Video Dislikes"
              value="18"
              icon={<ThumbsDown size={20} className="text-white" />}
              iconBgColor="bg-orange-600"
              linkTo="/admin/videos/dislikes"
              description="Total dislikes on videos"
            />

            <StatCard
              title="Saved Videos"
              value="76"
              icon={<Bookmark size={20} className="text-white" />}
              iconBgColor="bg-orange-300"
              linkTo="/admin/videos/saved"
              description="Videos saved by users"
            />
          </div>

          <div className="grid grid-cols-1 gap-3 mb-3">
            <div>
              <VideosChart timeRange={timeRange} />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <div>
              <EngagementChart timeRange={timeRange} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
