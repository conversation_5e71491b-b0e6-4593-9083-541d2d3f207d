
import Navbar from '@/components/layout/Navbar';
import ConversationList from '@/components/messaging/ConversationList';
import MessageView from '@/components/messaging/MessageView';

export default function MessagesPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="container py-6 flex-1">
        <div className="h-[calc(100vh-120px)] border border-gray-700 rounded-lg bg-lingstream-card overflow-hidden flex">
          <div className="w-80 border-r border-gray-700 flex-shrink-0">
            <ConversationList />
          </div>
          <div className="flex-1">
            <MessageView />
          </div>
        </div>
      </main>
    </div>
  );
}
