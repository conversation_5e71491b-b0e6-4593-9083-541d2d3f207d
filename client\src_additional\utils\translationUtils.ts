// Translation utilities for Bhashini API integration

// API endpoints and credentials
const INFERENCE_URL = "https://dhruva-api.bhashini.gov.in/services/inference/pipeline";
const USER_ID = "cee60134c6bb4d179efd3fda48ff32fe";
const API_KEY = "13a647c84b-2747-4f0c-afcd-2ac8235f5318";
const AUTHORIZATION_VALUE = "W9QK_zzb7Lnsc_xYAl30Gk64Z8rJU4r_2NBiguZjiMIdkUI_8p-E38M-zc_VVhun";

// Service and model IDs from the pipeline configuration
const NMT_SERVICE_ID = "ai4bharat/indictrans-v2-all-gpu--t4";
const NMT_MODEL_ID = "641d1d7892a6a31751ff1f5a";

// Script code mapping for Indian languages
function getScriptCode(languageCode: string): string {
  const scriptMapping: Record<string, string> = {
    "hi": "Deva",  // Hindi - Devanagari
    "en": "Latn",  // English - Latin
    "gu": "Gujr",  // Gujarati
    "bn": "Beng",  // Bengali
    "ta": "Taml",  // Tamil
    "te": "Telu",  // Telugu
    "mr": "Deva",  // Marathi - Devanagari
    "kn": "Knda",  // Kannada
    "ml": "Mlym",  // Malayalam
    "pa": "Guru",  // Punjabi - Gurmukhi
    "or": "Orya",  // Odia
    "as": "Beng",  // Assamese - Bengali
    "ur": "Arab",  // Urdu - Arabic
  };
  return scriptMapping[languageCode] || "Deva";  // Default to Devanagari if not found
}

// Check if a language is an Indian language supported by Bhashini
export function isIndianLanguage(langCode: string): boolean {
  return ["hi", "mr", "gu", "ta", "te", "bn", "kn", "ml", "pa", "or", "as", "ur"].includes(langCode);
}

/**
 * Translates text from source language to target language using Bhashini API
 * @param text Text to translate
 * @param sourceLang Source language code (default: "en")
 * @param targetLang Target language code
 * @returns Promise with translated text or error message
 */
export async function translateText(
  text: string,
  sourceLang: string = "en",
  targetLang: string
): Promise<string> {
  // If target language is not an Indian language or is the same as source, return original text
  if (!isIndianLanguage(targetLang) || sourceLang === targetLang) {
    console.log(`Translation not supported for ${targetLang} or same as source language`);
    return text;
  }

  // Headers for the API request
  const headers = {
    "Content-Type": "application/json",
    "userID": USER_ID,
    "ulcaApiKey": API_KEY,
    "Authorization": AUTHORIZATION_VALUE
  };

  // Payload for the translation request
  const payload = {
    "pipelineTasks": [
      {
        "taskType": "translation",
        "config": {
          "language": {
            "sourceLanguage": sourceLang,
            "targetLanguage": targetLang,
            "sourceScriptCode": getScriptCode(sourceLang),
            "targetScriptCode": getScriptCode(targetLang)
          },
          "serviceId": NMT_SERVICE_ID,
          "modelId": NMT_MODEL_ID
        }
      }
    ],
    "inputData": {
      "input": [
        {
          "source": text
        }
      ]
    }
  };

  try {
    console.log(`Translating from ${sourceLang} to ${targetLang}: "${text}"`);
    
    // Make the API request
    const response = await fetch(INFERENCE_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(payload)
    });

    // Process the response
    if (response.ok) {
      const result = await response.json();
      try {
        const outputText = result.pipelineResponse[0].output[0].target;
        console.log(`Translation result: "${outputText}"`);
        return outputText;
      } catch (e) {
        console.error("Error parsing response:", e);
        return text; // Return original text on error
      }
    } else {
      const errorText = await response.text();
      console.error(`Error: ${response.status}, ${errorText}`);
      return text; // Return original text on error
    }
  } catch (e) {
    console.error("Exception occurred:", e);
    return text; // Return original text on error
  }
}

/**
 * Translates an array of messages
 * @param messages Array of message objects with content property
 * @param targetLang Target language code
 * @param sourceLang Source language code (default: "en")
 * @returns Promise with array of translated messages
 */
export async function translateMessages(
  messages: any[],
  targetLang: string,
  sourceLang: string = "en"
): Promise<any[]> {
  // If target language is not an Indian language or is the same as source, return original messages
  if (!isIndianLanguage(targetLang) || sourceLang === targetLang) {
    return messages;
  }

  try {
    // Create a copy of messages to avoid modifying the original array
    const translatedMessages = await Promise.all(
      messages.map(async (message) => {
        // Skip if message doesn't have content property
        if (!message.content) return message;
        
        // Translate the message content
        const translatedContent = await translateText(message.content, sourceLang, targetLang);
        
        // Return a new message object with translated content
        return {
          ...message,
          content: translatedContent,
          // Add a flag to indicate this message has been translated
          isTranslated: translatedContent !== message.content
        };
      })
    );
    
    return translatedMessages;
  } catch (error) {
    console.error("Error translating messages:", error);
    return messages; // Return original messages on error
  }
}
