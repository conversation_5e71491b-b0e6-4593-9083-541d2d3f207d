
import { useLocation, useNavigate } from 'react-router-dom';
import { useVideos } from '@/context/VideoContext';
import Layout from '@/components/layout/Layout';
import VideoCard from '@/components/video/VideoCard';
import SearchVideoCard from '@/components/video/SearchVideoCard';
import ChatVideoCard from '@/components/video/ChatVideoCard';
import DatabaseVideoCard from '@/components/video/DatabaseVideoCard';
import { useState, useEffect, useRef } from 'react';
import { Loader2, Play, Mic } from 'lucide-react';
import ErrorBoundary from '@/components/ErrorBoundary';
import { useLanguage } from '@/context/LanguageContext';
import ChatbotBalloon from '@/components/chatbot/ChatbotBalloon';
import { useChatbot } from '@/context/ChatbotContext';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import { formatNumber, cleanVideoDescription } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import * as speechSynthesis from '@/utils/speechSynthesis';

// Interface for chat messages
interface ChatMessage {
  id: string;
  sender: 'user' | 'bot';
  text: string;
  timestamp: Date;
  videos?: any[]; // Add videos array for storing related videos
}

export default function HomePage() {
  const location = useLocation();
  const navigate = useNavigate();
  const { trendingVideos, getVideosByCategory, refreshVideos, isLoading } = useVideos();
  const { t, currentLanguage } = useLanguage();
  const { isChatbotEnabled, aiProviders } = useChatbot();
  const { currentUser } = useAuth();
  const { theme } = useTheme();
  const { toast } = useToast();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showChatbot, setShowChatbot] = useState(false);

  // Chat functionality states
  const [isChatMode, setIsChatMode] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isAIResponding, setIsAIResponding] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);



  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  // Simple voice-to-text states (for the static microphone button)
  const [isVoiceToTextListening, setIsVoiceToTextListening] = useState(false);
  const voiceToTextRecognitionRef = useRef<SpeechRecognition | null>(null);

  // Live voice interface state
  const [showLiveVoiceInterface, setShowLiveVoiceInterface] = useState(false);
  const [liveVoiceTranscript, setLiveVoiceTranscript] = useState('');
  const [liveVoiceResponse, setLiveVoiceResponse] = useState('');
  const liveRecognitionRef = useRef<SpeechRecognition | null>(null);

  // Continuous conversation state
  const [isConversationActive, setIsConversationActive] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get the most viewed video (first in trendingVideos since they're sorted by views)
  const mostViewedVideo = trendingVideos.length > 0 ? trendingVideos[0] : null;

  // Format message text to handle basic markdown-like formatting
  const formatMessageText = (text: string) => {
    // Split by newlines and process each line
    const lines = text.split('\n');
    return lines.map((line, index) => {
      // Process the line to handle both bold text, markdown links, and plain URLs
      const processLine = (lineText: string) => {
        const elements: React.ReactNode[] = [];
        let currentIndex = 0;

        // First, find all markdown links [text](url)
        const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
        let match: RegExpExecArray | null;

        while ((match = linkRegex.exec(lineText)) !== null) {
          // Add text before the link
          if (match.index > currentIndex) {
            const beforeText = lineText.slice(currentIndex, match.index);
            elements.push(processBoldText(beforeText, elements.length));
          }

          // Check if this is a video link and show video card instead of embedding
          const isVideoLink = match[2].includes('/watch?id=') || match[2].includes('/video/');
          if (isVideoLink) {
            // Extract video ID from URL
            const videoIdMatch = match[2].match(/[?&]id=([^&]+)/);
            if (videoIdMatch) {
              const videoId = videoIdMatch[1];
              elements.push(
                <ChatVideoCard
                  key={elements.length}
                  videoId={videoId}
                  title={match[1]}
                />
              );
            } else {
              // Fallback to link if video ID extraction fails
              elements.push(
                <a
                  key={elements.length}
                  href={match[2]}
                  className="text-blue-600 hover:text-blue-700 font-medium bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded-md transition-colors duration-200 no-underline hover:bg-blue-100 dark:hover:bg-blue-900/30"
                >
                  {match[1]}
                </a>
              );
            }
          } else {
            // Regular link
            elements.push(
              <a
                key={elements.length}
                href={match[2]}
                className="text-blue-500 hover:text-blue-600 underline"
              >
                {match[1]}
              </a>
            );
          }

          currentIndex = match.index + match[0].length;
        }

        // Add remaining text and process plain URLs
        if (currentIndex < lineText.length) {
          const remainingText = lineText.slice(currentIndex);
          elements.push(processTextWithPlainUrls(remainingText, elements.length));
        }

        return elements.length > 0 ? elements : [processTextWithPlainUrls(lineText, 0)];
      };

      // Helper function to process plain URLs in text
      const processTextWithPlainUrls = (text: string, keyPrefix: number) => {
        // Find plain URLs (http:// or https://)
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const parts = text.split(urlRegex);

        return parts.map((part, partIndex) => {
          if (part.match(urlRegex)) {
            // Check if this is a video URL and show video card instead of embedding
            const isVideoLink = part.includes('/watch?id=') || part.includes('/video/');
            if (isVideoLink) {
              // Extract video ID from URL
              const videoIdMatch = part.match(/[?&]id=([^&]+)/);
              if (videoIdMatch) {
                const videoId = videoIdMatch[1];
                return (
                  <ChatVideoCard
                    key={`${keyPrefix}-video-${partIndex}`}
                    videoId={videoId}
                    title="Related Video"
                  />
                );
              } else {
                // Fallback to link if video ID extraction fails
                return (
                  <a
                    key={`${keyPrefix}-url-${partIndex}`}
                    href={part}
                    className="text-blue-600 hover:text-blue-700 font-medium bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded-md transition-colors duration-200 no-underline hover:bg-blue-100 dark:hover:bg-blue-900/30 break-all"
                  >
                    {part}
                  </a>
                );
              }
            } else {
              // Regular URL
              return (
                <a
                  key={`${keyPrefix}-url-${partIndex}`}
                  href={part}
                  className="text-blue-500 hover:text-blue-600 underline break-all"
                >
                  {part}
                </a>
              );
            }
          } else {
            // This is regular text, process for bold/italic
            return processBoldText(part, keyPrefix + '-text-' + partIndex);
          }
        });
      };

      // Helper function to process bold and italic text
      const processBoldText = (text: string, keyPrefix: string | number) => {
        // First process bold text **text**
        const boldRegex = /\*\*(.*?)\*\*/g;
        const parts = text.split(boldRegex);

        return parts.map((part, partIndex) => {
          if (partIndex % 2 === 1) {
            return <strong key={`${keyPrefix}-bold-${partIndex}`} className="font-semibold">{part}</strong>;
          } else {
            // Process italic text *text* in non-bold parts
            const italicRegex = /\*(.*?)\*/g;
            const italicParts = part.split(italicRegex);

            return italicParts.map((italicPart, italicIndex) => {
              if (italicIndex % 2 === 1) {
                return <em key={`${keyPrefix}-italic-${partIndex}-${italicIndex}`} className="italic text-gray-600 dark:text-gray-300">{italicPart}</em>;
              }
              return italicPart;
            });
          }
        });
      };

      return (
        <div key={index} className={index > 0 ? 'mt-2' : ''}>
          {processLine(line)}
        </div>
      );
    });
  };



  // Handle sending a message
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isAIResponding) return;

    const userText = inputValue.trim();

    // Switch to chat mode if not already
    if (!isChatMode) {
      setIsChatMode(true);
    }

    // Add user message
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      sender: 'user',
      text: userText,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setInputValue('');

    // Get AI response for all queries
    await getAIResponse(userText);
  };



  // Extract key educational terms from user query - focused on platform's legal content
  const extractSearchKeywords = (query: string) => {
    // Convert to lowercase for matching
    const lowerQuery = query.toLowerCase();

    // Define keywords that match our platform's legal and educational content
    const platformKeywords = [
      // Legal & Law (our main content area)
      'law', 'legal', 'contract', 'employment', 'intellectual property', 'patent', 'trademark', 'copyright',
      'tenant', 'rental', 'housing', 'rights', 'workplace', 'discrimination', 'harassment', 'eviction',
      'agreement', 'breach', 'liability', 'damages', 'court', 'lawsuit', 'attorney', 'lawyer',
      'constitution', 'criminal', 'civil', 'corporate', 'litigation', 'judge',

      // Mathematics (only for exact matches)
      'logarithm', 'logarithms',

      // General learning terms (when combined with relevant topics)
      'learn', 'study', 'understand', 'explain', 'definition', 'example', 'tutorial', 'lesson', 'guide'
    ];

    // Find matching keywords in the query
    const foundKeywords = platformKeywords.filter(keyword =>
      lowerQuery.includes(keyword)
    );

    // For logarithm queries, add specific variations
    if (lowerQuery.includes('logarithm') || lowerQuery.includes('logarithms')) {
      foundKeywords.push('logarithm', 'logarithms', 'mathematics', 'math');
    }

    // Only include the original query if we found relevant platform keywords
    const searchTerms = foundKeywords.length > 0 ? [...foundKeywords, query] : [];

    // Remove duplicates and return unique search terms
    return [...new Set(searchTerms)];
  };

  // Search for relevant videos from DATABASE first, then JSON storage as fallback
  // UPDATED: Now prioritizes database over JSON storage for most up-to-date results
  const searchPlatformVideos = async (query: string) => {
    try {
      // Extract key search terms
      const searchKeywords = extractSearchKeywords(query);
      console.log('🔍 Extracted search keywords:', searchKeywords);

      let allVideos: any[] = [];

      // First, try searching in DATABASE for most up-to-date results
      console.log(`🔍 Searching DATABASE with query: "${query}"`);
      try {
        const databaseSearchUrl = `http://localhost:3001/api/v1/videos?search=${encodeURIComponent(query)}&limit=10&_t=${Date.now()}`;
        console.log(`🔍 Making DATABASE search request to: ${databaseSearchUrl}`);

        const databaseResponse = await fetch(databaseSearchUrl);
        console.log(`🔍 DATABASE search response status: ${databaseResponse.status}`);

        if (databaseResponse.ok) {
          const databaseData = await databaseResponse.json();
          console.log(`🔍 DATABASE search response:`, databaseData);
          const databaseVideos = databaseData.data?.videos || databaseData.videos || [];

          if (databaseVideos.length > 0) {
            console.log(`🔍 Found ${databaseVideos.length} videos in DATABASE for "${query}"`);
            allVideos.push(...databaseVideos);
          }
        }
      } catch (databaseError) {
        console.error('🔍 DATABASE search error:', databaseError);
      }

      // If no videos found in DATABASE, try JSON storage as fallback
      if (allVideos.length === 0) {
        console.log(`🔍 No videos found in DATABASE, trying JSON storage as fallback: "${query}"`);
        try {
          const jsonSearchUrl = `http://localhost:3001/api/v1/videos/json/videos/search?q=${encodeURIComponent(query)}&_t=${Date.now()}`;
          console.log(`🔍 Making JSON fallback request to: ${jsonSearchUrl}`);

          const jsonResponse = await fetch(jsonSearchUrl);
          console.log(`🔍 JSON fallback response status: ${jsonResponse.status}`);

          if (jsonResponse.ok) {
            const jsonData = await jsonResponse.json();
            console.log(`🔍 JSON fallback response data:`, jsonData);
            const jsonVideos = jsonData.data || [];

            if (jsonVideos.length > 0) {
              console.log(`🔍 Found ${jsonVideos.length} videos in JSON storage for "${query}"`);
              allVideos.push(...jsonVideos);
            }
          }
        } catch (jsonError) {
          console.error(`🔍 JSON fallback search error:`, jsonError);
        }
      }

      // Then search for each extracted keyword
      for (const keyword of searchKeywords) {
        if (keyword === query) continue; // Skip if same as original query

        console.log(`🔍 Searching for keyword: "${keyword}"`);

        try {
          const searchUrl = `http://localhost:3001/api/v1/videos?search=${encodeURIComponent(keyword)}&limit=5&_t=${Date.now()}`;
          console.log(`🔍 Making request to: ${searchUrl}`);

          const response = await fetch(searchUrl);
          console.log(`🔍 Response status: ${response.status}`);

          if (response.ok) {
            const data = await response.json();
            console.log(`🔍 Response data:`, data);

            const videos = data.data?.videos || data.videos || [];
            console.log(`🔍 Found ${videos.length} videos for keyword "${keyword}"`);

            if (videos.length > 0) {
              console.log(`🔍 Video titles:`, videos.map((v: any) => v.title));
            }

            allVideos = [...allVideos, ...videos];
          } else {
            console.error(`🔍 API request failed for keyword "${keyword}":`, response.status, response.statusText);
            const errorText = await response.text();
            console.error(`🔍 Error response:`, errorText);
          }
        } catch (fetchError) {
          console.error(`🔍 Fetch error for keyword "${keyword}":`, fetchError);
        }
      }

      // Remove duplicates based on video ID
      const uniqueVideos = allVideos.filter((video, index, self) =>
        index === self.findIndex(v => v.id === video.id || v.url === video.url)
      );

      // Limit to top 5 results
      const finalVideos = uniqueVideos.slice(0, 5);

      console.log(`🔍 Final search results for query "${query}":`, {
        totalFound: allVideos.length,
        uniqueVideos: uniqueVideos.length,
        finalVideos: finalVideos.length,
        keywords: searchKeywords
      });

      // Log found videos for debugging
      if (finalVideos.length > 0) {
        console.log(`🎥 Found ${finalVideos.length} videos for query "${query}":`, finalVideos.map((v: any) => v.title));
        finalVideos.forEach((video, index) => {
          console.log(`🎥 Video ${index + 1}:`, {
            id: video.id,
            title: video.title,
            url: video.url,
            category: video.category
          });
        });
      } else {
        console.log(`❌ No videos found for query "${query}" with keywords:`, searchKeywords);
      }

      return finalVideos;
    } catch (error) {
      console.error('🔍 Error searching videos:', error);
    }
    return [];
  };



  // Enhanced keyword extraction from both user query and AI response - focused on platform content
  const extractKeywordsFromText = (text: string) => {
    const lowerText = text.toLowerCase();

    // Keywords that match our platform's content (primarily legal)
    const platformKeywords = [
      // Law and Legal (our main content area)
      'law', 'legal', 'contract', 'employment', 'intellectual property', 'patent', 'trademark', 'copyright',
      'tenant', 'rental', 'housing', 'rights', 'workplace', 'discrimination', 'harassment', 'eviction',
      'agreement', 'breach', 'liability', 'damages', 'court', 'lawsuit', 'attorney', 'lawyer',
      'constitution', 'criminal', 'civil', 'corporate', 'litigation', 'judge', 'case', 'statute',
      'regulation', 'compliance', 'property',

      // Mathematics (only specific terms)
      'logarithm', 'logarithms',

      // General Education (when relevant to legal topics)
      'learn', 'study', 'understand', 'explain', 'definition', 'example', 'practice',
      'tutorial', 'lesson', 'course', 'education', 'teaching', 'guide', 'fundamentals'
    ];

    // Find matching keywords in the text
    const foundKeywords = platformKeywords.filter(keyword =>
      lowerText.includes(keyword)
    );

    // Extract important legal terms and concepts
    const words = text.toLowerCase().split(/\s+/);
    const legalWords = words.filter(word =>
      word.length > 4 && // Longer words are more likely to be meaningful
      (word.includes('law') || word.includes('legal') || word.includes('contract') ||
       word.includes('right') || word.includes('court') || word.includes('employ'))
    );

    // Only include the original text if we found relevant platform keywords
    const allKeywords = foundKeywords.length > 0 ?
      [...new Set([...foundKeywords, ...legalWords, text])] :
      [];

    return allKeywords;
  };

  // Enhanced video search using multiple keywords - DATABASE first
  const searchPlatformVideosEnhanced = async (keywords: string[]) => {
    try {
      console.log('🔍 Enhanced video search with keywords:', keywords);
      let allVideos: any[] = [];

      // Search for each keyword - try DATABASE first
      for (const keyword of keywords.slice(0, 5)) { // Limit to first 5 keywords to avoid too many requests
        if (keyword.length < 3) continue; // Skip very short keywords

        console.log(`🔍 Enhanced search for keyword: "${keyword}"`);

        // First try DATABASE search
        try {
          const databaseSearchUrl = `http://localhost:3001/api/v1/videos?search=${encodeURIComponent(keyword)}&limit=3&_t=${Date.now()}`;
          const databaseResponse = await fetch(databaseSearchUrl);

          if (databaseResponse.ok) {
            const databaseData = await databaseResponse.json();
            const databaseVideos = databaseData.data?.videos || databaseData.videos || [];

            if (databaseVideos.length > 0) {
              console.log(`🔍 Enhanced DATABASE search found ${databaseVideos.length} videos for keyword "${keyword}"`);
              allVideos.push(...databaseVideos.slice(0, 3)); // Limit to 3 per keyword
              continue; // Found videos in DATABASE, skip JSON search for this keyword
            }
          }
        } catch (databaseError) {
          console.error(`🔍 Enhanced DATABASE search error for keyword "${keyword}":`, databaseError);
        }

        // If no videos found in DATABASE, try JSON storage as fallback
        try {
          const jsonSearchUrl = `http://localhost:3001/api/v1/videos/json/videos/search?q=${encodeURIComponent(keyword)}&_t=${Date.now()}`;
          const jsonResponse = await fetch(jsonSearchUrl);

          if (jsonResponse.ok) {
            const jsonData = await jsonResponse.json();
            const jsonVideos = jsonData.data || [];

            if (jsonVideos.length > 0) {
              console.log(`🔍 Enhanced JSON fallback found ${jsonVideos.length} videos for keyword "${keyword}"`);
              allVideos.push(...jsonVideos.slice(0, 3)); // Limit to 3 per keyword
            }
          }
        } catch (jsonError) {
          console.error(`🔍 Enhanced JSON fallback error for keyword "${keyword}":`, jsonError);
        }
      }

      // Remove duplicates based on video ID
      const uniqueVideos = allVideos.filter((video, index, self) =>
        index === self.findIndex(v => v.id === video.id)
      );

      console.log(`🔍 Enhanced search found ${uniqueVideos.length} unique videos`);
      return uniqueVideos.slice(0, 5); // Return top 5 results

    } catch (error) {
      console.error('🔍 Error in enhanced video search:', error);
      return [];
    }
  };

  // Get AI response using OpenRouter
  const getAIResponse = async (userText: string) => {
    // Get OpenRouter configuration
    const openRouterConfig = aiProviders.openrouter;
    const isOpenRouterEnabled = openRouterConfig.enabled && openRouterConfig.apiKey;

    if (!isOpenRouterEnabled) {
      toast({
        title: "AI Not Configured",
        description: "Please enable OpenRouter and add your API key in settings.",
        variant: "destructive"
      });
      return;
    }

    setIsAIResponding(true);

    try {
      // Enhanced video query detection - check for video keywords OR educational topics
      const isVideoQuery = /video|watch|play|show|tutorial|lesson|learn|education|course|class|lecture|demonstration|example|explanation|guide|how to|step by step/i.test(userText);

      // Also check for mathematical/educational topics that might have videos
      const isEducationalTopic = /math|mathematics|logarithm|algebra|calculus|geometry|physics|chemistry|biology|science|law|legal|contract|property|rights|constitution|history|literature|language|programming|coding|computer|technology/i.test(userText);

      // Check for specific logarithm queries
      const isLogarithmQuery = /logarithm|logarithms|log|logs/i.test(userText);

      // Prepare system prompt based on query type
      let systemPrompt = `You are a helpful AI assistant for LawEngaxe, a comprehensive legal and educational platform. Provide concise, informative responses (2-3 sentences maximum). End your response naturally without mentioning videos, as relevant video links will be automatically appended to your response. `;

      if (isLogarithmQuery) {
        systemPrompt += `The user is asking about logarithms. Provide a clear, brief explanation of logarithms and their applications in mathematics, engineering, and computer science.`;
      } else if (isVideoQuery || isEducationalTopic) {
        systemPrompt += `The user is seeking educational content. Provide a helpful overview of the topic they're asking about.`;
      } else {
        systemPrompt += `Answer the user's question helpfully and concisely.`;
      }

      // Prepare messages for OpenRouter API
      const messages = [
        {
          role: 'system' as const,
          content: systemPrompt
        },
        {
          role: 'user' as const,
          content: userText
        }
      ];

      // Call OpenRouter API
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${openRouterConfig.apiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'LawEngaxe Search Assistant'
        },
        body: JSON.stringify({
          model: openRouterConfig.model || 'anthropic/claude-3-haiku-20240307',
          messages: messages,
          max_tokens: 150,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('OpenRouter API error:', errorData);
        throw new Error(`API Error: ${response.status}`);
      }

      const data = await response.json();
      let aiResponse = data.choices?.[0]?.message?.content || 'Sorry, I could not generate a response.';

      // Search for videos BEFORE adding the AI response to chat
      console.log(`🔍 Starting video search for query: "${userText}"`);

      // Extract keywords from the user query to search for relevant videos
      let videos: any[] = [];
      const searchKeywords = extractKeywordsFromText(userText);
      console.log(`🔍 Extracted keywords for search:`, searchKeywords);

      // Try searching with the most relevant keyword first - check DATABASE first
      for (const keyword of searchKeywords.slice(0, 3)) { // Try top 3 keywords
        if (keyword.length < 3) continue; // Skip very short keywords

        console.log(`🔍 Trying DATABASE search with keyword: "${keyword}"`);

        // First try DATABASE search
        try {
          const databaseKeywordResponse = await fetch(`http://localhost:3001/api/v1/videos?search=${encodeURIComponent(keyword)}&limit=5&_t=${Date.now()}`);
          console.log(`🔍 DATABASE keyword "${keyword}" search response status:`, databaseKeywordResponse.status);
          if (databaseKeywordResponse.ok) {
            const databaseKeywordData = await databaseKeywordResponse.json();
            console.log(`🔍 DATABASE keyword "${keyword}" search response data:`, databaseKeywordData);
            const databaseKeywordVideos = databaseKeywordData.data?.videos || databaseKeywordData.videos || [];
            if (databaseKeywordVideos.length > 0) {
              console.log(`🔍 DATABASE keyword "${keyword}" search found ${databaseKeywordVideos.length} videos`);
              videos = databaseKeywordVideos;
              break; // Found videos, stop searching
            }
          }
        } catch (databaseKeywordError) {
          console.error(`🔍 DATABASE keyword "${keyword}" search error:`, databaseKeywordError);
        }

        // If no videos found in DATABASE, try JSON storage as fallback
        if (videos.length === 0) {
          console.log(`🔍 Trying JSON fallback search with keyword: "${keyword}"`);
          try {
            const jsonKeywordResponse = await fetch(`http://localhost:3001/api/v1/videos/json/videos/search?q=${encodeURIComponent(keyword)}&_t=${Date.now()}`);
            console.log(`🔍 JSON keyword "${keyword}" search response status:`, jsonKeywordResponse.status);
            if (jsonKeywordResponse.ok) {
              const jsonKeywordData = await jsonKeywordResponse.json();
              console.log(`🔍 JSON keyword "${keyword}" search response data:`, jsonKeywordData);
              const jsonKeywordVideos = jsonKeywordData.data || [];
              if (jsonKeywordVideos.length > 0) {
                console.log(`🔍 JSON keyword "${keyword}" search found ${jsonKeywordVideos.length} videos`);
                videos = jsonKeywordVideos;
                break; // Found videos, stop searching
              }
            }
          } catch (jsonKeywordError) {
            console.error(`🔍 JSON keyword "${keyword}" search error:`, jsonKeywordError);
          }
        }
      }

      // If no videos found yet, try the original search
      if (videos.length === 0) {
        console.log(`🔍 About to call searchPlatformVideos with: "${userText}"`);
        videos = await searchPlatformVideos(userText);
        console.log(`🔍 Original search found ${videos.length} videos for "${userText}"`);
      }

      // If no videos found, try enhanced search with extracted keywords
      if (videos.length === 0) {
        console.log(`🔍 No videos found with original query, trying enhanced search...`);
        const userKeywords = extractKeywordsFromText(userText);
        const aiKeywords = extractKeywordsFromText(aiResponse);
        const allSearchKeywords = [...new Set([...userKeywords, ...aiKeywords])];
        videos = await searchPlatformVideosEnhanced(allSearchKeywords);
        console.log(`🔍 Enhanced search found ${videos.length} videos`);
      }

      // If still no videos found, try a general API call
      if (videos.length === 0) {
        console.log(`🔍 No videos found with enhanced search, trying general API call...`);
        try {
          const generalResponse = await fetch(`http://localhost:3001/api/v1/videos?limit=5&_t=${Date.now()}`);
          console.log(`🔍 General API call response status:`, generalResponse.status);
          if (generalResponse.ok) {
            const generalData = await generalResponse.json();
            console.log(`🔍 General API call response data:`, generalData);
            const generalVideos = generalData.data?.videos || generalData.videos || [];
            if (generalVideos.length > 0) {
              console.log(`🔍 General API call found ${generalVideos.length} videos`);
              // For demonstration, take the first video
              videos = generalVideos.slice(0, 1);
            }
          }
        } catch (generalError) {
          console.error(`🔍 General API call error:`, generalError);
        }
      }

      console.log(`🔍 Final video search completed. Found ${videos.length} videos.`);

      // Store videos for rendering as thumbnail cards
      let relatedVideos: any[] = [];
      if (videos.length > 0) {
        console.log(`🎥 Found ${videos.length} videos to display with thumbnails`);
        console.log(`🎥 Videos found:`, videos);
        relatedVideos = videos;

        // Log video details for debugging
        videos.forEach((video: any, index: number) => {
          console.log(`🎥 Video ${index + 1} details:`, {
            title: video.title,
            id: video.id,
            url: video.url,
            thumbnailUrl: video.thumbnailUrl,
            thumbnail: video.thumbnail,
            category: video.category,
            stats: video.stats
          });
        });

        console.log(`🎥 Videos will be displayed as thumbnail cards below AI response`);
      } else {
        // If no videos found at all, provide topic-specific sample links
        console.log(`🎥 No videos found, adding topic-specific sample link for demonstration`);

        // Determine topic-specific sample video based on query
        let sampleVideoId = null;
        const queryLower = userText.toLowerCase();

        // First, try to get a random video from the platform for the specific query
        try {
          console.log(`🎥 Trying to find any video for topic-specific fallback...`);
          const randomVideoResponse = await fetch(`http://localhost:3001/api/v1/videos?limit=10&_t=${Date.now()}`);
          if (randomVideoResponse.ok) {
            const randomVideoData = await randomVideoResponse.json();
            const randomVideos = randomVideoData.data?.videos || randomVideoData.videos || [];
            if (randomVideos.length > 0) {
              // Try to find a video that matches the topic
              let matchedVideo = null;

              // Look for topic-specific matches (title only)
              if (queryLower.includes('game') || queryLower.includes('theory') || queryLower.includes('strategy')) {
                matchedVideo = randomVideos.find(v =>
                  v.title?.toLowerCase().includes('game') ||
                  v.title?.toLowerCase().includes('theory') ||
                  v.title?.toLowerCase().includes('strategy')
                );
              } else if (queryLower.includes('law') || queryLower.includes('legal') || queryLower.includes('contract')) {
                matchedVideo = randomVideos.find(v =>
                  v.title?.toLowerCase().includes('law') ||
                  v.title?.toLowerCase().includes('legal') ||
                  v.title?.toLowerCase().includes('contract')
                );
              } else if (queryLower.includes('logarithm') || queryLower.includes('log')) {
                matchedVideo = randomVideos.find(v =>
                  v.title?.toLowerCase().includes('logarithm') ||
                  v.title?.toLowerCase().includes('log') ||
                  v.title?.toLowerCase().includes('math')
                );
              } else if (queryLower.includes('programming') || queryLower.includes('coding') || queryLower.includes('computer')) {
                matchedVideo = randomVideos.find(v =>
                  v.title?.toLowerCase().includes('programming') ||
                  v.title?.toLowerCase().includes('coding') ||
                  v.title?.toLowerCase().includes('computer') ||
                  v.title?.toLowerCase().includes('software')
                );
              }

              // If we found a topic-specific match, use it
              if (matchedVideo) {
                sampleVideoId = matchedVideo.url || matchedVideo.id;
                console.log(`🎥 Found topic-specific video: ${matchedVideo.title} (${sampleVideoId})`);
              } else {
                // Otherwise, use a random video from the available ones
                const randomIndex = Math.floor(Math.random() * randomVideos.length);
                sampleVideoId = randomVideos[randomIndex].url || randomVideos[randomIndex].id;
                console.log(`🎥 Using random video: ${randomVideos[randomIndex].title} (${sampleVideoId})`);
              }
            }
          }
        } catch (error) {
          console.error(`🎥 Error fetching videos for topic-specific fallback:`, error);
        }

        // Only use hardcoded fallbacks for topics that actually match our platform's content
        if (!sampleVideoId) {
          // Only show videos for legal topics that match our platform's content
          if (queryLower.includes('law') || queryLower.includes('legal') || queryLower.includes('contract') ||
              queryLower.includes('employment') || queryLower.includes('intellectual property') ||
              queryLower.includes('tenant') || queryLower.includes('rights')) {
            sampleVideoId = 'm7PtcY'; // Law-related video
          } else if (queryLower.includes('logarithm') || queryLower.includes('logarithms')) {
            // Only show logarithm video for exact logarithm queries, not general "log" queries
            sampleVideoId = 'sf8Sy2'; // Logarithm video
          } else {
            // For topics not related to our platform's content, don't show any video
            console.log(`🎥 No relevant videos found for query "${userText}" - not showing fallback video for unrelated topic`);
            sampleVideoId = null;
          }

          if (sampleVideoId) {
            console.log(`🎥 Using hardcoded fallback video: ${sampleVideoId}`);
          }
        }

        // Only add video link if we have a relevant video
        if (sampleVideoId) {
          const sampleVideoUrl = `http://localhost:5173/watch?id=${sampleVideoId}`;
          aiResponse += ` ${sampleVideoUrl}`;
          console.log(`🎥 Final AI response with topic-specific sample video link:`, aiResponse);
        } else {
          console.log(`🎥 No video link added - topic not relevant to platform content`);
        }
      }

      // Add the AI response with related videos to chat
      const botMessage: ChatMessage = {
        id: `bot-${Date.now()}`,
        sender: 'bot',
        text: aiResponse,
        timestamp: new Date(),
        videos: relatedVideos // Store videos for thumbnail display
      };

      setChatMessages(prev => [...prev, botMessage]);



    } catch (error) {
      console.error('Error getting AI response:', error);

      // Add error message
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        sender: 'bot',
        text: `I'm sorry, I'm having trouble connecting to the AI service right now. Please try again later.

You can browse our video library for educational content while I'm unavailable.`,
        timestamp: new Date()
      };

      setChatMessages(prev => [...prev, errorMessage]);

      toast({
        title: "AI Response Error",
        description: "Could not get AI response. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsAIResponding(false);
    }
  };

  // Handle key press in input
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Start voice recognition
  const startVoiceRecognition = () => {
    // If already listening, don't restart
    if (isListening && recognitionRef.current) {
      console.log('Already listening, not restarting recognition');
      return;
    }

    // If user clicked the microphone while it was active, stop listening
    if (isListening) {
      stopVoiceRecognition();
      return;
    }

    // Get OpenRouter configuration
    const openRouterConfig = aiProviders.openrouter;
    const isOpenRouterEnabled = openRouterConfig.enabled && openRouterConfig.apiKey;

    if (!isOpenRouterEnabled) {
      toast({
        title: "API Not Configured",
        description: "Please enable OpenRouter and add your API key in settings.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Use the Web Speech API for voice recognition
      if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        // Create a new recognition instance and store it in the ref
        recognitionRef.current = new SpeechRecognition();
        const recognition = recognitionRef.current;

        // Set language based on current language selection
        const langCode = currentLanguage.code === 'hi' ? 'hi-IN' : 'en-US';
        console.log(`Setting speech recognition language to: ${langCode}`);
        recognition.lang = langCode;
        recognition.continuous = false;
        recognition.interimResults = false;

        recognition.onstart = () => {
          console.log('Voice recognition started');
          setIsListening(true);
        };

        recognition.onresult = async (event) => {
          // Get the last result (most recent speech)
          const resultIndex = event.results.length - 1;
          const transcript = event.results[resultIndex][0].transcript;
          console.log('Voice recognition result:', transcript);

          if (transcript && transcript.trim()) {
            // Switch to chat mode if not already
            if (!isChatMode) {
              setIsChatMode(true);
            }

            // Create a user message
            const userMessage: ChatMessage = {
              id: `user-${Date.now()}`,
              sender: 'user',
              text: transcript,
              timestamp: new Date()
            };

            setChatMessages(prev => [...prev, userMessage]);

            // Get AI response for the voice input
            await getAIResponse(transcript);
          }
        };

        recognition.onerror = (event) => {
          console.error('Voice recognition error:', event.error);
          setIsListening(false);
          toast({
            title: "Voice Recognition Error",
            description: `Error: ${event.error}`,
            variant: "destructive"
          });
        };

        recognition.onend = () => {
          console.log('Voice recognition ended');
          setIsListening(false);
        };

        recognition.start();
      } else {
        toast({
          title: "Voice Recognition Not Supported",
          description: "Your browser doesn't support voice recognition. Try Chrome or Edge.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      setIsListening(false);
      toast({
        title: "Voice Recognition Error",
        description: "Could not start voice recognition.",
        variant: "destructive"
      });
    }
  };

  // Stop voice recognition
  const stopVoiceRecognition = () => {
    setIsListening(false);

    // Try to abort any active recognition using our reference
    try {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
        recognitionRef.current = null;
      }
    } catch (error) {
      console.error('Error stopping recognition:', error);
    }

    // Also stop any ongoing AI speech immediately
    if (isSpeaking) {
      speechSynthesis.stop();
      setIsSpeaking(false);
    }
  };

  // Simple voice-to-text function (just converts speech to text, no AI response)
  const startVoiceToText = () => {
    // If already listening, stop
    if (isVoiceToTextListening) {
      stopVoiceToText();
      return;
    }

    try {
      // Use the Web Speech API for voice recognition
      if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        // Create a new recognition instance
        voiceToTextRecognitionRef.current = new SpeechRecognition();
        const recognition = voiceToTextRecognitionRef.current;

        // Set language based on current language selection
        const langCode = currentLanguage.code === 'hi' ? 'hi-IN' : 'en-US';
        console.log(`Setting voice-to-text recognition language to: ${langCode}`);
        recognition.lang = langCode;
        recognition.continuous = false;
        recognition.interimResults = false;

        recognition.onstart = () => {
          console.log('Voice-to-text recognition started');
          setIsVoiceToTextListening(true);
        };

        recognition.onresult = (event) => {
          // Get the last result (most recent speech)
          const resultIndex = event.results.length - 1;
          const transcript = event.results[resultIndex][0].transcript;
          console.log('Voice-to-text result:', transcript);

          if (transcript && transcript.trim()) {
            // Just set the input value, don't trigger any AI response
            setInputValue(transcript);
          }
        };

        recognition.onerror = (event) => {
          console.error('Voice-to-text recognition error:', event.error);
          setIsVoiceToTextListening(false);
          toast({
            title: "Voice Recognition Error",
            description: `Error: ${event.error}`,
            variant: "destructive"
          });
        };

        recognition.onend = () => {
          console.log('Voice-to-text recognition ended');
          setIsVoiceToTextListening(false);
        };

        recognition.start();
      } else {
        toast({
          title: "Voice Recognition Not Supported",
          description: "Your browser doesn't support voice recognition. Try Chrome or Edge.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error starting voice-to-text recognition:', error);
      setIsVoiceToTextListening(false);
      toast({
        title: "Voice Recognition Error",
        description: "Could not start voice recognition.",
        variant: "destructive"
      });
    }
  };

  // Stop voice-to-text recognition
  const stopVoiceToText = () => {
    setIsVoiceToTextListening(false);

    // Try to abort any active recognition
    try {
      if (voiceToTextRecognitionRef.current) {
        voiceToTextRecognitionRef.current.abort();
        voiceToTextRecognitionRef.current = null;
      }
    } catch (error) {
      console.error('Error stopping voice-to-text recognition:', error);
    }
  };

  // Helper functions for conversation management
  const clearSilenceTimeout = () => {
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
      silenceTimeoutRef.current = null;
    }
  };

  const startSilenceTimeout = () => {
    clearSilenceTimeout();
    silenceTimeoutRef.current = setTimeout(() => {
      console.log('10 seconds of silence detected, restarting listening');
      // Instead of stopping conversation, just restart listening if conversation is still active
      if (isConversationActive && !isPaused) {
        console.log('Restarting listening after silence timeout');
        startLiveVoiceRecognition();
      }
    }, 10000); // 10 seconds
  };

  const stopConversation = () => {
    console.log('Stopping conversation');
    setIsConversationActive(false);
    setIsPaused(false);
    clearSilenceTimeout();
    stopLiveVoiceRecognition();
    if (isSpeaking) {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
    }
  };

  const startConversation = () => {
    console.log('Starting conversation - setting states');
    setIsConversationActive(true);
    setIsPaused(false);
    startLiveVoiceRecognition();
  };

  const pauseConversation = () => {
    console.log('Pausing conversation - stopping continuous loop');
    setIsPaused(true);
    clearSilenceTimeout();
    stopLiveVoiceRecognition();
    if (isSpeaking) {
      window.speechSynthesis.cancel(); // Cancel instead of pause for cleaner stop
      setIsSpeaking(false);
    }
  };

  const resumeConversation = () => {
    console.log('Resuming conversation - restarting continuous loop');
    setIsPaused(false);
    // Always start listening when resuming, regardless of speaking state
    setTimeout(() => {
      startLiveVoiceRecognition();
    }, 300); // Small delay to ensure clean state transition
  };

  // Live voice interface functions
  const startLiveVoiceRecognition = () => {
    // If already listening, stop
    if (isListening) {
      stopLiveVoiceRecognition();
      return;
    }

    try {
      // Use the Web Speech API for voice recognition
      if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        // Create a new recognition instance
        liveRecognitionRef.current = new SpeechRecognition();
        const recognition = liveRecognitionRef.current;

        // Set language based on current language selection
        const langCode = currentLanguage.code === 'hi' ? 'hi-IN' : 'en-US';
        console.log(`Setting live voice recognition language to: ${langCode}`);
        recognition.lang = langCode;
        recognition.continuous = false;
        recognition.interimResults = false;

        recognition.onstart = () => {
          console.log('Live voice recognition started');
          setIsListening(true);
          // Start the 10-second silence timeout
          startSilenceTimeout();
        };

        recognition.onresult = async (event) => {
          // Clear the silence timeout since user spoke
          clearSilenceTimeout();

          // Get the last result (most recent speech)
          const resultIndex = event.results.length - 1;
          const transcript = event.results[resultIndex][0].transcript;
          console.log('Live voice recognition result:', transcript);

          if (transcript && transcript.trim()) {
            setLiveVoiceTranscript(transcript);

            // Get OpenRouter configuration
            const openRouterConfig = aiProviders.openrouter;
            const isOpenRouterEnabled = openRouterConfig.enabled && openRouterConfig.apiKey;

            if (isOpenRouterEnabled) {
              // Get AI response for the voice input
              setIsAIResponding(true);
              try {
                const response = await getAIResponseText(transcript);
                setLiveVoiceResponse(response);

                // Speak the response
                setIsSpeaking(true);
                const utterance = new SpeechSynthesisUtterance(response);

                utterance.onend = () => {
                  console.log('AI finished speaking, checking conversation state:', { isConversationActive, isPaused });
                  setIsSpeaking(false);
                  // If conversation is still active and not paused, restart listening immediately
                  if (isConversationActive && !isPaused) {
                    console.log('AI finished speaking, restarting listening for continuous conversation');
                    setTimeout(() => {
                      startLiveVoiceRecognition();
                    }, 200); // Shorter delay for more responsive conversation
                  } else {
                    console.log('Conversation not active or paused, not restarting listening');
                  }
                };

                utterance.onerror = (error) => {
                  console.log('AI speech error:', error, 'checking conversation state:', { isConversationActive, isPaused });
                  setIsSpeaking(false);
                  // If conversation is still active and not paused, restart listening
                  if (isConversationActive && !isPaused) {
                    console.log('AI speech error, restarting listening for continuous conversation');
                    setTimeout(() => {
                      startLiveVoiceRecognition();
                    }, 300);
                  } else {
                    console.log('Conversation not active or paused, not restarting listening after error');
                  }
                };

                window.speechSynthesis.speak(utterance);
              } catch (error) {
                console.error('Error getting AI response:', error);
                setIsSpeaking(false);
                // Continue conversation without showing error toast to avoid interruption
                console.log('AI response error, continuing conversation');
                // If conversation is still active and not paused, restart listening even after error
                if (isConversationActive && !isPaused) {
                  setTimeout(() => {
                    startLiveVoiceRecognition();
                  }, 500);
                }
              } finally {
                setIsAIResponding(false);
              }
            } else {
              // No OpenRouter API configured - continue listening without showing toast
              console.log('OpenRouter API not configured, continuing conversation');
              // If conversation is still active and not paused, restart listening
              if (isConversationActive && !isPaused) {
                setTimeout(() => {
                  startLiveVoiceRecognition();
                }, 500);
              }
            }
          }
        };

        recognition.onerror = (event) => {
          console.error('Live voice recognition error:', event.error);
          setIsListening(false);
          clearSilenceTimeout();

          // Handle different error types
          if (event.error === 'no-speech') {
            // No speech detected - this is normal, continue the conversation if active
            if (isConversationActive && !isPaused) {
              console.log('No speech detected, restarting listening for continuous conversation');
              setTimeout(() => {
                startLiveVoiceRecognition();
              }, 500);
            }
          } else if (event.error === 'aborted') {
            // Recognition was aborted - this is normal when stopping/pausing
            console.log('Voice recognition aborted');
          } else {
            // Other errors - continue conversation without showing error toast to avoid interruption
            console.log('Voice recognition error:', event.error, 'continuing conversation');

            if (isConversationActive && !isPaused) {
              console.log('Voice recognition error, attempting to restart for continuous conversation');
              setTimeout(() => {
                startLiveVoiceRecognition();
              }, 1000);
            }
          }
        };

        recognition.onend = () => {
          console.log('Live voice recognition ended');
          setIsListening(false);
          clearSilenceTimeout();

          // If conversation is active and not paused, and we're not currently speaking,
          // restart listening (this handles cases where recognition ends naturally)
          if (isConversationActive && !isPaused && !isSpeaking) {
            console.log('Voice recognition ended naturally, restarting for continuous conversation');
            setTimeout(() => {
              startLiveVoiceRecognition();
            }, 500);
          }
        };

        recognition.start();
      } else {
        toast({
          title: "Voice Recognition Not Supported",
          description: "Your browser doesn't support voice recognition. Try Chrome or Edge.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error starting live voice recognition:', error);
      setIsListening(false);
      toast({
        title: "Voice Recognition Error",
        description: "Could not start voice recognition.",
        variant: "destructive"
      });
    }
  };

  const stopLiveVoiceRecognition = () => {
    setIsListening(false);
    clearSilenceTimeout();

    // Try to abort any active recognition
    try {
      if (liveRecognitionRef.current) {
        liveRecognitionRef.current.abort();
        liveRecognitionRef.current = null;
      }
    } catch (error) {
      console.error('Error stopping live voice recognition:', error);
    }
  };

  // Helper function to get AI response text
  const getAIResponseText = async (query: string): Promise<string> => {
    const openRouterConfig = aiProviders.openrouter;

    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openRouterConfig.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'LawEngaxe Voice Assistant'
        },
        body: JSON.stringify({
          model: openRouterConfig.model,
          messages: [
            {
              role: 'system',
              content: 'You are a helpful AI assistant. Provide short, concise responses. Keep answers brief and to the point.'
            },
            {
              role: 'user',
              content: query
            }
          ],
          max_tokens: 150,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0]?.message?.content || 'Sorry, I could not generate a response.';
    } catch (error) {
      console.error('Error calling OpenRouter API:', error);
      throw error;
    }
  };







  // Scroll to bottom of chat when new messages are added
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // Cleanup voice recognition when component unmounts or chat mode changes
  useEffect(() => {
    return () => {
      // Stop conversation and cleanup
      stopConversation();

      // Cleanup voice recognition when component unmounts
      if (recognitionRef.current) {
        recognitionRef.current.abort();
        recognitionRef.current = null;
      }
      // Cleanup voice-to-text recognition when component unmounts
      if (voiceToTextRecognitionRef.current) {
        voiceToTextRecognitionRef.current.abort();
        voiceToTextRecognitionRef.current = null;
      }
      // Cleanup live voice recognition when component unmounts
      if (liveRecognitionRef.current) {
        liveRecognitionRef.current.abort();
        liveRecognitionRef.current = null;
      }
      if (isSpeaking) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  // Stop voice recognition when chat mode is disabled
  useEffect(() => {
    if (!isChatMode && isListening) {
      stopVoiceRecognition();
    }
    if (!isChatMode && isVoiceToTextListening) {
      stopVoiceToText();
    }
  }, [isChatMode, isListening, isVoiceToTextListening]);

  // Refresh videos when component mounts (one time only)
  useEffect(() => {
    console.log('HomePage mounted - refreshing videos');
    refreshVideos();

    // Removed automatic periodic refresh to prevent unwanted reloads
    // Users can manually refresh using the refresh button if needed
  }, []);

  // Parse category from URL query params
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const category = params.get('category');

    if (category) {
      // Convert first letter to uppercase
      const formattedCategory = category.charAt(0).toUpperCase() + category.slice(1);
      setSelectedCategory(formattedCategory);
    } else {
      setSelectedCategory(null);
    }
  }, [location.search]);

  // Filter videos by selected category
  const displayVideos = selectedCategory
    ? getVideosByCategory(selectedCategory)
    : trendingVideos;

  const pageTitle = selectedCategory
    ? `${selectedCategory} ${t('home.videos')}`
    : t('home.trending_videos');

  return (
    <Layout>
      {/* Chatbot Balloon - only show when enabled */}
      {isChatbotEnabled && <ChatbotBalloon isOpen={showChatbot} onClose={() => setShowChatbot(false)} />}



      <div className="space-y-8 h-full">
        {/* Only show search/chat section when no category is selected */}
        {!selectedCategory && (
          <section className="flex flex-col items-center justify-center py-16">
            <h1 className="text-4xl font-semibold text-foreground mb-8 text-center">
              {t('home.help_heading')}
            </h1>

            <div className="w-full max-w-2xl mx-auto px-4">
              {/* Chat Messages Display */}
              {isChatMode && chatMessages.length > 0 && (
                <div
                  ref={chatContainerRef}
                  className={`mb-6 max-h-96 overflow-y-auto rounded-2xl p-4 space-y-3 border ${
                    theme === 'dark'
                      ? 'bg-gray-800 border-gray-700'
                      : 'bg-white border-gray-200'
                  }`}
                >
                  {chatMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-3`}
                    >


                      <div
                        className={`max-w-[75%] p-3 ${
                          message.sender === 'user'
                            ? 'rounded-t-2xl rounded-bl-2xl rounded-br-md shadow-md bg-lingstream-accent/10 border border-lingstream-accent/20 text-lingstream-accent'
                            : `rounded-t-2xl rounded-br-2xl rounded-bl-md shadow-md border ${
                                theme === 'dark'
                                  ? 'bg-gray-700/60 border-gray-600/50 text-white'
                                  : 'bg-gray-50/60 border-gray-200/50 text-gray-800'
                              }`
                        }`}
                      >
                        <div className="text-sm whitespace-pre-wrap">
                          {formatMessageText(message.text)}
                        </div>

                        {/* Display related videos with thumbnails for bot messages */}
                        {message.sender === 'bot' && message.videos && message.videos.length > 0 && (
                          <div className="mt-3 pt-3 border-t border-gray-200/50 dark:border-gray-600/50">
                            <div className="text-xs text-gray-600 dark:text-gray-400 mb-2 font-medium">
                              Related Videos:
                            </div>
                            <div className="space-y-2">
                              {message.videos.slice(0, 3).map((video: any, index: number) => (
                                <DatabaseVideoCard
                                  key={`${message.id}-video-${index}`}
                                  video={video}
                                  compact={true}
                                />
                              ))}
                            </div>
                          </div>
                        )}
                      </div>


                    </div>
                  ))}

                  {/* Loading indicator for AI response */}
                  {isAIResponding && (
                    <div className="flex justify-start mb-3">
                      <div className="bg-card rounded-t-2xl rounded-br-2xl rounded-bl-md shadow-md border border-border p-3">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Input Section */}
              <div className="flex items-center bg-card border border-border rounded-full px-6 py-4 shadow-lg hover:shadow-xl transition-shadow">
                <input
                  type="text"
                  placeholder={isChatMode ? "Type your message..." : t('home.search_placeholder')}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyPress}
                  className="flex-1 bg-transparent text-foreground placeholder-muted-foreground outline-none text-lg"
                  disabled={isAIResponding}
                />

                <div className="flex items-center space-x-3 ml-4">
                  <button
                    onClick={startVoiceToText}
                    disabled={isAIResponding}
                    className={`relative flex h-9 items-center justify-center rounded-full ${
                      isVoiceToTextListening
                        ? "bg-green-500 text-white"
                        : "bg-transparent border border-black text-black"
                    } transition-colors disabled:text-gray-50 disabled:opacity-30 w-9`}
                    title={
                      isVoiceToTextListening
                        ? "Click to stop voice input"
                        : "Click to start voice-to-text"
                    }
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </button>

                  {/* Conditional button: microphone when no text, send when text is present */}
                  {inputValue.trim() ? (
                    // Send button when there's text
                    <button
                      onClick={handleSendMessage}
                      disabled={isAIResponding}
                      className="relative flex h-9 items-center justify-center rounded-full bg-transparent border border-black text-black transition-colors disabled:text-gray-50 disabled:opacity-30 w-9"
                      title="Send message"
                    >
                      <div className="flex items-center justify-center">
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="icon-md">
                          <path d="M7.99992 14.9993V5.41334L4.70696 8.70631C4.31643 9.09683 3.68342 9.09683 3.29289 8.70631C2.90237 8.31578 2.90237 7.68277 3.29289 7.29225L8.29289 2.29225L8.36906 2.22389C8.76184 1.90354 9.34084 1.92613 9.70696 2.29225L14.707 7.29225L14.7753 7.36842C15.0957 7.76119 15.0731 8.34019 14.707 8.70631C14.3408 9.07242 13.7618 9.09502 13.3691 8.77467L13.2929 8.70631L9.99992 5.41334V14.9993C9.99992 15.5516 9.55221 15.9993 8.99992 15.9993C8.44764 15.9993 7.99993 15.5516 7.99992 14.9993Z" fill="currentColor"/>
                        </svg>
                      </div>
                    </button>
                  ) : (
                    // Microphone button when no text
                    <button
                      onClick={() => {
                        setShowLiveVoiceInterface(true);
                      }}
                      disabled={isAIResponding}
                      className="relative flex h-9 items-center justify-center rounded-full bg-transparent border border-black text-black transition-colors disabled:text-gray-50 disabled:opacity-30 w-9"
                      title="Click to start voice input"
                    >
                      <div className="flex items-center justify-center">
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="icon-md">
                          <path d="M5.66699 14.4165V3.5835C5.66699 2.89314 6.22664 2.3335 6.91699 2.3335C7.6072 2.33367 8.16699 2.89325 8.16699 3.5835V14.4165C8.16699 15.1068 7.6072 15.6663 6.91699 15.6665C6.22664 15.6665 5.66699 15.1069 5.66699 14.4165ZM9.83301 11.9165V6.0835C9.83301 5.39325 10.3928 4.83367 11.083 4.8335C11.7734 4.8335 12.333 5.39314 12.333 6.0835V11.9165C12.333 12.6069 11.7734 13.1665 11.083 13.1665C10.3928 13.1663 9.83301 12.6068 9.83301 11.9165ZM1.5 10.2505V7.75049C1.5 7.06013 2.05964 6.50049 2.75 6.50049C3.44036 6.50049 4 7.06013 4 7.75049V10.2505C3.99982 10.9407 3.44025 11.5005 2.75 11.5005C2.05975 11.5005 1.50018 10.9407 1.5 10.2505ZM14 10.2505V7.75049C14 7.06013 14.5596 6.50049 15.25 6.50049C15.9404 6.50049 16.5 7.06013 16.5 7.75049V10.2505C16.4998 10.9407 15.9402 11.5005 15.25 11.5005C14.5598 11.5005 14.0002 10.9407 14 10.2505Z" fill="currentColor"/>
                        </svg>
                      </div>
                    </button>
                  )}
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Videos Section */}
        <section>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">{pageTitle}</h2>
            <div className="flex gap-2">
              <button
                onClick={() => {
                  console.log('Manual refresh triggered');
                  refreshVideos();
                }}
                className="px-3 py-1 bg-lingstream-accent hover:bg-opacity-90 text-white rounded-md text-sm font-medium transition-colors flex items-center"
                disabled={isLoading}
              >
                <Loader2 className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                {t('common.refresh')}
              </button>
              <button
                onClick={() => {
                  console.log('Force refresh triggered');
                  // Clear all cached data and refresh
                  localStorage.removeItem('cachedVideos');
                  refreshVideos();
                }}
                className="px-3 py-1 bg-transparent border border-foreground text-foreground rounded-md text-sm font-medium"
              >
                {t('common.force_refresh')}
              </button>
            </div>
          </div>
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-lingstream-accent" />
              <span className="ml-2">{t('common.loading_videos')}</span>
            </div>
          ) : (
            <>
              <div className="mb-4 text-sm text-muted-foreground">
                {t('home.videos_found', { count: displayVideos.length })}
              </div>
              <ErrorBoundary
                fallback={
                  <div className={`col-span-full p-4 rounded border ${
                    theme === 'dark'
                      ? 'bg-red-900/20 border-red-400 text-red-400'
                      : 'bg-red-100 border-red-400 text-red-700'
                  }`}>
                    <h2 className="text-lg font-bold mb-2">{t('common.error_loading_videos')}</h2>
                    <p>{t('common.error_displaying_videos_desc')}</p>
                    <button
                      className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                      onClick={() => refreshVideos()}
                    >
                      {t('common.try_again')}
                    </button>
                  </div>
                }
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {displayVideos.map((video) => {
                    // Skip invalid videos
                    if (!video || !video.id) {
                      console.warn('Invalid video object in displayVideos:', video);
                      return null;
                    }

                    // Wrap each video card in its own error boundary
                    return (
                      <ErrorBoundary key={video.id} fallback={
                        <div className="bg-gray-800 rounded-lg p-4">
                          <div className="text-red-500">{t('common.error_rendering_video')}</div>
                        </div>
                      }>
                        <VideoCard video={video} />
                      </ErrorBoundary>
                    );
                  })}
                  {displayVideos.length === 0 && (
                    <p className="col-span-full text-center py-8 text-lingstream-muted">
                      {t('home.no_videos_refresh')}
                    </p>
                  )}
                </div>
              </ErrorBoundary>
            </>
          )}
        </section>
      </div>

      {/* Live Voice Interface - Floating Rectangle */}
      {showLiveVoiceInterface && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="w-80 h-96 bg-gradient-to-br from-blue-900 via-purple-900 to-black rounded-3xl shadow-2xl flex flex-col relative overflow-hidden">
            {/* Live indicator */}
            <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10">
              <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-3 py-1">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-white text-sm font-medium">Live</span>
              </div>
            </div>

            {/* Main content area */}
            <div className="flex-1 flex flex-col items-center justify-center space-y-6 px-6 pt-12 pb-20">
              {/* Visual feedback for listening/speaking */}
              <div className="relative">
                {/* Animated circles for visual feedback */}
                {(isListening || isSpeaking) && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className={`w-24 h-24 rounded-full border-2 ${
                      isListening ? 'border-green-400 animate-pulse' : 'border-blue-400 animate-ping'
                    } opacity-30`}></div>
                    <div className={`absolute w-16 h-16 rounded-full border-2 ${
                      isListening ? 'border-green-400 animate-pulse' : 'border-blue-400 animate-ping'
                    } opacity-50`} style={{animationDelay: '150ms'}}></div>
                  </div>
                )}

                {/* Central microphone icon */}
                <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
                  isListening
                    ? 'bg-green-500 shadow-lg shadow-green-500/50'
                    : isSpeaking
                      ? 'bg-blue-500 shadow-lg shadow-blue-500/50'
                      : 'bg-white/20 backdrop-blur-sm'
                } transition-all duration-300`}>
                  {isListening ? (
                    <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  ) : isSpeaking ? (
                    <svg className="h-8 w-8 text-white animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                    </svg>
                  ) : (
                    <svg className="h-8 w-8 text-white/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  )}
                </div>
              </div>

              {/* Status text */}
              <div className="text-center space-y-1">
                <h3 className="text-lg font-semibold text-white">
                  {isListening
                    ? 'Listening...'
                    : isSpeaking
                      ? 'Speaking...'
                      : isPaused
                        ? 'Conversation Paused'
                        : isConversationActive
                          ? 'Conversation Active'
                          : 'Ready to start'
                  }
                </h3>
                <p className="text-white/70 text-sm">
                  {isListening
                    ? 'Speak now (continuous mode)'
                    : isSpeaking
                      ? 'AI is responding...'
                      : isPaused
                        ? 'Tap play to resume'
                        : isConversationActive
                          ? 'Ready for next input'
                          : 'Tap mic to start conversation'
                  }
                </p>
              </div>

              {/* Transcript display */}
              {liveVoiceTranscript && (
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 max-w-full text-center">
                  <p className="text-white/90 text-xs font-medium mb-1">You said:</p>
                  <p className="text-white text-sm">{liveVoiceTranscript}</p>
                </div>
              )}

              {/* AI Response display */}
              {liveVoiceResponse && (
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 max-w-full text-center">
                  <p className="text-white/90 text-xs font-medium mb-1">AI Response:</p>
                  <p className="text-white text-xs">{liveVoiceResponse}</p>
                </div>
              )}
            </div>

            {/* Bottom controls - 2 buttons */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <div className="flex items-center space-x-6">
                {/* Pause button */}
                <button
                  onClick={() => {
                    if (isConversationActive) {
                      if (isPaused) {
                        // Resume conversation
                        resumeConversation();
                      } else {
                        // Pause conversation
                        pauseConversation();
                      }
                    } else {
                      // Start new conversation
                      startConversation();
                    }
                  }}
                  className={`w-12 h-12 rounded-full ${
                    isConversationActive && !isPaused
                      ? 'bg-white/30 hover:bg-white/40'
                      : 'bg-white/20 hover:bg-white/30'
                  } text-white backdrop-blur-sm transition-all duration-200 flex items-center justify-center`}
                >
                  {isConversationActive && !isPaused ? (
                    // Show pause icon when conversation is active
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6" />
                    </svg>
                  ) : isPaused ? (
                    // Show play icon when conversation is paused
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ) : (
                    // Show microphone icon when conversation is not started
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  )}
                </button>

                {/* Close (X) button */}
                <button
                  onClick={() => {
                    // Stop the entire conversation
                    stopConversation();
                    // Clear transcript and response
                    setLiveVoiceTranscript('');
                    setLiveVoiceResponse('');
                    // Close the interface
                    setShowLiveVoiceInterface(false);
                  }}
                  className="w-12 h-12 rounded-full bg-red-500/80 hover:bg-red-600 text-white transition-all duration-200 flex items-center justify-center"
                >
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
}

