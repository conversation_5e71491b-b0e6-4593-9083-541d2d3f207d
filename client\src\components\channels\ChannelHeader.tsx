import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { CheckCircle, Share2, MoreHorizontal, Plus, MessageSquare, Video, Loader2 } from 'lucide-react';
import { formatNumber } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/context/AuthContext';
import { useVideos } from '@/context/VideoContext';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import AddVideoWizard from '@/components/creator/AddVideoWizard';
import { videoAPI } from '@/services/api';

interface ChannelHeaderProps {
  channel: {
    id: string;
    name: string;
    displayName: string;
    description: string;
    avatar?: string;
    banner?: string;
    isVerified: boolean;
    ownerId: string;
    stats: {
      subscribers: number;
      totalViews: number;
      videoCount: number;
    };
  };
  activeTab: string;
  onTabChange: (value: string) => void;
  isSubscribed?: boolean;
  onSubscribe?: () => void;
  onShare?: () => void;
}

const ChannelHeader: React.FC<ChannelHeaderProps> = ({
  channel,
  activeTab,
  onTabChange,
  isSubscribed = false,
  onSubscribe,
  onShare,
}) => {
  const { currentUser } = useAuth();
  const { refreshVideos } = useVideos();
  const { toast } = useToast();
  const isOwner = currentUser?.id === channel.ownerId;

  // Dialog state
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSavingVideo, setIsSavingVideo] = useState(false);

  // Categories and languages for the AddVideoWizard
  const [categories] = useState([
    'Education', 'Entertainment', 'Gaming', 'Music', 'News',
    'Sports', 'Technology', 'Travel', 'Cooking', 'Fashion',
    'Health', 'Science', 'Business', 'Politics', 'Art', 'Comedy'
  ]);

  const [languages] = useState([
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
    { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
    { code: 'ru', name: 'Russian', flag: '🇷🇺' },
    { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
    { code: 'pt', name: 'Portuguese', flag: '🇧🇷' },
    { code: 'it', name: 'Italian', flag: '🇮🇹' },
    { code: 'nl', name: 'Dutch', flag: '🇳🇱' },
    { code: 'ko', name: 'Korean', flag: '🇰🇷' },
    { code: 'tr', name: 'Turkish', flag: '🇹🇷' },
  ]);

  // Handle save video
  const handleSaveVideo = async (data: {
    engaxeUrl: string;
    title: string;
    description: string;
    thumbnail: string | null;
    thumbnailFile: File | null;
    category: string;
    defaultLanguage: string;
    defaultLanguageUrl: string;
    additionalLanguages: {
      id: string;
      languageCode: string;
      url: string;
    }[];
  }) => {
    try {
      setIsSavingVideo(true);

      // Extract video languages for API
      const videoLanguages = [
        {
          code: data.defaultLanguage,
          name: languages.find(l => l.code === data.defaultLanguage)?.name || 'Unknown',
          flag: languages.find(l => l.code === data.defaultLanguage)?.flag || '🌐',
          url: data.defaultLanguageUrl,
          isDefault: true
        },
        ...data.additionalLanguages.map(lang => ({
          code: lang.languageCode,
          name: languages.find(l => l.code === lang.languageCode)?.name || 'Unknown',
          flag: languages.find(l => l.code === lang.languageCode)?.flag || '🌐',
          url: lang.url,
          isDefault: false
        }))
      ];

      // Process the URL to get the video ID
      const processedUrl = data.engaxeUrl;

      // Save to database using the videoAPI service
      console.log(`Saving video to database with channel ID: ${channel.id}`);
      const response = await videoAPI.saveEngaxeVideo(processedUrl, channel.id, videoLanguages);

      if (response && response.success) {
        // Show success message
        toast({
          title: "Video Saved Successfully",
          description: `Your video "${data.title}" has been saved to your channel.`,
          variant: "default"
        });

        // Refresh videos to include the newly saved video
        await refreshVideos();

        // Close dialog
        setIsDialogOpen(false);
      } else {
        console.error('API call succeeded but returned success=false:', response);
        toast({
          title: "Error Saving Video",
          description: response?.message || 'Unknown error saving video',
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error saving video:', error);
      toast({
        title: "Error Saving Video",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive"
      });
    } finally {
      setIsSavingVideo(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setIsDialogOpen(false);
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="space-y-4">
      {/* Banner */}
      <div className="relative h-32 sm:h-48 w-full bg-gradient-to-r from-pink-600 via-purple-600 to-indigo-700 rounded-lg overflow-hidden">
        {/* Background overlay with pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-pink-600 via-purple-600 to-indigo-700 opacity-90">
          <div className="absolute inset-0" style={{
            backgroundImage: "radial-gradient(circle at 20px 20px, rgba(255,255,255,0.15) 2px, transparent 0)",
            backgroundSize: "40px 40px"
          }}></div>
        </div>

        {channel.banner && (
          <img
            src={channel.banner}
            alt={`${channel.displayName} banner`}
            className="w-full h-full object-cover opacity-40"
            onError={(e) => {
              console.warn(`Failed to load banner for channel ${channel.name}`);
              // Remove the image on error to show the gradient background
              e.currentTarget.style.display = 'none';
            }}
          />
        )}

        {/* Channel name overlay - full width */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full px-4 py-2 backdrop-blur-sm">
            <h1
              className="text-center text-4xl sm:text-5xl font-bold italic text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 via-white to-yellow-300 tracking-wider drop-shadow-lg"
              style={{ fontFamily: "'Brush Script MT', 'Segoe Script', 'Comic Sans MS', cursive" }}
            >
              {channel.displayName.charAt(0).toUpperCase() + channel.displayName.slice(1)}'s Channel
            </h1>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r from-yellow-300 via-white to-yellow-300"></div>
      </div>

      {/* Channel info */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        <Avatar className="h-20 w-20 border-4 border-background -mt-10 sm:-mt-14 z-10">
          <AvatarImage
            src={channel.avatar || ''}
            alt={channel.displayName}
            onError={(e) => {
              console.warn(`Failed to load avatar for channel ${channel.name}`);
              e.currentTarget.style.display = 'none'; // Hide the image to show fallback
            }}
          />
          <AvatarFallback className="flex h-full w-full items-center justify-center rounded-full bg-white text-black border border-black">
            {getInitials(channel.displayName)}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold truncate">{channel.displayName}</h1>
            {channel.isVerified && (
              <CheckCircle className="h-5 w-5 text-blue-500" />
            )}
          </div>
          <div className="text-sm text-muted-foreground">
            <span className="font-medium">@{channel.name}</span> •
            <span className="ml-2">{formatNumber(channel.stats.subscribers)} subscribers</span> •
            <span className="ml-2">{formatNumber(channel.stats.videoCount)} videos</span>
          </div>
          <p className="text-sm mt-1 line-clamp-1">{channel.description}</p>
        </div>

        <div className="flex items-center gap-2 self-end sm:self-center mt-2 sm:mt-0">
          {isOwner && (
            <>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="default">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Video
                  </Button>
                </DialogTrigger>
                <DialogContent
                  className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto"
                  aria-describedby="video-dialog-description"
                >
                  <DialogHeader>
                    <DialogTitle>Add a New Video</DialogTitle>
                    <DialogDescription id="video-dialog-description">
                      Fill in the details to add a new video to your channel.
                    </DialogDescription>
                  </DialogHeader>

                  <AddVideoWizard
                    languages={languages}
                    categories={categories}
                    onSave={handleSaveVideo}
                    onCancel={handleCancel}
                    isSaving={isSavingVideo}
                  />
                </DialogContent>
              </Dialog>

              <Button
                onClick={() => window.location.href = `/creator-studio?tab=videos&channelId=${channel.id}`}
                variant="default"
              >
                <Video className="h-4 w-4 mr-2" />
                Videos Uploaded
              </Button>
            </>
          )}

          {!isOwner && (
            <>
              {onSubscribe && (
                <Button
                  variant={isSubscribed ? "outline" : "default"}
                  onClick={onSubscribe}
                >
                  {isSubscribed ? 'Subscribed' : 'Subscribe'}
                </Button>
              )}
            </>
          )}

          {onShare && (
            <Button variant="outline" size="icon" onClick={onShare}>
              <Share2 className="h-4 w-4" />
            </Button>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {isOwner && (
                <>
                  <DropdownMenuItem asChild>
                    <a href={`/studio/channel/${channel.id}`}>Edit Channel</a>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <a href={`/creator-studio?tab=videos&channelId=${channel.id}`}>Manage Videos</a>
                  </DropdownMenuItem>
                </>
              )}
              <DropdownMenuItem onClick={() => window.open(`/channels/${channel.name}`, '_blank')}>
                Open in new tab
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(window.location.href)}>
                Copy channel URL
              </DropdownMenuItem>
              {!isOwner && (
                <DropdownMenuItem>Report channel</DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
        <TabsList className="w-full justify-start">
          <TabsTrigger value="videos">Videos</TabsTrigger>
          <TabsTrigger value="playlists">Playlists</TabsTrigger>
          <TabsTrigger value="about">About</TabsTrigger>
          <TabsTrigger value="community">Community</TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
};

export default ChannelHeader;
