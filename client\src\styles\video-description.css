/* Video description styling */
.video-description {
  line-height: 1.5;
  word-break: break-word;
}

/* Grey background for video description */
.video-description-bg {
  background-color: #f0f0f0;
  border-radius: 0.5rem;
  padding: 1rem;
}

/* Dark mode adjustment for grey background */
:root[class~="dark"] .video-description-bg {
  background-color: #333333;
}

/* Line clamp for truncated description */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-description a {
  color: #0070f3;
  text-decoration: underline;
}

.video-description a:hover {
  text-decoration: none;
}

.video-description p {
  margin-bottom: 0.75rem;
}

.video-description br {
  display: block;
  content: "";
  margin-top: 0.5rem;
}

.video-description ul,
.video-description ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.video-description li {
  margin-bottom: 0.25rem;
}

.video-description h1,
.video-description h2,
.video-description h3,
.video-description h4,
.video-description h5,
.video-description h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.video-description img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
}

.video-description pre,
.video-description code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: monospace;
}

.video-description blockquote {
  border-left: 3px solid #ccc;
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
}

.video-description hr {
  border: 0;
  border-top: 1px solid #eee;
  margin: 1.5rem 0;
}

/* Dark mode adjustments */
:root[class~="dark"] .video-description a {
  color: #3b82f6;
}

:root[class~="dark"] .video-description pre,
:root[class~="dark"] .video-description code {
  background-color: rgba(255, 255, 255, 0.1);
}

:root[class~="dark"] .video-description blockquote {
  border-left-color: #555;
}

:root[class~="dark"] .video-description hr {
  border-top-color: #333;
}
