import { videoIdMappingService } from '../services';
import { isValidEngaxeId } from './id';

/**
 * Convert a hash ID to an Engaxe ID
 * @param hashId The hash ID to convert
 * @returns The corresponding Engaxe ID, or the original ID if no mapping exists
 */
export async function hashIdToEngaxeId(hashId: string): Promise<string> {
  // Check if the ID is already a valid Engaxe ID (6-7 alphanumeric characters)
  if (isValidEngaxeId(hashId)) {
    return hashId;
  }

  try {
    // Try to get the mapping from the service
    const engaxeId = await videoIdMappingService.getEngaxeIdFromHashId(hashId);

    if (engaxeId) {
      console.log(`Found mapping for hash ID ${hashId}: ${engaxeId}`);
      return engaxeId;
    }

    // If no mapping exists, try to create one
    const newMapping = await videoIdMappingService.createMapping(hashId, undefined, 'Auto-generated mapping');

    if (newMapping && newMapping.engaxeId) {
      console.log(`Created new mapping for hash ID ${hashId}: ${newMapping.engaxeId}`);
      return newMapping.engaxeId;
    }

    // If all else fails, return a default valid Engaxe ID
    // This is a fallback to ensure the video player works
    console.log(`No mapping found for hash ID ${hashId}, using default Engaxe ID`);
    return 'XLcMq2'; // Default to a known working Engaxe ID
  } catch (error) {
    console.error(`Error converting hash ID ${hashId} to Engaxe ID:`, error);
    return 'XLcMq2'; // Default to a known working Engaxe ID
  }
}
