import { Link } from 'react-router-dom';
import {
  Home,
  Calendar,
  Tag,
  AlertCircle,
  Info,
  Sparkles,
  Bug,
  Zap,
  ArrowUpCircle
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

// Import shadcn components
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

// Enhanced changelog interfaces
interface ChangelogChange {
  type: 'fixed' | 'added' | 'improved' | 'removed';
  description: string;
}

interface ChangelogItemProps {
  version: string;
  date: string;
  changes: ChangelogChange[];
  isLatest?: boolean;
}

const ChangelogItem = ({ version, date, changes, isLatest = false }: ChangelogItemProps) => {
  // Group changes by type
  const groupedChanges = changes.reduce((acc, change) => {
    if (!acc[change.type]) {
      acc[change.type] = [];
    }
    acc[change.type].push(change.description);
    return acc;
  }, {} as Record<string, string[]>);

  // Get icon and color for change type
  const getChangeTypeInfo = (type: string) => {
    switch (type) {
      case 'fixed':
        return {
          icon: <Bug className="h-4 w-4" />,
          label: 'Fixed',
          badgeClass: 'bg-green-100 text-green-800 hover:bg-green-200'
        };
      case 'added':
        return {
          icon: <Sparkles className="h-4 w-4" />,
          label: 'Added',
          badgeClass: 'bg-blue-100 text-blue-800 hover:bg-blue-200'
        };
      case 'improved':
        return {
          icon: <Zap className="h-4 w-4" />,
          label: 'Improved',
          badgeClass: 'bg-purple-100 text-purple-800 hover:bg-purple-200'
        };
      case 'removed':
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          label: 'Removed',
          badgeClass: 'bg-red-100 text-red-800 hover:bg-red-200'
        };
      default:
        return {
          icon: <Info className="h-4 w-4" />,
          label: 'Info',
          badgeClass: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
        };
    }
  };

  // Format date
  const formatDate = (dateStr: string) => {
    const [day, month, year] = dateStr.split('/');
    return `${day} ${['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][parseInt(month) - 1]} ${year}`;
  };

  return (
    <Card className="mb-6 overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg">v{version}</CardTitle>
            {isLatest && (
              <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
                Latest
              </Badge>
            )}
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="h-4 w-4 mr-1" />
            {formatDate(date)}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pb-3">
        <div className="space-y-4">
          {Object.entries(groupedChanges).map(([type, items]) => {
            const { icon, label, badgeClass } = getChangeTypeInfo(type);
            return (
              <div key={type} className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge className={badgeClass}>
                    <span className="flex items-center gap-1">
                      {icon}
                      {label}
                    </span>
                  </Badge>
                </div>
                <ul className="space-y-1 pl-6 list-disc">
                  {items.map((item, idx) => (
                    <li key={idx} className="text-sm">{item}</li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>
      </CardContent>
      {isLatest && (
        <CardFooter className="pt-0">
          <Button variant="outline" size="sm" className="w-full">
            <ArrowUpCircle className="h-4 w-4 mr-1" />
            View Full Release Notes
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export default function ChangelogsPage() {
  // Enhanced changelogs with change types
  const changelogs: ChangelogItemProps[] = [
    {
      version: '3.1.1',
      date: '30/05/2024',
      isLatest: true,
      changes: [
        { type: 'fixed', description: "Can't play playlist in android 13 browsers" },
        { type: 'fixed', description: "User data was not fully deleted from the database when deleting a user" },
        { type: 'fixed', description: "Recurring payment related issues" },
        { type: 'fixed', description: "TikTok import sometimes not working" },
        { type: 'fixed', description: "YouTube short import sometimes not working" },
        { type: 'fixed', description: "Auto import system in admin panel" },
        { type: 'fixed', description: "Auto import system for shorts in admin panel" },
        { type: 'fixed', description: "5+ other minor bugs" }
      ]
    },
    {
      version: '3.1.0',
      date: '23/03/2024',
      changes: [
        { type: 'added', description: "New dark mode theme option for better viewing experience" },
        { type: 'added', description: "Integration with YouTube Shorts API" },
        { type: 'improved', description: "Video player performance and compatibility" },
        { type: 'improved', description: "User profile management interface" },
        { type: 'fixed', description: "Login issues with certain email providers" },
        { type: 'removed', description: "Legacy import system that was causing performance issues" }
      ]
    },
    {
      version: '3.0.5',
      date: '15/02/2024',
      changes: [
        { type: 'fixed', description: "Mobile responsive layout issues" },
        { type: 'fixed', description: "Search functionality not working properly" },
        { type: 'improved', description: "Loading times for video thumbnails" },
        { type: 'added', description: "New analytics dashboard for content creators" }
      ]
    }
  ];

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header and Breadcrumb */}
            <div>
              <h1 className="text-2xl font-bold mb-2">Changelogs</h1>

              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="/admin" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Admin Panel
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Changelogs</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {/* Info Alert */}
            <Alert className="bg-blue-50 border-blue-200">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-700">
                Updates are provided freely for life-time if you own a valid purchase code. Don't have one?{' '}
                <a href="#" className="font-medium text-blue-700 hover:underline">Get Valid Purchase Code!</a>
              </AlertDescription>
            </Alert>

            {/* Version Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Version History</CardTitle>
                <CardDescription>
                  Track all changes and updates to the application
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-3">
                  {changelogs.map((changelog, index) => (
                    <Badge
                      key={index}
                      variant={changelog.isLatest ? "default" : "outline"}
                      className={changelog.isLatest ? "" : "hover:bg-muted"}
                    >
                      <Tag className="h-3 w-3 mr-1" />
                      v{changelog.version}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Changelogs */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Release Notes</h2>
              {changelogs.map((changelog, index) => (
                <ChangelogItem
                  key={index}
                  version={changelog.version}
                  date={changelog.date}
                  changes={changelog.changes}
                  isLatest={changelog.isLatest}
                />
              ))}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
