import React, { useState } from 'react';
import { Globe, Info, Key, Code, FileText, Award, DollarSign, MessageSquare, ThumbsUp, ThumbsDown, Play, Upload, Users, Crown } from 'lucide-react';

export default function WebsiteInformation() {
  const [activeTab, setActiveTab] = useState('basic');
  const [isSaving, setIsSaving] = useState(false);

  const handleSaveChanges = () => {
    setIsSaving(true);

    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      // Show success message
      alert('Settings saved successfully!');
    }, 1000);
  };

  return (
    <div className="max-w-full mx-auto">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center gap-2 text-sm mb-6 bg-orange-50 p-3 rounded-lg">
        <span className="flex items-center gap-1 text-gray-700">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-orange-500">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
          Admin Panel
        </span>
        <span className="text-gray-500">›</span>
        <span className="text-gray-700">Settings</span>
        <span className="text-gray-500">›</span>
        <span className="text-orange-600 font-medium">Website Information</span>
      </div>

      {/* Tabs */}
      <div className="mb-6 border-b border-gray-200">
        <div className="flex space-x-1">
          <button
            onClick={() => setActiveTab('basic')}
            className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
              activeTab === 'basic'
                ? 'bg-white text-orange-600 border-t border-l border-r border-gray-200'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center">
              <Globe className="h-4 w-4 mr-2 text-orange-500" />
              Basic Information
            </div>
          </button>
          <button
            onClick={() => setActiveTab('api')}
            className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
              activeTab === 'api'
                ? 'bg-white text-orange-600 border-t border-l border-r border-gray-200'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center">
              <Key className="h-4 w-4 mr-2 text-orange-500" />
              API & Integration
            </div>
          </button>
          <button
            onClick={() => setActiveTab('points')}
            className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
              activeTab === 'points'
                ? 'bg-white text-orange-600 border-t border-l border-r border-gray-200'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center">
              <Award className="h-4 w-4 mr-2 text-orange-500" />
              Point System
            </div>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column - Website Information */}
        {activeTab === 'basic' && (
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
              <Globe className="h-5 w-5 text-orange-500 mr-2" />
              Website Information
            </h2>

            {/* Site Title */}
            <div className="mb-6">
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Info className="h-4 w-4 text-orange-500 mr-2" />
                Site Title
              </label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="engave"
                defaultValue="engave"
              />
              <p className="text-xs text-gray-500 mt-1">Your website general title, it will appear on Google and on your browser tab.</p>
            </div>

            {/* Site Name */}
            <div className="mb-6">
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Info className="h-4 w-4 text-orange-500 mr-2" />
                Site Name
              </label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="engave"
                defaultValue="engave"
              />
              <p className="text-xs text-gray-500 mt-1">Your website name, it will appear on website's footer and E-mails.</p>
            </div>

            {/* Site Keywords */}
            <div className="mb-6">
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <FileText className="h-4 w-4 text-orange-500 mr-2" />
                Site Keywords
              </label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="interactive video, interactive experience"
                defaultValue="interactive video, interactive experience"
              />
              <p className="text-xs text-gray-500 mt-1">Your website's keyword, used mostly for SEO and search engines.</p>
            </div>

            {/* Site Description */}
            <div className="mb-6">
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <FileText className="h-4 w-4 text-orange-500 mr-2" />
                Site Description
              </label>
              <textarea
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent h-24"
                placeholder="engave allows you to make and experience interactive videos. Let's go interactive!"
                defaultValue="engave allows you to make and experience interactive videos. Let's go interactive!"
              ></textarea>
              <p className="text-xs text-gray-500 mt-1">Your website's description, used mostly for SEO and search engines. Max of 100 characters is recommended.</p>
            </div>
          </div>
        )}

        {/* Right Column - Features API Keys & Information */}
        {activeTab === 'basic' && (
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
              <Key className="h-5 w-5 text-orange-500 mr-2" />
              Features API Keys & Information
            </h2>

            {/* Google Analytics Code / Custom HTML Code */}
            <div className="mb-6">
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Code className="h-4 w-4 text-orange-500 mr-2" />
                Google Analytics Code / Custom HTML Code
              </label>
              <textarea
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent h-32 font-mono text-sm"
                placeholder="<!-- Paste your Google Analytics or custom HTML code here -->"
              ></textarea>
            </div>

            {/* Google vignette code */}
            <div className="mb-6">
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Code className="h-4 w-4 text-orange-500 mr-2" />
                Google vignette code
              </label>
              <textarea
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent h-32 font-mono text-sm"
                placeholder="<!-- Paste your Google Vignette code here -->"
              ></textarea>
            </div>

            {/* Extreme IP Lookup Key */}
            <div className="mb-6">
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Key className="h-4 w-4 text-orange-500 mr-2" />
                Extreme IP Lookup Key
              </label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="Enter your Extreme IP Lookup API key"
              />
              <p className="text-xs text-gray-500 mt-1">Extreme IP Lookup Key to get user continent for geo blocking.</p>
            </div>
          </div>
        )}

        {/* API & Integration Tab Content */}
        {activeTab === 'api' && (
          <div className="col-span-1 md:col-span-2 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
              <Key className="h-5 w-5 text-orange-500 mr-2" />
              API Keys & Integration
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                {/* Google Analytics Code */}
                <div className="mb-6">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                    <Code className="h-4 w-4 text-orange-500 mr-2" />
                    Google Analytics Code / Custom HTML Code
                  </label>
                  <textarea
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent h-32 font-mono text-sm"
                    placeholder="<!-- Paste your Google Analytics or custom HTML code here -->"
                  ></textarea>
                  <p className="text-xs text-gray-500 mt-1">Add your Google Analytics tracking code or any custom HTML code to be included in the site header.</p>
                </div>

                {/* Google Vignette Code */}
                <div className="mb-6">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                    <Code className="h-4 w-4 text-orange-500 mr-2" />
                    Google Vignette Code
                  </label>
                  <textarea
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent h-32 font-mono text-sm"
                    placeholder="<!-- Paste your Google Vignette code here -->"
                  ></textarea>
                  <p className="text-xs text-gray-500 mt-1">Add your Google Vignette code for mobile ad implementation.</p>
                </div>
              </div>

              <div>
                {/* Extreme IP Lookup Key */}
                <div className="mb-6">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                    <Key className="h-4 w-4 text-orange-500 mr-2" />
                    Extreme IP Lookup Key
                  </label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="Enter your Extreme IP Lookup API key"
                  />
                  <p className="text-xs text-gray-500 mt-1">Extreme IP Lookup Key to get user continent for geo blocking.</p>
                </div>

                {/* Bhashini API Configuration */}
                <div className="mb-6">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                    <Key className="h-4 w-4 text-orange-500 mr-2" />
                    Bhashini API Configuration
                  </label>
                  <div className="space-y-3">
                    <div>
                      <label className="text-xs text-gray-600 mb-1 block">User ID</label>
                      <input
                        type="text"
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        value="cee60134c6bb4d179efd3fda48ff32fe"
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="text-xs text-gray-600 mb-1 block">ULCA API Key</label>
                      <input
                        type="password"
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        value="13a647c84b-2747-4f0c-afcd-2ac8235f5318"
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="text-xs text-gray-600 mb-1 block">API Endpoint</label>
                      <input
                        type="text"
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        value="https://bhashini.gov.in/api/v1/inference/translation"
                        readOnly
                      />
                    </div>
                    <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                      <p className="text-sm text-green-800">
                        Bhashini API is configured and ready to use. You can now translate messages in Indian languages.
                      </p>
                    </div>
                    <button
                      className="w-full bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200"
                      onClick={() => {
                        alert('Bhashini API test successful!');
                      }}
                    >
                      Test Bhashini API
                    </button>
                  </div>
                </div>

                {/* Additional API Keys can be added here */}
                <div className="mb-6">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                    <Key className="h-4 w-4 text-orange-500 mr-2" />
                    Additional API Configuration
                  </label>
                  <div className="p-4 bg-orange-50 rounded-md text-sm text-orange-700">
                    <p>You can configure additional API integrations here. Contact support for more information about available integrations.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Point System Tab Content */}
      {activeTab === 'points' && (
        <div className="col-span-1 md:col-span-2 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h2 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
            <Award className="h-5 w-5 text-orange-500 mr-2" />
            Point System Settings
          </h2>

          <div className="space-y-6">
            {/* Point System Toggle */}
            <div className="flex justify-between items-center p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
              <div className="flex items-start">
                <Award className="h-5 w-5 text-orange-500 mr-2 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Point System</h3>
                  <p className="text-xs text-gray-500 mt-1">Gives the ability for users to earn points from liking, sharing, commenting and posting.</p>
                </div>
              </div>
              <div className="relative">
                <div className="flex items-center gap-2">
                  <button className="bg-orange-100 hover:bg-orange-200 text-orange-700 px-2 py-1 rounded text-xs">
                    <span className="flex items-center gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M12 20h9"></path>
                        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                      </svg>
                      Who can use this feature?
                    </span>
                  </button>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                  </label>
                </div>
              </div>
            </div>

            {/* Allow Withdrawal */}
            <div className="flex justify-between items-center p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
              <div>
                <h3 className="font-medium text-gray-900">Allow user to withdrawal earned points as currency?</h3>
                <p className="text-xs text-gray-500 mt-1">Allow users to transfer earned points into money and withdrawal.</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
              </label>
            </div>

            {/* Point Value */}
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
              <div className="mb-2">
                <h3 className="font-medium text-gray-900">$1.00 = ? Point</h3>
              </div>
              <input
                type="number"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="100"
                defaultValue="100"
              />
              <p className="text-xs text-gray-500 mt-1">How much does 1 dollar equal in points?</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-6">
                {/* Commenting on Videos */}
                <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
                  <div className="mb-2">
                    <h3 className="font-medium text-gray-900 flex items-center">
                      <MessageSquare className="h-4 w-4 text-orange-500 mr-2" />
                      Commenting on Videos
                    </h3>
                  </div>
                  <input
                    type="number"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="10"
                    defaultValue="10"
                  />
                  <p className="text-xs text-gray-500 mt-1">How many points does a user earn by creating comments?</p>
                </div>

                {/* Liking Videos */}
                <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
                  <div className="mb-2">
                    <h3 className="font-medium text-gray-900 flex items-center">
                      <ThumbsUp className="h-4 w-4 text-orange-500 mr-2" />
                      Liking Videos
                    </h3>
                  </div>
                  <input
                    type="number"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="5"
                    defaultValue="5"
                  />
                  <p className="text-xs text-gray-500 mt-1">How many points does a user earn by liking videos?</p>
                </div>

                {/* Disliking Videos */}
                <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
                  <div className="mb-2">
                    <h3 className="font-medium text-gray-900 flex items-center">
                      <ThumbsDown className="h-4 w-4 text-orange-500 mr-2" />
                      Disliking Videos
                    </h3>
                  </div>
                  <input
                    type="number"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="2"
                    defaultValue="2"
                  />
                  <p className="text-xs text-gray-500 mt-1">How many points does a user earn by disliking videos?</p>
                </div>
              </div>

              <div className="space-y-6">
                {/* Watching Videos */}
                <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
                  <div className="mb-2">
                    <h3 className="font-medium text-gray-900 flex items-center">
                      <Play className="h-4 w-4 text-orange-500 mr-2" />
                      Watching Videos
                    </h3>
                  </div>
                  <input
                    type="number"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="2"
                    defaultValue="2"
                  />
                  <p className="text-xs text-gray-500 mt-1">How many points does a user earn by watching videos?</p>
                </div>

                {/* Uploading Videos */}
                <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
                  <div className="mb-2">
                    <h3 className="font-medium text-gray-900 flex items-center">
                      <Upload className="h-4 w-4 text-orange-500 mr-2" />
                      Uploading Videos
                    </h3>
                  </div>
                  <input
                    type="number"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="20"
                    defaultValue="20"
                  />
                  <p className="text-xs text-gray-500 mt-1">How many points does a user earn by uploading videos?</p>
                </div>

                {/* Free Users Daily Limit */}
                <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
                  <div className="mb-2">
                    <h3 className="font-medium text-gray-900 flex items-center">
                      <Users className="h-4 w-4 text-orange-500 mr-2" />
                      Free Users Daily Limit
                    </h3>
                  </div>
                  <input
                    type="number"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="1000"
                    defaultValue="1000"
                  />
                  <p className="text-xs text-gray-500 mt-1">How many points can a free user earn in a day?</p>
                </div>
              </div>
            </div>

            {/* Pro Members Daily Limit */}
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
              <div className="mb-2">
                <h3 className="font-medium text-gray-900 flex items-center">
                  <Crown className="h-4 w-4 text-orange-500 mr-2" />
                  Pro Members Daily Limit
                </h3>
              </div>
              <input
                type="number"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="2000"
                defaultValue="2000"
              />
              <p className="text-xs text-gray-500 mt-1">How many points can a pro user earn in a day?</p>
            </div>

            {/* AdMob */}
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 hover:bg-white transition-colors duration-200">
              <div className="mb-2">
                <h3 className="font-medium text-gray-900">AdMob</h3>
              </div>
              <input
                type="number"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="10"
                defaultValue="10"
              />
              <p className="text-xs text-gray-500 mt-1">How many points does a user earn by AdMob?</p>
            </div>
          </div>
        </div>
      )}

      {/* Save Button */}
      <div className="flex justify-end mt-6">
        <button
          className="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-6 rounded-md transition-colors duration-200"
          onClick={handleSaveChanges}
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  );
}
