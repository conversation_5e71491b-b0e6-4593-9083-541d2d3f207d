import React, { useState } from 'react';
import UuidVideoPlayer from '@/components/video/UuidVideoPlayer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';

/**
 * A test page for playing videos with UUID format IDs
 */
export default function TestVideoPage() {
  const [videoId, setVideoId] = useState('8f23eb35-db65-44cb-906d-24356f2edaf2');
  const [currentVideoId, setCurrentVideoId] = useState(videoId);

  const handlePlay = () => {
    setCurrentVideoId(videoId);
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">UUID Video Test Page</h1>
      
      <div className="grid grid-cols-1 gap-8 max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Video Player</CardTitle>
            <CardDescription>
              Test playing videos with UUID format IDs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="aspect-video bg-black rounded-lg overflow-hidden mb-4">
              <UuidVideoPlayer videoId={currentVideoId} />
            </div>
            
            <div className="flex flex-col space-y-4">
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="video-id">Video ID (UUID format)</Label>
                <Input
                  id="video-id"
                  value={videoId}
                  onChange={(e) => setVideoId(e.target.value)}
                  placeholder="Enter UUID format video ID"
                  className="w-full"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setVideoId('8f23eb35-db65-44cb-906d-24356f2edaf2')}>
              Reset to Default
            </Button>
            <Button onClick={handlePlay}>Play Video</Button>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Test Videos</CardTitle>
            <CardDescription>
              Click on any of these test videos to play them
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button 
                variant="outline" 
                onClick={() => {
                  setVideoId('8f23eb35-db65-44cb-906d-24356f2edaf2');
                  setCurrentVideoId('8f23eb35-db65-44cb-906d-24356f2edaf2');
                }}
              >
                Test UUID 1
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setVideoId('4f6c268e-8f71-4615-bbcb-1d333823690e');
                  setCurrentVideoId('4f6c268e-8f71-4615-bbcb-1d333823690e');
                }}
              >
                Test UUID 2
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setVideoId('wollzl');
                  setCurrentVideoId('wollzl');
                }}
              >
                Known Working ID
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setVideoId('XLcMq2');
                  setCurrentVideoId('XLcMq2');
                }}
              >
                Another Working ID
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
