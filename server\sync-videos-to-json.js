/**
 * <PERSON>ript to sync all existing videos from database to JSON storage
 * This script should be run once to initialize the JSON storage with existing videos
 */

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/lawengaxe';

// JSON storage paths
const JSON_DIR = path.join(__dirname, 'json', 'videos');
const ALL_VIDEOS_FILE = path.join(JSON_DIR, 'all-videos.json');

/**
 * Connect to MongoDB
 */
async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

// Define video schema for direct database access
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  tags: [String],
  category: String,
  contentRating: String,
  processingStatus: String,
  stats: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    playlistAdds: { type: Number, default: 0 },
    averageWatchTime: { type: Number, default: 0 },
    retentionRate: { type: Number, default: 0 }
  },
  file: {
    originalName: String,
    size: Number,
    mimeType: String
  },
  languages: [{
    code: String,
    name: String,
    flag: String,
    url: String,
    isDefault: Boolean
  }],
  source: {
    type: String,
    originalUrl: String,
    platform: String,
    externalId: String
  },
  createdAt: Date,
  updatedAt: Date,
  deletedAt: Date
}, { collection: 'videos' });

/**
 * Format video for JSON storage
 */
function formatVideoForStorage(video) {
  // Generate search keywords
  const keywords = new Set();

  if (video.title) {
    video.title.toLowerCase().split(/\s+/).forEach(word => {
      if (word.length > 2) keywords.add(word);
    });
  }

  if (video.description) {
    video.description.toLowerCase().split(/\s+/).forEach(word => {
      if (word.length > 2) keywords.add(word);
    });
  }

  if (video.tags && Array.isArray(video.tags)) {
    video.tags.forEach(tag => {
      keywords.add(tag.toLowerCase());
    });
  }

  if (video.category) {
    keywords.add(video.category.toLowerCase());
  }

  return {
    id: video.id,
    title: video.title,
    description: video.description,
    url: video.url,
    thumbnailUrl: video.thumbnailUrl,
    duration: video.duration,
    userId: video.userId,
    channelId: video.channelId,
    visibility: video.visibility,
    tags: video.tags || [],
    category: video.category,
    contentRating: video.contentRating,
    processingStatus: video.processingStatus,
    stats: video.stats || {
      views: 0,
      likes: 0,
      dislikes: 0,
      comments: 0
    },
    file: video.file || {},
    languages: video.languages || [],
    source: video.source || {},
    createdAt: video.createdAt,
    updatedAt: video.updatedAt,
    searchKeywords: Array.from(keywords),
    watchUrl: `http://localhost:5173/watch?id=${video.id}`
  };
}

/**
 * Sync all videos to JSON storage
 */
async function syncVideosToJson() {
  try {
    console.log('🔄 Starting video sync to JSON storage...');

    // Create video model
    const VideoModel = mongoose.model('Video', VideoSchema);

    // Get all videos from database
    const videos = await VideoModel.find({ deletedAt: null }).lean();
    console.log(`📊 Found ${videos.length} videos in database`);

    if (videos.length === 0) {
      console.log('ℹ️ No videos found in database');
      return;
    }

    // Format videos for storage
    const formattedVideos = videos.map(formatVideoForStorage);

    // Create JSON data structure
    const jsonData = {
      metadata: {
        totalVideos: formattedVideos.length,
        lastUpdated: new Date().toISOString(),
        version: '1.0.0',
        syncedAt: new Date().toISOString(),
        description: 'All videos from LawEngaxe platform automatically synced from database'
      },
      videos: formattedVideos
    };

    // Create backup if file exists
    if (fs.existsSync(ALL_VIDEOS_FILE)) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFile = path.join(JSON_DIR, 'backups', `all-videos-backup-${timestamp}.json`);
      fs.copyFileSync(ALL_VIDEOS_FILE, backupFile);
      console.log(`📦 Created backup: ${backupFile}`);
    }

    // Write to JSON file
    fs.writeFileSync(ALL_VIDEOS_FILE, JSON.stringify(jsonData, null, 2));
    console.log('✅ Successfully synced all videos to JSON storage');

    // Display statistics
    const categories = {};
    const visibility = {};

    formattedVideos.forEach(video => {
      if (video.category) {
        categories[video.category] = (categories[video.category] || 0) + 1;
      }
      if (video.visibility) {
        visibility[video.visibility] = (visibility[video.visibility] || 0) + 1;
      }
    });

    console.log('📈 JSON Storage Statistics:');
    console.log(`   Total Videos: ${formattedVideos.length}`);
    console.log(`   Last Updated: ${jsonData.metadata.lastUpdated}`);
    console.log(`   Categories: ${JSON.stringify(categories, null, 2)}`);
    console.log(`   Visibility: ${JSON.stringify(visibility, null, 2)}`);

    // Show sample videos
    console.log('\n📋 Sample Videos:');
    formattedVideos.slice(0, 5).forEach((video, index) => {
      console.log(`   ${index + 1}. ${video.title} (${video.id})`);
      console.log(`      URL: ${video.watchUrl}`);
      console.log(`      Keywords: ${video.searchKeywords.slice(0, 5).join(', ')}`);
    });

  } catch (error) {
    console.error('❌ Error syncing videos to JSON storage:', error);
    throw error;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🚀 Video JSON Storage Sync Tool');
    console.log('================================');
    
    await connectToDatabase();
    await syncVideosToJson();
    
    console.log('✅ Sync completed successfully');
    
  } catch (error) {
    console.error('❌ Sync failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { syncVideosToJson };
