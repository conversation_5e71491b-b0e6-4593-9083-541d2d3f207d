/**
 * Utility functions for the video player
 */

/**
 * Initialize video player fullscreen functionality
 * This function adds event listeners to handle H5P fullscreen buttons in the video player
 */
export const initVideoPlayerFullscreen = (): void => {
  // Function to handle fullscreen toggle
  const toggleVideoFullscreen = (container: HTMLElement): void => {
    try {
      if (document.fullscreenElement) {
        // Exit fullscreen
        document.exitFullscreen().catch(err => {
          console.error('Error exiting fullscreen:', err);

          // Try alternative methods if standard method fails
          try {
            if ((document as any).mozCancelFullScreen) {
              (document as any).mozCancelFullScreen();
            } else if ((document as any).webkitExitFullscreen) {
              (document as any).webkitExitFullscreen();
            } else if ((document as any).msExitFullscreen) {
              (document as any).msExitFullscreen();
            }
          } catch (altErr) {
            console.error('Alternative exit fullscreen methods failed:', altErr);
          }
        });
      } else {
        // Prepare container for fullscreen
        container.style.position = 'relative';
        container.style.width = '100%';
        container.style.height = '100%';

        // Enter fullscreen
        const requestFullscreen = container.requestFullscreen ||
                                 (container as any).mozRequestFullScreen ||
                                 (container as any).webkitRequestFullscreen ||
                                 (container as any).msRequestFullscreen;

        if (requestFullscreen) {
          // Try with options if supported
          try {
            if ('navigationUI' in (Element.prototype as any).requestFullscreen.prototype) {
              requestFullscreen.call(container, { navigationUI: 'hide' }).catch(err => {
                console.error('Error entering fullscreen with options:', err);
                // Fallback to standard fullscreen
                requestFullscreen.call(container).catch(err => {
                  console.error('Error entering fullscreen:', err);
                });
              });
            } else {
              requestFullscreen.call(container).catch(err => {
                console.error('Error entering fullscreen:', err);
              });
            }
          } catch (err) {
            console.error('Error with fullscreen options:', err);
            // Fallback to standard fullscreen
            requestFullscreen.call(container).catch(err => {
              console.error('Error entering fullscreen:', err);
            });
          }
        } else {
          console.error('Fullscreen API is not supported in this browser');
        }
      }
    } catch (error) {
      console.error('Error toggling video fullscreen:', error);
    }
  };

  // Add a global click event listener to catch H5P fullscreen button clicks in the video player
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;

    console.log('Video player click detected on:', target);

    // Check if the clicked element is an H5P fullscreen button
    // Use a more comprehensive check to catch all possible fullscreen buttons
    if (
      target &&
      (
        // Check for aria-label containing "fullscreen"
        (target.hasAttribute('aria-label') &&
         target.getAttribute('aria-label')?.toLowerCase().includes('fullscreen')) ||

        // Check for the specific h5p-control h5p-fullscreen button
        (target.classList.contains('h5p-control') &&
         target.classList.contains('h5p-fullscreen')) ||

        // Check for role="button" and tabindex="0" attributes
        (target.getAttribute('role') === 'button' &&
         target.getAttribute('tabindex') === '0' &&
         (
           // Either has fullscreen in aria-label
           (target.hasAttribute('aria-label') &&
            target.getAttribute('aria-label')?.toLowerCase().includes('fullscreen')) ||
           // Or has the h5p-fullscreen class
           target.classList.contains('h5p-fullscreen')
         ))
      )
    ) {
      console.log('H5P fullscreen button click detected in video player');

      // Find the container element - try multiple approaches
      let container = document.getElementById('engaxe-player-container');

      // If not found, try other selectors
      if (!container) {
        container = target.closest('.h5p-iframe-wrapper') ||
                   target.closest('.h5p-content') ||
                   target.closest('.video-player') ||
                   target.closest('.h5p-video-container') ||
                   document.querySelector('#video-container') as HTMLElement;
      }

      if (container) {
        console.log('Found container for fullscreen:', container);

        // Prevent default behavior
        event.preventDefault();
        event.stopPropagation();

        // Toggle fullscreen
        toggleVideoFullscreen(container);
      } else {
        console.error('No suitable container found for fullscreen');
      }
    }
  }, true);

  // Also add a specific handler for the exact button mentioned in the request
  const setupSpecificFullscreenButton = () => {
    const specificButton = document.querySelector('div[role="button"][tabindex="0"].h5p-control.h5p-fullscreen');

    if (specificButton) {
      console.log('Found specific H5P fullscreen button:', specificButton);

      // Remove any existing click listeners to avoid duplicates
      specificButton.removeEventListener('click', handleSpecificButtonClick);

      // Add our click handler
      specificButton.addEventListener('click', handleSpecificButtonClick);

      // Make sure it's visible and clickable
      (specificButton as HTMLElement).style.pointerEvents = 'auto';
      (specificButton as HTMLElement).style.cursor = 'pointer';
      (specificButton as HTMLElement).style.zIndex = '9999';
    } else {
      // If not found, try again after a short delay
      setTimeout(setupSpecificFullscreenButton, 500);
    }
  };

  // Handler for the specific button
  const handleSpecificButtonClick = (event: Event) => {
    console.log('Specific H5P fullscreen button clicked');

    // Prevent default behavior
    event.preventDefault();
    event.stopPropagation();

    // Find the container
    const container = document.getElementById('engaxe-player-container') ||
                     document.querySelector('#video-container') as HTMLElement;

    if (container) {
      // Toggle fullscreen
      toggleVideoFullscreen(container);
    }
  };

  // Initial setup
  setupSpecificFullscreenButton();

  // Set up a mutation observer to detect when the button is added to the DOM
  const observer = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any of the added nodes contain the specific button
        for (let i = 0; i < mutation.addedNodes.length; i++) {
          const node = mutation.addedNodes[i];
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;
            if (element.querySelector('div[role="button"][tabindex="0"].h5p-control.h5p-fullscreen')) {
              // If found, set it up
              setupSpecificFullscreenButton();
              break;
            }
          }
        }
      }
    }
  });

  // Start observing the document body for changes
  observer.observe(document.body, { childList: true, subtree: true });
};
