import { ConversationModel, IConversation, UserModel } from '../models';
import { createNotFoundError, createBadRequestError } from '../utils/errors/AppError';
import { ErrorCodes } from '../utils/errors/errorCodes';

/**
 * Service for managing conversations
 */
class ConversationService {
  /**
   * Get all conversations for a user (both as user and as creator)
   */
  async getConversationsForUser(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      sort?: string;
      status?: 'active' | 'archived' | 'closed';
      isPinned?: boolean;
      search?: string;
    } = {}
  ) {
    const {
      page = 1,
      limit = 10,
      sort = '-lastMessageAt',
      status = 'active',
      isPinned,
      search,
    } = options;

    // Build query to find conversations where the user is either the user or the creator
    const query: any = {
      $or: [
        { userId: userId },
        { creatorId: userId }
      ],
      status,
      deletedAt: null,
    };

    // Add pinned filter if specified
    if (isPinned !== undefined) {
      // Check if pinned by user or creator depending on role
      query.$or = [
        { userId: userId, isPinnedByUser: isPinned },
        { creatorId: userId, isPinnedByCreator: isPinned }
      ];
    }

    // Add search filter if specified
    if (search) {
      query.subject = { $regex: search, $options: 'i' };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await ConversationModel.countDocuments(query);

    // Get conversations
    const conversations = await ConversationModel.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get both creator and user details for each conversation
    const conversationsWithDetails = await Promise.all(
      conversations.map(async (conversation) => {
        // Get creator details
        const creator = await UserModel.findOne(
          { id: conversation.creatorId, deletedAt: null },
          'id username displayName avatar'
        ).lean();

        // Get user details
        const user = await UserModel.findOne(
          { id: conversation.userId, deletedAt: null },
          'id username displayName avatar'
        ).lean();

        // Ensure metadata has all required fields
        if (!conversation.metadata) {
          conversation.metadata = {};
        }

        if (!conversation.metadata.tags) {
          conversation.metadata.tags = [];
        }

        if (!conversation.metadata.customFields) {
          conversation.metadata.customFields = {};
        }

        // Create default objects if not found
        const creatorObj = creator || {
          id: conversation.creatorId,
          username: 'Unknown Creator',
          displayName: 'Unknown Creator',
          avatar: null
        };

        const userObj = user || {
          id: conversation.userId,
          username: 'Unknown User',
          displayName: 'Unknown User',
          avatar: null
        };

        return {
          ...conversation,
          creator: {
            ...creatorObj,
            presence: {
              isOnline: false,
              lastActiveAt: new Date().toISOString()
            }
          },
          user: {
            ...userObj,
            presence: {
              isOnline: false,
              lastActiveAt: new Date().toISOString()
            }
          }
        };
      })
    );

    return {
      data: conversationsWithDetails,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get all conversations for a creator
   */
  async getConversationsForCreator(
    creatorId: string,
    options: {
      page?: number;
      limit?: number;
      sort?: string;
      status?: 'active' | 'archived' | 'closed';
      isPinned?: boolean;
      search?: string;
    } = {}
  ) {
    const {
      page = 1,
      limit = 10,
      sort = '-lastMessageAt',
      status = 'active',
      isPinned,
      search,
    } = options;

    // Build query
    const query: any = {
      creatorId,
      status,
      deletedAt: null,
    };

    // Add pinned filter if specified
    if (isPinned !== undefined) {
      query.isPinnedByCreator = isPinned;
    }

    // Add search filter if specified
    if (search) {
      query.subject = { $regex: search, $options: 'i' };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await ConversationModel.countDocuments(query);

    // Get conversations
    const conversations = await ConversationModel.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get user details for each conversation
    const conversationsWithUsers = await Promise.all(
      conversations.map(async (conversation) => {
        const user = await UserModel.findOne(
          { id: conversation.userId, deletedAt: null },
          'id username displayName avatar'
        ).lean();

        // Ensure metadata has all required fields
        if (!conversation.metadata) {
          conversation.metadata = {};
        }

        if (!conversation.metadata.tags) {
          conversation.metadata.tags = [];
        }

        if (!conversation.metadata.customFields) {
          conversation.metadata.customFields = {};
        }

        // Create a default user object if not found
        const userObj = user || {
          id: conversation.userId,
          username: 'Unknown User',
          displayName: 'Unknown User',
          avatar: null
        };

        return {
          ...conversation,
          user: {
            ...userObj,
            presence: {
              isOnline: false,
              lastActiveAt: new Date().toISOString()
            }
          },
        };
      })
    );

    return {
      data: conversationsWithUsers,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get a conversation by ID
   */
  async getConversationById(conversationId: string, userId: string) {
    // Get conversation
    const conversation = await ConversationModel.findOne({
      id: conversationId,
      deletedAt: null,
      $or: [{ userId }, { creatorId: userId }],
    }).lean();

    if (!conversation) {
      throw createNotFoundError('Conversation not found', ErrorCodes.CONVERSATION_NOT_FOUND);
    }

    // Ensure metadata has all required fields
    if (!conversation.metadata) {
      conversation.metadata = {};
    }

    if (!conversation.metadata.tags) {
      conversation.metadata.tags = [];
    }

    if (!conversation.metadata.customFields) {
      conversation.metadata.customFields = {};
    }

    // Get creator details
    const creator = await UserModel.findOne(
      { id: conversation.creatorId, deletedAt: null },
      'id username displayName avatar'
    ).lean();

    // Get user details
    const user = await UserModel.findOne(
      { id: conversation.userId, deletedAt: null },
      'id username displayName avatar'
    ).lean();

    // Create default objects if not found
    const creatorObj = creator || {
      id: conversation.creatorId,
      username: 'Unknown Creator',
      displayName: 'Unknown Creator',
      avatar: null
    };

    const userObj = user || {
      id: conversation.userId,
      username: 'Unknown User',
      displayName: 'Unknown User',
      avatar: null
    };

    // Get other participant details
    const otherParticipantId =
      conversation.userId === userId ? conversation.creatorId : conversation.userId;
    const otherParticipant = await UserModel.findOne(
      { id: otherParticipantId, deletedAt: null },
      'id username displayName avatar'
    ).lean();

    // Create default other participant if not found
    const otherParticipantObj = otherParticipant || {
      id: otherParticipantId,
      username: otherParticipantId === conversation.creatorId ? 'Unknown Creator' : 'Unknown User',
      displayName: otherParticipantId === conversation.creatorId ? 'Unknown Creator' : 'Unknown User',
      avatar: null
    };

    return {
      ...conversation,
      creator: {
        ...creatorObj,
        presence: {
          isOnline: false,
          lastActiveAt: new Date().toISOString()
        }
      },
      user: {
        ...userObj,
        presence: {
          isOnline: false,
          lastActiveAt: new Date().toISOString()
        }
      },
      otherParticipant: {
        ...otherParticipantObj,
        presence: {
          isOnline: false,
          lastActiveAt: new Date().toISOString()
        }
      },
    };
  }

  /**
   * Create a new conversation
   */
  async createConversation(data: {
    creatorId: string;
    userId: string;
    subject: string;
    userLanguage?: string;
    creatorLanguage?: string;
    settings?: {
      notifications?: boolean;
      readReceipts?: boolean;
    };
    metadata?: {
      tags?: string[];
      customFields?: Record<string, any>;
    };
  }) {
    // Validate creator exists
    const creator = await UserModel.findOne({
      id: data.creatorId,
      deletedAt: null,
    });

    if (!creator) {
      throw createNotFoundError('Creator not found', ErrorCodes.USER_NOT_FOUND);
    }

    // Validate user exists
    const user = await UserModel.findOne({
      id: data.userId,
      deletedAt: null,
    });

    if (!user) {
      throw createNotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);
    }

    // Check if conversation already exists
    const existingConversation = await ConversationModel.findOne({
      creatorId: data.creatorId,
      userId: data.userId,
      status: 'active',
      deletedAt: null,
    });

    if (existingConversation) {
      // Return existing conversation with user and creator details
      const conversationObj = existingConversation.toObject();

      // Ensure metadata has all required fields
      if (!conversationObj.metadata) {
        conversationObj.metadata = {};
      }

      if (!conversationObj.metadata.tags) {
        conversationObj.metadata.tags = [];
      }

      if (!conversationObj.metadata.customFields) {
        conversationObj.metadata.customFields = {};
      }

      // Add user and creator details
      return {
        ...conversationObj,
        user: {
          id: user.id,
          username: user.username,
          displayName: user.displayName || user.username,
          avatar: user.avatar,
          presence: {
            isOnline: false,
            lastActiveAt: new Date().toISOString()
          }
        },
        creator: {
          id: creator.id,
          username: creator.username,
          displayName: creator.displayName || creator.username,
          avatar: creator.avatar,
          presence: {
            isOnline: false,
            lastActiveAt: new Date().toISOString()
          }
        }
      };
    }

    // Create new conversation with all required fields
    const conversation = new ConversationModel({
      creatorId: data.creatorId,
      userId: data.userId,
      subject: data.subject,
      userLanguage: data.userLanguage || user.language || 'en',
      creatorLanguage: data.creatorLanguage || creator.language || 'en',
      settings: {
        notifications: data.settings?.notifications !== undefined ? data.settings.notifications : true,
        readReceipts: data.settings?.readReceipts !== undefined ? data.settings.readReceipts : true,
      },
      // Ensure metadata has all required fields
      metadata: {
        tags: data.metadata?.tags || [],
        customFields: data.metadata?.customFields || {},
      },
      // Add required fields from base schema
      createdBy: data.userId, // Use the user ID as the creator of the conversation
      updatedBy: data.userId, // Use the user ID as the updater of the conversation
    });

    // Save conversation
    await conversation.save();

    // Return conversation with user and creator details
    const conversationObj = conversation.toObject();

    // Ensure metadata has all required fields
    if (!conversationObj.metadata) {
      conversationObj.metadata = {};
    }

    if (!conversationObj.metadata.tags) {
      conversationObj.metadata.tags = [];
    }

    if (!conversationObj.metadata.customFields) {
      conversationObj.metadata.customFields = {};
    }

    // Add user and creator details
    return {
      ...conversationObj,
      user: {
        id: user.id,
        username: user.username,
        displayName: user.displayName || user.username,
        avatar: user.avatar,
        presence: {
          isOnline: false,
          lastActiveAt: new Date().toISOString()
        }
      },
      creator: {
        id: creator.id,
        username: creator.username,
        displayName: creator.displayName || creator.username,
        avatar: creator.avatar,
        presence: {
          isOnline: false,
          lastActiveAt: new Date().toISOString()
        }
      }
    };
  }

  /**
   * Update a conversation
   */
  async updateConversation(
    conversationId: string,
    userId: string,
    data: {
      subject?: string;
      status?: 'active' | 'archived' | 'closed';
      isPinned?: boolean;
      settings?: {
        notifications?: boolean;
        readReceipts?: boolean;
      };
      metadata?: {
        tags?: string[];
        customFields?: Record<string, any>;
      };
    }
  ) {
    // Get conversation
    const conversation = await ConversationModel.findOne({
      id: conversationId,
      deletedAt: null,
      $or: [{ userId }, { creatorId: userId }],
    });

    if (!conversation) {
      throw createNotFoundError('Conversation not found', ErrorCodes.CONVERSATION_NOT_FOUND);
    }

    // Update fields
    if (data.subject) {
      conversation.subject = data.subject;
    }

    if (data.status) {
      conversation.status = data.status;
    }

    if (data.isPinned !== undefined) {
      if (conversation.userId === userId) {
        conversation.isPinnedByUser = data.isPinned;
      } else {
        conversation.isPinnedByCreator = data.isPinned;
      }
    }

    if (data.settings) {
      if (data.settings.notifications !== undefined) {
        conversation.settings.notifications = data.settings.notifications;
      }

      if (data.settings.readReceipts !== undefined) {
        conversation.settings.readReceipts = data.settings.readReceipts;
      }
    }

    if (data.metadata) {
      if (data.metadata.tags) {
        conversation.metadata.tags = data.metadata.tags;
      }

      if (data.metadata.customFields) {
        conversation.metadata.customFields = {
          ...conversation.metadata.customFields,
          ...data.metadata.customFields,
        };
      }
    }

    // Save conversation
    await conversation.save();

    return conversation;
  }

  /**
   * Delete a conversation (soft delete)
   */
  async deleteConversation(conversationId: string, userId: string) {
    // Get conversation
    const conversation = await ConversationModel.findOne({
      id: conversationId,
      deletedAt: null,
      $or: [{ userId }, { creatorId: userId }],
    });

    if (!conversation) {
      throw createNotFoundError('Conversation not found', ErrorCodes.CONVERSATION_NOT_FOUND);
    }

    // Soft delete
    conversation.deletedAt = new Date();
    await conversation.save();

    return { success: true };
  }

  /**
   * Mark all messages in a conversation as read
   */
  async markConversationAsRead(conversationId: string, userId: string) {
    // Get conversation
    const conversation = await ConversationModel.findOne({
      id: conversationId,
      deletedAt: null,
      $or: [{ userId }, { creatorId: userId }],
    });

    if (!conversation) {
      throw createNotFoundError('Conversation not found', ErrorCodes.CONVERSATION_NOT_FOUND);
    }

    // Update unread count
    if (conversation.userId === userId) {
      conversation.userUnreadCount = 0;
    } else {
      conversation.creatorUnreadCount = 0;
    }

    // Save conversation
    await conversation.save();

    return { success: true };
  }

  /**
   * Update last message timestamp
   */
  async updateLastMessageTimestamp(conversationId: string) {
    await ConversationModel.updateOne(
      { id: conversationId },
      { lastMessageAt: new Date() }
    );
  }

  /**
   * Increment unread count
   */
  async incrementUnreadCount(conversationId: string, recipientId: string) {
    const conversation = await ConversationModel.findOne({
      id: conversationId,
      deletedAt: null,
    });

    if (!conversation) {
      return;
    }

    if (conversation.userId === recipientId) {
      conversation.userUnreadCount += 1;
    } else if (conversation.creatorId === recipientId) {
      conversation.creatorUnreadCount += 1;
    }

    await conversation.save();
  }
}

export const conversationService = new ConversationService();
