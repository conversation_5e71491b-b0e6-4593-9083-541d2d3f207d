import { FastifyRequest, FastifyReply, RouteGenericInterface } from 'fastify';
import { VideoModel } from '../models';
import { AuthenticatedUser } from '../types/user';

// Define a Language interface
interface Language {
  code: string;
  name: string;
  flag?: string;
  isDefault: boolean;
  url: string;
}

// Extend AuthenticatedUser to include isAdmin property
interface ExtendedAuthenticatedUser extends AuthenticatedUser {
  isAdmin?: boolean;
}

// Define a custom request type with authenticated user
interface AuthenticatedRequest<T extends RouteGenericInterface = RouteGenericInterface> extends FastifyRequest<T> {
  user: ExtendedAuthenticatedUser;
}

// Helper function to get a flag emoji for a language code
function getLanguageFlag(code: string): string {
  if (!code) return '🌐';

  switch (code.toLowerCase()) {
    case 'en': return '🇺🇸';
    case 'hi': return '🇮🇳';
    case 'es': return '🇪🇸';
    case 'fr': return '🇫🇷';
    case 'de': return '🇩🇪';
    case 'ja': return '🇯🇵';
    case 'zh': return '🇨🇳';
    case 'ru': return '🇷🇺';
    case 'ar': return '🇸🇦';
    case 'pt': return '🇵🇹';
    case 'it': return '🇮🇹';
    case 'nl': return '🇳🇱';
    case 'ko': return '🇰🇷';
    case 'tr': return '🇹🇷';
    // Add more Indian languages
    case 'bn': return '🇮🇳'; // Bengali
    case 'ta': return '🇮🇳'; // Tamil
    case 'te': return '🇮🇳'; // Telugu
    case 'mr': return '🇮🇳'; // Marathi
    case 'gu': return '🇮🇳'; // Gujarati
    case 'kn': return '🇮🇳'; // Kannada
    case 'ml': return '🇮🇳'; // Malayalam
    case 'pa': return '🇮🇳'; // Punjabi
    case 'or': return '🇮🇳'; // Odia
    case 'as': return '🇮🇳'; // Assamese
    case 'ur': return '🇵🇰'; // Urdu
    default: return '🌐';
  }
}

// Check if a string is a valid Engaxe ID (6-7 alphanumeric characters)
function isValidEngaxeId(id: string): boolean {
  if (!id || typeof id !== 'string') {
    return false;
  }
  return /^[a-zA-Z0-9]{6,7}$/.test(id);
}

export class FixLanguagesController {
  /**
   * Fix languages for all videos in the database
   */
  fixAllLanguages = async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;

      // Check if user is admin
      // In development mode, we'll allow any user to fix languages
      const isDevelopment = process.env.NODE_ENV === 'development';

      if (!isDevelopment && !request.user.isAdmin) {
        return reply.code(403).send({
          success: false,
          message: 'Only admins can fix all video languages',
          error: {
            code: 'FORBIDDEN',
            message: 'Only admins can fix all video languages',
          },
        });
      }

      // If we're in development mode and isAdmin is undefined, log a warning
      if (isDevelopment && request.user.isAdmin === undefined) {
        console.warn('Warning: isAdmin property is undefined. In production, this would fail.');
      }

      console.log(`Admin user ${userId} requested to fix all video languages`);

      // Get all videos
      const videos = await VideoModel.find({});
      console.log(`Found ${videos.length} videos in the database`);

      let updatedCount = 0;
      let skippedCount = 0;
      let errorCount = 0;

      // Process each video
      for (const video of videos) {
        try {
          console.log(`Processing video: ${video.title || 'Untitled'} (ID: ${video.id || 'Unknown ID'})`);

          // Debug log the video object
          console.log('Video object:', JSON.stringify(video, null, 2));

          // Check if the video already has languages
          if (video.languages && Array.isArray(video.languages) && video.languages.length > 0) {
            console.log(`Video already has ${video.languages.length} languages`);

            // Debug log the languages
            console.log('Languages:', JSON.stringify(video.languages, null, 2));

            // Validate existing languages
            let needsUpdate = false;
            const updatedLanguages: Language[] = video.languages.map(lang => {
              // Debug log each language
              console.log('Processing language:', JSON.stringify(lang, null, 2));

              // Create a copy to avoid mutating the original
              // Handle both Mongoose documents and plain objects
              const processedLang = {
                code: typeof lang === 'object' && lang !== null ? (lang.code || '') : '',
                name: typeof lang === 'object' && lang !== null ? (lang.name || '') : '',
                flag: typeof lang === 'object' && lang !== null ? (lang.flag || '') : '',
                isDefault: typeof lang === 'object' && lang !== null ? !!lang.isDefault : false,
                url: typeof lang === 'object' && lang !== null ? (lang.url || '') : ''
              };

              // Ensure language has a code
              if (!processedLang.code) {
                processedLang.code = 'en';
                needsUpdate = true;
                console.log(`Added missing code 'en' to language`);
              }

              // Ensure language has a name
              if (!processedLang.name) {
                processedLang.name = processedLang.code === 'en' ? 'English' : `Language (${processedLang.code})`;
                needsUpdate = true;
                console.log(`Added missing name '${processedLang.name}' to language`);
              }

              // Ensure language has a flag
              if (!processedLang.flag) {
                processedLang.flag = getLanguageFlag(processedLang.code);
                needsUpdate = true;
                console.log(`Added missing flag '${processedLang.flag}' to language ${processedLang.name}`);
              }

              // Ensure language has a URL
              if (!processedLang.url) {
                processedLang.url = video.url || video.id;
                needsUpdate = true;
                console.log(`Added missing URL to language ${processedLang.name}`);
              }

              // Ensure URL is a valid Engaxe ID
              if (!isValidEngaxeId(processedLang.url)) {
                console.log(`Language ${processedLang.name} has invalid URL: ${processedLang.url}`);
                if (isValidEngaxeId(video.url)) {
                  processedLang.url = video.url;
                  needsUpdate = true;
                  console.log(`Updated language URL to video URL: ${video.url}`);
                } else if (isValidEngaxeId(video.id)) {
                  processedLang.url = video.id;
                  needsUpdate = true;
                  console.log(`Updated language URL to video ID: ${video.id}`);
                }
              }

              // Ensure isDefault is a boolean
              if (typeof processedLang.isDefault !== 'boolean') {
                processedLang.isDefault = processedLang.code === 'en';
                needsUpdate = true;
                console.log(`Set isDefault to ${processedLang.isDefault} for language ${processedLang.name}`);
              }

              return processedLang;
            });

            // Check if we have at least one default language
            const hasDefaultLanguage = updatedLanguages.some(lang => lang.isDefault);
            if (!hasDefaultLanguage && updatedLanguages.length > 0) {
              updatedLanguages[0].isDefault = true;
              needsUpdate = true;
              console.log(`Set first language ${updatedLanguages[0].name} as default`);
            }

            if (needsUpdate) {
              // Update the video with the fixed languages
              video.languages = updatedLanguages;
              await video.save();
              updatedCount++;
              console.log(`Updated languages for video: ${video.title}`);
            } else {
              skippedCount++;
              console.log(`No updates needed for video: ${video.title}`);
            }
          } else {
            // Video has no languages, add a default English language
            console.log(`Video has no languages, adding default English language`);

            try {
              // Create a default English language with the video URL
              const defaultLanguage: Language = {
                code: 'en',
                name: 'English',
                flag: '🇺🇸',
                isDefault: true,
                url: video.url || video.id || ''
              };

              // Log the default language we're adding
              console.log('Adding default language:', JSON.stringify(defaultLanguage, null, 2));

              // Set the languages array directly
              video.languages = [defaultLanguage];

              // Save the video
              await video.save();
              updatedCount++;
              console.log(`Added default English language to video: ${video.title || 'Untitled'}`);
            } catch (saveErr) {
              const saveErrorMessage = saveErr instanceof Error ? saveErr.message : String(saveErr);
              console.error(`Failed to save video ${video.id || 'Unknown ID'} with default language:`, saveErrorMessage);

              // Log more detailed error information
              console.error('Save error details:', saveErr);

              if (saveErr instanceof Error && saveErr.stack) {
                console.error('Save error stack:', saveErr.stack);
              }

              errorCount++;
            }
          }
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : String(err);
          console.error(`Error processing video ${video.id || 'Unknown ID'}:`, errorMessage);

          // Log more detailed error information
          console.error('Error details:', err);

          if (err instanceof Error && err.stack) {
            console.error('Error stack:', err.stack);
          }

          // Try to save the video with a default language even if there was an error
          try {
            // Create a default English language
            const defaultLanguage: Language = {
              code: 'en',
              name: 'English',
              flag: '🇺🇸',
              isDefault: true,
              url: video.url || video.id || ''
            };

            // Set the languages array directly
            video.languages = [defaultLanguage];

            // Save the video
            await video.save();
            console.log(`Saved video ${video.id || 'Unknown ID'} with default language after error`);
            updatedCount++;
          } catch (saveErr) {
            console.error(`Failed to save video ${video.id || 'Unknown ID'} with default language:`,
              saveErr instanceof Error ? saveErr.message : String(saveErr));
            errorCount++;
          }
        }
      }

      const result = {
        total: videos.length,
        updated: updatedCount,
        skipped: skippedCount,
        errors: errorCount
      };

      console.log('Update complete!');
      console.log(`Total videos: ${videos.length}`);
      console.log(`Updated: ${updatedCount}`);
      console.log(`Skipped: ${skippedCount}`);
      console.log(`Errors: ${errorCount}`);

      return reply.code(200).send({
        success: true,
        message: 'All video languages fixed successfully',
        stats: result,
      });
    } catch (error) {
      console.error('Error fixing all video languages:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      return reply.code(500).send({
        success: false,
        message: 'Error fixing all video languages',
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: errorMessage,
        },
      });
    }
  };
}
