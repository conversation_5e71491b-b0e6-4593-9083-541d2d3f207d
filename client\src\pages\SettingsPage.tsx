import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminNavbar from '@/components/admin/AdminNavbar';
import { Settings as SettingsIcon } from 'lucide-react';
import FeatureUsageModal from '@/components/modals/FeatureUsageModal';
import WebsiteInformation from '@/components/settings/WebsiteInformation';
import EmailSetup from '@/components/settings/EmailSetup';
import SocialLoginSettings from '@/components/settings/SocialLoginSettings';

// Settings sidebar options
const settingsOptions = [
  { id: 'general', label: 'General Configuration' },
  { id: 'website-information', label: 'Website Information' },
  { id: 'email', label: 'E-mail Setup' },
  { id: 'social', label: 'Social Login Settings' },
];

export default function SettingsPage() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeSection, setActiveSection] = useState('general');
  const [activeSettingsTab, setActiveSettingsTab] = useState('general');
  const [isFeatureModalOpen, setIsFeatureModalOpen] = useState(false);
  const [featureModalName, setFeatureModalName] = useState('');

  // Set active section based on URL path
  useEffect(() => {
    const path = location.pathname;
    if (path === '/settings') {
      setActiveSection('general');
    } else if (path.startsWith('/settings/')) {
      const section = path.split('/')[2];
      // Special handling for website-information
      if (section === 'website-information') {
        setActiveSection('website-information');
      } else if (settingsOptions.some(option => option.id === section)) {
        setActiveSection(section);
      }
    }
  }, [location.pathname]);

  // Redirect non-admin users to home page
  useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // If not admin, don't render the settings page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />
      <div className="flex-1 flex flex-col">
        <AdminNavbar />
        <div className="flex-1 bg-gray-100 p-6 overflow-y-auto">
          <FeatureUsageModal isOpen={isFeatureModalOpen} onClose={() => setIsFeatureModalOpen(false)} featureName={featureModalName} />

          {/* Settings Content */}
          <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-6">
            <h1 className="text-xl font-bold mb-6 text-black">{settingsOptions.find(o => o.id === activeSection)?.label}</h1>

            {(activeSection === 'website' || activeSection === 'website-information') && (
              <WebsiteInformation />
            )}

            {activeSection === 'general' && (
              <div>
                {/* Breadcrumb Navigation */}
                <div className="flex items-center gap-2 text-sm mb-4 bg-orange-50 p-2 rounded">
                  <span className="flex items-center gap-1 text-black font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                      <polyline points="9 22 9 12 15 12 15 22"></polyline>
                    </svg>
                    Admin Panel
                  </span>
                  <span className="text-gray-600">›</span>
                  <span className="text-black">Settings</span>
                  <span className="text-gray-600">›</span>
                  <span className="text-orange-600 font-medium">General Configuration</span>
                </div>

                {/* Tabs */}
                <div className="mb-6 border-b border-gray-200">
                  <div className="flex space-x-1">
                    <button
                      onClick={() => setActiveSettingsTab('general')}
                      className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
                        activeSettingsTab === 'general'
                          ? 'bg-white text-orange-600 border-t border-l border-r border-gray-200'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                          <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                          <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        General Configuration
                      </div>
                    </button>
                    <button
                      onClick={() => setActiveSettingsTab('login')}
                      className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
                        activeSettingsTab === 'login'
                          ? 'bg-white text-orange-600 border-t border-l border-r border-gray-200'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                          <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                          <circle cx="9" cy="7" r="4"></circle>
                          <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        Login & Registration
                      </div>
                    </button>
                    <button
                      onClick={() => setActiveSettingsTab('other')}
                      className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
                        activeSettingsTab === 'other'
                          ? 'bg-white text-orange-600 border-t border-l border-r border-gray-200'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                          <circle cx="12" cy="12" r="1"></circle>
                          <circle cx="19" cy="12" r="1"></circle>
                          <circle cx="5" cy="12" r="1"></circle>
                        </svg>
                        Other Settings
                      </div>
                    </button>
                  </div>
                </div>

                {/* Error Alert removed */}

                {/* General Configuration Tab Content */}
                {activeSettingsTab === 'general' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Left Column */}
                    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                      <h2 className="text-lg font-semibold mb-4 text-black flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-orange-500">
                          <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                          <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        General Configuration
                      </h2>

                    {/* Developer Mode */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Developer Mode</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Turn on error reporting so developer can see errors.</p>
                    </div>

                    {/* Developers (API System) */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Developers (API System)</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Show /developers page to all users for API requests.</p>
                    </div>

                    {/* Maintenance Mode */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Maintenance Mode</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Turn the whole site under Maintenance.<br />You can get the site back by visiting https://engaxe.com/login?access=admin</p>
                    </div>

                    {/* SEO Links */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">SEO Links</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Enable SEO links E.g: site.com/this-is-a-video-_ID.html, this will improve your Google Ranking</p>
                    </div>

                    {/* History System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">History System</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Users will be able to view their watched videos.</p>
                    </div>

                    {/* Popular Channels */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Popular Channels</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Show popular channels ranked by most subscribers.</p>
                    </div>

                    {/* Article System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Article System</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Create articles in blog section.</p>
                    </div>

                    {/* Show Articles In Home Page */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Show Articles In Home Page</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Articles will seen in home page.</p>
                    </div>

                    {/* +18 Pop-up */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">+18 Pop-up</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Show +18 Pop-up when user access the site.</p>
                    </div>

                    {/* +18 Block Time */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">+18 Block Time</h3>
                      <input type="number" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="1" />
                      <p className="text-sm text-gray-700">Set the amount of hours to block a user which isn't above 18 years old.</p>
                    </div>

                    {/* Language Modal */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Language Modal</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Show language modal when user access the site.</p>
                    </div>

                    {/* Default Language */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Default Language</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="english">
                        <option value="english">English</option>
                        <option value="spanish">Spanish</option>
                        <option value="french">French</option>
                        <option value="german">German</option>
                        <option value="hindi">Hindi</option>
                      </select>
                      <p className="text-sm text-gray-700">Choose the site default language.</p>
                    </div>

                    {/* Report Copyright */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Report Copyright</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Allow users to create copyright takedown requests.</p>
                    </div>

                    {/* Playlist Subscription */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Playlist Subscription</h3>
                        <div className="flex items-center">
                          <button
                            className="bg-gray-200 hover:bg-gray-300 rounded-md p-1 mr-2"
                            title="Who can use this feature?"
                            onClick={() => {
                              setFeatureModalName('Playlist Subscription');
                              setIsFeatureModalOpen(true);
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M12 20h9"></path>
                              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                            </svg>
                          </button>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" className="sr-only peer" defaultChecked />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                          </label>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700">Allow users to subscribe to playlists</p>
                    </div>

                    {/* Create Post System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Create Post System</h3>
                        <div className="flex items-center">
                          <button
                            className="bg-gray-200 hover:bg-gray-300 rounded-md p-1 mr-2"
                            title="Who can use this feature?"
                            onClick={() => {
                              setFeatureModalName('Create Post System');
                              setIsFeatureModalOpen(true);
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M12 20h9"></path>
                              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                            </svg>
                          </button>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" className="sr-only peer" />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                          </label>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700">Allow users to create post under channel page.</p>
                    </div>

                    {/* Censored Words */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Censored Words</h3>
                      <input type="text" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" placeholder="bad,words,separated,by,comma" />
                      <p className="text-sm text-gray-700">Set censored words, separated by a comma (,)</p>
                    </div>

                    {/* Date Format */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Date Format</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black">
                        <option value="dd mmm yyyy">dd mmmm yyyy</option>
                        <option value="mm/dd/yyyy">mm/dd/yyyy</option>
                        <option value="dd/mm/yyyy">dd/mm/yyyy</option>
                        <option value="yyyy/mm/dd">yyyy/mm/dd</option>
                      </select>
                      <p className="text-sm text-gray-700">Set your site default date format.</p>
                    </div>

                    {/* Default Showen Comments */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-black">Default Showen Comments</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="20">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="30">30</option>
                        <option value="40">40</option>
                        <option value="50">50</option>
                      </select>
                      <p className="text-sm text-black">How many comments to show by default?</p>
                    </div>
                  </div>

                  {/* Right Column */}
                    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                      <h2 className="text-lg font-semibold mb-4 text-black flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-orange-500">
                          <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                          <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                        </svg>
                        Website Settings
                      </h2>

                    {/* Auto Username On Register */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Auto Username On Register</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Generate an auto username on sign up.<br />Registration form will ask for user's first name and last name.</p>
                    </div>

                    {/* Two-Factor Settings */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Two-Factor Settings</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Send confirmation code to email or SMS when user login.</p>
                    </div>

                    {/* Google Authenticator Settings */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Google Authenticator Settings</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Google Authenticator code when user login.</p>
                    </div>

                    {/* Authy Settings */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Authy Settings</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Authy code when user login.</p>
                    </div>

                    {/* Authy Token */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Authy Token</h3>
                      <input type="text" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" placeholder="Enter Authy Token" />
                      <p className="text-sm text-gray-700">Authy Token from your twilio account</p>
                    </div>

                    {/* Password Complexity System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Password Complexity System</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">The system will require a powerful password on sign up, including letters, numbers and special characters.</p>
                    </div>

                    {/* Remember This Device */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Remember This Device</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Remember this device in welcome page.</p>
                    </div>

                    {/* Recaptcha */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Recaptcha</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Enable reCaptcha to prevent spam.</p>
                    </div>

                    {/* Recaptcha Key */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Recaptcha Key</h3>
                      <input type="text" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="6LeiIZcqAAAAGtfRjWyjOK8hgOYCA0Kp_3wyjNy" />
                      <p className="text-sm text-gray-700">Your reCaptcha site key</p>
                    </div>

                    {/* Prevent Bad Login Attempts */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Prevent Bad Login Attempts</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Enable this feature to track and stop brute-force attacks.</p>
                    </div>

                    {/* Login Limit */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Login Limit</h3>
                      <input type="number" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="4" />
                      <p className="text-sm text-gray-700">How many times a user can try to login before a lockout?</p>
                    </div>

                    {/* Lockout Time (In Minutes) */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Lockout Time (In Minutes)</h3>
                      <input type="number" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="10" />
                      <p className="text-sm text-gray-700">For how long should the user stay locked out?</p>
                    </div>

                    <h2 className="text-lg font-semibold mb-4 mt-6 text-black">User Configuration</h2>

                    {/* Delete User Account */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Delete User Account</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Allow users to delete their accounts.</p>
                    </div>

                    {/* User Verification Badge */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">User Verification Badge</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Adding verification badge to users</p>
                    </div>

                    {/* User Block System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">User Block System</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Allow users to block each other.</p>
                    </div>

                    {/* Donation System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Donation System</h3>
                        <div className="flex items-center">
                          <button
                            className="bg-gray-200 hover:bg-gray-300 rounded-md p-1 mr-2"
                            title="Who can use this feature?"
                            onClick={() => {
                              setFeatureModalName('Donation System');
                              setIsFeatureModalOpen(true);
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M12 20h9"></path>
                              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                            </svg>
                          </button>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" className="sr-only peer" />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                          </label>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700">Allow users to donate to channels.</p>
                    </div>

                    {/* User Invite System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">User Invite System</h3>
                        <div className="flex items-center">
                          <button
                            className="bg-gray-200 hover:bg-gray-300 rounded-md p-1 mr-2"
                            title="Who can use this feature?"
                            onClick={() => {
                              setFeatureModalName('User Invite System');
                              setIsFeatureModalOpen(true);
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M12 20h9"></path>
                              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                            </svg>
                          </button>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" className="sr-only peer" />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                          </label>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700">Allow users to invite other users to your site.</p>
                    </div>

                    {/* How many links can a user generate? */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">How many links can a user generate?</h3>
                      <input type="number" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="10" />
                      <p className="text-sm text-gray-700">Set the maximum number of invite links a user can generate.</p>
                    </div>

                    {/* User can generate X links within? */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">User can generate X links within?</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="1 Hour">
                        <option value="1 Hour">1 Hour</option>
                        <option value="2 Hours">2 Hours</option>
                        <option value="12 Hours">12 Hours</option>
                        <option value="1 Day">1 Day</option>
                        <option value="1 Week">1 Week</option>
                        <option value="1 Month">1 Month</option>
                      </select>
                      <p className="text-sm text-gray-700">Set the time period for link generation limits.</p>
                    </div>

                    <h2 className="text-lg font-semibold mb-4 mt-6 text-black">Other Settings</h2>

                    {/* Messaging & Notifications Server */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Messaging & Notifications Server</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="WebSockets">
                        <option value="WebSockets">WebSockets</option>
                        <option value="AJAX">AJAX</option>
                      </select>
                      <p className="text-sm text-gray-700">Choose which server to use, NodeJS or Ajax.</p>
                    </div>

                    {/* Comment System */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Comment System</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="AJAX (PlayTube)">
                        <option value="Both">Both</option>
                        <option value="AJAX (PlayTube)">AJAX (PlayTube)</option>
                        <option value="FaceBook">FaceBook</option>
                      </select>
                      <p className="text-sm text-gray-700">Choose the comment system default provider.</p>
                    </div>
                  </div>
                </div>
                )}

                {/* Login & Registration Tab Content */}
                {activeSettingsTab === 'login' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left Column */}
                  <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <h2 className="text-lg font-semibold mb-4 text-black flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-orange-500">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                      Login & Registration
                    </h2>

                    {/* Auto Username On Register */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Auto Username On Register</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Generate username automatically for new users.</p>
                    </div>

                    {/* Email Confirmation */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Email Confirmation</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Send email confirmation to users when they register.</p>
                    </div>

                    {/* Allow Login With */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Allow Login With</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="Username and Email">
                        <option value="Username">Username</option>
                        <option value="Email">Email</option>
                        <option value="Username and Email">Username and Email</option>
                        <option value="Username, Email and Phone">Username, Email and Phone</option>
                      </select>
                      <p className="text-sm text-gray-700">Choose what users can use to login to your site.</p>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <h2 className="text-lg font-semibold mb-4 text-black flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-orange-500">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                      Security Settings
                    </h2>

                    {/* Brute Force Detection */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Brute Force Detection</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Enable this feature to track and stop brute-force attacks.</p>
                    </div>

                    {/* Max Login Attempts */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Max Login Attempts</h3>
                      <input type="number" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="5" />
                      <p className="text-sm text-gray-700">How many login attempts before user gets locked out?</p>
                    </div>

                    {/* Lockout Time (In Minutes) */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Lockout Time (In Minutes)</h3>
                      <input type="number" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="10" />
                      <p className="text-sm text-gray-700">For how long should the user stay locked out?</p>
                    </div>
                  </div>
                </div>
                )}

                {/* Other Settings Tab Content */}
                {activeSettingsTab === 'other' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left Column */}
                  <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <h2 className="text-lg font-semibold mb-4 text-black flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-orange-500">
                        <circle cx="12" cy="12" r="3"></circle>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                      </svg>
                      User Configuration
                    </h2>

                    {/* How many links can a user generate? */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">How many links can a user generate?</h3>
                      <input type="number" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="10" />
                      <p className="text-sm text-gray-700">Set the maximum number of invite links a user can generate.</p>
                    </div>

                    {/* User can generate X links within? */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">User can generate X links within?</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="1 Hour">
                        <option value="1 Hour">1 Hour</option>
                        <option value="2 Hours">2 Hours</option>
                        <option value="12 Hours">12 Hours</option>
                        <option value="1 Day">1 Day</option>
                        <option value="1 Week">1 Week</option>
                        <option value="1 Month">1 Month</option>
                      </select>
                      <p className="text-sm text-gray-700">Set the time period for link generation limits.</p>
                    </div>

                    {/* Default Showen Comments */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-black">Default Showen Comments</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="20">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="30">30</option>
                        <option value="40">40</option>
                        <option value="50">50</option>
                      </select>
                      <p className="text-sm text-black">How many comments to show by default?</p>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <h2 className="text-lg font-semibold mb-4 text-black flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-orange-500">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                      System Settings
                    </h2>

                    {/* Comment System */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Comment System</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="AJAX (PlayTube)">
                        <option value="Both">Both</option>
                        <option value="AJAX (PlayTube)">AJAX (PlayTube)</option>
                        <option value="FaceBook">FaceBook</option>
                      </select>
                      <p className="text-sm text-gray-700">Choose the comment system default provider.</p>
                    </div>

                    {/* Server Type */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Server Type</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="NodeJS">
                        <option value="NodeJS">NodeJS</option>
                        <option value="AJAX">AJAX</option>
                      </select>
                      <p className="text-sm text-gray-700">Choose which server to use, NodeJS or Ajax.</p>
                    </div>

                    {/* Cache System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Cache System</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Enable caching to improve performance.</p>
                    </div>
                  </div>
                </div>
                )}

                <div className="flex justify-end mt-6">
                  <button
                    className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700"
                    onClick={() => alert('Settings saved successfully!')}
                  >
                    Save Changes
                  </button>
                </div>
              </div>
            )}

            {activeSection === 'email' && <EmailSetup />}

            {activeSection === 'social' && (
              <SocialLoginSettings />
            )}

            {activeSection !== 'general' && activeSection !== 'email' && activeSection !== 'social' && activeSection !== 'website-information' && (
              <div className="py-8 text-center text-gray-500">
                <p>Settings for {settingsOptions.find(o => o.id === activeSection)?.label} will be configured here.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
