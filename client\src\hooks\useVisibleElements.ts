import { useState, useEffect, useRef } from 'react';

/**
 * Hook to track which elements are visible in the viewport
 * @param rootMargin Additional margin around the root element
 * @returns Object with functions to observe and unobserve elements, and a set of visible element IDs
 */
export function useVisibleElements(rootMargin = '0px') {
  const [visibleIds, setVisibleIds] = useState<Set<string>>(new Set());
  const observerRef = useRef<IntersectionObserver | null>(null);
  const elementsMap = useRef<Map<string, HTMLElement>>(new Map());

  // Initialize the intersection observer
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const newVisibleIds = new Set(visibleIds);
        
        entries.forEach((entry) => {
          const id = entry.target.getAttribute('data-message-id');
          if (!id) return;
          
          if (entry.isIntersecting) {
            newVisibleIds.add(id);
          } else {
            newVisibleIds.delete(id);
          }
        });
        
        setVisibleIds(newVisibleIds);
      },
      { rootMargin }
    );

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [rootMargin]);

  // Function to observe an element
  const observeElement = (id: string, element: HTMLElement) => {
    if (!observerRef.current) return;
    
    elementsMap.current.set(id, element);
    observerRef.current.observe(element);
  };

  // Function to unobserve an element
  const unobserveElement = (id: string) => {
    if (!observerRef.current) return;
    
    const element = elementsMap.current.get(id);
    if (element) {
      observerRef.current.unobserve(element);
      elementsMap.current.delete(id);
    }
  };

  return {
    observeElement,
    unobserveElement,
    visibleIds
  };
}
