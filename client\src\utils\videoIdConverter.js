/**
 * Utility functions for converting between hash IDs and Engaxe IDs
 * This version uses localStorage to store and retrieve mappings dynamically
 */

/**
 * Extract Engaxe video ID from a URL or direct ID
 * @param {string} url - Engaxe URL or video ID
 * @returns {string|null} - Video ID or null if not a valid Engaxe URL or ID
 */
export const extractEngaxeVideoId = (url) => {
  if (!url) return null;

  console.log(`Attempting to extract video ID from input: ${url}`);

  // If it's just a video ID (no slashes, dots, or protocol)
  if (!url.includes('/') && !url.includes('.') && !url.includes(':')) {
    // Check for standard Engaxe ID format (6-7 alphanumeric characters)
    if (/^[a-zA-Z0-9]{6,7}$/.test(url)) {
      console.log(`Input appears to be a standard Engaxe ID: ${url}`);
      return url;
    }
    // We only accept 6-7 character Engaxe IDs now
    else {
      console.log(`Input is not a valid 6-7 character Engaxe ID: ${url}`);
      return null;
    }
  }

  // Extract video ID from URL
  if (url.includes('engaxe.com/v/')) {
    const match = url.match(/engaxe\.com\/v\/([a-zA-Z0-9]{6,7})/);
    if (match && match[1]) {
      return match[1];
    }
  } else if (url.includes('engaxe.com/e/')) {
    const match = url.match(/engaxe\.com\/e\/([a-zA-Z0-9]{6,7})/);
    if (match && match[1]) {
      return match[1];
    }
  } else if (url.includes('engaxe.com/watch/')) {
    const match = url.match(/engaxe\.com\/watch\/([a-zA-Z0-9]{6,7})/);
    if (match && match[1]) {
      return match[1];
    }
  }

  // Fallback: Try to extract any 6-7 character alphanumeric ID from a URL path
  if (url.includes('/')) {
    const fallbackMatch = url.match(/\/([a-zA-Z0-9]{6,7})(?:\/|$)/);
    if (fallbackMatch && fallbackMatch[1]) {
      console.log(`Extracted ID using fallback method: ${fallbackMatch[1]} (original: ${url})`);
      return fallbackMatch[1];
    }
  }

  console.log('Failed to extract video ID from input');
  return null;
};

// Initial known mappings
const initialMappings = {
  '518747bd406f98b608757178b242a1c5': 'sf8Sy2',
  'f6cca96c084ff0b11f1425dd58efc204': 'a71tuY',
};

// Load mappings from localStorage or use initial mappings
const loadMappings = () => {
  try {
    const storedMappings = localStorage.getItem('videoIdMappings');
    if (storedMappings) {
      // Merge stored mappings with initial mappings
      return { ...initialMappings, ...JSON.parse(storedMappings) };
    }
  } catch (error) {
    console.error('Error loading mappings from localStorage:', error);
  }

  // If no stored mappings or error, use initial mappings
  return { ...initialMappings };
};

// Save mappings to localStorage
const saveMappings = (mappings) => {
  try {
    localStorage.setItem('videoIdMappings', JSON.stringify(mappings));
  } catch (error) {
    console.error('Error saving mappings to localStorage:', error);
  }
};

// Get the current mappings
let idMappings = loadMappings();

/**
 * Add a new mapping between a hash ID and an Engaxe ID
 * @param {string} hashId - The hash ID to map
 * @param {string} engaxeId - The Engaxe ID to map to
 */
export const addMapping = (hashId, engaxeId) => {
  if (!hashId || !engaxeId) return;

  // Only add if it's a valid Engaxe ID
  if (isValidEngaxeId(engaxeId)) {
    console.log(`Adding new mapping: ${hashId} -> ${engaxeId}`);
    idMappings = { ...idMappings, [hashId]: engaxeId };
    saveMappings(idMappings);
  }
};

/**
 * Check if a string is a hash ID (32 hex characters)
 * @param {string} id - The ID to check
 * @returns {boolean} - True if the ID is a hash ID
 */
export const isHashId = (id) => {
  return id && typeof id === 'string' && /^[a-f0-9]{32}$/.test(id);
};

/**
 * Check if a string is a valid Engaxe ID (6-7 alphanumeric characters)
 * @param {string} id - The ID to check
 * @returns {boolean} - True if the ID is a valid Engaxe ID
 */
export const isValidEngaxeId = (id) => {
  return id && typeof id === 'string' && /^[a-zA-Z0-9]{6,7}$/.test(id);
};

/**
 * Get a flag emoji for a language code
 * @param {string} code - The language code
 * @returns {string} - The flag emoji for the language
 */
export const getLanguageFlag = (code) => {
  if (!code) return '🌐';

  switch (code.toLowerCase()) {
    case 'en': return '🇺🇸';
    case 'hi': return '🇮🇳';
    case 'es': return '🇪🇸';
    case 'fr': return '🇫🇷';
    case 'de': return '🇩🇪';
    case 'ja': return '🇯🇵';
    case 'zh': return '🇨🇳';
    case 'ru': return '🇷🇺';
    case 'ar': return '🇸🇦';
    case 'pt': return '🇵🇹';
    case 'it': return '🇮🇹';
    case 'nl': return '🇳🇱';
    case 'ko': return '🇰🇷';
    case 'tr': return '🇹🇷';
    // Add more Indian languages
    case 'bn': return '🇮🇳'; // Bengali
    case 'ta': return '🇮🇳'; // Tamil
    case 'te': return '🇮🇳'; // Telugu
    case 'mr': return '🇮🇳'; // Marathi
    case 'gu': return '🇮🇳'; // Gujarati
    case 'kn': return '🇮🇳'; // Kannada
    case 'ml': return '🇮🇳'; // Malayalam
    case 'pa': return '🇮🇳'; // Punjabi
    case 'or': return '🇮🇳'; // Odia
    case 'as': return '🇮🇳'; // Assamese
    case 'ur': return '🇵🇰'; // Urdu
    default: return '🌐';
  }
};

/**
 * Generate a random Engaxe-like ID (6-7 alphanumeric characters)
 * @returns {string} - A random Engaxe-like ID
 */
export const generateRandomEngaxeId = () => {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  const length = 6 + Math.floor(Math.random() * 2); // 6 or 7 characters
  let result = '';

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return result;
};

/**
 * Convert a hash ID to an Engaxe ID
 * @param {string} hashId - The hash ID to convert
 * @param {boolean} createIfMissing - Whether to create a new mapping if none exists
 * @returns {string} - The corresponding Engaxe ID, or a generated ID if not found
 */
export const hashIdToEngaxeId = (hashId, createIfMissing = true) => {
  if (!hashId) return null;

  // If it's already a valid Engaxe ID, return it as is
  if (isValidEngaxeId(hashId)) {
    console.log(`ID ${hashId} is already a valid Engaxe ID`);
    return hashId;
  }

  // If it's not a hash ID, return it as is
  if (!isHashId(hashId)) {
    console.log(`ID ${hashId} is not a hash ID, returning as is`);
    return hashId;
  }

  // Look up the hash ID in the mappings
  if (idMappings[hashId]) {
    console.log(`Converting hash ID ${hashId} to Engaxe ID ${idMappings[hashId]}`);
    return idMappings[hashId];
  }

  // If not found and createIfMissing is true, create a new mapping
  if (createIfMissing) {
    // Generate a new Engaxe-like ID
    const newEngaxeId = generateRandomEngaxeId();
    console.log(`Generated new Engaxe ID for hash ID ${hashId}: ${newEngaxeId}`);

    // Add the new mapping
    addMapping(hashId, newEngaxeId);

    return newEngaxeId;
  }

  // If not found and createIfMissing is false, return a fallback ID
  console.log(`No mapping found for hash ID ${hashId}, using fallback ID`);
  return 'sf8Sy2'; // Fallback to a known working ID
};

/**
 * Process a video object to ensure it has a valid Engaxe ID
 * @param {Object} video - The video object to process
 * @returns {Object} - The processed video object
 */
export const processVideoForClient = (video) => {
  if (!video) return video;

  const processedVideo = { ...video };

  // ALWAYS generate a unique Engaxe ID for each video based on its hash ID
  // This ensures each video has its own unique playable ID
  if (processedVideo.id) {
    // If the video has no URL or the URL is not a valid Engaxe ID, generate one
    if (!processedVideo.url || !isValidEngaxeId(processedVideo.url)) {
      console.log(`Video ${processedVideo.id} needs a valid Engaxe ID`);

      // Check if we already have a mapping for this hash ID
      const existingMapping = idMappings[processedVideo.id];
      if (existingMapping) {
        console.log(`Using existing mapping for ${processedVideo.id}: ${existingMapping}`);
        processedVideo.url = existingMapping;
      } else {
        // Generate a new unique Engaxe ID based on the hash ID
        // Use the first 6 characters of the hash ID if they form a valid Engaxe ID
        const firstSixChars = processedVideo.id.substring(0, 6);
        if (isValidEngaxeId(firstSixChars)) {
          console.log(`Using first 6 characters of hash ID as Engaxe ID: ${firstSixChars}`);
          processedVideo.url = firstSixChars;
          // Save this mapping for future use
          addMapping(processedVideo.id, firstSixChars);
        } else {
          // Otherwise, generate a completely new random Engaxe ID
          const newEngaxeId = generateRandomEngaxeId();
          console.log(`Generated new random Engaxe ID for ${processedVideo.id}: ${newEngaxeId}`);
          processedVideo.url = newEngaxeId;
          // Save this mapping for future use
          addMapping(processedVideo.id, newEngaxeId);
        }
      }
    } else if (isValidEngaxeId(processedVideo.url)) {
      // If the URL is already a valid Engaxe ID, make sure we have a mapping for it
      console.log(`Video ${processedVideo.id} already has valid Engaxe ID: ${processedVideo.url}`);
      addMapping(processedVideo.id, processedVideo.url);
    }
  } else {
    console.error('Video has no ID, cannot process properly');
  }

  // Process languages if they exist
  if (processedVideo.languages && Array.isArray(processedVideo.languages)) {
    console.log(`Processing ${processedVideo.languages.length} languages for video ${processedVideo.id}:`);

    // Log all languages for debugging
    processedVideo.languages.forEach((lang, idx) => {
      console.log(`Language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url || 'none'}, isDefault: ${lang.isDefault || false}`);
    });

    // Check if we have at least one language with isDefault=true
    const hasDefaultLanguage = processedVideo.languages.some(lang => lang.isDefault);
    console.log(`Has default language: ${hasDefaultLanguage}`);

    processedVideo.languages = processedVideo.languages.map((lang, index) => {
      // Create a new language object to avoid mutating the original
      const processedLang = { ...lang };

      // Ensure language has a code
      if (!processedLang.code) {
        processedLang.code = 'en';
        console.log(`Language missing code, defaulting to 'en'`);
      }

      // Ensure language has a name
      if (!processedLang.name) {
        processedLang.name = processedLang.code === 'en' ? 'English' : `Language ${index + 1}`;
        console.log(`Language missing name, defaulting to '${processedLang.name}'`);
      }

      // Ensure language has a flag
      if (!processedLang.flag) {
        processedLang.flag = getLanguageFlag(processedLang.code);
        console.log(`Language missing flag, adding ${processedLang.flag} for ${processedLang.code}`);
      }

      // Set isDefault if not already set
      if (typeof processedLang.isDefault === 'undefined') {
        // If no default language yet, make the first one default
        // or make English the default if it exists
        if (!hasDefaultLanguage && (index === 0 || processedLang.code === 'en')) {
          processedLang.isDefault = true;
          console.log(`Setting ${processedLang.code} as default language`);
        } else {
          processedLang.isDefault = false;
        }
      }

      // Process the language URL
      if (processedLang.url) {
        // Try to extract an Engaxe ID if it's a full URL
        const extractedId = extractEngaxeVideoId(processedLang.url);
        if (extractedId) {
          console.log(`Extracted Engaxe ID for language ${processedLang.code}: ${extractedId} (original: ${processedLang.url})`);
          processedLang.url = extractedId;
        }

        // Check if the URL is a valid Engaxe ID
        if (isValidEngaxeId(processedLang.url)) {
          console.log(`Language ${processedLang.code} has valid Engaxe ID: ${processedLang.url}`);
          return processedLang;
        }
      }

      // If language URL is missing or invalid, use the video URL
      if (processedVideo.url) {
        console.log(`Language ${processedLang.code} has invalid URL: ${processedLang.url}, using video URL: ${processedVideo.url}`);
        return { ...processedLang, url: processedVideo.url };
      }

      return processedLang;
    });

    // If no default language was set, make the first one default
    if (!hasDefaultLanguage && processedVideo.languages.length > 0) {
      console.log(`No default language found, setting ${processedVideo.languages[0].code} as default`);
      processedVideo.languages[0].isDefault = true;
    }

    // Log the final processed languages
    console.log(`Final processed languages for video ${processedVideo.id}:`);
    processedVideo.languages.forEach((lang, idx) => {
      console.log(`Language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url}, isDefault: ${lang.isDefault}`);
    });
  } else if (processedVideo.url) {
    // If no languages, create a default one
    console.log(`No languages found for video ${processedVideo.id}, creating default English language`);
    processedVideo.languages = [{
      code: 'en',
      name: 'English',
      flag: getLanguageFlag('en'),
      isDefault: true,
      url: processedVideo.url
    }];
  }

  return processedVideo;
};

/**
 * Process an array of videos to ensure they all have valid Engaxe IDs
 * @param {Array} videos - The array of videos to process
 * @returns {Array} - The processed array of videos
 */
export const processVideosForClient = (videos) => {
  if (!videos || !Array.isArray(videos)) return videos;

  return videos.map(video => processVideoForClient(video));
};
