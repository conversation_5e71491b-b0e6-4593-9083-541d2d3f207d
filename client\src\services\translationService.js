/**
 * Simple translation service that simulates a translation API
 * In a real application, you would use a real translation API like Google Translate
 */

// Language codes and their names
export const supportedLanguages = [
  { code: 'en', name: 'English 🇺🇸' },
  // Indian languages
  { code: 'hi', name: 'Hindi 🇮🇳' },
  { code: 'mr', name: 'Marathi 🇮🇳' },
  { code: 'gu', name: 'Gujarati 🇮🇳' },
  { code: 'ta', name: 'Tamil 🇮🇳' },
  { code: 'te', name: 'Telugu 🇮🇳' },
  { code: 'bn', name: 'Bengali 🇮🇳' },
  { code: 'kn', name: 'Kannada 🇮🇳' },
  { code: 'ml', name: 'Malayalam 🇮🇳' },
  // Foreign languages
  { code: 'de', name: 'German 🇩🇪' }
];

// Simple translation function that simulates a translation API
export const translateText = (text, targetLanguage) => {
  if (targetLanguage === 'en') {
    return text; // No translation needed for English
  }

  // For demonstration purposes, we'll use these prefixes to simulate translation
  const translationPrefixes = {
    'hi': 'हिंदी: ',
    'mr': 'मराठी: ',
    'gu': 'ગુજરાતી: ',
    'ta': 'தமிழ்: ',
    'te': 'తెలుగు: ',
    'bn': 'বাংলা: ',
    'kn': 'ಕನ್ನಡ: ',
    'ml': 'മലയാളം: ',
    'es': 'Español: ',
    'fr': 'Français: ',
    'de': 'Deutsch: ',
    'zh': '中文: ',
    'ja': '日本語: ',
    'ru': 'Русский: ',
    'ar': 'العربية: '
  };

  // In a real app, you would call a translation API here
  // For now, we'll just add a prefix to simulate translation
  return translationPrefixes[targetLanguage] + text;
};

// Export the translation service
const translationService = {
  translate: translateText,
  supportedLanguages
};

export default translationService;
