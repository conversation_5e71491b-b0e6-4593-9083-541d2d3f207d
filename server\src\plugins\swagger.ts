import { FastifyInstance, FastifyPluginAsync } from 'fastify';
import fastifyPlugin from 'fastify-plugin';
import swagger from '@fastify/swagger';
import swaggerUI from '@fastify/swagger-ui';
import { version } from '../../package.json';

/**
 * Plugin to add Swagger/OpenAPI documentation to Fastify
 */
const swaggerPlugin: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  // Register Swagger plugin
  await fastify.register(swagger, {
    openapi: {
      info: {
        title: 'LawEngaxe API',
        description: 'API documentation for the LawEngaxe platform',
        version,
        contact: {
          name: 'LawEngaxe Support',
          email: '<EMAIL>',
          url: 'https://lawengaxe.com/support',
        },
        license: {
          name: 'MIT',
          url: 'https://opensource.org/licenses/MIT',
        },
      },
      servers: [
        {
          url: `${process.env.API_URL || 'http://localhost:3000/api/v1'}`,
          description: 'Development Server',
        },
        {
          url: 'https://api.lawengaxe.com/api/v1',
          description: 'Production Server',
        },
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
      tags: [
        { name: 'Auth', description: 'Authentication endpoints' },
        { name: 'Users', description: 'User management endpoints' },
        { name: 'Roles', description: 'Role management endpoints' },
        { name: 'Permissions', description: 'Permission management endpoints' },
        { name: 'Videos', description: 'Video management endpoints' },
        { name: 'Conversations', description: 'Conversation management endpoints' },
        { name: 'Messages', description: 'Message management endpoints' },
        { name: 'Notifications', description: 'Notification management endpoints' },
        { name: 'System', description: 'System configuration endpoints' },
      ],
    },
  });

  // Register Swagger UI plugin
  await fastify.register(swaggerUI, {
    routePrefix: '/documentation',
    uiConfig: {
      docExpansion: 'list',
      deepLinking: true,
      displayRequestDuration: true,
      filter: true,
    },
    staticCSP: true,
    transformStaticCSP: (header) => header,
    transformSpecification: (swaggerObject) => {
      // Add security requirement to all operations
      if (swaggerObject.paths) {
        Object.keys(swaggerObject.paths).forEach((path) => {
          const pathItem = swaggerObject.paths[path];
          Object.keys(pathItem).forEach((method) => {
            // Skip if it's a parameter
            if (method === 'parameters') return;
            
            // Add security requirement to operation
            const operation = pathItem[method];
            
            // Skip auth endpoints
            if (path.includes('/auth/login') || path.includes('/auth/refresh-token') || 
                path.includes('/users/register') || path.includes('/auth/verify-email') ||
                path.includes('/auth/forgot-password') || path.includes('/auth/reset-password')) {
              return;
            }
            
            operation.security = [{ bearerAuth: [] }];
          });
        });
      }
      return swaggerObject;
    },
  });
};

export default fastifyPlugin(swaggerPlugin);
