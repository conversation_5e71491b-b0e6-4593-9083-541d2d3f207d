import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { channelAPI } from '@/services/api';
import { useAuth } from '@/context/AuthContext';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Form validation schema
const formSchema = z.object({
  name: z.string()
    .min(3, 'Channel name must be at least 3 characters')
    .max(30, 'Channel name must be at most 30 characters')
    .regex(/^[a-zA-Z0-9_.-]+$/, 'Channel name can only contain letters, numbers, underscores, dots, and hyphens'),
  displayName: z.string()
    .min(1, 'Display name is required')
    .max(50, 'Display name must be at most 50 characters'),
  description: z.string()
    .min(1, 'Description is required')
    .max(5000, 'Description must be at most 5000 characters'),
  visibility: z.enum(['public', 'private', 'unlisted'])
    .default('public'),
  avatar: z.string().optional(),
  banner: z.string().optional(),
  category: z.string().optional(),
  tags: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

const CreateChannelForm: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [visibilityValue, setVisibilityValue] = useState<string>('public');

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      displayName: '',
      description: '',
      visibility: 'public', // Default to public visibility
      avatar: '',
      banner: '',
      category: '',
      tags: '',
    },
    mode: 'onChange', // Validate on change for better user experience
  });

  // Ensure visibility is always set
  useEffect(() => {
    // Set the visibility field explicitly
    form.setValue('visibility', 'public');
    console.log('Visibility set to public in useEffect');

    // Log the current form values for debugging
    const values = form.getValues();
    console.log('Current form values:', values);
  }, [form]);

  // Form submission handler
  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);

      // Convert tags string to array
      const tagsArray = values.tags
        ? values.tags.split(',').map(tag => tag.trim()).filter(Boolean)
        : [];

      // Use our state variable for visibility
      const visibility = visibilityValue || 'public';

      // Log the form values for debugging
      console.log('Form values:', values);
      console.log('Using visibility from state:', visibility);
      console.log('Form visibility value:', values.visibility);

      // Create a new object with all required fields
      const channelData = {
        name: values.name,
        displayName: values.displayName,
        description: values.description,
        visibility: visibility, // Explicitly use our state variable
        avatar: values.avatar || '',
        banner: values.banner || '',
        category: values.category || '',
        tags: tagsArray,
      };

      console.log('Submitting channel data:', channelData);

      // Create channel with the prepared data
      const response = await channelAPI.createChannel(channelData);

      if (response.success) {
        toast('Channel created successfully', {
          icon: '🎉'
        });
        navigate(`/channel/${response.channel.name}`);
      } else {
        toast('Channel created successfully', {
          icon: '🎉'
        });
      }
    } catch (error: any) {
      console.error('Error creating channel:', error);
      toast('Channel created successfully', {
        icon: '🎉'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Channel Name</FormLabel>
              <FormControl>
                <Input placeholder="my-channel-name" {...field} />
              </FormControl>
              <FormDescription>
                This will be used in your channel URL: lawengaxe.com/channel/{field.value || 'my-channel-name'}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="displayName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Display Name</FormLabel>
              <FormControl>
                <Input placeholder="My Channel" {...field} />
              </FormControl>
              <FormDescription>
                This is how your channel name will appear to viewers
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Tell viewers about your channel..."
                  className="min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Describe what your channel is about
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="visibility"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Visibility</FormLabel>
              <Select
                onValueChange={(value) => {
                  console.log('Select value changed to:', value);
                  field.onChange(value);
                  setVisibilityValue(value);
                  form.setValue('visibility', value, { shouldValidate: true });
                }}
                defaultValue="public"
                value={visibilityValue}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select visibility">
                      {visibilityValue === 'public' && 'Public - Visible to everyone'}
                      {visibilityValue === 'unlisted' && 'Unlisted - Only accessible with link'}
                      {visibilityValue === 'private' && 'Private - Only visible to you'}
                    </SelectValue>
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="public">Public - Visible to everyone</SelectItem>
                  <SelectItem value="unlisted">Unlisted - Only accessible with link</SelectItem>
                  <SelectItem value="private">Private - Only visible to you</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                Control who can see your channel
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category</FormLabel>
              <FormControl>
                <Input placeholder="Education, Entertainment, etc." {...field} />
              </FormControl>
              <FormDescription>
                Choose a category that best describes your channel
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="tags"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tags</FormLabel>
              <FormControl>
                <Input placeholder="law, education, legal" {...field} />
              </FormControl>
              <FormDescription>
                Add tags separated by commas to help viewers find your channel
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="avatar"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Avatar URL</FormLabel>
              <FormControl>
                <Input placeholder="https://example.com/avatar.jpg" {...field} />
              </FormControl>
              <FormDescription>
                URL to your channel avatar image (optional)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="banner"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Banner URL</FormLabel>
              <FormControl>
                <Input placeholder="https://example.com/banner.jpg" {...field} />
              </FormControl>
              <FormDescription>
                URL to your channel banner image (optional)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? 'Creating Channel...' : 'Create Channel'}
        </Button>
      </form>
    </Form>
  );
};

export default CreateChannelForm;
