import mongoose, { Schema } from 'mongoose';
import BaseSchema, { IBaseEntity } from './base.model';

/**
 * Interface for Like document
 */
export interface ILike extends IBaseEntity {
  videoId: string;
  userId: string;
  timestamp: Date;
}

/**
 * Like schema definition
 */
const LikeSchema = new Schema<ILike>(
  {
    videoId: {
      type: String,
      required: true,
      ref: 'Video',
      index: true,
    },
    userId: {
      type: String,
      required: true,
      ref: 'User',
      index: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

// Merge with base schema
LikeSchema.add(BaseSchema);

// Add compound index for videoId and userId to ensure a user can only like a video once
LikeSchema.index({ videoId: 1, userId: 1 }, { unique: true });

// Create and export the Like model
const LikeModel = mongoose.model<ILike>('Like', LikeSchema);
export default LikeModel;
