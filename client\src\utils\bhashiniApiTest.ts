import { getAuthToken, executeModelComputation, BhashiniConfig } from '@/services/bhashiniApiService';

/**
 * Test Bhashini API Pipeline
 * 
 * This function tests the Bhashini API pipeline by:
 * 1. Authenticating with the Bhashini API
 * 2. Executing a simple translation task
 * 3. Returning the results
 * 
 * @param config Bhashini API configuration
 * @param testText Text to translate for testing
 * @param sourceLanguage Source language code (ISO)
 * @param targetLanguage Target language code (ISO)
 * @returns Test results with status and details
 */
export async function testBhashiniPipeline(
  config: BhashiniConfig,
  testText: string = 'Hello, how are you?',
  sourceLanguage: string = 'en',
  targetLanguage: string = 'hi'
): Promise<{
  success: boolean;
  message: string;
  details?: {
    authToken?: string;
    translationResult?: string;
    rawResponse?: any;
    error?: any;
  };
}> {
  try {
    console.log('Testing Bhashini API pipeline with config:', {
      apiKey: config.apiKey ? `${config.apiKey.substring(0, 5)}...` : 'not provided',
      userId: config.userId ? `${config.userId.substring(0, 5)}...` : 'not provided',
      ulcaApiKey: config.ulcaApiKey ? `${config.ulcaApiKey.substring(0, 5)}...` : 'not provided'
    });

    // Step 1: Test authentication
    console.log('Step 1: Testing authentication...');
    const authToken = await getAuthToken(config);
    
    if (!authToken) {
      return {
        success: false,
        message: 'Authentication failed: No token received',
        details: { error: 'No authentication token received' }
      };
    }
    
    console.log('Authentication successful, token received');

    // Step 2: Test translation
    console.log(`Step 2: Testing translation from ${sourceLanguage} to ${targetLanguage}...`);
    const translationResponse = await executeModelComputation(
      'ai4bharat/indictrans-v2-all-gpu',
      'translation',
      [{ source: testText }],
      {
        language: {
          sourceLanguage,
          targetLanguage
        }
      },
      config
    );

    // Check if translation was successful
    if (
      translationResponse &&
      translationResponse.output &&
      translationResponse.output.length > 0 &&
      translationResponse.output[0].target
    ) {
      const translatedText = translationResponse.output[0].target;
      
      return {
        success: true,
        message: 'Bhashini API pipeline test successful',
        details: {
          authToken: `${authToken.substring(0, 10)}...`,
          translationResult: translatedText,
          rawResponse: translationResponse
        }
      };
    } else {
      return {
        success: false,
        message: 'Translation failed: Invalid response format',
        details: {
          authToken: `${authToken.substring(0, 10)}...`,
          rawResponse: translationResponse,
          error: 'Invalid response format'
        }
      };
    }
  } catch (error: any) {
    console.error('Error testing Bhashini API pipeline:', error);
    
    return {
      success: false,
      message: `Bhashini API pipeline test failed: ${error.message || 'Unknown error'}`,
      details: {
        error: error.message || 'Unknown error'
      }
    };
  }
}

export default testBhashiniPipeline;
