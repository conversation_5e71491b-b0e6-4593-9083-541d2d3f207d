import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Globe, Lock, Link2 } from 'lucide-react';
import { formatNumber } from '@/lib/utils';
import { getInitials, handleAvatarError } from '@/lib/avatar-utils';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface ChannelCardProps {
  channel: {
    id: string;
    name: string;
    displayName: string;
    description: string;
    avatar?: string;
    isVerified: boolean;
    visibility?: 'public' | 'private' | 'unlisted';
    stats: {
      subscribers: number;
      videoCount: number;
    };
    tags?: string[];
  };
  isSubscribed?: boolean;
  onSubscribe?: () => void;
  compact?: boolean;
}

const ChannelCard: React.FC<ChannelCardProps> = ({
  channel,
  isSubscribed = false,
  onSubscribe,
  compact = false,
}) => {
  const { id, name, displayName, description, avatar, isVerified, visibility = 'public', stats, tags } = channel;

  // Using the shared getInitials function from avatar-utils

  if (compact) {
    return (
      <Card className="overflow-hidden hover:shadow-md transition-shadow flex flex-col h-full">
        <Link to={`/channels/${name}`} className="block flex-1">
          <div className="flex items-center p-4">
            {/* Standardized avatar implementation */}
            <Avatar className="h-10 w-10 mr-3">
              <AvatarImage
                src={avatar || ''}
                alt={displayName}
                onError={(e) => handleAvatarError(e, displayName)}
              />
              <AvatarFallback>{getInitials(displayName)}</AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center">
                <h3 className="text-sm font-medium truncate">{displayName}</h3>
                {isVerified && (
                  <CheckCircle className="h-4 w-4 text-blue-500 ml-1" />
                )}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="ml-1">
                        {visibility === 'private' && <Lock className="h-3 w-3 text-muted-foreground" />}
                        {visibility === 'unlisted' && <Link2 className="h-3 w-3 text-muted-foreground" />}
                        {visibility === 'public' && <Globe className="h-3 w-3 text-muted-foreground" />}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      {visibility === 'private' && 'Private - Only visible to you'}
                      {visibility === 'unlisted' && 'Unlisted - Only accessible with link'}
                      {visibility === 'public' && 'Public - Visible to everyone'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <p className="text-xs text-muted-foreground truncate">
                {formatNumber(stats.subscribers)} subscribers
              </p>
            </div>
          </div>
        </Link>
        {onSubscribe && (
          <CardFooter className="pt-0 pb-4 px-4 mt-auto">
            <Button
              variant={isSubscribed ? "outline" : "default"}
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                onSubscribe();
              }}
              className="w-full"
            >
              {isSubscribed ? 'Subscribed' : 'Subscribe'}
            </Button>
          </CardFooter>
        )}
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow flex flex-col h-full">
      <Link to={`/channels/${name}`} className="block">
        <CardHeader className="pb-2">
          <div className="flex items-center">
            {/* Standardized avatar implementation */}
            <Avatar className="h-12 w-12 mr-3">
              <AvatarImage
                src={avatar || ''}
                alt={displayName}
                onError={(e) => handleAvatarError(e, displayName)}
              />
              <AvatarFallback>{getInitials(displayName)}</AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center">
                <CardTitle className="text-lg">{displayName}</CardTitle>
                {isVerified && (
                  <CheckCircle className="h-4 w-4 text-blue-500 ml-1" />
                )}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="ml-1">
                        {visibility === 'private' && <Lock className="h-4 w-4 text-muted-foreground" />}
                        {visibility === 'unlisted' && <Link2 className="h-4 w-4 text-muted-foreground" />}
                        {visibility === 'public' && <Globe className="h-4 w-4 text-muted-foreground" />}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      {visibility === 'private' && 'Private - Only visible to you'}
                      {visibility === 'unlisted' && 'Unlisted - Only accessible with link'}
                      {visibility === 'public' && 'Public - Visible to everyone'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <CardDescription className="text-sm">
                {formatNumber(stats.subscribers)} subscribers • {formatNumber(stats.videoCount)} videos
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Link>

      <CardContent className="pb-2 flex-1">
        <p className="text-sm line-clamp-2">{description}</p>

        {tags && tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {tags.slice(0, 3).map(tag => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{tags.length - 3} more
              </Badge>
            )}
          </div>
        )}
      </CardContent>

      <CardFooter className="mt-auto">
        {onSubscribe && (
          <Button
            variant={isSubscribed ? "outline" : "default"}
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              onSubscribe();
            }}
            className="w-full"
          >
            {isSubscribed ? 'Subscribed' : 'Subscribe'}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default ChannelCard;
