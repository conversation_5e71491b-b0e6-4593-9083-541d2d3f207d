/**
 * TypeScript implementation of the Python Bhashini translation tool
 * This is a direct port of the Python code to TypeScript
 */

import { translateText, SUPPORTED_LANGUAGES } from '../services/bhashiniDirectApi';

/**
 * Main function to run the translation tool
 * This mimics the Python script's main function
 */
async function main() {
  console.log("=".repeat(50));
  console.log("Bhashini Translation Tool");
  console.log("=".repeat(50));

  // Source language is fixed to English
  const sourceLang = "en";

  // Display all available target languages
  console.log("\nAvailable target languages:");
  console.log("-".repeat(30));

  // Create a numbered list of languages (excluding English as source)
  const languageOptions: Record<number, string> = {};
  let i = 1;
  
  for (const [code, name] of Object.entries(SUPPORTED_LANGUAGES)) {
    if (code !== sourceLang) { // Skip English as it's the source
      languageOptions[i] = code;
      console.log(`${i}. ${name}`);
      i++;
    }
  }

  // In a browser environment, we'd use a form instead of command line input
  // For this example, we'll just use a predefined target language
  const targetLang = "hi"; // Hindi
  const targetLangName = SUPPORTED_LANGUAGES[targetLang];
  
  console.log(`\nTranslating from English to ${targetLangName}`);
  
  // Sample text to translate
  const text = "Hello, how are you? This is a test of the Bhashini translation API.";
  console.log(`Text to translate: ${text}`);

  // Perform translation
  console.log("\nTranslating...");
  const translation = await translateText(text, sourceLang, targetLang);

  // Display result
  console.log("\nTranslation Result:");
  console.log("-".repeat(30));
  console.log(`Original (English): ${text}`);
  console.log(`Translation (${targetLangName}): ${translation}`);
  console.log("=".repeat(50));
}

/**
 * Function to translate text to all supported languages
 * This is an extension of the Python script to test all languages
 */
async function translateToAllLanguages(text: string, sourceLang: string = "en") {
  console.log("=".repeat(50));
  console.log("Bhashini Translation Tool - All Languages Test");
  console.log("=".repeat(50));

  console.log(`\nSource text (${sourceLang}): ${text}`);
  console.log("\nTranslating to all supported languages:");
  console.log("-".repeat(30));

  // Translate to all languages except the source language
  for (const [code, name] of Object.entries(SUPPORTED_LANGUAGES)) {
    if (code !== sourceLang) {
      try {
        console.log(`\nTranslating to ${name} (${code})...`);
        const translation = await translateText(text, sourceLang, code);
        console.log(`Translation: ${translation}`);
      } catch (error) {
        console.error(`Error translating to ${code}: ${error}`);
      }
    }
  }

  console.log("\n" + "=".repeat(50));
}

// Export the functions for use in other files
export { main, translateToAllLanguages };

// If this file is run directly, execute the main function
if (require.main === module) {
  main().catch(error => {
    console.error("Error:", error);
  });
}
