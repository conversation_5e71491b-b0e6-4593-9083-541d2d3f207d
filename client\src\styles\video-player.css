/* Video Player Custom Styles */

/* H5P video container with fixed height to match the reference */
.h5p-video-container {
  width: 100% !important;
  height: 532px !important; /* Reduced height to match the iframe height */
  position: relative !important;
}

/* Make sure the iframe inside the container takes up the full space */
.h5p-video-container iframe,
#engaxe-player-container iframe {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  display: block !important;
}

/* Styles to match the reference HTML structure */
.tag_main_player {
  width: 100%;
}

.player-video {
  width: 100%;
}

.video-player {
  width: 100%;
}

.h5p-iframe-wrapper {
  width: 100%;
  height: 532px;
  position: relative;
}

/* Make sure the iframe has the exact dimensions from the reference */
.h5p-iframe {
  width: 100% !important;
  height: 532px !important;
  border: none !important;
  display: block !important;
}

/* H5P fullscreen button styles */
div[role="button"][tabindex="0"][aria-label="Fullscreen"],
div[role="button"][tabindex="0"].h5p-control.h5p-fullscreen {
  cursor: pointer !important;
  pointer-events: auto !important;
  z-index: 9999 !important;
  position: relative !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  border-radius: 4px !important;
  padding: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  min-height: 32px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

div[role="button"][tabindex="0"][aria-label="Fullscreen"]:hover,
div[role="button"][tabindex="0"].h5p-control.h5p-fullscreen:hover {
  background-color: rgba(0, 0, 0, 0.7) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

div[role="button"][tabindex="0"][aria-label="Fullscreen"]:active,
div[role="button"][tabindex="0"].h5p-control.h5p-fullscreen:active {
  background-color: rgba(0, 0, 0, 0.9) !important;
  transform: scale(0.95) !important;
}

/* Video player fullscreen container */
.fullscreen-video-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9000 !important;
  background-color: #000 !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  overflow: hidden !important;
}

/* Make sure the video container's child div takes up full height in fullscreen */
.fullscreen-video-container > div {
  height: 100vh !important;
  max-height: 100vh !important;
}

/* Active fullscreen container */
.fullscreen-active {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* Fullscreen styles */
:fullscreen {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

:fullscreen #engaxe-player-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  background-color: #000 !important;
  z-index: 9000 !important;
}

:-webkit-full-screen {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

:-webkit-full-screen #engaxe-player-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  background-color: #000 !important;
  z-index: 9000 !important;
}

:-moz-full-screen {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

:-moz-full-screen #engaxe-player-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  background-color: #000 !important;
  z-index: 9000 !important;
}

:-ms-fullscreen {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

:-ms-fullscreen #engaxe-player-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  background-color: #000 !important;
  z-index: 9000 !important;
}

/* Minimize button container styles */
.video-minimize-button-container {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 9999 !important;
  display: none !important; /* Hidden by default */
}

/* Ensure the minimize button is visible above all content in fullscreen */
:fullscreen .video-minimize-button-container {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 10000 !important; /* Higher than any other element */
}

:-webkit-full-screen .video-minimize-button-container {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 10000 !important;
}

:-moz-full-screen .video-minimize-button-container {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 10000 !important;
}

:-ms-fullscreen .video-minimize-button-container {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 10000 !important;
}

/* Minimize button styles */
.video-minimize-button {
  background-color: white !important;
  color: black !important;
  border: 1px solid black !important;
  border-radius: 4px !important;
  padding: 8px !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color 0.2s ease !important;
}

/* Removed hover and active effects */

/* Only show minimize button in fullscreen mode */
:fullscreen .video-minimize-button-container {
  display: flex !important;
}

:-webkit-full-screen .video-minimize-button-container {
  display: flex !important;
}

:-moz-full-screen .video-minimize-button-container {
  display: flex !important;
}

:-ms-fullscreen .video-minimize-button-container {
  display: flex !important;
}
