import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Clock } from 'lucide-react';

interface ChatVideoCardProps {
  videoId: string;
  title?: string;
}

interface VideoMetadata {
  id: string;
  title: string;
  description: string;
  duration: number;
  thumbnail: string;
  url: string;
}

export default function ChatVideoCard({ videoId, title }: ChatVideoCardProps) {
  const [videoData, setVideoData] = useState<VideoMetadata | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Parse duration string in format "MM:SS" or "HH:MM:SS" to seconds
  const parseDurationToSeconds = (durationStr: string | number): number => {
    if (!durationStr) return 0;

    // If it's already a number, return it
    if (typeof durationStr === 'number') {
      return durationStr;
    }

    // If it's a string that doesn't contain colons, try to parse as number
    if (typeof durationStr === 'string' && !durationStr.includes(':')) {
      const parsed = parseInt(durationStr, 10);
      return isNaN(parsed) ? 0 : parsed;
    }

    // Handle MM:SS or HH:MM:SS format
    if (typeof durationStr === 'string') {
      const cleanDuration = durationStr.trim();
      const parts = cleanDuration.split(':');

      if (parts.length === 2) {
        // MM:SS format
        const minutes = parseInt(parts[0], 10) || 0;
        const seconds = parseInt(parts[1], 10) || 0;
        return (minutes * 60) + seconds;
      } else if (parts.length === 3) {
        // HH:MM:SS format
        const hours = parseInt(parts[0], 10) || 0;
        const minutes = parseInt(parts[1], 10) || 0;
        const seconds = parseInt(parts[2], 10) || 0;
        return (hours * 3600) + (minutes * 60) + seconds;
      }
    }

    return 0;
  };

  // Format duration from seconds to MM:SS or HH:MM:SS
  const formatDuration = (duration: string | number): string => {
    const seconds = parseDurationToSeconds(duration);
    if (!seconds || seconds === 0) return '0:00';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  };

  useEffect(() => {
    const fetchVideoData = async () => {
      if (!videoId) {
        setError('No video ID provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Try multiple endpoints to find the video
        const endpoints = [
          `http://localhost:3001/api/v1/videos/json/videos/${videoId}`,
          `http://localhost:3001/api/v1/videos/${videoId}`,
          `http://localhost:3001/api/v1/videos/metadata/${videoId}`
        ];

        let videoData = null;
        let lastError = null;

        for (const endpoint of endpoints) {
          try {
            console.log(`🎥 Trying endpoint: ${endpoint}`);
            const response = await fetch(endpoint);

            if (response.ok) {
              const data = await response.json();
              console.log(`🎥 Success from ${endpoint}:`, data);

              // Handle different response structures
              const video = data.data || data.video || data;

              if (video && (video.id || video.title)) {
                videoData = {
                  id: video.id || videoId,
                  title: video.title || title || 'Untitled Video',
                  description: video.description || '',
                  duration: video.duration || 0,
                  thumbnail: video.thumbnail || video.thumbnailUrl || '/placeholder.svg',
                  url: video.url || video.id || videoId
                };
                break;
              }
            } else {
              console.log(`🎥 Failed ${endpoint}: ${response.status} ${response.statusText}`);
              lastError = `HTTP ${response.status}: ${response.statusText}`;
            }
          } catch (err) {
            console.log(`🎥 Error with ${endpoint}:`, err);
            lastError = err instanceof Error ? err.message : 'Network error';
          }
        }

        if (videoData) {
          setVideoData(videoData);
        } else {
          // If all endpoints fail, create fallback data
          console.warn(`🎥 All endpoints failed for video ${videoId}, using fallback`);
          setVideoData({
            id: videoId,
            title: title || 'Video',
            description: '',
            duration: 0,
            thumbnail: '/placeholder.svg',
            url: videoId
          });
        }
      } catch (error) {
        console.error(`🎥 Unexpected error fetching video ${videoId}:`, error);
        // Create fallback data even on unexpected errors
        setVideoData({
          id: videoId,
          title: title || 'Video',
          description: '',
          duration: 0,
          thumbnail: '/placeholder.svg',
          url: videoId
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchVideoData();
  }, [videoId, title]);

  if (isLoading) {
    return (
      <div className="my-2 w-full max-w-md">
        <div className="flex items-center bg-gray-50 dark:bg-gray-800 rounded-lg p-2 border border-gray-200 dark:border-gray-700 animate-pulse">
          <div className="w-20 h-14 bg-gray-300 dark:bg-gray-600 rounded-md flex-shrink-0"></div>
          <div className="flex-1 ml-3">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
          </div>
        </div>
      </div>
    );
  }

  // Only show error if we don't have any video data at all
  if (!videoData) {
    return (
      <div className="my-2 w-full max-w-md">
        <div className="flex items-center bg-red-50 dark:bg-red-900/20 rounded-lg p-2 border border-red-200 dark:border-red-800">
          <div className="w-20 h-14 bg-red-200 dark:bg-red-800 rounded-md flex-shrink-0 flex items-center justify-center">
            <span className="text-red-500 text-xs">Error</span>
          </div>
          <div className="flex-1 ml-3">
            <p className="text-sm text-red-600 dark:text-red-400">Failed to load video</p>
            <p className="text-xs text-red-500 dark:text-red-500">{error || 'Unknown error'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="my-2 w-full max-w-md">
      <Link 
        to={`/watch?id=${videoData.url || videoData.id}`}
        className="flex items-center bg-gray-50 dark:bg-gray-800 rounded-lg p-2 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow group"
      >
        {/* Thumbnail */}
        <div className="relative w-20 h-14 bg-gray-200 dark:bg-gray-700 rounded-md overflow-hidden flex-shrink-0">
          <img
            src={videoData.thumbnail || '/placeholder.svg'}
            alt={videoData.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            onError={(e) => {
              e.currentTarget.src = '/placeholder.svg';
            }}
          />
          
          {/* Duration badge */}
          {parseDurationToSeconds(videoData.duration) > 0 && (
            <div className="absolute bottom-1 right-1 bg-black bg-opacity-80 text-white text-xs px-1 py-0.5 rounded text-[10px]">
              {formatDuration(videoData.duration)}
            </div>
          )}
        </div>
        
        {/* Video info */}
        <div className="flex-1 ml-3 min-w-0">
          <h3 className="font-medium text-sm text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
            {videoData.title}
          </h3>
          {videoData.duration > 0 && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {formatDuration(videoData.duration)}
            </p>
          )}
        </div>
      </Link>
    </div>
  );
}
