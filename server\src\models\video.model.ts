import mongoose, { <PERSON>hem<PERSON>, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * Video interface extending the base entity
 */
export interface IVideo extends IBaseEntity {
  /** Title of the video */
  title: string;

  /** Detailed description of the video content */
  description: string;

  /** URL to the video file */
  url: string;

  /** URL to the video thumbnail image */
  thumbnailUrl: string;

  /** Duration of the video in seconds */
  duration: number;

  /** ID of the user who uploaded this video */
  userId: string;

  /** ID of the channel this video belongs to */
  channelId: string;

  /**
   * Video visibility setting
   *
   * Possible values:
   * - public: Visible to everyone
   * - unlisted: Only visible with direct link
   * - private: Only visible to the creator
   * - scheduled: Will become public at scheduledPublishTime
   */
  visibility: 'public' | 'unlisted' | 'private' | 'scheduled';

  /** When this video is scheduled to be published (for scheduled visibility) */
  scheduledPublishTime?: Date;

  /** Array of tags/keywords associated with this video */
  tags: string[];

  /**
   * Video category
   * Examples: 'Education', 'Entertainment', 'Gaming', 'Music'
   */
  category: string;

  /**
   * Content rating/maturity level
   *
   * Possible values:
   * - general: Suitable for all audiences
   * - teen: Suitable for teenagers
   * - mature: Suitable for mature audiences only
   * - explicit: Contains explicit content
   */
  contentRating: 'general' | 'teen' | 'mature' | 'explicit';

  /**
   * Processing status of the video
   *
   * Possible values:
   * - uploading: Video is being uploaded
   * - processing: Video is being processed
   * - ready: Video is ready for viewing
   * - failed: Video processing failed
   * - transcoding: Video is being transcoded to different formats
   */
  processingStatus: 'uploading' | 'processing' | 'ready' | 'failed' | 'transcoding';

  /** Error message if processing failed */
  processingError?: string;

  /** Whether comments are enabled for this video */
  commentsEnabled: boolean;

  /** Whether ratings are enabled for this video */
  ratingsEnabled: boolean;

  /** Whether embedding is allowed for this video */
  embeddingEnabled: boolean;

  /** Video statistics */
  stats: {
    /** Number of views */
    views: number;

    /** Number of likes */
    likes: number;

    /** Number of dislikes */
    dislikes: number;

    /** Number of comments */
    comments: number;

    /** Number of shares */
    shares: number;

    /** Number of times added to playlists */
    playlistAdds: number;

    /** Average watch time in seconds */
    averageWatchTime: number;

    /** Retention rate (percentage of video watched on average) */
    retentionRate: number;
  };

  /** Video file details */
  file: {
    /** Original filename */
    originalName: string;

    /** File size in bytes */
    size: number;

    /** MIME type */
    mimeType: string;

    /** Video codec */
    codec: string;

    /** Video resolution (e.g., '1920x1080') */
    resolution: string;

    /** Video bitrate in kbps */
    bitrate: number;

    /** Video frame rate */
    frameRate: number;
  };

  /** Available video quality variants */
  variants: Array<{
    /** Quality label (e.g., '1080p', '720p', '480p') */
    quality: string;

    /** URL to this quality variant */
    url: string;

    /** Resolution of this variant (e.g., '1920x1080') */
    resolution: string;

    /** Bitrate of this variant in kbps */
    bitrate: number;

    /** File size of this variant in bytes */
    size: number;
  }>;

  /** Captions/subtitles for the video */
  captions: Array<{
    /** Language code (ISO 639-1) */
    language: string;

    /** Language name (e.g., 'English', 'Spanish') */
    label: string;

    /** URL to the caption file */
    url: string;

    /** Whether this is auto-generated */
    isAutoGenerated: boolean;

    /** Whether this is the default caption track */
    isDefault: boolean;
  }>;

  /** Monetization settings */
  monetization: {
    /** Whether monetization is enabled */
    enabled: boolean;

    /**
     * Monetization types enabled for this video
     * Array can include: 'ads', 'sponsorship', 'subscription'
     */
    types: string[];

    /** Whether mid-roll ads are enabled */
    midrollEnabled: boolean;

    /** Timestamps for mid-roll ads in seconds */
    midrollTimestamps: number[];
  };

  /** Chapters/segments of the video */
  chapters: Array<{
    /** Title of the chapter */
    title: string;

    /** Start time of the chapter in seconds */
    startTime: number;

    /** End time of the chapter in seconds */
    endTime: number;

    /** Optional thumbnail for this chapter */
    thumbnailUrl?: string;
  }>;

  /** Location information */
  location?: {
    /** Latitude */
    latitude: number;

    /** Longitude */
    longitude: number;

    /** Location name */
    name: string;
  };

  /** Copyright information */
  copyright: {
    /** Owner of the copyright */
    owner: string;

    /** Type of license */
    license: string;

    /** Whether reuse is allowed */
    allowReuse: boolean;

    /** Whether commercial use is allowed */
    allowCommercialUse: boolean;

    /** Whether modification is allowed */
    allowModification: boolean;

    /** Attribution requirements */
    attributionRequired: boolean;
  };

  /** Source information for imported or embedded videos */
  source?: {
    /** Type of source */
    type: 'import' | 'embed';

    /** Original URL of the video */
    originalUrl: string;

    /** Platform the video is from (e.g., 'youtube', 'vimeo') */
    platform: string;

    /** External ID on the source platform */
    externalId?: string;
  };

  /** Available languages for the video */
  languages?: Array<{
    /** Language code (ISO 639-1) */
    code: string;

    /** Language name (e.g., 'English', 'Spanish') */
    name: string;

    /** Flag emoji for the language */
    flag?: string;

    /** Whether this is the default language */
    isDefault: boolean;

    /** URL or ID for this language version */
    url: string;
  }>;
}

/**
 * Video schema definition
 */
const VideoSchema = new Schema<IVideo>(
  {
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 5000,
    },
    url: {
      type: String,
      required: true,
    },
    thumbnailUrl: {
      type: String,
      required: true,
    },
    duration: {
      type: Number,
      required: true,
      min: 0,
    },
    userId: {
      type: String,
      required: true,
      ref: 'User',
    },
    channelId: {
      type: String,
      required: true,
      ref: 'Channel',
    },
    visibility: {
      type: String,
      enum: ['public', 'unlisted', 'private', 'scheduled'],
      default: 'private',
    },
    scheduledPublishTime: {
      type: Date,
    },
    tags: {
      type: [String],
      default: [],
    },
    category: {
      type: String,
      required: true,
    },
    contentRating: {
      type: String,
      enum: ['general', 'teen', 'mature', 'explicit'],
      default: 'general',
    },
    processingStatus: {
      type: String,
      enum: ['uploading', 'processing', 'ready', 'failed', 'transcoding'],
      default: 'uploading',
    },
    processingError: {
      type: String,
    },
    commentsEnabled: {
      type: Boolean,
      default: true,
    },
    ratingsEnabled: {
      type: Boolean,
      default: true,
    },
    embeddingEnabled: {
      type: Boolean,
      default: true,
    },
    stats: {
      views: {
        type: Number,
        default: 0,
      },
      likes: {
        type: Number,
        default: 0,
      },
      dislikes: {
        type: Number,
        default: 0,
      },
      comments: {
        type: Number,
        default: 0,
      },
      shares: {
        type: Number,
        default: 0,
      },
      playlistAdds: {
        type: Number,
        default: 0,
      },
      averageWatchTime: {
        type: Number,
        default: 0,
      },
      retentionRate: {
        type: Number,
        default: 0,
      },
    },
    file: {
      originalName: {
        type: String,
        required: true,
      },
      size: {
        type: Number,
        required: true,
      },
      mimeType: {
        type: String,
        required: true,
      },
      codec: {
        type: String,
      },
      resolution: {
        type: String,
      },
      bitrate: {
        type: Number,
      },
      frameRate: {
        type: Number,
      },
    },
    variants: [
      {
        quality: {
          type: String,
          required: true,
        },
        url: {
          type: String,
          required: true,
        },
        resolution: {
          type: String,
          required: true,
        },
        bitrate: {
          type: Number,
          required: true,
        },
        size: {
          type: Number,
          required: true,
        },
      },
    ],
    captions: [
      {
        language: {
          type: String,
          required: true,
        },
        label: {
          type: String,
          required: true,
        },
        url: {
          type: String,
          required: true,
        },
        isAutoGenerated: {
          type: Boolean,
          default: false,
        },
        isDefault: {
          type: Boolean,
          default: false,
        },
      },
    ],
    monetization: {
      enabled: {
        type: Boolean,
        default: false,
      },
      types: {
        type: [String],
        default: [],
      },
      midrollEnabled: {
        type: Boolean,
        default: false,
      },
      midrollTimestamps: {
        type: [Number],
        default: [],
      },
    },
    chapters: [
      {
        title: {
          type: String,
          required: true,
        },
        startTime: {
          type: Number,
          required: true,
        },
        endTime: {
          type: Number,
          required: true,
        },
        thumbnailUrl: {
          type: String,
        },
      },
    ],
    location: {
      latitude: {
        type: Number,
      },
      longitude: {
        type: Number,
      },
      name: {
        type: String,
      },
    },
    copyright: {
      owner: {
        type: String,
        default: '',
      },
      license: {
        type: String,
        default: 'All Rights Reserved',
      },
      allowReuse: {
        type: Boolean,
        default: false,
      },
      allowCommercialUse: {
        type: Boolean,
        default: false,
      },
      allowModification: {
        type: Boolean,
        default: false,
      },
      attributionRequired: {
        type: Boolean,
        default: true,
      },
    },
    source: {
      type: {
        type: String,
        enum: ['import', 'embed'],
      },
      originalUrl: {
        type: String,
      },
      platform: {
        type: String,
      },
      externalId: {
        type: String,
      },
    },
    languages: [
      {
        code: {
          type: String,
          required: true,
        },
        name: {
          type: String,
          required: true,
        },
        flag: {
          type: String,
        },
        isDefault: {
          type: Boolean,
          default: false,
        },
        url: {
          type: String,
          required: true,
        },
      },
    ],
  }
);

// Merge with base schema
VideoSchema.add(BaseSchema);

// Add pre-save hook to ensure languages have URLs, truncate long descriptions, and validate Engaxe IDs
VideoSchema.pre('save', function(next) {
  // Clean and truncate description if needed
  if (this.description) {
    // Clean numbered tags like #[150], #[153], etc. and br tags
    this.description = this.description
      .replace(/#\[\d+\]/g, '')
      .replace(/<br\s*\/?>/gi, ' ')
      .replace(/&lt;br\s*\/?&gt;/gi, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Truncate if it exceeds 5000 characters
    if (this.description.length > 5000) {
      console.log(`Truncating description from ${this.description.length} to 5000 characters`);
      this.description = this.description.substring(0, 4997) + '...';
    }
  }

  // Validate Engaxe IDs - ensure they are 6-7 character alphanumeric strings
  // This is critical for video playback to work correctly
  if (this.source && this.source.platform === 'engaxe') {
    // Validate the URL field
    if (this.url) {
      const isValidEngaxeId = /^[a-zA-Z0-9]{6,7}$/.test(this.url);
      if (!isValidEngaxeId) {
        console.error(`Invalid Engaxe ID in url field: ${this.url}. Must be 6-7 alphanumeric characters.`);
        return next(new Error('Invalid Engaxe ID. Must be 6-7 alphanumeric characters.'));
      }
    }

    // Validate the source.externalId field
    if (this.source.externalId) {
      const isValidExternalId = /^[a-zA-Z0-9]{6,7}$/.test(this.source.externalId);
      if (!isValidExternalId) {
        console.error(`Invalid Engaxe ID in source.externalId field: ${this.source.externalId}. Must be 6-7 alphanumeric characters.`);
        return next(new Error('Invalid Engaxe ID. Must be 6-7 alphanumeric characters.'));
      }
    }

    // Validate the source.originalUrl field if it's just an ID
    if (this.source.originalUrl && !this.source.originalUrl.includes('/') && !this.source.originalUrl.includes('.')) {
      const isValidOriginalUrl = /^[a-zA-Z0-9]{6,7}$/.test(this.source.originalUrl);
      if (!isValidOriginalUrl) {
        console.error(`Invalid Engaxe ID in source.originalUrl field: ${this.source.originalUrl}. Must be 6-7 alphanumeric characters.`);
        return next(new Error('Invalid Engaxe ID. Must be 6-7 alphanumeric characters.'));
      }
    }

    // Validate language URLs if they're just IDs
    if (this.languages && this.languages.length > 0) {
      for (const lang of this.languages) {
        if (lang.url && !lang.url.includes('/') && !lang.url.includes('.')) {
          const isValidLangUrl = /^[a-zA-Z0-9]{6,7}$/.test(lang.url);
          if (!isValidLangUrl) {
            console.error(`Invalid Engaxe ID in language URL: ${lang.url}. Must be 6-7 alphanumeric characters.`);
            return next(new Error('Invalid Engaxe ID in language URL. Must be 6-7 alphanumeric characters.'));
          }
        }
      }
    }
  }

  // Process languages
  console.log(`Processing languages for video ${this.id} (${this.title})`);

  // If there are no languages, add a default one with the video URL
  if (!this.languages || this.languages.length === 0) {
    console.log(`No languages found, creating default English language with URL: ${this.url}`);
    this.languages = [{
      code: 'en',
      name: 'English',
      flag: '🇺🇸',
      isDefault: true,
      url: this.url
    }];
  } else {
    // At this point, we know this.languages is defined and has at least one element
    const languages = this.languages; // Create a local variable to satisfy TypeScript
    console.log(`Found ${languages.length} languages, ensuring they all have URLs and flags`);

    // Check if we have at least one default language
    const hasDefaultLanguage = languages.some(lang => lang.isDefault);

    // Ensure all languages have a URL and flag
    languages.forEach((lang, index) => {
      // Ensure name and code exist
      const name = lang.name || 'Unknown';
      const code = lang.code || 'unknown';

      console.log(`Processing language ${index + 1}/${languages.length}: ${name} (${code})`);

      // Ensure URL
      if (!lang.url || lang.url === '') {
        console.log(`Language ${name} has no URL, using video URL: ${this.url}`);
        lang.url = this.url;
      }

      // Ensure flag
      if (!lang.flag) {
        // Add flag based on language code
        switch (code) {
          case 'en': lang.flag = '🇺🇸'; break;
          case 'hi': lang.flag = '🇮🇳'; break;
          case 'es': lang.flag = '🇪🇸'; break;
          case 'fr': lang.flag = '🇫🇷'; break;
          case 'de': lang.flag = '🇩🇪'; break;
          case 'ja': lang.flag = '🇯🇵'; break;
          case 'zh': lang.flag = '🇨🇳'; break;
          case 'ru': lang.flag = '🇷🇺'; break;
          case 'ar': lang.flag = '🇸🇦'; break;
          case 'pt': lang.flag = '🇵🇹'; break;
          case 'it': lang.flag = '🇮🇹'; break;
          case 'nl': lang.flag = '🇳🇱'; break;
          case 'ko': lang.flag = '🇰🇷'; break;
          case 'tr': lang.flag = '🇹🇷'; break;
          default: lang.flag = '🌐'; break;
        }
        console.log(`Added flag ${lang.flag} for language ${name}`);
      }

      // Set isDefault if not already set
      if (typeof lang.isDefault === 'undefined') {
        // If no default language yet, make the first one default
        // or make English the default if it exists
        if (!hasDefaultLanguage && (index === 0 || code === 'en')) {
          console.log(`Setting language ${name} as default`);
          lang.isDefault = true;
        } else {
          lang.isDefault = false;
        }
      }
    });

    // If no default language was set, make the first one default
    if (!hasDefaultLanguage && languages.length > 0) {
      const firstName = languages[0].name || 'Unknown';
      console.log(`No default language found, setting ${firstName} as default`);
      languages[0].isDefault = true;
    }
  }

  next();
});

// Add indexes for common queries
VideoSchema.index({ userId: 1 });
VideoSchema.index({ channelId: 1 });
VideoSchema.index({ visibility: 1 });
VideoSchema.index({ tags: 1 });
VideoSchema.index({ category: 1 });
VideoSchema.index({ 'stats.views': -1 });
VideoSchema.index({ createdAt: -1 });

// Create and export the Video model
const VideoModel = mongoose.model<IVideo>('Video', VideoSchema);
export default VideoModel;
