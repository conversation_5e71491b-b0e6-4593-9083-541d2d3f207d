import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import { channelAPI } from '@/services/api';
import ChannelGrid from '@/components/channels/ChannelGrid';
import { toast } from 'sonner';

export default function UserChannels() {
  const { currentUser } = useAuth();
  const { t } = useLanguage();
  const [channels, setChannels] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch user's channels
  useEffect(() => {
    const fetchUserChannels = async () => {
      if (!currentUser) return;
      
      try {
        setIsLoading(true);
        const response = await channelAPI.getUserChannels(currentUser.id);
        
        if (response.success) {
          setChannels(response.channels);
        } else {
          toast.error('Failed to load channels');
        }
      } catch (error) {
        console.error('Error fetching user channels:', error);
        toast.error('Failed to load channels');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUserChannels();
  }, [currentUser]);

  if (channels.length === 0 && !isLoading) {
    return (
      <div className="text-center p-8 border border-dashed rounded-lg">
        <h3 className="font-medium mb-2">You don't have any channels yet</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Create a channel to start uploading videos
        </p>
        <Link to="/create-channel">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create Channel
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Your Channels</h2>
        <Link to="/create-channel">
          <Button variant="outline" size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Create Channel
          </Button>
        </Link>
      </div>
      
      <ChannelGrid
        channels={channels}
        isLoading={isLoading}
        emptyMessage="You don't have any channels yet"
      />
    </div>
  );
}
