<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LawEngaxe Chat Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .container {
      display: flex;
      gap: 20px;
    }
    .sidebar {
      width: 30%;
    }
    .chat-area {
      width: 70%;
      border: 1px solid #ccc;
      border-radius: 5px;
      padding: 10px;
    }
    .messages {
      height: 400px;
      overflow-y: auto;
      border: 1px solid #eee;
      padding: 10px;
      margin-bottom: 10px;
    }
    .message {
      margin-bottom: 10px;
      padding: 8px;
      border-radius: 5px;
    }
    .message.sent {
      background-color: #e3f2fd;
      margin-left: 20%;
    }
    .message.received {
      background-color: #f5f5f5;
      margin-right: 20%;
    }
    .message .sender {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .message .time {
      font-size: 0.8em;
      color: #666;
      text-align: right;
    }
    .input-area {
      display: flex;
      gap: 10px;
    }
    .input-area input {
      flex-grow: 1;
      padding: 8px;
    }
    .input-area button {
      padding: 8px 15px;
      background-color: #4caf50;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    .input-area button:hover {
      background-color: #45a049;
    }
    .status {
      margin-top: 10px;
      padding: 5px;
      border-radius: 5px;
    }
    .status.connected {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    .status.disconnected {
      background-color: #ffebee;
      color: #c62828;
    }
    .conversation-list {
      margin-bottom: 20px;
    }
    .conversation {
      padding: 10px;
      border: 1px solid #eee;
      border-radius: 5px;
      margin-bottom: 5px;
      cursor: pointer;
    }
    .conversation:hover {
      background-color: #f5f5f5;
    }
    .conversation.active {
      background-color: #e3f2fd;
    }
    .login-form {
      margin-bottom: 20px;
    }
    .login-form input {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
    }
    .login-form button {
      width: 100%;
      padding: 8px;
      background-color: #2196f3;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    .login-form button:hover {
      background-color: #1e88e5;
    }
  </style>
</head>
<body>
  <h1>LawEngaxe Chat Test</h1>
  
  <div class="container">
    <div class="sidebar">
      <div class="login-form">
        <h3>Login</h3>
        <input type="text" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="admin123">
        <button id="login-btn">Login</button>
      </div>
      
      <h3>Conversations</h3>
      <div class="conversation-list" id="conversation-list">
        <!-- Conversations will be loaded here -->
      </div>
      
      <div>
        <h3>New Conversation</h3>
        <input type="text" id="creator-id" placeholder="Creator ID">
        <input type="text" id="subject" placeholder="Subject">
        <button id="create-conversation-btn">Create</button>
      </div>
    </div>
    
    <div class="chat-area">
      <h3 id="conversation-title">Select a conversation</h3>
      <div class="messages" id="messages">
        <!-- Messages will be loaded here -->
      </div>
      
      <div class="input-area">
        <input type="text" id="message-input" placeholder="Type a message...">
        <button id="send-btn">Send</button>
      </div>
      
      <div class="status" id="status">
        Not connected
      </div>
    </div>
  </div>

  <script>
    // Global variables
    let token = '';
    let userId = '';
    let socket = null;
    let activeConversationId = null;
    
    // DOM elements
    const loginBtn = document.getElementById('login-btn');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const conversationList = document.getElementById('conversation-list');
    const conversationTitle = document.getElementById('conversation-title');
    const messagesContainer = document.getElementById('messages');
    const messageInput = document.getElementById('message-input');
    const sendBtn = document.getElementById('send-btn');
    const statusElement = document.getElementById('status');
    const creatorIdInput = document.getElementById('creator-id');
    const subjectInput = document.getElementById('subject');
    const createConversationBtn = document.getElementById('create-conversation-btn');
    
    // API base URL
    const API_BASE_URL = 'http://localhost:3002/api/v1';
    
    // Event listeners
    loginBtn.addEventListener('click', login);
    sendBtn.addEventListener('click', sendMessage);
    createConversationBtn.addEventListener('click', createConversation);
    messageInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessage();
      }
    });
    
    // Functions
    async function login() {
      try {
        const email = emailInput.value;
        const password = passwordInput.value;
        
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password }),
        });
        
        const data = await response.json();
        
        if (data.success) {
          token = data.data.accessToken;
          userId = data.data.user.id;
          
          // Connect to WebSocket
          connectWebSocket();
          
          // Load conversations
          loadConversations();
          
          // Update UI
          loginBtn.textContent = 'Logged In';
          loginBtn.disabled = true;
          emailInput.disabled = true;
          passwordInput.disabled = true;
        } else {
          alert('Login failed: ' + data.message);
        }
      } catch (error) {
        console.error('Login error:', error);
        alert('Login failed: ' + error.message);
      }
    }
    
    function connectWebSocket() {
      // Close existing connection if any
      if (socket) {
        socket.close();
      }
      
      // Create new WebSocket connection
      socket = new WebSocket(`ws://localhost:3002/ws/chat`);
      
      // WebSocket event handlers
      socket.onopen = () => {
        console.log('WebSocket connected');
        statusElement.textContent = 'Connected';
        statusElement.className = 'status connected';
        
        // Authenticate with token
        socket.send(JSON.stringify({
          type: 'auth',
          payload: {
            token,
          },
        }));
      };
      
      socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        console.log('WebSocket message:', data);
        
        switch (data.type) {
          case 'auth_success':
            console.log('Authentication successful');
            break;
            
          case 'auth_error':
            console.error('Authentication failed:', data.payload.message);
            statusElement.textContent = 'Authentication failed';
            statusElement.className = 'status disconnected';
            break;
            
          case 'message_sent':
            // Add message to UI
            addMessageToUI(data.payload.message, true);
            break;
            
          case 'new_message':
            // Add message to UI if it's for the active conversation
            if (data.payload.message.conversationId === activeConversationId) {
              addMessageToUI(data.payload.message, false);
              
              // Send read receipt
              socket.send(JSON.stringify({
                type: 'read_receipt',
                payload: {
                  messageId: data.payload.message.id,
                },
              }));
            } else {
              // Update conversation list to show unread message
              loadConversations();
            }
            break;
            
          case 'typing_indicator':
            // Show typing indicator
            if (data.payload.conversationId === activeConversationId) {
              statusElement.textContent = `${data.payload.userId} is typing...`;
              setTimeout(() => {
                statusElement.textContent = 'Connected';
              }, 3000);
            }
            break;
            
          case 'user_status':
            // Update user status in conversation list
            loadConversations();
            break;
            
          case 'error':
            console.error('WebSocket error:', data.payload.message);
            break;
        }
      };
      
      socket.onclose = () => {
        console.log('WebSocket disconnected');
        statusElement.textContent = 'Disconnected';
        statusElement.className = 'status disconnected';
      };
      
      socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        statusElement.textContent = 'Error: ' + error.message;
        statusElement.className = 'status disconnected';
      };
      
      // Send ping every 30 seconds to keep connection alive
      setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({
            type: 'ping',
            payload: {
              timestamp: new Date(),
            },
          }));
        }
      }, 30000);
    }
    
    async function loadConversations() {
      try {
        const response = await fetch(`${API_BASE_URL}/conversations`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        const data = await response.json();
        
        if (data.success) {
          // Clear conversation list
          conversationList.innerHTML = '';
          
          // Add conversations to list
          data.data.forEach(conversation => {
            const conversationElement = document.createElement('div');
            conversationElement.className = 'conversation';
            if (conversation.id === activeConversationId) {
              conversationElement.className += ' active';
            }
            
            const otherParticipant = conversation.creator || conversation.user;
            const unreadCount = conversation.creatorId === userId ? conversation.creatorUnreadCount : conversation.userUnreadCount;
            
            conversationElement.innerHTML = `
              <div><strong>${conversation.subject}</strong></div>
              <div>${otherParticipant ? otherParticipant.displayName : 'Unknown'}</div>
              ${unreadCount > 0 ? `<div><strong>${unreadCount} unread</strong></div>` : ''}
            `;
            
            conversationElement.addEventListener('click', () => {
              loadMessages(conversation.id, conversation.subject);
            });
            
            conversationList.appendChild(conversationElement);
          });
        } else {
          console.error('Failed to load conversations:', data.message);
        }
      } catch (error) {
        console.error('Error loading conversations:', error);
      }
    }
    
    async function loadMessages(conversationId, subject) {
      try {
        activeConversationId = conversationId;
        conversationTitle.textContent = subject;
        
        // Update active conversation in list
        const conversations = document.querySelectorAll('.conversation');
        conversations.forEach(conv => {
          conv.classList.remove('active');
          if (conv.dataset.id === conversationId) {
            conv.classList.add('active');
          }
        });
        
        // Clear messages container
        messagesContainer.innerHTML = '';
        
        // Load messages
        const response = await fetch(`${API_BASE_URL}/messages/conversation/${conversationId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        const data = await response.json();
        
        if (data.success) {
          // Add messages to UI
          data.data.forEach(message => {
            addMessageToUI(message, message.senderId === userId);
          });
          
          // Mark conversation as read
          await fetch(`${API_BASE_URL}/conversations/${conversationId}/read`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          });
          
          // Scroll to bottom
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        } else {
          console.error('Failed to load messages:', data.message);
        }
      } catch (error) {
        console.error('Error loading messages:', error);
      }
    }
    
    function addMessageToUI(message, isSent) {
      const messageElement = document.createElement('div');
      messageElement.className = `message ${isSent ? 'sent' : 'received'}`;
      
      const sender = message.sender ? message.sender.displayName : (isSent ? 'You' : 'Unknown');
      const time = new Date(message.createdAt).toLocaleTimeString();
      
      messageElement.innerHTML = `
        <div class="sender">${sender}</div>
        <div class="content">${message.content}</div>
        <div class="time">${time}</div>
      `;
      
      messagesContainer.appendChild(messageElement);
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    function sendMessage() {
      if (!activeConversationId) {
        alert('Please select a conversation first');
        return;
      }
      
      const content = messageInput.value.trim();
      if (!content) {
        return;
      }
      
      // Send message via WebSocket
      socket.send(JSON.stringify({
        type: 'message',
        payload: {
          conversationId: activeConversationId,
          content,
          contentType: 'text',
          clientId: Date.now().toString(),
        },
      }));
      
      // Clear input
      messageInput.value = '';
    }
    
    async function createConversation() {
      try {
        const creatorId = creatorIdInput.value.trim();
        const subject = subjectInput.value.trim();
        
        if (!creatorId || !subject) {
          alert('Please enter creator ID and subject');
          return;
        }
        
        const response = await fetch(`${API_BASE_URL}/conversations`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            creatorId,
            subject,
          }),
        });
        
        const data = await response.json();
        
        if (data.success) {
          // Clear inputs
          creatorIdInput.value = '';
          subjectInput.value = '';
          
          // Reload conversations
          loadConversations();
          
          // Load the new conversation
          loadMessages(data.data.id, data.data.subject);
        } else {
          alert('Failed to create conversation: ' + data.message);
        }
      } catch (error) {
        console.error('Error creating conversation:', error);
        alert('Failed to create conversation: ' + error.message);
      }
    }
  </script>
</body>
</html>
