import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Type } from '@sinclair/typebox';
import { conversationController } from '../controllers/conversation.controller';
import { authenticate } from '../middleware/auth';
import { ErrorResponseSchema, SuccessResponseSchema } from '../middleware/swagger';

/**
 * Conversation routes
 */
export default async function conversationRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // Apply authentication middleware to all routes
  fastify.addHook('preHandler', authenticate);

  // Get all conversations for the current user
  fastify.get('/', {
    schema: {
      tags: ['Conversations'],
      description: 'Get all conversations for the current user',
      querystring: Type.Object({
        page: Type.Optional(Type.Number({ minimum: 1 })),
        limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100 })),
        sort: Type.Optional(Type.String()),
        status: Type.Optional(Type.Enum({ active: 'active', archived: 'archived', closed: 'closed' })),
        isPinned: Type.Optional(Type.Boolean()),
        search: Type.Optional(Type.String()),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Array(
            Type.Object({
              id: Type.String(),
              creatorId: Type.String(),
              userId: Type.String(),
              subject: Type.String(),
              status: Type.String(),
              creatorLanguage: Type.String(),
              userLanguage: Type.String(),
              lastMessageAt: Type.String({ format: 'date-time' }),
              creatorUnreadCount: Type.Number(),
              userUnreadCount: Type.Number(),
              isPinnedByCreator: Type.Boolean(),
              isPinnedByUser: Type.Boolean(),
              settings: Type.Object({
                notifications: Type.Boolean(),
                readReceipts: Type.Boolean(),
              }),
              metadata: Type.Object({
                tags: Type.Array(Type.String()),
                customFields: Type.Record(Type.String(), Type.Any()),
              }),
              createdAt: Type.String({ format: 'date-time' }),
              updatedAt: Type.String({ format: 'date-time' }),
              creator: Type.Optional(
                Type.Object({
                  id: Type.String(),
                  username: Type.String(),
                  displayName: Type.String(),
                  avatar: Type.Optional(Type.String()),
                })
              ),
              user: Type.Optional(
                Type.Object({
                  id: Type.String(),
                  username: Type.String(),
                  displayName: Type.String(),
                  avatar: Type.Optional(Type.String()),
                })
              ),
            })
          ),
          pagination: Type.Object({
            total: Type.Number(),
            page: Type.Number(),
            limit: Type.Number(),
            pages: Type.Number(),
          }),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, conversationController.getConversations);

  // Get a conversation by ID
  fastify.get('/:id', {
    schema: {
      tags: ['Conversations'],
      description: 'Get a conversation by ID',
      params: Type.Object({
        id: Type.String(),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Object({
            id: Type.String(),
            creatorId: Type.String(),
            userId: Type.String(),
            subject: Type.String(),
            status: Type.String(),
            creatorLanguage: Type.String(),
            userLanguage: Type.String(),
            lastMessageAt: Type.String({ format: 'date-time' }),
            creatorUnreadCount: Type.Number(),
            userUnreadCount: Type.Number(),
            isPinnedByCreator: Type.Boolean(),
            isPinnedByUser: Type.Boolean(),
            settings: Type.Object({
              notifications: Type.Boolean(),
              readReceipts: Type.Boolean(),
            }),
            metadata: Type.Object({
              tags: Type.Array(Type.String()),
              customFields: Type.Record(Type.String(), Type.Any()),
            }),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
            otherParticipant: Type.Object({
              id: Type.String(),
              username: Type.String(),
              displayName: Type.String(),
              avatar: Type.Optional(Type.String()),
            }),
          }),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, conversationController.getConversationById);

  // Create a new conversation
  fastify.post('/', {
    schema: {
      tags: ['Conversations'],
      description: 'Create a new conversation',
      body: Type.Object({
        creatorId: Type.String(),
        subject: Type.String({ minLength: 1, maxLength: 200 }),
        initialMessage: Type.Optional(Type.String()),
        userLanguage: Type.Optional(Type.String()),
        creatorLanguage: Type.Optional(Type.String()),
        settings: Type.Optional(
          Type.Object({
            notifications: Type.Optional(Type.Boolean()),
            readReceipts: Type.Optional(Type.Boolean()),
          })
        ),
        metadata: Type.Optional(
          Type.Object({
            tags: Type.Optional(Type.Array(Type.String())),
            customFields: Type.Optional(Type.Record(Type.String(), Type.Any())),
          })
        ),
      }),
      response: {
        201: Type.Object({
          success: Type.Boolean(),
          data: Type.Object({
            id: Type.String(),
            creatorId: Type.String(),
            userId: Type.String(),
            subject: Type.String(),
            status: Type.String(),
            creatorLanguage: Type.String(),
            userLanguage: Type.String(),
            lastMessageAt: Type.String({ format: 'date-time' }),
            creatorUnreadCount: Type.Number(),
            userUnreadCount: Type.Number(),
            isPinnedByCreator: Type.Boolean(),
            isPinnedByUser: Type.Boolean(),
            settings: Type.Object({
              notifications: Type.Boolean(),
              readReceipts: Type.Boolean(),
            }),
            metadata: Type.Object({
              tags: Type.Array(Type.String()),
              customFields: Type.Record(Type.String(), Type.Any()),
            }),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          }),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, conversationController.createConversation);

  // Update a conversation
  fastify.patch('/:id', {
    schema: {
      tags: ['Conversations'],
      description: 'Update a conversation',
      params: Type.Object({
        id: Type.String(),
      }),
      body: Type.Object({
        subject: Type.Optional(Type.String({ minLength: 1, maxLength: 200 })),
        status: Type.Optional(Type.Enum({ active: 'active', archived: 'archived', closed: 'closed' })),
        isPinned: Type.Optional(Type.Boolean()),
        settings: Type.Optional(
          Type.Object({
            notifications: Type.Optional(Type.Boolean()),
            readReceipts: Type.Optional(Type.Boolean()),
          })
        ),
        metadata: Type.Optional(
          Type.Object({
            tags: Type.Optional(Type.Array(Type.String())),
            customFields: Type.Optional(Type.Record(Type.String(), Type.Any())),
          })
        ),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Object({
            id: Type.String(),
            creatorId: Type.String(),
            userId: Type.String(),
            subject: Type.String(),
            status: Type.String(),
            creatorLanguage: Type.String(),
            userLanguage: Type.String(),
            lastMessageAt: Type.String({ format: 'date-time' }),
            creatorUnreadCount: Type.Number(),
            userUnreadCount: Type.Number(),
            isPinnedByCreator: Type.Boolean(),
            isPinnedByUser: Type.Boolean(),
            settings: Type.Object({
              notifications: Type.Boolean(),
              readReceipts: Type.Boolean(),
            }),
            metadata: Type.Object({
              tags: Type.Array(Type.String()),
              customFields: Type.Record(Type.String(), Type.Any()),
            }),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          }),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, conversationController.updateConversation);

  // Delete a conversation
  fastify.delete('/:id', {
    schema: {
      tags: ['Conversations'],
      description: 'Delete a conversation',
      params: Type.Object({
        id: Type.String(),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, conversationController.deleteConversation);

  // Mark a conversation as read
  fastify.post('/:id/read', {
    schema: {
      tags: ['Conversations'],
      description: 'Mark a conversation as read',
      params: Type.Object({
        id: Type.String(),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, conversationController.markConversationAsRead);
}
