import api from './api';

/**
 * Like API service
 */
export const likeAPI = {
  /**
   * Toggle like for a video
   * @param videoId Video ID
   * @returns Object with isLiked status and updated like count
   */
  toggleLike: async (videoId: string) => {
    try {
      const response = await api.post(`/likes/${videoId}`);
      return response.data;
    } catch (error) {
      console.error('Error toggling like:', error);
      throw error;
    }
  },

  /**
   * Check if a user has liked a video
   * @param videoId Video ID
   * @returns Boolean indicating if the user has liked the video
   */
  hasUserLiked: async (videoId: string) => {
    try {
      const response = await api.get(`/likes/${videoId}/check`);
      return response.data;
    } catch (error) {
      console.error('Error checking if user liked video:', error);
      throw error;
    }
  },

  /**
   * Get like count for a video
   * @param videoId Video ID
   * @returns Number of likes for the video
   */
  getLikeCount: async (videoId: string) => {
    try {
      const response = await api.get(`/likes/${videoId}/count`);
      return response.data;
    } catch (error) {
      console.error('Error getting like count:', error);
      throw error;
    }
  },
};
