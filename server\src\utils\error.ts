/**
 * Custom error class for application errors
 */
export class AppError extends Error {
  statusCode: number;
  errorCode: string;
  isOperational: boolean;

  constructor(message: string, statusCode = 500, errorCode = 'INTERNAL_SERVER_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.isOperational = true;

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Create a bad request error (400)
 */
export function createBadRequestError(message: string, errorCode = 'BAD_REQUEST'): AppError {
  return new AppError(message, 400, errorCode);
}

/**
 * Create an unauthorized error (401)
 */
export function createUnauthorizedError(message: string, errorCode = 'UNAUTHORIZED'): AppError {
  return new AppError(message, 401, errorCode);
}

/**
 * Create a forbidden error (403)
 */
export function createForbiddenError(message: string, errorCode = 'FORBIDDEN'): AppError {
  return new AppError(message, 403, errorCode);
}

/**
 * Create a not found error (404)
 */
export function createNotFoundError(message: string, errorCode = 'NOT_FOUND'): AppError {
  return new AppError(message, 404, errorCode);
}

/**
 * Create a conflict error (409)
 */
export function createConflictError(message: string, errorCode = 'CONFLICT'): AppError {
  return new AppError(message, 409, errorCode);
}

/**
 * Create an internal server error (500)
 */
export function createInternalServerError(message: string, errorCode = 'INTERNAL_SERVER_ERROR'): AppError {
  return new AppError(message, 500, errorCode);
}

/**
 * Create a service unavailable error (503)
 */
export function createServiceUnavailableError(message: string, errorCode = 'SERVICE_UNAVAILABLE'): AppError {
  return new AppError(message, 503, errorCode);
}

/**
 * Error codes for specific error scenarios
 */
export const ErrorCodes = {
  // Authentication errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_DISABLED: 'ACCOUNT_DISABLED',
  ACCOUNT_NOT_VERIFIED: 'ACCOUNT_NOT_VERIFIED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  UNAUTHORIZED: 'UNAUTHORIZED',
  
  // Authorization errors
  FORBIDDEN: 'FORBIDDEN',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // Resource errors
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  CONFLICT: 'CONFLICT',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  
  // Server errors
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR: 'DATABASE_ERROR',
  
  // API errors
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  API_ERROR: 'API_ERROR',
  THIRD_PARTY_API_ERROR: 'THIRD_PARTY_API_ERROR'
};
