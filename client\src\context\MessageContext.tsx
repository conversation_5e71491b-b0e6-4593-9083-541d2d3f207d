
import React, { createContext, useContext, useState, useEffect } from 'react';
import { Message, User, Conversation } from '@/types';
import { useAuth } from './AuthContext';
import messageAPI from '@/services/messageApi';
import { useToast } from '@/hooks/use-toast';

// Extend Window interface to include our mockMessages
declare global {
  interface Window {
    mockMessages: { [key: string]: Message[] };
  }
}

interface MessageContextType {
  conversations: Conversation[];
  activeConversation: Conversation | null;
  messages: Message[];
  sendTextMessage: (content: string, conversationId: string, guestId?: string) => void;
  sendVoiceMessage: (audioBlob: Blob, conversationId: string, guestId?: string) => void;
  translateMessage: (messageId: string, targetLanguage: string) => void;
  translateAllMessages: (targetLanguage: string) => void;
  setActiveConversation: (conversationId: string) => void;
  markAsRead: (messageIds: string[]) => void;
  isLoading: boolean;
  error: string | null;
  currentLanguage: string;
  setCurrentLanguage: (language: string) => void;
  createConversation: (data: {
    creatorId: string;
    subject: string;
    initialMessage: string;
    guestId?: string;
    settings?: {
      notifications?: boolean;
      encryption?: boolean;
      retention?: number;
      readReceipts?: boolean;
    };
    metadata?: {
      tags?: string[];
      customFields?: Record<string, any>;
    };
  }) => Promise<any>;
}

const MessageContext = createContext<MessageContextType | undefined>(undefined);

export const useMessages = () => {
  const context = useContext(MessageContext);
  if (context === undefined) {
    throw new Error('useMessages must be used within a MessageProvider');
  }
  return context;
};

// Mock data for initial conversations
const mockUsers: User[] = [
  { id: 'u1', username: 'CodeMaster', avatar: '/placeholder.svg', isOnline: true },
  { id: 'u2', username: 'VideoFan', avatar: '/placeholder.svg', isOnline: false },
  { id: 'u3', username: 'LearnDaily', avatar: '/placeholder.svg', isOnline: true },
  { id: 'u4', username: 'TechLover', avatar: '/placeholder.svg', isOnline: false }
];

const generateMockConversations = (): Conversation[] => {
  return [
    {
      id: 'c1',
      participants: [mockUsers[0]],
      lastMessage: {
        id: 'm1',
        sender: mockUsers[0],
        content: 'Hey! Did you check out the new tutorial?',
        timestamp: new Date(Date.now() - 15 * 60000).toISOString(),
        read: false
      },
      unreadCount: 1
    },
    {
      id: 'c2',
      participants: [mockUsers[1]],
      lastMessage: {
        id: 'm2',
        sender: mockUsers[1],
        content: 'Thanks for the language resources!',
        timestamp: new Date(Date.now() - 60 * 60000).toISOString(),
        read: true
      },
      unreadCount: 0
    },
    {
      id: 'c3',
      participants: [mockUsers[2]],
      lastMessage: {
        id: 'm3',
        sender: mockUsers[2],
        content: 'I\'m interested in collaborating on a video',
        timestamp: new Date(Date.now() - 24 * 60 * 60000).toISOString(),
        read: true
      },
      unreadCount: 0
    },
    {
      id: 'c4',
      participants: [mockUsers[3]],
      lastMessage: {
        id: 'm4',
        sender: mockUsers[3],
        content: 'Did you see my comment on your video?',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60000).toISOString(),
        read: false
      },
      unreadCount: 1
    }
  ];
};

export const MessageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { currentUser } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>(generateMockConversations());
  const [activeConversation, setActiveConversationState] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const { toast } = useToast();

  // Load saved messages from localStorage or use default mock data
  const loadSavedMessages = (): { [key: string]: Message[] } => {
    try {
      const savedMessages = localStorage.getItem('persistentMessages');
      if (savedMessages) {
        return JSON.parse(savedMessages);
      }
    } catch (error) {
      console.error('Failed to load messages from localStorage:', error);
    }
    return defaultMockMessages;
  };

  // Default mock messages as fallback
  const defaultMockMessages: { [key: string]: Message[] } = {
    'c1': [
      {
        id: 'msg1_c1',
        sender: mockUsers[0],
        content: 'Hey! Did you check out the new tutorial?',
        timestamp: new Date(Date.now() - 15 * 60000).toISOString(),
        read: false
      },
      {
        id: 'msg2_c1',
        sender: { id: 'self', username: 'Me', avatar: '/placeholder.svg' },
        content: 'Not yet, what is it about?',
        timestamp: new Date(Date.now() - 10 * 60000).toISOString(),
        read: true
      },
      {
        id: 'msg3_c1',
        sender: mockUsers[0],
        content: 'It\'s about building a multilingual video platform. I think you\'ll find it useful!',
        timestamp: new Date(Date.now() - 5 * 60000).toISOString(),
        read: false
      }
    ],
    'c2': [
      {
        id: 'msg1_c2',
        sender: mockUsers[1],
        content: 'Thanks for the language resources!',
        timestamp: new Date(Date.now() - 60 * 60000).toISOString(),
        read: true
      },
      {
        id: 'msg2_c2',
        sender: { id: 'self', username: 'Me', avatar: '/placeholder.svg' },
        content: 'You\'re welcome! Did you find them helpful?',
        timestamp: new Date(Date.now() - 55 * 60000).toISOString(),
        read: true
      },
      {
        id: 'msg3_c2',
        sender: mockUsers[1],
        content: 'Yes, they were exactly what I needed for my project.',
        timestamp: new Date(Date.now() - 50 * 60000).toISOString(),
        read: true
      }
    ],
    'c3': [
      {
        id: 'msg1_c3',
        sender: mockUsers[2],
        content: 'Hello, I\'m interested in collaborating on a video with you.',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60000).toISOString(),
        read: true
      },
      {
        id: 'msg2_c3',
        sender: { id: 'self', username: 'Me', avatar: '/placeholder.svg' },
        content: 'That sounds great! What kind of collaboration are you thinking?',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60000).toISOString(),
        read: true
      },
      {
        id: 'msg3_c3',
        sender: mockUsers[2],
        content: 'I\'m thinking about a multi-language programming tutorial series.',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60000 + 30 * 60000).toISOString(),
        read: true
      },
      {
        id: 'msg4_c3',
        sender: mockUsers[2],
        content: 'We could use Engaxe to handle the translations automatically.',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60000 + 35 * 60000).toISOString(),
        read: true,
        isVoice: true,
        audioUrl: 'https://example.com/voice-message.mp3'
      }
    ],
    'c4': [
      {
        id: 'msg1_c4',
        sender: mockUsers[3],
        content: 'Did you see my comment on your video?',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60000).toISOString(),
        read: false
      },
      {
        id: 'msg2_c4',
        sender: { id: 'self', username: 'Me', avatar: '/placeholder.svg' },
        content: 'Yes, I did! Thanks for the feedback.',
        timestamp: new Date(Date.now() - 1.5 * 24 * 60 * 60000).toISOString(),
        read: true
      },
      {
        id: 'msg3_c4',
        sender: mockUsers[3],
        content: 'You\'re welcome! I really enjoyed the content.',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60000).toISOString(),
        read: false
      }
    ]
  };

  // Make mockMessages accessible outside the component and initialize with saved data
  const mockMessages: { [key: string]: Message[] } = window.mockMessages = loadSavedMessages();

  // Function to save messages to localStorage
  const saveMessagesToLocalStorage = () => {
    try {
      localStorage.setItem('persistentMessages', JSON.stringify(mockMessages));
      console.log('Messages saved to localStorage');
    } catch (error) {
      console.error('Failed to save messages to localStorage:', error);
    }
  };

  // Initialize mockMessages with the correct conversation IDs
  useEffect(() => {
    // Log the mockMessages to help with debugging
    console.log('Initial mockMessages:', mockMessages);
    console.log('Available conversation IDs:', Object.keys(mockMessages));

    // Save initial messages to localStorage
    saveMessagesToLocalStorage();
  }, []);

  // Fetch conversations from API when component mounts
  useEffect(() => {
    if (currentUser) {
      const fetchConversations = async () => {
        try {
          setIsLoading(true);
          setError(null);

          // For now, use mock data but simulate API call
          setTimeout(() => {
            const mockConversations = generateMockConversations();
            setConversations(mockConversations);

            // If no conversation is active, set the first one as active
            if (!activeConversation && mockConversations.length > 0) {
              const firstConversation = mockConversations[0];
              setActiveConversationState(firstConversation);

              // Load messages for the first conversation
              const conversationMessages = mockMessages[firstConversation.id] || [];
              console.log('Auto-loading messages for first conversation:', firstConversation.id);
              console.log('Found messages:', conversationMessages);
              setMessages(conversationMessages);
            }

            setIsLoading(false);
          }, 500);

          // In a real implementation, we would use the API:
          // const response = await messageAPI.getConversations();
          // if (response.success && response.data) {
          //   setConversations(response.data);
          // }
        } catch (err) {
          console.error('Failed to fetch conversations:', err);
          setError('Failed to load conversations');
          toast({
            title: 'Error',
            description: 'Failed to load conversations',
            variant: 'destructive'
          });
        } finally {
          setIsLoading(false);
        }
      };

      fetchConversations();

      // Set up polling for new conversations
      const intervalId = setInterval(() => {
        // In a real implementation, we would poll for new conversations
        // fetchConversations();
      }, 30000); // Poll every 30 seconds

      return () => clearInterval(intervalId);
    }
  }, [currentUser, toast, activeConversation]);

  // Load messages when active conversation changes
  useEffect(() => {
    if (activeConversation) {
      const fetchMessages = async () => {
        try {
          setIsLoading(true);
          setError(null);

          // For now, use mock data but simulate API call
          setTimeout(() => {
            const conversationMessages = mockMessages[activeConversation.id] || [];
            console.log('Loading messages for conversation:', activeConversation.id);
            console.log('Found messages:', conversationMessages);
            console.log('All available mock messages:', mockMessages);
            setMessages(conversationMessages);
            setIsLoading(false);
          }, 500);

          // In a real implementation, we would use the API:
          // const response = await messageAPI.getMessagesByConversation(activeConversation.id);
          // if (response.success && response.data) {
          //   // Transform API response to match our Message type
          //   const formattedMessages = response.data.map((msg: any) => {
          //     const isSelf = msg.senderId === currentUser?.id;
          //
          //     // Create a sender object
          //     const sender: User = isSelf
          //       ? { id: 'self', username: 'Me', avatar: currentUser?.avatar || '/placeholder.svg' }
          //       : {
          //           id: msg.senderId,
          //           username: activeConversation.participants[0].username,
          //           avatar: activeConversation.participants[0].avatar
          //         };
          //
          //     return {
          //       id: msg.id,
          //       sender,
          //       content: msg.content,
          //       timestamp: msg.createdAt,
          //       read: msg.readStatus?.isRead || false,
          //       isVoice: msg.contentType === 'voice',
          //       audioUrl: msg.attachments?.find((a: any) => a.type === 'audio')?.url
          //     };
          //   });
          //
          //   setMessages(formattedMessages);
          //
          //   // Mark conversation as read
          //   messageAPI.markConversationAsRead(activeConversation.id)
          //     .catch(err => console.error('Failed to mark conversation as read:', err));
          // }
        } catch (err) {
          console.error('Failed to fetch messages:', err);
          setError('Failed to load messages');
          toast({
            title: 'Error',
            description: 'Failed to load messages',
            variant: 'destructive'
          });
        } finally {
          setIsLoading(false);
        }
      };

      fetchMessages();

      // Removed automatic polling to prevent unwanted reloads
      // Messages will be updated through real-time events or manual refresh

      return () => {
        // Cleanup function - no interval to clear
      };
    }
  }, [activeConversation, currentUser, toast]);

  const setActiveConversation = (conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId) || null;
    setActiveConversationState(conversation);

    // Mark messages as read
    if (conversation) {
      // Immediately load messages for this conversation
      const conversationMessages = mockMessages[conversationId] || [];
      console.log('Immediately loading messages for conversation:', conversationId);
      console.log('Found messages:', conversationMessages);
      setMessages(conversationMessages);

      const updatedConversations = conversations.map(c =>
        c.id === conversationId ? { ...c, unreadCount: 0 } : c
      );
      setConversations(updatedConversations);
    }
  };

  const sendTextMessage = async (content: string, conversationId: string, guestId?: string) => {
    if (!content.trim()) return;

    try {
      // Determine sender based on whether user is logged in or a guest
      let sender;
      if (currentUser) {
        sender = { id: 'self', username: 'Me', avatar: currentUser.avatar || '/placeholder.svg' };
      } else if (guestId) {
        sender = { id: guestId, username: 'Guest', avatar: '/placeholder.svg', guestId };
      } else {
        // Fallback if neither currentUser nor guestId is provided
        sender = { id: 'guest', username: 'Guest', avatar: '/placeholder.svg' };
      }

      // Optimistically add message to UI
      const optimisticMessage: Message = {
        id: `temp-${Date.now()}`,
        sender,
        content,
        timestamp: new Date().toISOString(),
        read: true
      };

      // Add to current messages view
      setMessages(prev => [...prev, optimisticMessage]);

      // Also add to our mock messages store for persistence
      if (!mockMessages[conversationId]) {
        mockMessages[conversationId] = [];
      }
      mockMessages[conversationId] = [...mockMessages[conversationId], optimisticMessage];

      // Save to localStorage for permanent storage
      saveMessagesToLocalStorage();

      // In a real implementation, we would send to the API:
      // const response = await messageAPI.sendTextMessage({
      //   conversationId,
      //   content,
      //   contentType: 'text'
      // });

      // For now, simulate API call with a delay
      setTimeout(() => {
        // Simulate receiving a response message after a delay
        if (Math.random() > 0.3) { // 70% chance to get a response
          const responseMessage: Message = {
            id: `msg-${Date.now() + 1}`,
            sender: activeConversation?.participants[0] || {
              id: 'other',
              username: 'User',
              avatar: '/placeholder.svg'
            },
            content: getRandomResponse(content),
            timestamp: new Date(Date.now() + 2000).toISOString(),
            read: false
          };

          // Add the response message after a delay
          setTimeout(() => {
            // Add to current messages view
            setMessages(prev => [...prev, responseMessage]);

            // Also add to our mock messages store for persistence
            if (mockMessages[conversationId]) {
              mockMessages[conversationId] = [...mockMessages[conversationId], responseMessage];

              // Save to localStorage for permanent storage
              saveMessagesToLocalStorage();
            }

            // Update conversation last message
            const updatedConversations = conversations.map(c =>
              c.id === conversationId ? {
                ...c,
                lastMessage: responseMessage,
                unreadCount: (c.unreadCount || 0) + 1
              } : c
            );
            setConversations(updatedConversations);
          }, 2000);
        }
      }, 1000);

      // Update conversation last message immediately with our message
      const updatedConversations = conversations.map(c =>
        c.id === conversationId ? {
          ...c,
          lastMessage: optimisticMessage
        } : c
      );
      setConversations(updatedConversations);
    } catch (err) {
      console.error('Failed to send message:', err);
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive'
      });

      // Remove optimistic message from UI
      setMessages(prev => prev.filter(msg => !msg.id.startsWith('temp-')));

      // Also remove from mock messages store
      if (mockMessages[conversationId]) {
        mockMessages[conversationId] = mockMessages[conversationId].filter(msg => !msg.id.startsWith('temp-'));
      }
    }
  };

  // Helper function to generate random responses
  const getRandomResponse = (message: string): string => {
    const responses = [
      `Thanks for your message: "${message.substring(0, 30)}${message.length > 30 ? '...' : ''}"`,
      "I'll get back to you soon.",
      "Thanks for reaching out!",
      "I appreciate your message.",
      "Let me think about that...",
      "Interesting point!",
      "I understand your concern.",
      "Good question, let me check."
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const sendVoiceMessage = async (audioBlob: Blob, conversationId: string) => {
    try {
      // In a real app, we'd upload this to a server and get a URL
      const audioUrl = URL.createObjectURL(audioBlob);

      // Create a FormData object to send the audio file
      const formData = new FormData();
      formData.append('audio', audioBlob, 'voice-message.webm');
      formData.append('conversationId', conversationId);
      formData.append('contentType', 'voice');

      // Optimistically add message to UI
      const optimisticMessage: Message = {
        id: `temp-${Date.now()}`,
        sender: { id: 'self', username: 'Me', avatar: currentUser?.avatar || '/placeholder.svg' },
        content: 'Voice message',
        timestamp: new Date().toISOString(),
        isVoice: true,
        audioUrl,
        read: true
      };

      setMessages(prev => [...prev, optimisticMessage]);

      // In a real implementation, we would send to the API:
      // const response = await messageAPI.sendVoiceMessage(formData);

      // For now, simulate API call with a delay
      setTimeout(() => {
        // Update conversation last message
        const updatedConversations = conversations.map(c =>
          c.id === conversationId ? { ...c, lastMessage: { ...optimisticMessage, content: 'Voice message' } } : c
        );
        setConversations(updatedConversations);

        // Simulate receiving a response message after a delay
        if (Math.random() > 0.5) { // 50% chance to get a response
          const responseMessage: Message = {
            id: `msg-${Date.now() + 1}`,
            sender: activeConversation?.participants[0] || {
              id: 'other',
              username: 'User',
              avatar: '/placeholder.svg'
            },
            content: 'Thanks for your voice message. I\'ll listen to it soon.',
            timestamp: new Date(Date.now() + 3000).toISOString(),
            read: false
          };

          // Add the response message after a delay
          setTimeout(() => {
            setMessages(prev => [...prev, responseMessage]);

            // Update conversation last message
            const updatedConversations = conversations.map(c =>
              c.id === conversationId ? {
                ...c,
                lastMessage: responseMessage,
                unreadCount: (c.unreadCount || 0) + 1
              } : c
            );
            setConversations(updatedConversations);
          }, 3000);
        }
      }, 1000);
    } catch (err) {
      console.error('Failed to send voice message:', err);
      toast({
        title: 'Error',
        description: 'Failed to send voice message',
        variant: 'destructive'
      });

      // Remove optimistic message on error
      setMessages(prev => prev.filter(msg => !msg.id.startsWith('temp-')));
    }
  };

  const translateMessage = async (messageId: string, targetLanguage: string) => {
    // If target language is English, reset to original content
    if (targetLanguage === 'en') {
      setMessages(prevMessages => prevMessages.map(message => {
        if (message.id === messageId && message.isTranslated) {
          return {
            ...message,
            content: message.originalContent || message.content,
            isTranslated: false,
            originalLanguage: 'en'
          };
        }
        return message;
      }));

      setCurrentLanguage('en');
      return;
    }

    // Find the message to translate
    const messageToTranslate = messages.find(message => message.id === messageId);
    if (!messageToTranslate) return;

    // Always preserve the original content if not already saved
    const originalContent = messageToTranslate.isTranslated ?
      messageToTranslate.originalContent || messageToTranslate.content :
      messageToTranslate.content;

    try {
      // Import the translation function
      const { translateText } = await import('@/context/TranslationContext');

      // If it's a voice message, we simulate transcription and translation
      if (messageToTranslate.isVoice) {
        setMessages(prevMessages => prevMessages.map(message => {
          if (message.id === messageId) {
            return {
              ...message,
              content: `[Transcribed & Translated to ${targetLanguage.toUpperCase()}] "${originalContent}"`,
              isTranslated: true,
              originalContent: originalContent,
              originalLanguage: 'en' // assuming original is English
            };
          }
          return message;
        }));
      } else {
        // For text messages, use the Bhashini translation service
        // Get the translation context
        const translationContext = (window as any).__TRANSLATION_CONTEXT__;

        if (!translationContext) {
          console.error('Translation context not available');
          return;
        }

        // Use the translation function from the context
        const translatedContent = await translationContext.translateText(
          originalContent,
          targetLanguage,
          'en' // assuming original is English
        );

        // Update the message with the translated content
        setMessages(prevMessages => prevMessages.map(message => {
          if (message.id === messageId) {
            return {
              ...message,
              content: translatedContent,
              isTranslated: true,
              originalContent: originalContent,
              originalLanguage: 'en' // assuming original is English
            };
          }
          return message;
        }));
      }
    } catch (error) {
      console.error('Error translating message:', error);

      // Fallback to simple translation if Bhashini fails
      let translatedContent = `[${targetLanguage.toUpperCase()}] ${originalContent}`;

      setMessages(prevMessages => prevMessages.map(message => {
        if (message.id === messageId) {
          return {
            ...message,
            content: translatedContent,
            isTranslated: true,
            originalContent: originalContent,
            originalLanguage: 'en' // assuming original is English
          };
        }
        return message;
      }));
    }

    // Update current language
    setCurrentLanguage(targetLanguage);
  };

  const translateAllMessages = async (targetLanguage: string) => {
    // If target language is English, reset all messages to original content
    if (targetLanguage === 'en') {
      setMessages(prevMessages => prevMessages.map(message => {
        if (message.isTranslated) {
          return {
            ...message,
            content: message.originalContent || message.content,
            isTranslated: false,
            originalLanguage: 'en'
          };
        }
        return message;
      }));

      setCurrentLanguage('en');
      return;
    }

    try {
      // Get the translation context
      const translationContext = (window as any).__TRANSLATION_CONTEXT__;

      if (!translationContext) {
        console.error('Translation context not available');
        return;
      }

      // Create batches of messages to translate (max 5 messages per batch)
      const BATCH_SIZE = 5;
      const messageBatches: Message[][] = [];
      const updatedMessages = [...messages];

      // Prepare batches
      for (let i = 0; i < updatedMessages.length; i += BATCH_SIZE) {
        messageBatches.push(updatedMessages.slice(i, i + BATCH_SIZE));
      }

      // Process each batch in parallel
      await Promise.all(messageBatches.map(async (batch) => {
        // Process messages in this batch in parallel
        await Promise.all(batch.map(async (message, index) => {
          // Find the index in the original array
          const originalIndex = messages.findIndex(m => m.id === message.id);
          if (originalIndex === -1) return;

          // Always preserve the original content if not already saved
          const originalContent = message.isTranslated ?
            message.originalContent || message.content :
            message.content;

          // If it's a voice message, we simulate transcription and translation
          if (message.isVoice) {
            updatedMessages[originalIndex] = {
              ...message,
              content: `[Transcribed & Translated to ${targetLanguage.toUpperCase()}] "${originalContent}"`,
              isTranslated: true,
              originalContent: originalContent,
              originalLanguage: 'en' // assuming original is English
            };
          } else {
            try {
              // For text messages, use the Bhashini translation service
              const translatedContent = await translationContext.translateText(
                originalContent,
                targetLanguage,
                'en' // assuming original is English
              );

              updatedMessages[originalIndex] = {
                ...message,
                content: translatedContent,
                isTranslated: true,
                originalContent: originalContent,
                originalLanguage: 'en' // assuming original is English
              };
            } catch (error) {
              console.error(`Error translating message ${message.id}:`, error);

              // Fallback to simple translation if Bhashini fails
              updatedMessages[originalIndex] = {
                ...message,
                content: `[${targetLanguage.toUpperCase()}] ${originalContent}`,
                isTranslated: true,
                originalContent: originalContent,
                originalLanguage: 'en' // assuming original is English
              };
            }
          }
        }));
      }));

      // Update all messages at once
      setMessages(updatedMessages);
    } catch (error) {
      console.error('Error translating all messages:', error);

      // Fallback to simple translation if Bhashini fails
      setMessages(prevMessages => prevMessages.map(message => {
        // Always preserve the original content if not already saved
        const originalContent = message.isTranslated ?
          message.originalContent || message.content :
          message.content;

        return {
          ...message,
          content: `[${targetLanguage.toUpperCase()}] ${originalContent}`,
          isTranslated: true,
          originalContent: originalContent,
          originalLanguage: 'en' // assuming original is English
        };
      }));
    }

    // Update current language
    setCurrentLanguage(targetLanguage);
  };

  const markAsRead = (messageIds: string[]) => {
    setMessages(prevMessages =>
      prevMessages.map(message =>
        messageIds.includes(message.id) ? { ...message, read: true } : message
      )
    );
  };

  // Create a new conversation with a creator or add to existing one
  const createConversation = async (data: {
    creatorId: string;
    subject: string;
    initialMessage: string;
    guestId?: string;
    settings?: {
      notifications?: boolean;
      encryption?: boolean;
      retention?: number;
      readReceipts?: boolean;
    };
    metadata?: {
      tags?: string[];
      customFields?: Record<string, any>;
    };
  }) => {
    try {
      setIsLoading(true);
      setError(null);

      // Log the incoming data to debug
      console.log('Create conversation data:', data);

      // Determine if this is a guest user
      const isGuestUser = !currentUser && data.guestId;
      console.log('Is guest user:', isGuestUser, 'Guest ID:', data.guestId);

      // Check if a conversation with this creator already exists
      // For guest users, we need to check using the guest ID
      const existingConversation = conversations.find(c => {
        if (isGuestUser && data.guestId) {
          // For guest users, check if there's a conversation with this creator and guest ID
          return c.participants.some(p => p.id === data.creatorId) &&
                 c.metadata?.guestId === data.guestId;
        } else {
          // For logged-in users, check if there's a conversation with this creator
          return c.participants.some(p => p.id === data.creatorId);
        }
      });

      // DIRECT FIX: Force the creator name to be 'CodeMaster'
      // This is based on the mock data in VideoContext.tsx
      let creatorName = 'CodeMaster';

      console.log('Using creator name:', creatorName);

      if (existingConversation) {
        console.log('Found existing conversation:', existingConversation.id);

        // Add the new message to the existing conversation
        // Determine sender based on whether user is logged in or a guest
        let sender;
        if (currentUser) {
          sender = { id: 'self', username: 'Me', avatar: currentUser.avatar || '/placeholder.svg' };
        } else if (data.guestId) {
          sender = { id: data.guestId, username: 'Guest', avatar: '/placeholder.svg', guestId: data.guestId };
        } else {
          // Fallback if neither currentUser nor guestId is provided
          sender = { id: 'guest', username: 'Guest', avatar: '/placeholder.svg' };
        }

        const newMessage: Message = {
          id: `msg-${Date.now()}`,
          sender,
          content: data.initialMessage,
          timestamp: new Date().toISOString(),
          read: true
        };

        // Update the conversation's last message
        const updatedConversation = {
          ...existingConversation,
          lastMessage: newMessage
        };

        // Update conversations list
        setConversations(prev => [
          updatedConversation,
          ...prev.filter(c => c.id !== existingConversation.id)
        ]);

        // If this is the active conversation, add the message to the messages list
        if (activeConversation?.id === existingConversation.id) {
          setMessages(prev => [...prev, newMessage]);
        }

        // Add to our mock messages store for persistence
        if (!mockMessages[existingConversation.id]) {
          mockMessages[existingConversation.id] = [];
        }
        mockMessages[existingConversation.id] = [...mockMessages[existingConversation.id], newMessage];

        // Save to localStorage for permanent storage
        saveMessagesToLocalStorage();

        return { success: true, data: updatedConversation };
      } else {
        // Create a new conversation
        console.log('Creating new conversation with creator ID:', data.creatorId);

        // For now, simulate API call with a delay
        await new Promise(resolve => setTimeout(resolve, 500));

        const newConversationId = `c${Date.now()}`;

        const creator = {
          id: data.creatorId,
          username: creatorName,
          avatar: '/placeholder.svg',
          isOnline: Math.random() > 0.5
        };

        console.log('Created conversation with creator:', creator);

        // Determine sender based on whether user is logged in or a guest
        let sender;
        if (currentUser) {
          sender = { id: 'self', username: 'Me', avatar: currentUser.avatar || '/placeholder.svg' };
        } else if (data.guestId) {
          sender = { id: data.guestId, username: 'Guest', avatar: '/placeholder.svg', guestId: data.guestId };
        } else {
          // Fallback if neither currentUser nor guestId is provided
          sender = { id: 'guest', username: 'Guest', avatar: '/placeholder.svg' };
        }

        const initialMessage: Message = {
          id: `msg-${Date.now()}`,
          sender,
          content: data.initialMessage,
          timestamp: new Date().toISOString(),
          read: true
        };

        const newConversation: Conversation = {
          id: newConversationId,
          participants: [creator],
          lastMessage: initialMessage,
          unreadCount: 0,
          metadata: data.guestId ? { guestId: data.guestId } : undefined
        };

        // Add the new conversation to the list
        setConversations(prev => [newConversation, ...prev]);

        // Store the initial message in our mock messages store
        mockMessages[newConversationId] = [initialMessage];

        // Save to localStorage for permanent storage
        saveMessagesToLocalStorage();

        // If this conversation becomes active, we'll need the message
        if (activeConversation?.id === newConversationId) {
          setMessages([initialMessage]);
        }

        return { success: true, data: newConversation };
      }
    } catch (err) {
      console.error('Failed to create conversation:', err);
      setError('Failed to create conversation');
      toast({
        title: 'Error',
        description: 'Failed to create conversation',
        variant: 'destructive'
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MessageContext.Provider value={{
      conversations,
      activeConversation,
      messages,
      sendTextMessage,
      sendVoiceMessage,
      translateMessage,
      translateAllMessages,
      setActiveConversation,
      markAsRead,
      isLoading,
      error,
      currentLanguage,
      setCurrentLanguage,
      createConversation
    }}>
      {children}
    </MessageContext.Provider>
  );
};
