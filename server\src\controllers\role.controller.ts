import { FastifyRequest, FastifyReply } from 'fastify';
import roleService from '../services/role.service';
import { IRole } from '../models/role.model';
import { AuthenticatedUser } from '../types/user';

/**
 * Role controller for handling role-related requests
 */
export class RoleController {
  /**
   * Create a new role
   */
  async createRole(
    request: FastifyRequest<{
      Body: Partial<IRole>;
    }>,
    reply: FastifyReply
  ) {
    try {
      const roleData = request.body;
      const createdBy = (request.user as AuthenticatedUser).id;

      const role = await roleService.createRole(roleData, createdBy);

      return reply.code(201).send({
        success: true,
        message: 'Role created successfully',
        role: {
          id: role.id,
          name: role.name,
          description: role.description,
          code: role.code,
          permissions: role.permissions,
          isSystem: role.isSystem,
          isActive: role.isActive,
          priority: role.priority,
          createdAt: role.createdAt,
        },
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to create role',
      });
    }
  }

  /**
   * Get all roles
   */
  async getAllRoles(
    request: FastifyRequest<{
      Querystring: {
        page?: number;
        limit?: number;
        search?: string;
        isActive?: boolean;
        isSystem?: boolean;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const options = request.query;
      const result = await roleService.getAllRoles(options);

      // Map roles to include only necessary fields
      const roles = result.roles.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description,
        code: role.code,
        permissions: role.permissions,
        isSystem: role.isSystem,
        isActive: role.isActive,
        priority: role.priority,
        createdAt: role.createdAt,
      }));

      return reply.code(200).send({
        success: true,
        roles,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          pages: result.pages,
        },
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: (error as Error).message || 'Failed to get roles',
      });
    }
  }

  /**
   * Get role by ID
   */
  async getRoleById(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const role = await roleService.getRoleById(id);

      return reply.code(200).send({
        success: true,
        role: {
          id: role.id,
          name: role.name,
          description: role.description,
          code: role.code,
          permissions: role.permissions,
          isSystem: role.isSystem,
          isActive: role.isActive,
          priority: role.priority,
          parentId: role.parentId,
          maxUsers: role.maxUsers,
          isAssignable: role.isAssignable,
          autoAssignConditions: role.autoAssignConditions,
          createdAt: role.createdAt,
          updatedAt: role.updatedAt,
        },
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(404).send({
        success: false,
        message: (error as Error).message || 'Role not found',
      });
    }
  }

  /**
   * Update role
   */
  async updateRole(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
      Body: Partial<IRole>;
    }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const roleData = request.body;
      const updatedBy = (request.user as AuthenticatedUser).id;

      const role = await roleService.updateRole(id, roleData, updatedBy);

      return reply.code(200).send({
        success: true,
        message: 'Role updated successfully',
        role: {
          id: role.id,
          name: role.name,
          description: role.description,
          code: role.code,
          permissions: role.permissions,
          isActive: role.isActive,
          priority: role.priority,
          updatedAt: role.updatedAt,
        },
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to update role',
      });
    }
  }

  /**
   * Delete role
   */
  async deleteRole(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const deletedBy = (request.user as AuthenticatedUser).id;

      await roleService.deleteRole(id, deletedBy);

      return reply.code(200).send({
        success: true,
        message: 'Role deleted successfully',
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to delete role',
      });
    }
  }

  /**
   * Assign roles to user
   */
  async assignRolesToUser(
    request: FastifyRequest<{
      Params: {
        userId: string;
      };
      Body: {
        roleIds: string[];
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { userId } = request.params;
      const { roleIds } = request.body;
      const updatedBy = (request.user as AuthenticatedUser).id;

      const user = await roleService.assignRolesToUser(userId, roleIds, updatedBy);

      return reply.code(200).send({
        success: true,
        message: 'Roles assigned successfully',
        user: {
          id: user.id,
          username: user.username,
          roles: user.roles,
        },
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to assign roles',
      });
    }
  }

  /**
   * Get user roles
   */
  async getUserRoles(
    request: FastifyRequest<{
      Params: {
        userId: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { userId } = request.params;
      const roles = await roleService.getUserRoles(userId);

      return reply.code(200).send({
        success: true,
        roles: roles.map(role => ({
          id: role.id,
          name: role.name,
          code: role.code,
          permissions: role.permissions,
        })),
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to get user roles',
      });
    }
  }
}

export default new RoleController();
