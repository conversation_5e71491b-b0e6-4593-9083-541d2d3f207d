import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import './styles/h5p.css'
import './styles/video-player.css'
import { initH5PFullscreen, initSpecificH5PFullscreenButton } from './utils/h5pUtils'
import { initVideoPlayerFullscreen } from './utils/videoPlayerUtils'
import { initAutoHideScrollbars } from './utils/scrollUtils'
import { initH5PFullscreenFix } from './utils/h5pFullscreenFix'

// Initialize H5P and video player fullscreen functionality
document.addEventListener('DOMContentLoaded', () => {
  // Initialize all fullscreen functionality
  initH5PFullscreen();
  initVideoPlayerFullscreen();
  initSpecificH5PFullscreenButton();

  // Apply the special fix for the H5P fullscreen button
  initH5PFullscreenFix();

  // Initialize auto-hide scrollbars
  setTimeout(() => {
    initAutoHideScrollbars();
  }, 500); // Small delay to ensure DOM is fully loaded

  // Add a direct event listener for the specific fullscreen button
  // This is a last resort approach to ensure the button works
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;

    // Check if the clicked element matches any of our fullscreen button selectors
    if (
      target && (
        // Check for standard H5P fullscreen button
        (target.getAttribute('role') === 'button' &&
         target.getAttribute('tabindex') === '0' &&
         target.classList.contains('h5p-control') &&
         target.classList.contains('h5p-fullscreen')) ||

        // Check for fullscreen button by aria-label
        (target.getAttribute('role') === 'button' &&
         target.getAttribute('aria-label') === 'Fullscreen') ||

        // Check for any element with h5p-fullscreen class
        target.classList.contains('h5p-fullscreen') ||

        // Check for any element with fullscreen in the aria-label
        (target.hasAttribute('aria-label') &&
         target.getAttribute('aria-label')?.toLowerCase().includes('fullscreen'))
      )
    ) {
      console.log('Global click handler for H5P fullscreen button');

      // Prevent default behavior
      event.preventDefault();
      event.stopPropagation();

      // Find the container using multiple strategies
      // First try to find specific containers by ID
      let container = document.getElementById('engaxe-player-container');

      // If not found, try other common container IDs
      if (!container) {
        container = document.getElementById('video-container');
      }

      // If still not found, try to find by class names
      if (!container) {
        container = document.querySelector('.h5p-iframe-wrapper') as HTMLElement;
      }

      // If still not found, try to find the closest container from the event target
      if (!container && event.target) {
        container = target.closest('.h5p-iframe-wrapper') ||
                   target.closest('.h5p-content') ||
                   target.closest('.video-player') ||
                   target.closest('.h5p-video-container') as HTMLElement;
      }

      // If still not found, try to find any iframe parent
      if (!container && event.target) {
        const iframe = target.closest('iframe');
        if (iframe && iframe.parentElement) {
          container = iframe.parentElement;
        }
      }

      // Last resort: find any video container on the page
      if (!container) {
        container = document.querySelector('.video-player') ||
                   document.querySelector('.h5p-iframe-wrapper') ||
                   document.querySelector('.h5p-content') ||
                   document.querySelector('#video-container') as HTMLElement;
      }

      if (container) {
        try {
          if (document.fullscreenElement) {
            document.exitFullscreen().catch(err => {
              console.error('Error exiting fullscreen:', err);

              // Try alternative methods
              try {
                if ((document as any).mozCancelFullScreen) {
                  (document as any).mozCancelFullScreen();
                } else if ((document as any).webkitExitFullscreen) {
                  (document as any).webkitExitFullscreen();
                } else if ((document as any).msExitFullscreen) {
                  (document as any).msExitFullscreen();
                }
              } catch (altErr) {
                console.error('Alternative exit fullscreen methods failed:', altErr);
              }
            });
          } else {
            // Prepare container for fullscreen
            container.style.position = 'relative';
            container.style.width = '100%';
            container.style.height = '100%';

            // Try to enter fullscreen with all possible methods
            try {
              // Standard method
              if (container.requestFullscreen) {
                container.requestFullscreen().catch(err => {
                  console.error('Error with standard fullscreen:', err);
                  tryAlternativeMethods();
                });
              } else {
                tryAlternativeMethods();
              }

              // Function to try alternative fullscreen methods
              function tryAlternativeMethods() {
                if ((container as any).mozRequestFullScreen) {
                  (container as any).mozRequestFullScreen();
                } else if ((container as any).webkitRequestFullscreen) {
                  (container as any).webkitRequestFullscreen();
                } else if ((container as any).msRequestFullscreen) {
                  (container as any).msRequestFullscreen();
                } else {
                  console.error('Fullscreen API is not supported in this browser');
                }
              }
            } catch (error) {
              console.error('All fullscreen methods failed:', error);
            }
          }
        } catch (error) {
          console.error('Error toggling fullscreen:', error);
        }
      } else {
        // Last resort: try to make the document body fullscreen
        try {
          if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
              console.error('Error making document fullscreen:', err);
            });
          } else {
            document.exitFullscreen();
          }
        } catch (error) {
          console.error('Error with document fullscreen:', error);
        }
      }
    }
  }, true);

  // Add a mousedown event listener to catch events before they're processed
  document.addEventListener('mousedown', (event) => {
    const target = event.target as HTMLElement;

    // Check if the clicked element matches any of our fullscreen button selectors
    if (
      target && (
        // Check for standard H5P fullscreen button
        (target.getAttribute('role') === 'button' &&
         target.getAttribute('tabindex') === '0' &&
         target.classList.contains('h5p-control') &&
         target.classList.contains('h5p-fullscreen')) ||

        // Check for fullscreen button by aria-label
        (target.getAttribute('role') === 'button' &&
         target.getAttribute('aria-label') === 'Fullscreen') ||

        // Check for any element with h5p-fullscreen class
        target.classList.contains('h5p-fullscreen') ||

        // Check for any element with fullscreen in the aria-label
        (target.hasAttribute('aria-label') &&
         target.getAttribute('aria-label')?.toLowerCase().includes('fullscreen'))
      )
    ) {
      console.log('Mousedown on H5P fullscreen button detected');

      // Mark this element to be handled on click
      target.setAttribute('data-fullscreen-pending', 'true');

      // Apply additional styles to make it more clickable
      target.style.setProperty('pointer-events', 'auto', 'important');
      target.style.setProperty('cursor', 'pointer', 'important');
      target.style.setProperty('z-index', '99999', 'important');

      // Don't prevent default here to allow focus
    }
  }, true);
});

createRoot(document.getElementById("root")!).render(<App />);
