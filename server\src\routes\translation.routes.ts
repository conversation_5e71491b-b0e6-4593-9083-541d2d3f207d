import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Type } from '@sinclair/typebox';
import { TranslationController } from '../controllers/translation.controller';
import { ErrorResponseSchema } from '../schemas/error.schema';

/**
 * Translation routes
 */
export default async function translationRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  const translationController = new TranslationController();

  // Translate text
  fastify.post('/translate', {
    schema: {
      tags: ['Translation'],
      description: 'Translate text using Bhashini API',
      body: Type.Object({
        text: Type.String(),
        sourceLanguage: Type.String(),
        targetLanguage: Type.String(),
        userId: Type.Optional(Type.String()),
        ulcaApiKey: Type.Optional(Type.String()),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Object({
            translatedText: Type.String(),
            sourceLanguage: Type.String(),
            targetLanguage: Type.String(),
          }),
        }),
        400: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, translationController.translateText);

  // Detect language
  fastify.post('/detect-language', {
    schema: {
      tags: ['Translation'],
      description: 'Detect language of text using Bhashini API',
      body: Type.Object({
        text: Type.String(),
        userId: Type.Optional(Type.String()),
        ulcaApiKey: Type.Optional(Type.String()),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Object({
            detectedLanguage: Type.String(),
            confidence: Type.Number(),
          }),
        }),
        400: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, translationController.detectLanguage);

  // Test Bhashini API connection
  fastify.post('/test-connection', {
    schema: {
      tags: ['Translation'],
      description: 'Test connection to Bhashini API',
      body: Type.Object({
        userId: Type.Optional(Type.String()),
        ulcaApiKey: Type.Optional(Type.String()),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
        }),
        400: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, translationController.testConnection);
}
