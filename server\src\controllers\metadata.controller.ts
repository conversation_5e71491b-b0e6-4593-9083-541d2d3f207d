import { FastifyRequest, FastifyReply } from 'fastify';
import { MetadataService } from '../services/metadata.service';
import { AuthenticatedUser } from '../types/user';

/**
 * Controller for metadata-related endpoints
 */
export class MetadataController {
  private metadataService: MetadataService;

  constructor() {
    this.metadataService = new MetadataService();
  }

  /**
   * Fetch video metadata from a URL
   */
  fetchVideoMetadata = async (request: FastifyRequest<{
    Querystring: {
      url: string;
    }
  }>, reply: FastifyReply) => {
    try {
      const { url } = request.query;
      console.log(`Received metadata request for URL: ${url}`);

      if (!url) {
        console.error('URL parameter is missing');
        return reply.code(400).send({
          success: false,
          message: 'URL is required',
          error: {
            code: 'MISSING_PARAMETER',
            message: 'URL parameter is required',
          },
        });
      }

      // Get user ID if authenticated
      const userId = (request.user as AuthenticatedUser)?.id;
      console.log(`User ID from request: ${userId || 'none'}`);

      try {
        const metadata = await this.metadataService.fetchVideoMetadata(url, userId);
        console.log('Successfully fetched metadata:', JSON.stringify(metadata, null, 2));

        return reply.code(200).send({
          success: true,
          metadata,
        });
      } catch (metadataError: any) {
        console.error('Error fetching metadata:', metadataError);

        // For development, provide fallback metadata instead of failing
        console.warn('Providing fallback metadata for development');

        // Extract video ID from URL
        const urlParts = url.split('/');
        const videoId = urlParts[urlParts.length - 1] || 'unknown';

        // Create fallback metadata
        const fallbackMetadata = {
          title: `Engaxe Video ${videoId}`,
          description: 'This is a sample description for an Engaxe video.',
          thumbnailUrl: `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`,
          embedUrl: `https://engaxe.com/embed/${videoId}`,
          platform: 'engaxe',
          externalId: videoId,
          originalUrl: `https://engaxe.com/v/${videoId}`,
          author: 'Engaxe Creator',
          authorUrl: `https://engaxe.com/creator/`,
          duration: 330, // 5:30 in seconds
          views: 1000,
          likes: 50,
          dislikes: 5,
          publishedAt: new Date().toISOString(),
          category: 'Education',
          tags: ['video', 'sample', 'engaxe'],
          videoUrl: `https://engaxe.com/videos/${videoId}/stream`,
        };

        console.log('Using fallback metadata:', JSON.stringify(fallbackMetadata, null, 2));

        return reply.code(200).send({
          success: true,
          metadata: fallbackMetadata,
        });
      }
    } catch (error: any) {
      console.error('Unexpected error in fetchVideoMetadata controller:', error);
      request.log.error(error);

      return reply.code(error.statusCode || 400).send({
        success: false,
        message: error.message || 'Failed to fetch video metadata',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };
}
