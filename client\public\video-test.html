<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Engaxe Video Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .test-section {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .video-container {
            width: 100%;
            aspect-ratio: 16/9;
            background-color: #000;
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            flex-grow: 1;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        .test-button {
            background-color: #2196F3;
        }
        .test-button:hover {
            background-color: #0b7dda;
        }
        .method-selector {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
        }
        .results {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 4px;
            white-space: pre-wrap;
        }
    </style>
    <!-- Load the Engaxe embed script -->
    <script src="https://engaxe.com/embed.js"></script>
</head>
<body>
    <h1>Engaxe Video Test</h1>
    
    <div class="container">
        <div class="test-section">
            <h2>Method 1: Using ngxEmbed Function</h2>
            <div class="controls">
                <input type="text" id="video-id-1" placeholder="Enter Engaxe video ID" value="XLcMq2">
                <button id="load-btn-1">Load Video</button>
            </div>
            <div class="test-buttons">
                <button class="test-button" data-target="1" data-id="XLcMq2">Test ID 1</button>
                <button class="test-button" data-target="1" data-id="xW36l7">Test ID 2</button>
                <button class="test-button" data-target="1" data-id="suZKhW">Test ID 3</button>
                <button class="test-button" data-target="1" data-id="wollzl">Test ID 4</button>
            </div>
            <div id="video-container-1" class="video-container"></div>
            <div id="results-1" class="results">Results will appear here...</div>
        </div>
        
        <div class="test-section">
            <h2>Method 2: Using iframe</h2>
            <div class="controls">
                <input type="text" id="video-id-2" placeholder="Enter Engaxe video ID" value="XLcMq2">
                <button id="load-btn-2">Load Video</button>
            </div>
            <div class="test-buttons">
                <button class="test-button" data-target="2" data-id="XLcMq2">Test ID 1</button>
                <button class="test-button" data-target="2" data-id="xW36l7">Test ID 2</button>
                <button class="test-button" data-target="2" data-id="suZKhW">Test ID 3</button>
                <button class="test-button" data-target="2" data-id="wollzl">Test ID 4</button>
            </div>
            <div id="video-container-2" class="video-container"></div>
            <div id="results-2" class="results">Results will appear here...</div>
        </div>
    </div>

    <script>
        // Function to log results
        function logResult(containerId, message) {
            const resultsElement = document.getElementById(`results-${containerId}`);
            const timestamp = new Date().toLocaleTimeString();
            resultsElement.textContent += `\n[${timestamp}] ${message}`;
            resultsElement.scrollTop = resultsElement.scrollHeight;
        }
        
        // Function to load video using ngxEmbed
        function loadWithNgxEmbed(videoId, containerId) {
            const container = document.getElementById(`video-container-${containerId}`);
            
            // Clear container
            container.innerHTML = '';
            
            // Create a div for the embed
            const embedDiv = document.createElement('div');
            embedDiv.id = `engaxe-player-${containerId}`;
            embedDiv.style.width = '100%';
            embedDiv.style.height = '100%';
            container.appendChild(embedDiv);
            
            logResult(containerId, `Loading video ${videoId} using ngxEmbed function`);
            
            try {
                // Check if ngxEmbed is available
                if (typeof window.ngxEmbed === 'function') {
                    window.ngxEmbed(`engaxe-player-${containerId}`, videoId);
                    logResult(containerId, 'ngxEmbed function called successfully');
                } else {
                    logResult(containerId, 'ERROR: ngxEmbed function not available');
                    loadWithIframe(videoId, containerId);
                }
            } catch (error) {
                logResult(containerId, `ERROR: ${error.message}`);
                loadWithIframe(videoId, containerId);
            }
        }
        
        // Function to load video using iframe
        function loadWithIframe(videoId, containerId) {
            const container = document.getElementById(`video-container-${containerId}`);
            
            // Clear container
            container.innerHTML = '';
            
            logResult(containerId, `Loading video ${videoId} using iframe method`);
            
            // Create iframe
            const iframe = document.createElement('iframe');
            iframe.src = `https://engaxe.com/e/${videoId}`;
            iframe.width = '100%';
            iframe.height = '100%';
            iframe.style.border = 'none';
            iframe.allowFullscreen = true;
            iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share';
            
            // Add load event listener
            iframe.addEventListener('load', () => {
                logResult(containerId, 'Iframe loaded successfully');
            });
            
            // Add error event listener
            iframe.addEventListener('error', () => {
                logResult(containerId, 'ERROR: Iframe failed to load');
            });
            
            container.appendChild(iframe);
        }
        
        // Set up event listeners for Method 1
        document.getElementById('load-btn-1').addEventListener('click', () => {
            const videoId = document.getElementById('video-id-1').value.trim();
            if (videoId) {
                loadWithNgxEmbed(videoId, '1');
            } else {
                logResult('1', 'ERROR: Please enter a video ID');
            }
        });
        
        // Set up event listeners for Method 2
        document.getElementById('load-btn-2').addEventListener('click', () => {
            const videoId = document.getElementById('video-id-2').value.trim();
            if (videoId) {
                loadWithIframe(videoId, '2');
            } else {
                logResult('2', 'ERROR: Please enter a video ID');
            }
        });
        
        // Set up event listeners for test buttons
        document.querySelectorAll('.test-button').forEach(button => {
            button.addEventListener('click', () => {
                const target = button.getAttribute('data-target');
                const videoId = button.getAttribute('data-id');
                
                // Update the input field
                document.getElementById(`video-id-${target}`).value = videoId;
                
                // Load the video
                if (target === '1') {
                    loadWithNgxEmbed(videoId, '1');
                } else {
                    loadWithIframe(videoId, '2');
                }
            });
        });
        
        // Load default videos on page load
        window.addEventListener('DOMContentLoaded', () => {
            // Load Method 1 default video
            const videoId1 = document.getElementById('video-id-1').value.trim();
            if (videoId1) {
                loadWithNgxEmbed(videoId1, '1');
            }
            
            // Load Method 2 default video
            const videoId2 = document.getElementById('video-id-2').value.trim();
            if (videoId2) {
                loadWithIframe(videoId2, '2');
            }
        });
    </script>
</body>
</html>
