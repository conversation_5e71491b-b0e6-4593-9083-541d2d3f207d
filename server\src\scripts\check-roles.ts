import { RoleModel } from '../models';
import { connectDB } from '../config/database';
import mongoose from 'mongoose';

async function checkRoles() {
  try {
    // Connect to the database
    await connectDB();
    
    // Find all roles
    const roles = await RoleModel.find({});
    
    console.log(`Found ${roles.length} roles:`);
    
    // Display role details
    roles.forEach(role => {
      console.log(`
Role ID: ${role.id}
Name: ${role.name}
Code: ${role.code}
Description: ${role.description}
Permissions: ${role.permissions.length} permissions
Is System: ${role.isSystem}
Is Active: ${role.isActive}
Created At: ${role.createdAt}
      `);
    });
    
    // Close the database connection
    await mongoose.connection.close();
    
  } catch (error) {
    console.error('Error checking roles:', error);
    process.exit(1);
  }
}

// Run the function
checkRoles().catch(console.error);
