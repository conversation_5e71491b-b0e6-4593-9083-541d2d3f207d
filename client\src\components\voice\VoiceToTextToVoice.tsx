import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Mi<PERSON>, MicOff, Volume2, Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useChatbot } from '@/context/ChatbotContext';
import { useLanguage } from '@/context/LanguageContext';
import * as speechSynthesis from '@/utils/speechSynthesis';

interface VoiceToTextToVoiceProps {
  className?: string;
}

export default function VoiceToTextToVoice({ className }: VoiceToTextToVoiceProps) {
  const { toast } = useToast();
  const { aiProviders } = useChatbot();
  const { currentLanguage } = useLanguage();

  // States
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [response, setResponse] = useState('');
  const [messages, setMessages] = useState<Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
  }>>([]);

  // References
  const recognition = useRef<SpeechRecognition | null>(null);

  // Get OpenRouter configuration
  const openRouterConfig = aiProviders.openrouter;
  const isOpenRouterEnabled = openRouterConfig.enabled && openRouterConfig.apiKey;

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check if SpeechRecognition is available
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

      if (SpeechRecognition) {
        try {
          recognition.current = new SpeechRecognition();
          recognition.current.continuous = false;
          recognition.current.interimResults = false;

          // Set language based on current language selection
          const langCode = currentLanguage.code === 'hi' ? 'hi-IN' : 'en-US';
          recognition.current.lang = langCode;
          console.log(`Setting speech recognition language to: ${langCode}`);

          // Set up recognition event handlers
          recognition.current.onresult = handleSpeechResult;
          recognition.current.onerror = handleSpeechError;
          recognition.current.onend = () => {
            console.log('Speech recognition ended');
            setIsListening(false);
          };
          recognition.current.onstart = () => {
            console.log('Speech recognition started');
            setIsListening(true);
          };
        } catch (error) {
          console.error('Error initializing speech recognition:', error);
        }
      }
    }

    // Cleanup
    return () => {
      if (recognition.current) {
        try {
          recognition.current.abort();
        } catch (error) {
          console.error('Error cleaning up speech recognition:', error);
        }
      }
      speechSynthesis.stop();
    };
  }, []);

  // Handle speech recognition result
  const handleSpeechResult = (event: SpeechRecognitionEvent) => {
    try {
      console.log('Processing speech result:', event);

      if (event.results && event.results.length > 0 && event.results[0].length > 0) {
        const text = event.results[0][0].transcript;
        console.log('Transcript:', text);

        if (text && text.trim() !== '') {
          setTranscript(text);

          // Add user message to chat
          const userMessage = {
            role: 'user' as const,
            content: text,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, userMessage]);

          // Process the user's message and get response
          processUserMessage(text);
        } else {
          console.log('Empty transcript received');
        }
      } else {
        console.error('Invalid speech recognition results structure:', event.results);
      }
    } catch (error) {
      console.error('Error handling speech result:', error);
      toast({
        title: "Speech Processing Error",
        description: "Could not process your speech. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle speech recognition error
  const handleSpeechError = (event: SpeechRecognitionErrorEvent) => {
    console.error('Speech recognition error:', event.error, event.message);
    setIsListening(false);

    toast({
      title: "Speech Recognition Error",
      description: `Error: ${event.error}. Please try again.`,
      variant: "destructive"
    });
  };

  // Toggle speech recognition
  const toggleListening = () => {
    // If AI is currently speaking, stop the speech
    if (isSpeaking) {
      speechSynthesis.stop();
      setIsSpeaking(false);
      return;
    }

    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // Start speech recognition
  const startListening = () => {
    if (!recognition.current) {
      toast({
        title: "Speech Recognition Unavailable",
        description: "Your browser doesn't support speech recognition. Try Chrome or Edge.",
        variant: "destructive"
      });
      return;
    }

    if (isSpeaking) {
      speechSynthesis.stop();
      setIsSpeaking(false);
    }

    try {
      recognition.current.start();
      setIsListening(true);
    } catch (error) {
      console.error('Error starting speech recognition:', error);
      toast({
        title: "Speech Recognition Error",
        description: "Could not start listening. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Stop speech recognition
  const stopListening = () => {
    if (recognition.current) {
      try {
        recognition.current.stop();
      } catch (error) {
        console.error('Error stopping speech recognition:', error);
      }
    }
    setIsListening(false);
  };

  // Process user message and get response from OpenRouter
  const processUserMessage = async (text: string) => {
    if (!isOpenRouterEnabled) {
      toast({
        title: "API Not Configured",
        description: "Please enable OpenRouter or OpenAI and add your API key in settings.",
        variant: "destructive"
      });
      return;
    }

    // Validate API key format
    if (!openRouterConfig.apiKey.startsWith('sk-or-') && !openRouterConfig.apiKey.startsWith('sk-proj-')) {
      toast({
        title: "Invalid API Key Format",
        description: "API key should start with 'sk-or-' or 'sk-proj-'.",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Format messages for the API
      const formattedMessages = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Add the current message
      formattedMessages.push({
        role: 'user',
        content: text
      });

      // Determine the API endpoint and model based on the API key format
      const isOpenRouterKey = openRouterConfig.apiKey.startsWith('sk-or-');
      const apiEndpoint = isOpenRouterKey
        ? 'https://openrouter.ai/api/v1/chat/completions'
        : 'https://api.openai.com/v1/chat/completions';

      const apiModel = isOpenRouterKey
        ? (openRouterConfig.model || 'anthropic/claude-3-haiku') // OpenRouter model
        : (openRouterConfig.model || 'gpt-3.5-turbo'); // OpenAI model

      console.log(`Using API key (masked): ${openRouterConfig.apiKey.substring(0, 10)}...`);
      console.log(`Using API endpoint: ${apiEndpoint}`);
      console.log(`Using model: ${apiModel}`);

      // Call API
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${openRouterConfig.apiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'LawEngaxe Voice Assistant'
        },
        body: JSON.stringify({
          model: apiModel,
          messages: formattedMessages
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `Failed to get response from API (${apiEndpoint})`);
      }

      const data = await response.json();
      console.log('API response:', data);

      // Extract the response text
      const responseText = data.choices[0]?.message?.content || 'Sorry, I could not generate a response.';
      setResponse(responseText);

      // Add assistant message to chat
      const assistantMessage = {
        role: 'assistant' as const,
        content: responseText,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, assistantMessage]);

      // Speak the response
      speakText(responseText);
    } catch (error) {
      console.error('Error processing message:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to process your message",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Speak text using speech synthesis
  const speakText = (text: string) => {
    try {
      console.log('Speaking text:', text);

      // Set speaking state
      setIsSpeaking(true);

      // Get language code for speech synthesis
      const langCode = currentLanguage.code === 'hi' ? 'hi-IN' : 'en-US';
      console.log(`Using speech synthesis language: ${langCode}`);

      // Use our speech synthesis utility
      speechSynthesis.speak(text, {
        lang: langCode,
        rate: 1.0,
        pitch: 1.0,
        onStart: () => {
          console.log('Speech started');
          setIsSpeaking(true);
        },
        onEnd: () => {
          console.log('Speech ended');
          setIsSpeaking(false);
        },
        onError: (error) => {
          console.error('Speech synthesis error:', error);
          setIsSpeaking(false);
          toast({
            title: "Speech Synthesis Error",
            description: "Could not speak the response. Please check your audio settings.",
            variant: "destructive"
          });
        }
      });
    } catch (error) {
      console.error('Error with speech synthesis:', error);
      setIsSpeaking(false);
      toast({
        title: "Speech Synthesis Error",
        description: "Could not speak the response. Please check your audio settings.",
        variant: "destructive"
      });
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Voice Assistant</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col space-y-4">
          {messages.map((msg, index) => (
            <div
              key={index}
              className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`p-3 rounded-lg max-w-[80%] ${
                  msg.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                {msg.content}
              </div>
            </div>
          ))}

          {isProcessing && (
            <div className="flex justify-center">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          )}
        </div>

        <div className="flex justify-center space-x-2">
          <Button
            variant={isListening ? "destructive" : "default"}
            size="lg"
            onClick={toggleListening}
            disabled={isProcessing}
            className="rounded-full h-16 w-16 flex items-center justify-center"
          >
            {isListening ? <MicOff className="h-6 w-6" /> : <Mic className="h-6 w-6" />}
          </Button>

          {response && (
            <Button
              variant="outline"
              size="lg"
              onClick={() => speakText(response)}
              disabled={isSpeaking || isListening || isProcessing}
              className="rounded-full h-16 w-16 flex items-center justify-center"
            >
              <Volume2 className="h-6 w-6" />
            </Button>
          )}
        </div>

        {!isOpenRouterEnabled && (
          <div className="text-center text-sm text-red-500">
            API is not configured. Please enable OpenRouter or OpenAI and add your API key in settings.
          </div>
        )}
      </CardContent>
    </Card>
  );
}
