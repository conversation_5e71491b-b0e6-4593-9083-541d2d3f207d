import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTheme } from '@/context/ThemeContext';
import {
  LayoutDashboard,
  Settings,
  DollarSign,
  Languages,
  Users,
  Video,
  Palette,
  ChevronDown,
  ChevronUp,
  UserCog,
  ShieldCheck,
  Globe,
  Upload,
  Play,
  Mail,
  Share2,
  Radio,
  Clock,
  Database,
  FileText,
  FolderPlus,
  List,
  Star,
  Wrench,
  BarChart2,
  FileText as PageIcon,
  Map,
  Smartphone,
  Save,
  Info,
  History,
  Building2,
  BarChart3,
  Palette as BrandingIcon,
  LayoutGrid
} from 'lucide-react';

interface SubItemProps {
  icon: React.ReactNode;
  label: string;
  to: string;
  active: boolean;
  hasSubmenu?: boolean;
  onClick?: () => void;
  isOpen?: boolean;
  subItems?: SubItemProps[];
}

interface SidebarItemProps {
  icon: React.ReactNode;
  label: string;
  to: string;
  active: boolean;
  hasSubmenu?: boolean;
  onClick?: () => void;
  isOpen?: boolean;
  subItems?: SubItemProps[];
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon,
  label,
  to,
  active,
  hasSubmenu = false,
  onClick,
  isOpen,
  subItems = []
}) => {
  const hasSubItems = subItems.length > 0;
  const { theme } = useTheme();

  const ItemContent = () => (
    <>
      <div className="flex items-center flex-1">
        <span className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mr-3`}>{icon}</span>
        <span className={theme === 'dark' ? 'text-gray-200' : 'text-gray-800'}>{label}</span>
      </div>
      {hasSubmenu && (
        <span className={theme === 'dark' ? 'text-gray-500' : 'text-gray-600'}>
          {isOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        </span>
      )}
    </>
  );

  return (
    <>
      {hasSubmenu ? (
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onClick && onClick();
          }}
          className={`w-full flex items-center py-3 px-4 ${theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-200'} transition-colors text-left ${
            active ? (theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200') : ''
          }`}
        >
          <ItemContent />
        </button>
      ) : (
        <Link
          to={to}
          className={`flex items-center py-3 px-4 ${theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-200'} transition-colors ${
            active ? (theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200') : ''
          }`}
        >
          <ItemContent />
        </Link>
      )}

      {/* Submenu items */}
      {hasSubItems && isOpen && (
        <div className={theme === 'dark' ? 'bg-[#111111]' : 'bg-gray-200'}>
          {subItems.map((item, index) => (
            <div key={index}>
              {item.hasSubmenu && item.subItems ? (
                <div className={item.active ? (theme === 'dark' ? 'bg-gray-800' : 'bg-gray-300') : ''}>
                  <button
                    className={`w-full flex items-center py-2 px-4 pl-12 ${theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-300'} transition-colors cursor-pointer text-left`}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      item.onClick && item.onClick();
                    }}
                  >
                    <div className="flex items-center flex-1">
                      <span className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mr-3`}>{item.icon}</span>
                      <span className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} text-sm`}>{item.label}</span>
                    </div>
                    <span className={theme === 'dark' ? 'text-gray-500' : 'text-gray-600'}>
                      {item.isOpen ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                    </span>
                  </button>
                  {item.isOpen && item.subItems && (
                    <div className={theme === 'dark' ? 'bg-[#0A0A0A]' : 'bg-gray-300'}>
                      {item.subItems.map((subItem, subIndex) => (
                        <Link
                          key={subIndex}
                          to={subItem.to}
                          className={`flex items-center py-2 px-4 pl-16 ${theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-400'} transition-colors ${
                            subItem.active ? (theme === 'dark' ? 'bg-gray-800' : 'bg-gray-400') : ''
                          }`}
                        >
                          <div className="flex items-center flex-1">
                            <span className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mr-3`}>{subItem.icon}</span>
                            <span className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} text-sm`}>{subItem.label}</span>
                          </div>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <Link
                  to={item.to}
                  className={`flex items-center py-2 px-4 pl-12 ${theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-300'} transition-colors ${
                    item.active ? (theme === 'dark' ? 'bg-gray-800' : 'bg-gray-300') : ''
                  }`}
                >
                  <div className="flex items-center flex-1">
                    <span className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mr-3`}>{item.icon}</span>
                    <span className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} text-sm`}>{item.label}</span>
                  </div>
                </Link>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default function AdminSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const { theme } = useTheme();

  const isActive = (path: string) => {
    return currentPath === path || currentPath.startsWith(`${path}/`);
  };

  // Check active states for settings submenu items
  const isWebsiteInfoActive = isActive('/settings/website-information');
  const isEmailSetupActive = isActive('/settings/email');
  const isSocialLoginActive = isActive('/settings/social');
  const isLanguageActive = isActive('/admin/languages') || isActive('/admin/languages/add') || isActive('/admin/languages/manage');

  // Check active states for content submenu items
  const isContentActive = isActive('/admin/articles') || isActive('/admin/articles/create') || isActive('/admin/articles/manage') ||
                        isActive('/admin/pages') || isActive('/admin/pages/custom') || isActive('/admin/pages/manage') ||
                        isActive('/admin/pages/faqs') || isActive('/admin/pages/seo');

  const toggleSubmenu = (menu: string) => {
    if (openSubmenu === menu) {
      // If clicking on the same menu that's already open, close it
      setOpenSubmenu(null);
    } else {
      // Special handling for videos-import submenu
      if (menu === 'videos-import') {
        // Make sure videos menu is open when toggling videos-import
        if (openSubmenu !== 'videos' && openSubmenu !== 'videos-import') {
          // First open videos menu, then videos-import in a separate render cycle
          setOpenSubmenu('videos');
          setTimeout(() => {
            setOpenSubmenu('videos-import');
          }, 10);
        } else if (openSubmenu === 'videos') {
          // Videos menu is already open, just open the import submenu
          setOpenSubmenu('videos-import');
        } else {
          // Import submenu is already open, close it and go back to videos
          setOpenSubmenu('videos');
        }
      } else if (menu === 'videos' && openSubmenu === 'videos-import') {
        // If videos menu is clicked while import submenu is open, just close the import submenu
        setOpenSubmenu('videos');
      } else {
        // For other menus, just set them as open
        setOpenSubmenu(menu);
      }
    }
  };

  return (
    <div className={`w-64 min-w-64 max-w-64 flex-shrink-0 ${theme === 'dark' ? 'bg-[#1A1A1A]' : 'bg-gray-100'} h-screen flex flex-col overflow-y-auto overflow-x-hidden`}>
      <div className="p-4">
        <Link to="/" className="flex items-center">
          <span className={`${theme === 'dark' ? 'text-white' : 'text-gray-900'} text-2xl font-bold`}>
            <span className="text-[#FF5722]">Legal</span>
            <span className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>Aid</span>
          </span>
        </Link>
      </div>

      <div className="flex-1 overflow-y-auto">
        <SidebarItem
          icon={<LayoutDashboard size={20} />}
          label="Dashboard"
          to="/admin"
          active={isActive('/admin')}
        />

        <SidebarItem
          icon={<Settings size={20} />}
          label="Settings"
          to="#"
          active={isActive('/settings') || isActive('/settings/general') || isActive('/settings/email') || isActive('/settings/social') || isActive('/settings/website-information') || isLanguageActive}
          hasSubmenu
          onClick={() => toggleSubmenu('settings')}
          isOpen={openSubmenu === 'settings'}
          subItems={[
            {
              icon: <Settings size={18} />,
              label: "General Configuration",
              to: "/settings/general",
              active: isActive('/settings/general')
            },
            {
              icon: <Globe size={18} />,
              label: "Website Information",
              to: "/settings/website-information",
              active: isWebsiteInfoActive
            },
            {
              icon: <Mail size={18} />,
              label: "E-mail Setup",
              to: "/settings/email",
              active: isEmailSetupActive
            },
            {
              icon: <Share2 size={18} />,
              label: "Social Login Settings",
              to: "/settings/social",
              active: isSocialLoginActive
            },
            {
              icon: <Languages size={18} />,
              label: "Manage Languages",
              to: "/admin/languages/manage",
              active: isLanguageActive
            }
          ]}
        />


        <SidebarItem
          icon={<Users size={20} />}
          label="Users"
          to="#"
          active={isActive('/admin/users') || isActive('/admin/users/manage') || isActive('/admin/users/verification') || isActive('/admin/users/custom-fields')}
          hasSubmenu
          onClick={() => toggleSubmenu('users')}
          isOpen={openSubmenu === 'users'}
          subItems={[
            {
              icon: <UserCog size={18} />,
              label: "User Management",
              to: "/admin/users/manage",
              active: isActive('/admin/users/manage')
            },
            {
              icon: <ShieldCheck size={18} />,
              label: "Creator Verification",
              to: "/admin/users/verification",
              active: isActive('/admin/users/verification')
            },
            {
              icon: <UserCog size={18} />,
              label: "Custom Profile Fields",
              to: "/admin/users/custom-fields",
              active: isActive('/admin/users/custom-fields')
            }
          ]}
        />
        <SidebarItem
          icon={<LayoutGrid size={20} />}
          label="Content"
          to="#"
          active={isContentActive}
          hasSubmenu
          onClick={() => toggleSubmenu('content')}
          isOpen={openSubmenu === 'content'}
          subItems={[
            {
              icon: <FileText size={18} />,
              label: "Articles",
              to: "#",
              active: isActive('/admin/articles') || isActive('/admin/articles/create') || isActive('/admin/articles/manage'),
              hasSubmenu: true,
              onClick: () => toggleSubmenu('content-articles'),
              isOpen: openSubmenu === 'content-articles',
              subItems: [
                {
                  icon: <FolderPlus size={16} />,
                  label: "Create New Article",
                  to: "/admin/articles/create",
                  active: isActive('/admin/articles/create')
                },
                {
                  icon: <FileText size={16} />,
                  label: "Manage Articles",
                  to: "/admin/articles/manage",
                  active: isActive('/admin/articles/manage')
                }
              ]
            },
            {
              icon: <PageIcon size={18} />,
              label: "Pages",
              to: "#",
              active: isActive('/admin/pages') || isActive('/admin/pages/custom') || isActive('/admin/pages/manage') || isActive('/admin/pages/faqs') || isActive('/admin/pages/seo'),
              hasSubmenu: true,
              onClick: () => toggleSubmenu('content-pages'),
              isOpen: openSubmenu === 'content-pages',
              subItems: [
                {
                  icon: <PageIcon size={16} />,
                  label: "Manage Custom Pages",
                  to: "/admin/pages/custom",
                  active: isActive('/admin/pages/custom')
                },
                {
                  icon: <PageIcon size={16} />,
                  label: "Manage Pages",
                  to: "/admin/pages/manage",
                  active: isActive('/admin/pages/manage')
                },
                {
                  icon: <PageIcon size={16} />,
                  label: "Manage FAQs",
                  to: "/admin/pages/faqs",
                  active: isActive('/admin/pages/faqs')
                },
                {
                  icon: <PageIcon size={16} />,
                  label: "Manage Pages SEO",
                  to: "/admin/pages/seo",
                  active: isActive('/admin/pages/seo')
                }
              ]
            }
          ]}
        />

        <SidebarItem
          icon={<Video size={20} />}
          label="Videos"
          to="#"
          active={isActive('/admin/videos') || isActive('/admin/videos/manage') || isActive('/admin/videos/comments') ||
                 isActive('/admin/videos/import') || isActive('/admin/videos/import/dailymotion') || isActive('/admin/videos/import/twitch') ||
                 isActive('/admin/videos/import/engaxe') || isActive('/admin/videos/fix') ||
                 isActive('/admin/categories') || isActive('/admin/categories/manage') || isActive('/admin/categories/sub')}
          hasSubmenu
          onClick={() => toggleSubmenu('videos')}
          isOpen={openSubmenu === 'videos'}
          subItems={[
            {
              icon: <Video size={18} />,
              label: "Manage Videos",
              to: "/admin/videos/manage",
              active: isActive('/admin/videos/manage')
            },
            {
              icon: <Video size={18} />,
              label: "Manage Video Comments",
              to: "/admin/videos/comments",
              active: isActive('/admin/videos/comments')
            },
            {
              icon: <Video size={18} />,
              label: "Import Videos",
              to: "#",
              active: isActive('/admin/videos/import') || isActive('/admin/videos/import/dailymotion') || isActive('/admin/videos/import/twitch') ||
                      isActive('/admin/videos/import/engaxe'),
              hasSubmenu: true,
              onClick: () => toggleSubmenu('videos-import'),
              isOpen: openSubmenu === 'videos-import',
              subItems: [

                {
                  icon: <Video size={16} />,
                  label: "Import From Dailymotion",
                  to: "/admin/videos/import/dailymotion",
                  active: isActive('/admin/videos/import/dailymotion')
                },
                {
                  icon: <Video size={16} />,
                  label: "Import From Twitch",
                  to: "/admin/videos/import/twitch",
                  active: isActive('/admin/videos/import/twitch')
                },
                {
                  icon: <Video size={16} />,
                  label: "Import From Engaxe",
                  to: "/admin/videos/import/engaxe",
                  active: isActive('/admin/videos/import/engaxe')
                }
              ]
            },
            {
              icon: <Wrench size={18} />,
              label: "Fix Video Languages",
              to: "/admin/videos/fix",
              active: isActive('/admin/videos/fix')
            },
            {
              icon: <List size={18} />,
              label: "Manage Categories",
              to: "/admin/categories/manage",
              active: isActive('/admin/categories/manage')
            },
            {
              icon: <List size={18} />,
              label: "Manage Sub Categories",
              to: "/admin/categories/sub",
              active: isActive('/admin/categories/sub')
            }
          ]}
        />





        <SidebarItem
          icon={<Wrench size={20} />}
          label="Tools"
          to="#"
          active={isActive('/admin/tools') || isActive('/admin/tools/announcements') || isActive('/admin/tools/ban-users') || isActive('/admin/tools/activities') || isActive('/admin/tools/mass-notifications') || isActive('/admin/tools/newsletters') || isActive('/admin/backup') || isActive('/admin/system-status')}
          hasSubmenu
          onClick={() => toggleSubmenu('tools')}
          isOpen={openSubmenu === 'tools'}
          subItems={[
            {
              icon: <Wrench size={18} />,
              label: "Admin Tools",
              to: "/admin/tools",
              active: isActive('/admin/tools')
            },
            {
              icon: <Wrench size={18} />,
              label: "Manage Announcements",
              to: "/admin/tools/announcements",
              active: isActive('/admin/tools/announcements')
            },
            {
              icon: <Wrench size={18} />,
              label: "Ban Users",
              to: "/admin/tools/ban-users",
              active: isActive('/admin/tools/ban-users')
            },
            {
              icon: <Wrench size={18} />,
              label: "Manage Activities",
              to: "/admin/tools/activities",
              active: isActive('/admin/tools/activities')
            },
            {
              icon: <Wrench size={18} />,
              label: "Mass Notifications",
              to: "/admin/tools/mass-notifications",
              active: isActive('/admin/tools/mass-notifications')
            },
            {
              icon: <Wrench size={18} />,
              label: "Newsletter",
              to: "/admin/tools/newsletters",
              active: isActive('/admin/tools/newsletters')
            },
            {
              icon: <Save size={18} />,
              label: "Backup",
              to: "/admin/backup",
              active: isActive('/admin/backup')
            },
            {
              icon: <Info size={18} />,
              label: "System Status",
              to: "/admin/system-status",
              active: isActive('/admin/system-status')
            },
            {
              icon: <FileText size={18} />,
              label: "System Logs",
              to: "/admin/logs",
              active: isActive('/admin/logs')
            }
          ]}
        />

        <SidebarItem
          icon={<BarChart2 size={20} />}
          label="Reports"
          to="#"
          active={isActive('/admin/reports') || isActive('/admin/reports/video') || isActive('/admin/reports/copyright')}
          hasSubmenu
          onClick={() => toggleSubmenu('reports')}
          isOpen={openSubmenu === 'reports'}
          subItems={[
            {
              icon: <BarChart2 size={18} />,
              label: "Manage Video Reports",
              to: "/admin/reports/video",
              active: isActive('/admin/reports/video')
            },
            {
              icon: <BarChart2 size={18} />,
              label: "Manage Copyright Reports",
              to: "/admin/reports/copyright",
              active: isActive('/admin/reports/copyright')
            }
          ]}
        />



        <SidebarItem
          icon={<Map size={20} />}
          label="Sitemap"
          to="#"
          active={isActive('/admin/sitemap') || isActive('/admin/sitemap/create')}
          hasSubmenu
          onClick={() => toggleSubmenu('sitemap')}
          isOpen={openSubmenu === 'sitemap'}
          subItems={[
            {
              icon: <Map size={18} />,
              label: "Create Sitemap",
              to: "/admin/sitemap/create",
              active: isActive('/admin/sitemap/create')
            }
          ]}
        />

        <SidebarItem
          icon={<Smartphone size={20} />}
          label="Mobile & API Settings"
          to="#"
          active={isActive('/admin/mobile-api') || isActive('/admin/mobile-api/keys') || isActive('/admin/mobile-api/push')}
          hasSubmenu
          onClick={() => toggleSubmenu('mobile-api')}
          isOpen={openSubmenu === 'mobile-api'}
          subItems={[
            {
              icon: <Smartphone size={18} />,
              label: "Manage API Access Keys",
              to: "/admin/mobile-api/keys",
              active: isActive('/admin/mobile-api/keys')
            },
            {
              icon: <Smartphone size={18} />,
              label: "Push Notifications System",
              to: "/admin/mobile-api/push",
              active: isActive('/admin/mobile-api/push')
            }
          ]}
        />




      </div>
    </div>
  );
}
