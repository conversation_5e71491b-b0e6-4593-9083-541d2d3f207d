import axios from 'axios';

/**
 * Parse duration string in format "MM:SS" or "HH:MM:SS" to seconds
 */
function parseDurationToSeconds(durationStr: string | number): number {
  if (!durationStr) {
    return 0;
  }

  // If it's already a number, return it
  if (typeof durationStr === 'number') {
    return durationStr;
  }

  // If it's a string that doesn't contain colons, try to parse as number
  if (typeof durationStr === 'string' && !durationStr.includes(':')) {
    const parsed = parseInt(durationStr, 10);
    return isNaN(parsed) ? 0 : parsed;
  }

  // Handle MM:SS or HH:MM:SS format
  if (typeof durationStr === 'string') {
    const cleanDuration = durationStr.trim();
    const parts = cleanDuration.split(':');

    if (parts.length === 2) {
      // MM:SS format
      const minutes = parseInt(parts[0], 10) || 0;
      const seconds = parseInt(parts[1], 10) || 0;
      return (minutes * 60) + seconds;
    } else if (parts.length === 3) {
      // HH:MM:SS format
      const hours = parseInt(parts[0], 10) || 0;
      const minutes = parseInt(parts[1], 10) || 0;
      const seconds = parseInt(parts[2], 10) || 0;
      return (hours * 3600) + (minutes * 60) + seconds;
    }
  }

  return 0;
}

/**
 * Service for interacting with the Engaxe API
 */
export class EngaxeApiService {
  // No need for API URLs or server key as we're only using the AJAX endpoint
  private readonly ajaxUrl: string = 'https://engaxe.com/aj/get-video';
  private readonly serverKey: string = '1312a113c58715637a94437389326a49';

  constructor() {
    console.log('Initializing EngaxeApiService with AJAX endpoint only');
  }

  /**
   * Mock login to Engaxe
   * @param username Username or email
   * @param password User password
   * @returns Mock login response with user_id and session_id
   */
  async login(username: string, password: string) {
    try {
      console.log(`Mock login to Engaxe with username: ${username}`);

      // Generate mock user ID and session ID based on username
      const userId = `user_${username.replace(/[^a-zA-Z0-9]/g, '')}`;
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
      const deviceId = `device_${Math.random().toString(36).substring(2, 10)}`;

      console.log(`Generated mock Engaxe credentials: userId=${userId}, sessionId=${sessionId}, deviceId=${deviceId}`);

      return {
        userId,
        sessionId,
        deviceId,
      };
    } catch (error: any) {
      console.error('Engaxe mock login error:', error?.message || error);
      throw new Error('Failed to login to Engaxe: ' + (error?.message || 'Unknown error'));
    }
  }

  /**
   * Get video details from Engaxe
   * @param videoId Engaxe video ID
   * @returns Video details
   */
  async getVideoDetails(videoId: string) {
    try {
      console.log(`Fetching video details for Engaxe video ID: ${videoId} using AJAX endpoint`);
      return await this.getVideoDetailsAjax(videoId);
    } catch (error: any) {
      console.error('Engaxe get video details error:', error?.message || error);
      throw error;
    }
  }

  /**
   * Mock logout from Engaxe
   * @param sessionId User's session ID
   * @param userId User's ID
   * @returns Logout response
   */
  async logout(sessionId: string, userId: string) {
    try {
      console.log(`Mock logout from Engaxe: sessionId=${sessionId}, userId=${userId}`);
      return { success: true };
    } catch (error: any) {
      console.error('Engaxe mock logout error:', error?.message || error);
      throw new Error('Failed to logout from Engaxe: ' + (error?.message || 'Unknown error'));
    }
  }

  /**
   * Mock get user sessions
   * @param sessionId User's session ID
   * @param userId User's ID
   * @returns User sessions
   */
  async getUserSessions(sessionId: string, userId: string) {
    try {
      console.log(`Mock get user sessions from Engaxe: sessionId=${sessionId}, userId=${userId}`);
      return {
        sessions: [
          {
            id: sessionId,
            device: 'Web Browser',
            platform: 'Web',
            ip: '127.0.0.1',
            lastActive: new Date().toISOString(),
            isCurrent: true
          }
        ]
      };
    } catch (error: any) {
      console.error('Engaxe mock get user sessions error:', error?.message || error);
      throw new Error('Failed to get user sessions from Engaxe: ' + (error?.message || 'Unknown error'));
    }
  }

  /**
   * Mock add video comment
   * @param videoId Engaxe video ID
   * @param text Comment text
   * @param sessionId User's session ID
   * @param userId User's ID
   * @returns Comment response
   */
  async addVideoComment(videoId: string, text: string, sessionId: string, userId: string) {
    try {
      console.log(`Mock add comment to Engaxe video: videoId=${videoId}, userId=${userId}, text=${text}`);
      return {
        id: `comment_${Date.now()}`,
        text,
        user_id: userId,
        video_id: videoId,
        time: new Date().toISOString(),
        likes: 0,
        dislikes: 0,
        replies: 0
      };
    } catch (error: any) {
      console.error('Engaxe mock add comment error:', error?.message || error);
      throw new Error('Failed to add comment to Engaxe video: ' + (error?.message || 'Unknown error'));
    }
  }

  /**
   * Mock get video comments
   * @param videoId Engaxe video ID
   * @param sessionId User's session ID (optional)
   * @param userId User's ID (optional)
   * @param limit Number of comments to return (optional)
   * @param offset Pagination offset (optional)
   * @returns Comments for the video
   */
  async getVideoComments(videoId: string, sessionId?: string, userId?: string, limit: number = 20, offset: number = 0) {
    try {
      console.log(`Mock get comments for Engaxe video: videoId=${videoId}, limit=${limit}, offset=${offset}`);
      return {
        comments: Array(Math.min(limit, 5)).fill(0).map((_, i) => ({
          id: `comment_${i + offset}`,
          text: `This is a mock comment ${i + offset} for video ${videoId}`,
          user_id: userId || `user_${i + offset}`,
          video_id: videoId,
          time: new Date(Date.now() - (i * 3600000)).toISOString(),
          likes: Math.floor(Math.random() * 10),
          dislikes: Math.floor(Math.random() * 3),
          replies: Math.floor(Math.random() * 2),
          user: {
            id: userId || `user_${i + offset}`,
            username: `User ${i + offset}`,
            avatar: `https://placehold.co/100x100/333333/FFFFFF?text=U${i + offset}`
          }
        })),
        total: 5
      };
    } catch (error: any) {
      console.error('Engaxe mock get comments error:', error?.message || error);
      throw new Error('Failed to get comments for Engaxe video: ' + (error?.message || 'Unknown error'));
    }
  }

  /**
   * Mock upload video to Engaxe
   * @param videoData Video data
   * @param sessionId User's session ID
   * @param userId User's ID
   * @returns Upload response
   */
  async uploadVideo(
    videoData: {
      title: string;
      description: string;
      tags: string;
      category_id: string;
      video: Buffer | string; // File buffer or path
    },
    sessionId: string,
    userId: string
  ) {
    try {
      console.log(`Mock upload video to Engaxe: title=${videoData.title}, userId=${userId}`);
      return {
        video_id: `upload_${Date.now()}`,
        title: videoData.title,
        description: videoData.description,
        tags: videoData.tags,
        category_id: videoData.category_id,
        thumbnail: 'https://placehold.co/480x360/333333/FFFFFF?text=Upload',
        duration: '120',
        size: typeof videoData.video === 'string' ? 0 : videoData.video.length,
        url: `https://engaxe.com/videos/upload_${Date.now()}`,
        user_id: userId
      };
    } catch (error: any) {
      console.error('Engaxe mock upload video error:', error?.message || error);
      throw new Error('Failed to upload video to Engaxe: ' + (error?.message || 'Unknown error'));
    }
  }

  /**
   * Get video details using the AJAX endpoint
   * @param videoId Engaxe video ID
   * @returns Video details and owner information
   */
  async getVideoDetailsAjax(videoId: string) {
    try {
      console.log(`Fetching video details using AJAX endpoint for video ID: ${videoId}`);

      // Make the AJAX request to get video details
      const response = await axios.get(`${this.ajaxUrl}?id=${videoId}`, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'X-Requested-With': 'XMLHttpRequest',
          'Referer': 'https://engaxe.com/'
        },
        timeout: 15000
      });

      console.log('Engaxe AJAX response status:', response.status);
      console.log('Engaxe AJAX response data:', JSON.stringify(response.data, null, 2));

      // Check if the response contains data
      if (response.data && response.status === 200) {
        console.log('Successfully retrieved video details via AJAX');

        // Process the response data
        // The AJAX endpoint might return data in a different format than the API
        // We need to normalize it to match our expected format
        const videoData = response.data.status === 'success' ? response.data.data : response.data;

        // Parse duration to seconds
        const durationInSeconds = parseDurationToSeconds(videoData.duration);
        console.log(`Duration parsing: "${videoData.duration}" -> ${durationInSeconds} seconds`);

        // Create a standardized video object
        return {
          id: videoId,
          video_id: videoId,
          title: videoData.title || `Engaxe Video ${videoId}`,
          description: videoData.description || '',
          thumbnail: videoData.thumbnail || `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`,
          duration: durationInSeconds,
          duration_raw: videoData.duration, // Keep original for debugging
          views: videoData.views || '0',
          likes: videoData.likes || '0',
          dislikes: videoData.dislikes || '0',
          category_name: videoData.category_name || 'Uncategorized',
          tags: videoData.tags || '',
          owner: videoData.owner || {
            id: videoData.user_id || '0',
            username: videoData.username || 'Unknown',
            avatar: videoData.avatar || ''
          }
        };
      } else {
        console.warn('AJAX endpoint returned unexpected response');
        throw new Error('Failed to get video details from AJAX endpoint');
      }
    } catch (error: any) {
      console.error('Engaxe AJAX get video details error:', error?.message || error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }

      // Try an alternative approach - using the embed endpoint
      try {
        console.log('Trying alternative approach - using embed endpoint');
        const embedResponse = await axios.get(`https://engaxe.com/embed/${videoId}`, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Referer': 'https://engaxe.com/'
          },
          timeout: 15000
        });

        console.log('Embed response status:', embedResponse.status);

        // If we got a successful response, we can extract some basic info from the HTML
        if (embedResponse.status === 200) {
          console.log('Successfully retrieved embed page');

          // Extract title from HTML (this is a simple approach and might need refinement)
          const titleMatch = embedResponse.data.match(/<title>(.*?)<\/title>/);
          const title = titleMatch ? titleMatch[1].replace(' - engaxe', '') : `Engaxe Video ${videoId}`;

          // Extract thumbnail URL if possible
          const thumbnailMatch = embedResponse.data.match(/property="og:image" content="(.*?)"/);
          const thumbnail = thumbnailMatch ? thumbnailMatch[1] : `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`;

          // Extract description if possible
          const descriptionMatch = embedResponse.data.match(/property="og:description" content="(.*?)"/);
          const description = descriptionMatch ? descriptionMatch[1] : '';

          return {
            id: videoId,
            video_id: videoId,
            title: title,
            description: description,
            thumbnail: thumbnail,
            duration: '0',
            views: '0',
            likes: '0',
            dislikes: '0',
            category_name: 'Uncategorized',
            tags: '',
            owner: {
              id: '0',
              username: 'Unknown',
              avatar: ''
            }
          };
        }
      } catch (embedError: any) {
        console.error('Embed approach also failed:', embedError?.message || embedError);
      }

      // For development, provide fallback data
      console.warn('Providing fallback data for development');

      // Customize mock data based on video ID
      let mockTitle, mockDescription, mockCategory;

      if (videoId === 'XLcMq2') {
        mockTitle = 'Understanding Legal Rights in Employment';
        mockDescription = 'A comprehensive guide to understanding your legal rights in the workplace, including discrimination, harassment, and wrongful termination.';
        mockCategory = 'Employment Law';
      } else if (videoId === 'a71tuY') {
        mockTitle = 'Contract Law Fundamentals';
        mockDescription = 'Learn the basics of contract law including formation, terms, conditions, and remedies for breach of contract.';
        mockCategory = 'Contract Law';
      } else {
        // Default mock data
        mockTitle = `Engaxe Video ${videoId}`;
        mockDescription = 'This is a sample description for an Engaxe video.';
        mockCategory = 'Education';
      }

      return {
        id: videoId,
        video_id: videoId,
        title: mockTitle,
        description: mockDescription,
        thumbnail: `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`,
        duration: 330, // Already in seconds for mock data
        duration_raw: '5:30', // Mock raw duration for debugging
        views: '1000',
        likes: '50',
        dislikes: '5',
        category_name: mockCategory,
        tags: 'video,sample,engaxe',
        owner: {
          id: '456',
          username: 'Engaxe Creator',
          avatar: 'https://placehold.co/100x100/333333/FFFFFF?text=EC'
        }
      };
    }
  }
}

// Export a singleton instance
export const engaxeApiService = new EngaxeApiService();
