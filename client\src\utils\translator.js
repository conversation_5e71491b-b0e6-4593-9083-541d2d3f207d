/**
 * Simple translator utility that simulates Google Translate
 */

// Language prefixes for simulation
const languagePrefixes = {
  'hi': 'हिंदी: ',
  'mr': 'मराठी: ',
  'gu': 'ગુજરાતી: ',
  'ta': 'தமிழ்: ',
  'te': 'తెలుగు: ',
  'bn': 'বাংলা: ',
  'kn': 'ಕನ್ನಡ: ',
  'ml': 'മലയാളം: ',
  'es': 'Español: ',
  'fr': 'Français: ',
  'de': 'Deutsch: ',
  'zh': '中文: ',
  'ja': '日本語: ',
  'ru': 'Русский: ',
  'ar': 'العربية: '
};

// Common words and phrases with translations
const dictionary = {
  'hello': {
    'hi': 'नमस्ते',
    'mr': 'नमस्कार',
    'gu': 'નમસ્તે',
    'ta': 'வணக்கம்',
    'te': 'హలో',
    'bn': 'হ্যালো',
    'kn': 'ಹಲೋ',
    'ml': 'ഹലോ',
    'es': 'Hola',
    'fr': 'Bonjour',
    'de': 'Hallo',
    'zh': '你好',
    'ja': 'こんにちは',
    'ru': 'Привет',
    'ar': 'مرحبا'
  },
  'how are you': {
    'hi': 'आप कैसे हैं',
    'mr': 'तुम्ही कसे आहात',
    'gu': 'તમે કેમ છો',
    'ta': 'நீங்கள் எப்படி இருக்கிறீர்கள்',
    'te': 'మీరు ఎలా ఉన్నారు',
    'bn': 'আপনি কেমন আছেন',
    'kn': 'ನೀವು ಹೇಗಿದ್ದೀರಿ',
    'ml': 'സുഖമാണോ',
    'es': '¿Cómo estás',
    'fr': 'Comment allez-vous',
    'de': 'Wie geht es dir',
    'zh': '你好吗',
    'ja': 'お元気ですか',
    'ru': 'Как дела',
    'ar': 'كيف حالك'
  },
  'thank you': {
    'hi': 'धन्यवाद',
    'mr': 'धन्यवाद',
    'gu': 'આભાર',
    'ta': 'நன்றி',
    'te': 'ధన్యవాదాలు',
    'bn': 'ধন্যবাদ',
    'kn': 'ಧನ್ಯವಾದಗಳು',
    'ml': 'നന്ദി',
    'es': 'Gracias',
    'fr': 'Merci',
    'de': 'Danke',
    'zh': '谢谢',
    'ja': 'ありがとう',
    'ru': 'Спасибо',
    'ar': 'شكرا'
  },
  'yes': {
    'hi': 'हां',
    'mr': 'होय',
    'gu': 'હા',
    'ta': 'ஆம்',
    'te': 'అవును',
    'bn': 'হ্যাঁ',
    'kn': 'ಹೌದು',
    'ml': 'അതെ',
    'es': 'Sí',
    'fr': 'Oui',
    'de': 'Ja',
    'zh': '是的',
    'ja': 'はい',
    'ru': 'Да',
    'ar': 'نعم'
  },
  'no': {
    'hi': 'नहीं',
    'mr': 'नाही',
    'gu': 'ના',
    'ta': 'இல்லை',
    'te': 'లేదు',
    'bn': 'না',
    'kn': 'ಇಲ್ಲ',
    'ml': 'ഇല്ല',
    'es': 'No',
    'fr': 'Non',
    'de': 'Nein',
    'zh': '不',
    'ja': 'いいえ',
    'ru': 'Нет',
    'ar': 'لا'
  },
  'good': {
    'hi': 'अच्छा',
    'mr': 'चांगले',
    'gu': 'સારું',
    'ta': 'நல்லது',
    'te': 'మంచిది',
    'bn': 'ভালো',
    'kn': 'ಒಳ್ಳೆಯದು',
    'ml': 'നല്ലത്',
    'es': 'Bueno',
    'fr': 'Bon',
    'de': 'Gut',
    'zh': '好',
    'ja': '良い',
    'ru': 'Хорошо',
    'ar': 'جيد'
  },
  'bad': {
    'hi': 'बुरा',
    'mr': 'वाईट',
    'gu': 'ખરાબ',
    'ta': 'கெட்டது',
    'te': 'చెడ్డది',
    'bn': 'খারাপ',
    'kn': 'ಕೆಟ್ಟದು',
    'ml': 'മോശം',
    'es': 'Malo',
    'fr': 'Mauvais',
    'de': 'Schlecht',
    'zh': '坏',
    'ja': '悪い',
    'ru': 'Плохо',
    'ar': 'سيئ'
  },
  'please': {
    'hi': 'कृपया',
    'mr': 'कृपया',
    'gu': 'કૃપા કરીને',
    'ta': 'தயவுசெய்து',
    'te': 'దయచేసి',
    'bn': 'দয়া করে',
    'kn': 'ದಯವಿಟ್ಟು',
    'ml': 'ദയവായി',
    'es': 'Por favor',
    'fr': 'S\'il vous plaît',
    'de': 'Bitte',
    'zh': '请',
    'ja': 'お願いします',
    'ru': 'Пожалуйста',
    'ar': 'من فضلك'
  },
  'sorry': {
    'hi': 'माफ़ करें',
    'mr': 'माफ करा',
    'gu': 'માફ કરો',
    'ta': 'மன்னிக்கவும்',
    'te': 'క్షమించండి',
    'bn': 'দুঃখিত',
    'kn': 'ಕ್ಷಮಿಸಿ',
    'ml': 'ക്ഷമിക്കണം',
    'es': 'Lo siento',
    'fr': 'Désolé',
    'de': 'Entschuldigung',
    'zh': '对不起',
    'ja': 'すみません',
    'ru': 'Извините',
    'ar': 'آسف'
  },
  'help': {
    'hi': 'मदद',
    'mr': 'मदत',
    'gu': 'મદદ',
    'ta': 'உதவி',
    'te': 'సహాయం',
    'bn': 'সাহায্য',
    'kn': 'ಸಹಾಯ',
    'ml': 'സഹായം',
    'es': 'Ayuda',
    'fr': 'Aide',
    'de': 'Hilfe',
    'zh': '帮助',
    'ja': '助けて',
    'ru': 'Помощь',
    'ar': 'مساعدة'
  },
  'because': {
    'hi': 'क्योंकि',
    'mr': 'कारण',
    'gu': 'કારણ કે',
    'ta': 'ஏனென்றால்',
    'te': 'ఎందుకంటే',
    'bn': 'কারণ',
    'kn': 'ಏಕೆಂದರೆ',
    'ml': 'കാരണം',
    'es': 'porque',
    'fr': 'parce que',
    'de': 'weil',
    'zh': '因为',
    'ja': 'なぜなら',
    'ru': 'потому что',
    'ar': 'لأن'
  },
  'class': {
    'hi': 'कक्षा',
    'mr': 'वर्ग',
    'gu': 'વર્ગ',
    'ta': 'வகுப்பு',
    'te': 'తరగతి',
    'bn': 'ক্লাস',
    'kn': 'ತರಗತಿ',
    'ml': 'ക്ലാസ്',
    'es': 'clase',
    'fr': 'classe',
    'de': 'Klasse',
    'zh': '课程',
    'ja': 'クラス',
    'ru': 'класс',
    'ar': 'فصل'
  },
  'classes': {
    'hi': 'कक्षाएं',
    'mr': 'वर्ग',
    'gu': 'વર્ગો',
    'ta': 'வகுப்புகள்',
    'te': 'తరగతులు',
    'bn': 'ক্লাসগুলি',
    'kn': 'ತರಗತಿಗಳು',
    'ml': 'ക്ലാസുകൾ',
    'es': 'clases',
    'fr': 'cours',
    'de': 'Klassen',
    'zh': '课程',
    'ja': 'クラス',
    'ru': 'занятия',
    'ar': 'فصول'
  },
  'ending': {
    'hi': 'समाप्त हो रहा है',
    'mr': 'संपत आहे',
    'gu': 'સમાપ્ત થઈ રહ્યું છે',
    'ta': 'முடிவடைகிறது',
    'te': 'ముగుస్తోంది',
    'bn': 'শেষ হচ্ছে',
    'kn': 'ಕೊನೆಗೊಳ್ಳುತ್ತಿದೆ',
    'ml': 'അവസാനിക്കുന്നു',
    'es': 'terminando',
    'fr': 'se terminant',
    'de': 'endet',
    'zh': '结束',
    'ja': '終わる',
    'ru': 'заканчивается',
    'ar': 'ينتهي'
  },
  'my': {
    'hi': 'मेरा',
    'mr': 'माझे',
    'gu': 'મારું',
    'ta': 'என்னுடைய',
    'te': 'నా',
    'bn': 'আমার',
    'kn': 'ನನ್ನ',
    'ml': 'എന്റെ',
    'es': 'mi',
    'fr': 'mon',
    'de': 'mein',
    'zh': '我的',
    'ja': '私の',
    'ru': 'мой',
    'ar': 'بلدي'
  }
};

/**
 * Translates a text to the target language
 * @param {string} text - The text to translate
 * @param {string} targetLanguage - The target language code
 * @returns {string} - The translated text
 */
export function translateText(text, targetLanguage) {
  if (!text || targetLanguage === 'en') {
    return text;
  }

  // For demonstration purposes, we'll use a simple algorithm
  // In a real app, you would call a translation API

  // 1. Try to find exact matches for common phrases
  for (const [phrase, translations] of Object.entries(dictionary)) {
    if (text.toLowerCase() === phrase.toLowerCase()) {
      if (translations[targetLanguage]) {
        return translations[targetLanguage];
      }
    }
  }

  // 2. Try to find exact matches for specific phrases
  if (text.toLowerCase() === 'because my classes are ending') {
    const translations = {
      'hi': 'क्योंकि मेरी कक्षाएं समाप्त हो रही हैं',
      'mr': 'कारण माझे वर्ग संपत आहेत',
      'gu': 'કારણ કે મારા વર્ગો સમાપ્ત થઈ રહ્યા છે',
      'ta': 'ஏனென்றால் என் வகுப்புகள் முடிவடைகின்றன',
      'te': 'ఎందుకంటే నా తరగతులు ముగుస్తున్నాయి',
      'bn': 'কারণ আমার ক্লাসগুলি শেষ হচ্ছে',
      'kn': 'ಏಕೆಂದರೆ ನನ್ನ ತರಗತಿಗಳು ಕೊನೆಗೊಳ್ಳುತ್ತಿವೆ',
      'ml': 'കാരണം എന്റെ ക്ലാസുകൾ അവസാനിക്കുന്നു',
      'es': 'porque mis clases están terminando',
      'fr': 'parce que mes cours se terminent',
      'de': 'weil meine Klassen enden',
      'zh': '因为我的课程正在结束',
      'ja': '私のクラスが終わるからです',
      'ru': 'потому что мои занятия заканчиваются',
      'ar': 'لأن فصولي تنتهي'
    };
    
    if (translations[targetLanguage]) {
      return translations[targetLanguage];
    }
  }

  // 3. For other text, add a language prefix to simulate translation
  return languagePrefixes[targetLanguage] + text;
}

export default {
  translateText
};
