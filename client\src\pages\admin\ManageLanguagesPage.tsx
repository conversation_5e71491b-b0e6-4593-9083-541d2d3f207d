import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import {
  Search,
  Filter,
  Edit,
  Trash2,
  Check,
  X,
  Globe,
  Languages,
  Plus,
  Save,
  ArrowUpDown,
  MoreHorizontal,
  Flag,
  Code,
  ArrowRightLeft,
  Settings
} from 'lucide-react';

// Import shadcn UI components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import { Switch } from '@/components/ui/switch';

// Sample language data
const languagesData = [
  { id: 1, name: 'English', code: 'en', direction: 'ltr', isDefault: true, isActive: true },
  { id: 2, name: 'Arabic', code: 'ar', direction: 'rtl', isDefault: false, isActive: true },
  { id: 3, name: 'Dutch', code: 'nl', direction: 'ltr', isDefault: false, isActive: true },
  { id: 4, name: 'French', code: 'fr', direction: 'ltr', isDefault: false, isActive: true },
  { id: 5, name: 'German', code: 'de', direction: 'ltr', isDefault: false, isActive: true },
  { id: 6, name: 'Russian', code: 'ru', direction: 'ltr', isDefault: false, isActive: true },
  { id: 7, name: 'Spanish', code: 'es', direction: 'ltr', isDefault: false, isActive: true },
  { id: 8, name: 'Turkish', code: 'tr', direction: 'ltr', isDefault: false, isActive: true },
  { id: 9, name: 'Hindi', code: 'hi', direction: 'ltr', isDefault: false, isActive: true },
  { id: 10, name: 'Chinese', code: 'zh', direction: 'ltr', isDefault: false, isActive: true },
  { id: 11, name: 'Urdu', code: 'ur', direction: 'rtl', isDefault: false, isActive: false },
  { id: 12, name: 'Indonesian', code: 'id', direction: 'ltr', isDefault: false, isActive: true },
  { id: 13, name: 'Croatian', code: 'hr', direction: 'ltr', isDefault: false, isActive: false },
  { id: 14, name: 'Hebrew', code: 'he', direction: 'rtl', isDefault: false, isActive: true },
  { id: 15, name: 'Bengali', code: 'bn', direction: 'ltr', isDefault: false, isActive: false },
  { id: 16, name: 'Japanese', code: 'ja', direction: 'ltr', isDefault: false, isActive: true },
  { id: 17, name: 'Portuguese', code: 'pt', direction: 'ltr', isDefault: false, isActive: true },
  { id: 18, name: 'Italian', code: 'it', direction: 'ltr', isDefault: false, isActive: true },
  { id: 19, name: 'Persian', code: 'fa', direction: 'rtl', isDefault: false, isActive: false },
  { id: 20, name: 'Swedish', code: 'sv', direction: 'ltr', isDefault: false, isActive: true },
  { id: 21, name: 'Vietnamese', code: 'vi', direction: 'ltr', isDefault: false, isActive: true },
  { id: 22, name: 'Danish', code: 'da', direction: 'ltr', isDefault: false, isActive: true },
  { id: 23, name: 'Filipino', code: 'fil', direction: 'ltr', isDefault: false, isActive: false },
];

interface Language {
  id: number;
  name: string;
  code: string;
  direction: 'ltr' | 'rtl';
  isDefault: boolean;
  isActive: boolean;
}

export default function ManageLanguagesPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();

  // State for languages
  const [languages, setLanguages] = useState<Language[]>(languagesData);
  const [selectedLanguages, setSelectedLanguages] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  // State for edit dialog
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<Language | null>(null);

  // State for add language form
  const [newLanguageName, setNewLanguageName] = useState('');
  const [newLanguageCode, setNewLanguageCode] = useState('');
  const [newLanguageDirection, setNewLanguageDirection] = useState<'ltr' | 'rtl'>('ltr');
  const [newLanguageIsDefault, setNewLanguageIsDefault] = useState(false);
  const [newLanguageIsActive, setNewLanguageIsActive] = useState(true);

  // Filter languages based on search term and active tab
  const filteredLanguages = languages.filter(language => {
    const matchesSearch = language.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         language.code.toLowerCase().includes(searchTerm.toLowerCase());

    if (activeTab === 'all') return matchesSearch;
    if (activeTab === 'active') return matchesSearch && language.isActive;
    if (activeTab === 'inactive') return matchesSearch && !language.isActive;
    if (activeTab === 'rtl') return matchesSearch && language.direction === 'rtl';

    return matchesSearch;
  });

  // Handle select all languages
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedLanguages(filteredLanguages.map(lang => lang.id));
    } else {
      setSelectedLanguages([]);
    };
  };

  // Handle select single language
  const handleSelectLanguage = (id: number, checked: boolean) => {
    if (checked) {
      setSelectedLanguages([...selectedLanguages, id]);
    } else {
      setSelectedLanguages(selectedLanguages.filter(langId => langId !== id));
    }
  };

  // Handle delete selected languages
  const handleDeleteSelected = () => {
    if (selectedLanguages.length === 0) return;

    setLanguages(languages.filter(lang => !selectedLanguages.includes(lang.id)));
    setSelectedLanguages([]);
  };

  // Handle edit language
  const handleEditLanguage = (language: Language) => {
    setCurrentLanguage(language);
    setEditDialogOpen(true);
  };

  // Handle save edited language
  const handleSaveEdit = () => {
    if (!currentLanguage) return;

    setLanguages(languages.map(lang =>
      lang.id === currentLanguage.id ? currentLanguage : lang
    ));

    setEditDialogOpen(false);
    setCurrentLanguage(null);
  };

  // Handle delete language
  const handleDeleteLanguage = (id: number) => {
    setLanguages(languages.filter(lang => lang.id !== id));
  };

  // Handle toggle language active status
  const handleToggleActive = (id: number) => {
    setLanguages(languages.map(lang =>
      lang.id === id ? { ...lang, isActive: !lang.isActive } : lang
    ));
  };

  // Handle add new language
  const handleAddLanguage = () => {
    if (!newLanguageName || !newLanguageCode) return;

    // If setting as default, update other languages
    let updatedLanguages = [...languages];
    if (newLanguageIsDefault) {
      updatedLanguages = updatedLanguages.map(lang => ({
        ...lang,
        isDefault: false
      }));
    }

    const newLanguage: Language = {
      id: Math.max(...languages.map(l => l.id)) + 1,
      name: newLanguageName,
      code: newLanguageCode,
      direction: newLanguageDirection,
      isDefault: newLanguageIsDefault,
      isActive: newLanguageIsActive
    };

    setLanguages([...updatedLanguages, newLanguage]);

    // Reset form
    setNewLanguageName('');
    setNewLanguageCode('');
    setNewLanguageDirection('ltr');
    setNewLanguageIsDefault(false);
    setNewLanguageIsActive(true);
  };

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <main className="flex-1 bg-background p-6 overflow-y-auto">
          {/* Header and Breadcrumb */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Language Management</h1>
              <Breadcrumb className="mt-1">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/admin" className="flex items-center gap-1">
                      <Globe className="h-3.5 w-3.5" />
                      Admin Panel
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/settings" className="flex items-center gap-1">
                      <Settings className="h-3.5 w-3.5" />
                      Settings
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage className="flex items-center gap-1">
                      <Globe className="h-3.5 w-3.5" />
                      Manage Languages
                    </BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                className="gap-2"
                onClick={() => navigate('/admin/languages/add')}
              >
                <Plus className="h-4 w-4" />
                Add Language
              </Button>

              <Button
                variant="destructive"
                className="gap-2"
                onClick={handleDeleteSelected}
                disabled={selectedLanguages.length === 0}
              >
                <Trash2 className="h-4 w-4" />
                Delete Selected
              </Button>
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Sidebar with Add Language Form */}
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Languages className="h-5 w-5 text-primary" />
                  Add New Language
                </CardTitle>
                <CardDescription>
                  Add a new language to your application
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Flag className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="language-name">Language Name</Label>
                  </div>
                  <Input
                    id="language-name"
                    placeholder="e.g. French"
                    value={newLanguageName}
                    onChange={(e) => setNewLanguageName(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Code className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="language-code">Language Code</Label>
                  </div>
                  <Input
                    id="language-code"
                    placeholder="e.g. fr"
                    value={newLanguageCode}
                    onChange={(e) => setNewLanguageCode(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">ISO language code (2 letters)</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <ArrowRightLeft className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="language-direction">Text Direction</Label>
                  </div>
                  <Select
                    value={newLanguageDirection}
                    onValueChange={(value: 'ltr' | 'rtl') => setNewLanguageDirection(value)}
                  >
                    <SelectTrigger id="language-direction">
                      <SelectValue placeholder="Select direction" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ltr">Left to Right (LTR)</SelectItem>
                      <SelectItem value="rtl">Right to Left (RTL)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2 pt-2">
                  <Checkbox
                    id="is-default"
                    checked={newLanguageIsDefault}
                    onCheckedChange={(checked) => setNewLanguageIsDefault(checked as boolean)}
                  />
                  <Label htmlFor="is-default">Set as default language</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is-active"
                    checked={newLanguageIsActive}
                    onCheckedChange={(checked) => setNewLanguageIsActive(checked as boolean)}
                  />
                  <Label htmlFor="is-active">Active</Label>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleAddLanguage} className="w-full gap-2">
                  <Plus className="h-4 w-4" />
                  Add Language
                </Button>
              </CardFooter>
            </Card>

            {/* Main Languages Table */}
            <Card className="md:col-span-3">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5 text-primary" />
                  Available Languages
                </CardTitle>
                <CardDescription>
                  Manage your application's languages
                </CardDescription>

                <div className="flex flex-col sm:flex-row gap-4 mt-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={16} />
                    <Input
                      placeholder="Search languages..."
                      className="pl-9"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>

                  <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full sm:w-auto">
                    <TabsList className="grid grid-cols-4 w-full sm:w-auto">
                      <TabsTrigger value="all">All</TabsTrigger>
                      <TabsTrigger value="active">Active</TabsTrigger>
                      <TabsTrigger value="inactive">Inactive</TabsTrigger>
                      <TabsTrigger value="rtl">RTL</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedLanguages.length > 0 && selectedLanguages.length === filteredLanguages.length}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>Language</TableHead>
                        <TableHead>Code</TableHead>
                        <TableHead>Direction</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredLanguages.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center h-24 text-muted-foreground">
                            No languages found matching your search criteria.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredLanguages.map((language) => (
                          <TableRow key={language.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedLanguages.includes(language.id)}
                                onCheckedChange={(checked) => handleSelectLanguage(language.id, checked as boolean)}
                              />
                            </TableCell>
                            <TableCell className="font-medium">{language.name}</TableCell>
                            <TableCell>{language.code}</TableCell>
                            <TableCell>{language.direction.toUpperCase()}</TableCell>
                            <TableCell>
                              {language.isDefault ? (
                                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                  <Check className="mr-1 h-3 w-3" />
                                  Default
                                </span>
                              ) : language.isActive ? (
                                <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                                  Active
                                </span>
                              ) : (
                                <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                                  Inactive
                                </span>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-8 w-8 p-0"
                                        onClick={() => handleEditLanguage(language)}
                                      >
                                        <span className="sr-only">Edit</span>
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Edit language</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>

                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-8 w-8 p-0"
                                        onClick={() => handleToggleActive(language.id)}
                                      >
                                        <span className="sr-only">{language.isActive ? 'Disable' : 'Enable'}</span>
                                        {language.isActive ? (
                                          <X className="h-4 w-4 text-muted-foreground" />
                                        ) : (
                                          <Check className="h-4 w-4 text-muted-foreground" />
                                        )}
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>{language.isActive ? 'Disable' : 'Enable'} language</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>

                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-8 w-8 p-0"
                                        onClick={() => handleDeleteLanguage(language.id)}
                                        disabled={language.isDefault}
                                      >
                                        <span className="sr-only">Delete</span>
                                        <Trash2 className="h-4 w-4 text-destructive" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Delete language</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>

      {/* Edit Language Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              Edit Language
            </DialogTitle>
            <DialogDescription>
              Update the language details below
            </DialogDescription>
          </DialogHeader>

          {currentLanguage && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-language-name">Language Name</Label>
                <Input
                  id="edit-language-name"
                  value={currentLanguage.name}
                  onChange={(e) => setCurrentLanguage({...currentLanguage, name: e.target.value})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-language-code">Language Code</Label>
                <Input
                  id="edit-language-code"
                  value={currentLanguage.code}
                  onChange={(e) => setCurrentLanguage({...currentLanguage, code: e.target.value})}
                />
                <p className="text-xs text-muted-foreground">ISO language code (2 letters)</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-language-direction">Text Direction</Label>
                <Select
                  value={currentLanguage.direction}
                  onValueChange={(value: 'ltr' | 'rtl') => setCurrentLanguage({...currentLanguage, direction: value})}
                >
                  <SelectTrigger id="edit-language-direction">
                    <SelectValue placeholder="Select direction" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ltr">Left to Right (LTR)</SelectItem>
                    <SelectItem value="rtl">Right to Left (RTL)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="edit-is-default"
                  checked={currentLanguage.isDefault}
                  onCheckedChange={(checked) => setCurrentLanguage({...currentLanguage, isDefault: checked as boolean})}
                  disabled={currentLanguage.isDefault} // Can't unset default
                />
                <Label htmlFor="edit-is-default">Set as default language</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="edit-is-active"
                  checked={currentLanguage.isActive}
                  onCheckedChange={(checked) => setCurrentLanguage({...currentLanguage, isActive: checked as boolean})}
                />
                <Label htmlFor="edit-is-active">Active</Label>
              </div>
            </div>
          )}

          <DialogFooter className="flex space-x-2 justify-end">
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveEdit}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
