import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a number with abbreviations (K, M, B) for thousands, millions, billions
 */
export function formatNumber(num: number): string {
  if (num === undefined || num === null) return '0';

  if (num < 1000) return num.toString();

  const abbreviations = ['', 'K', 'M', 'B', 'T'];
  const tier = Math.floor(Math.log10(Math.abs(num)) / 3);

  if (tier >= abbreviations.length) return num.toString();

  const suffix = abbreviations[tier];
  const scale = Math.pow(10, tier * 3);
  const scaled = num / scale;

  return scaled.toFixed(1).replace(/\.0$/, '') + suffix;
}

/**
 * Clean video description by removing numbered tags like #[150], #[153], etc.
 * and other unwanted formatting
 */
export function cleanVideoDescription(description: string): string {
  if (!description) return '';

  return description
    // Remove numbered tags like #[150], #[153], etc.
    .replace(/#\[\d+\]/g, '')
    // Remove HTML br tags (both <br> and <br/>)
    .replace(/<br\s*\/?>/gi, ' ')
    // Remove &lt;br&gt; encoded br tags
    .replace(/&lt;br\s*\/?&gt;/gi, ' ')
    // Remove multiple consecutive spaces
    .replace(/\s+/g, ' ')
    // Remove leading/trailing whitespace
    .trim();
}

/**
 * Format duration in seconds to HH:MM:SS or MM:SS format
 */
export function formatDuration(seconds: number): string {
  if (seconds === undefined || seconds === null || seconds < 0) return '0:00';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  const formattedSeconds = remainingSeconds.toString().padStart(2, '0');

  if (hours > 0) {
    const formattedMinutes = minutes.toString().padStart(2, '0');
    return `${hours}:${formattedMinutes}:${formattedSeconds}`;
  }

  return `${minutes}:${formattedSeconds}`;
}
