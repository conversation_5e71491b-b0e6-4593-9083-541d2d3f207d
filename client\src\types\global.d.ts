// Global type declarations

declare global {
  interface EngaxePlayerOptions {
    controls?: boolean;
    fullscreen?: boolean;
    autoplay?: boolean;
    startTime?: number;
    volume?: number;
    timeline?: boolean;
    audio?: boolean;
    showControls?: boolean;
    displayControls?: boolean;
    showTimeline?: boolean;
    allowFullscreen?: boolean;
    playerVersion?: string;
    uiStyle?: 'default' | 'youtube' | 'minimal';
    playerStyle?: string;
    skin?: string;
    responsive?: boolean;
    adaptiveUI?: boolean;
    youtubeStyle?: boolean;
    controlsTimeout?: number;
    controlsHideDelay?: number;
  }

  interface EngaxePlayerEventCallback {
    (data?: any): void;
  }

  interface EngaxePlayerAPI {
    addEventListener?: (event: string, callback: EngaxePlayerEventCallback) => void;
    removeEventListener?: (event: string, callback: EngaxePlayerEventCallback) => void;
    play?: () => void;
    pause?: () => void;
    seekTo?: (time: number) => void;
    getCurrentTime?: () => number;
    getDuration?: () => number;
    setVolume?: (volume: number) => void;
    getVolume?: () => number;
  }

  interface Window {
    ngxEmbed: ((containerId: string, videoId: string, options?: EngaxePlayerOptions) => void) & EngaxePlayerAPI;
  }
}

export {};
