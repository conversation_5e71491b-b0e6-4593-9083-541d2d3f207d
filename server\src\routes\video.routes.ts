import { FastifyInstance, FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { VideoController } from '../controllers/video.controller';
import {
  UploadVideoSchema,
  ImportVideoSchema,
  UpdateVideoSchema,
  GetVideoSchema,
  GetVideosSchema,
  GetChannelVideosSchema,
  DeleteVideoSchema,
  SaveEngaxeVideoSchema
} from '../schemas/video.schema';
import { authenticate } from '../plugins/authenticate';

const videoRoutes: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  const videoController = new VideoController();

  // Upload a new video (authenticated)
  fastify.post<{
    Body: any
  }>(
    '/upload',
    {
      schema: UploadVideoSchema,
      preHandler: authenticate,
    },
    (request, reply) => videoController.uploadVideo(request as any, reply)
  );

  // Import a video from an external source (authenticated)
  fastify.post<{
    Body: any
  }>(
    '/import',
    {
      schema: ImportVideoSchema,
      preHandler: authenticate,
    },
    (request, reply) => videoController.importVideo(request as any, reply)
  );

  // Save Engaxe video to database (authenticated)
  fastify.post<{
    Body: {
      videoId: string;
      channelId: string;
    }
  }>(
    '/save-engaxe',
    {
      schema: SaveEngaxeVideoSchema,
      preHandler: authenticate,
    },
    (request, reply) => videoController.saveEngaxeVideo(request as any, reply)
  );

  // Update an existing video (authenticated)
  fastify.put<{
    Params: { id: string },
    Body: any
  }>(
    '/:id',
    {
      schema: UpdateVideoSchema,
      preHandler: authenticate,
    },
    (request, reply) => videoController.updateVideo(request as any, reply)
  );

  // Get a video by ID
  fastify.get<{
    Params: { id: string },
    Querystring: { view?: boolean }
  }>(
    '/:id',
    {
      schema: GetVideoSchema,
    },
    (request, reply) => videoController.getVideoById(request as any, reply)
  );

  // CRITICAL FIX: Get a video by URL
  fastify.get<{
    Params: { url: string },
    Querystring: { view?: boolean }
  }>(
    '/url/:url',
    {
      schema: GetVideoSchema,
    },
    (request, reply) => videoController.getVideoByUrl(request as any, reply)
  );

  // CRITICAL FIX: Fix all video URLs in the database
  fastify.post(
    '/fix-urls',
    {
      preHandler: authenticate, // Add authentication to ensure only admins can access this endpoint
    },
    (request, reply) => videoController.fixVideoUrls(request as any, reply)
  );

  // Delete all videos from the database (admin only)
  fastify.post(
    '/delete-all',
    {
      preHandler: authenticate, // Add authentication to ensure only admins can access this endpoint
    },
    (request, reply) => videoController.deleteAllVideos(request as any, reply)
  );

  // Create mappings for all videos (admin only)
  fastify.post(
    '/create-mappings',
    {
      preHandler: authenticate, // Add authentication to ensure only admins can access this endpoint
    },
    (request, reply) => videoController.createAllMappings(request as any, reply)
  );

  // Update languages for all videos (admin only)
  fastify.post(
    '/update-languages',
    {
      preHandler: authenticate, // Add authentication to ensure only admins can access this endpoint
    },
    (request, reply) => videoController.updateAllVideoLanguages(request as any, reply)
  );

  // Get videos with pagination and filtering
  fastify.get<{
    Querystring: any
  }>(
    '/',
    {
      schema: GetVideosSchema,
    },
    (request, reply) => videoController.getVideos(request as any, reply)
  );

  // Get videos by channel
  fastify.get<{
    Params: { channelId: string },
    Querystring: any
  }>(
    '/channel/:channelId',
    {
      schema: GetChannelVideosSchema,
    },
    (request, reply) => videoController.getChannelVideos(request as any, reply)
  );

  // Get Engaxe video details
  fastify.get<{
    Querystring: { videoId: string }
  }>(
    '/engaxe-details',
    {},
    (request, reply) => videoController.getEngaxeVideoDetails(request as any, reply)
  );

  // Get video metadata from Engaxe
  fastify.get<{
    Params: { videoId: string }
  }>(
    '/metadata/:videoId',
    {},
    (request, reply) => videoController.getVideoMetadata(request as any, reply)
  );

  // Delete a video (authenticated)
  fastify.delete<{
    Params: { id: string }
  }>(
    '/:id',
    {
      schema: DeleteVideoSchema,
      preHandler: authenticate,
    },
    (request, reply) => videoController.deleteVideo(request as any, reply)
  );


};

export default videoRoutes;
