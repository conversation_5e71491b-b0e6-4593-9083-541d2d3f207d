import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import permissionController from '../controllers/permission.controller';
import {
  createPermissionSchema,
  getAllPermissionsSchema,
  getPermissionByIdSchema,
  updatePermissionSchema,
  deletePermissionSchema,
  assignPermissionsToUserSchema,
} from '../schemas/permission.schema';
import { authenticate } from '../middleware/auth';
import { checkPermission } from '../middleware/rbac';
import { adminActionLogger } from '../middleware/auditLogger';

/**
 * Permission routes
 */
export default async function permissionRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // All permission routes require authentication
  fastify.register(async (fastify: FastifyInstance) => {
    // Apply authentication middleware to all routes in this context
    fastify.addHook('preHandler', authenticate);

    // Routes for reading permissions (requires basic permission)
    fastify.register(async (fastify: FastifyInstance) => {
      fastify.addHook('preHandler', checkPermission('permission:read'));

      fastify.get('/', { schema: getAllPermissionsSchema }, permissionController.getAllPermissions as any);
      fastify.get('/:id', { schema: getPermissionByIdSchema }, permissionController.getPermissionById as any);
      fastify.get('/user/:userId', permissionController.getUserPermissions as any);
      fastify.get('/check/:userId/:permissionCode', permissionController.checkUserPermission as any);
    });

    // Routes for managing permissions (requires admin permissions)
    fastify.register(async (fastify: FastifyInstance) => {
      fastify.post(
        '/',
        {
          schema: createPermissionSchema,
          preHandler: [checkPermission('permission:create'), adminActionLogger('create', 'permission', 'Permission creation')],
        },
        permissionController.createPermission as any
      );

      fastify.put(
        '/:id',
        {
          schema: updatePermissionSchema,
          preHandler: [checkPermission('permission:update'), adminActionLogger('update', 'permission', 'Permission update')],
        },
        permissionController.updatePermission as any
      );

      fastify.delete(
        '/:id',
        {
          schema: deletePermissionSchema,
          preHandler: [checkPermission('permission:delete'), adminActionLogger('delete', 'permission', 'Permission deletion')],
        },
        permissionController.deletePermission as any
      );

      fastify.post(
        '/assign/:userId',
        {
          schema: assignPermissionsToUserSchema,
          preHandler: [checkPermission('permission:assign'), adminActionLogger('assign', 'permission', 'Permission assignment to user')],
        },
        permissionController.assignPermissionsToUser as any
      );
    });
  });
}
