import mongoose, { Schema, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * Report interface extending the base entity
 */
export interface IReport extends IBaseEntity {
  /**
   * Type of content being reported
   *
   * Possible values:
   * - video: Report about a video
   * - comment: Report about a comment
   * - user: Report about a user's behavior
   * - channel: Report about a channel
   */
  reportType: 'video' | 'comment' | 'user' | 'channel' | 'issue';

  /** ID of the content being reported */
  targetId: string;

  /** User ID of the person submitting the report */
  reporterId: string;

  /** Username of the person submitting the report (for display purposes) */
  reporterUsername: string;

  /**
   * Category of the report
   * Examples: 'harassment', 'copyright', 'violence', 'spam'
   */
  category: string;

  /** Specific reason for the report */
  reason: string;

  /** Detailed description provided by the reporter */
  description: string;

  /** URLs or IDs of supporting evidence (screenshots, etc.) */
  evidence: string[];

  /**
   * Current status of the report
   *
   * Possible values:
   * - pending: Report has been submitted but not yet reviewed
   * - investigating: Report is being reviewed by moderators
   * - resolved: Report has been addressed and closed
   * - rejected: Report was determined to be invalid
   * - safe: Content was reviewed and found to be safe/appropriate
   */
  status: 'pending' | 'investigating' | 'resolved' | 'rejected' | 'safe';

  /**
   * Priority level of the report
   *
   * Possible values:
   * - low: Non-urgent issue
   * - medium: Standard priority
   * - high: Requires prompt attention
   * - urgent: Requires immediate attention (e.g., illegal content)
   */
  priority: 'low' | 'medium' | 'high' | 'urgent';

  /** User ID of the moderator assigned to handle this report */
  assignedTo?: string;

  /** Video title or content title (for display purposes) */
  contentTitle?: string;

  /** Resolution information (when status is 'resolved' or 'safe') */
  resolution?: {
    /**
     * Action taken
     * Examples: 'content_removed', 'warning_issued', 'account_suspended', 'marked_safe'
     */
    action: string;

    /** Notes about the resolution */
    note: string;

    /** User ID of the moderator who took the action */
    takenBy: string;

    /** When the action was taken */
    takenAt: Date;
  };

  /** Admin comments on the report */
  adminComments?: Array<{
    /** The comment text */
    comment: string;

    /** User ID of the admin who added the comment */
    addedBy: string;

    /** When the comment was added */
    addedAt: Date;
  }>;

  /** History of actions taken on this report */
  history: Array<{
    /**
     * Type of action
     * Examples: 'created', 'assigned', 'status_changed', 'comment_added'
     */
    action: string;

    /** User ID of the person who performed the action */
    actor: string;

    /** When the action occurred */
    timestamp: Date;

    /** Additional information about the action */
    note: string;
  }>;
}

/**
 * Report schema definition
 */
const ReportSchema = new Schema<IReport>(
  {
    reportType: {
      type: String,
      enum: ['video', 'comment', 'user', 'channel', 'issue'],
      required: true,
    },
    targetId: {
      type: String,
      required: true,
    },
    reporterId: {
      type: String,
      required: true,
      ref: 'User',
    },
    reporterUsername: {
      type: String,
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    reason: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      default: '',
    },
    evidence: {
      type: [String],
      default: [],
    },
    status: {
      type: String,
      enum: ['pending', 'investigating', 'resolved', 'rejected', 'safe'],
      default: 'pending',
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium',
    },
    assignedTo: {
      type: String,
      ref: 'User',
    },
    contentTitle: {
      type: String,
    },
    resolution: {
      action: {
        type: String,
      },
      note: {
        type: String,
      },
      takenBy: {
        type: String,
        ref: 'User',
      },
      takenAt: {
        type: Date,
      },
    },
    adminComments: [
      {
        comment: {
          type: String,
          required: true,
        },
        addedBy: {
          type: String,
          required: true,
          ref: 'User',
        },
        addedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    history: [
      {
        action: {
          type: String,
          required: true,
        },
        actor: {
          type: String,
          required: true,
          ref: 'User',
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
        note: {
          type: String,
          default: '',
        },
      },
    ],
  }
);

// Merge with base schema
ReportSchema.add(BaseSchema);

// Add indexes for common queries
ReportSchema.index({ reportType: 1, status: 1 });
ReportSchema.index({ targetId: 1 });
ReportSchema.index({ reporterId: 1 });
ReportSchema.index({ createdAt: -1 });

// Create and export the Report model
const ReportModel = mongoose.model<IReport>('Report', ReportSchema);
export default ReportModel;
