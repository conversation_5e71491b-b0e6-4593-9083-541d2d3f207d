import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Home, Plus, Edit, Trash2, Search, Filter, FileText, Calendar, Tag, User } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { useTheme } from '@/context/ThemeContext';

interface Post {
  id: string;
  title: string;
  author: string;
  category: string;
  status: 'published' | 'draft' | 'scheduled';
  date: string;
  views: number;
}

export default function ManagePostsPage() {
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [currentFilter, setCurrentFilter] = useState('all');

  // Sample data for posts
  const posts: Post[] = [
    {
      id: '1',
      title: 'Getting Started with Legal Research',
      author: '<PERSON>',
      category: 'Research',
      status: 'published',
      date: '2023-12-15',
      views: 245
    },
    {
      id: '2',
      title: 'Understanding Contract Law Basics',
      author: '<PERSON>',
      category: 'Contracts',
      status: 'published',
      date: '2023-11-22',
      views: 189
    },
    {
      id: '3',
      title: 'Top 10 Legal Resources for Beginners',
      author: 'Michael <PERSON>',
      category: 'Resources',
      status: 'scheduled',
      date: '2024-01-05',
      views: 0
    },
    {
      id: '4',
      title: 'How to Prepare for a Court Appearance',
      author: 'Emily Davis',
      category: 'Court',
      status: 'draft',
      date: '2023-12-30',
      views: 0
    },
    {
      id: '5',
      title: 'Legal Ethics in Modern Practice',
      author: 'Robert Wilson',
      category: 'Ethics',
      status: 'published',
      date: '2023-10-18',
      views: 312
    }
  ];

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedPosts([]);
    } else {
      setSelectedPosts(filteredPosts.map(post => post.id));
    }
    setSelectAll(!selectAll);
  };

  const handleSelectPost = (id: string) => {
    if (selectedPosts.includes(id)) {
      setSelectedPosts(selectedPosts.filter(postId => postId !== id));
      setSelectAll(false);
    } else {
      setSelectedPosts([...selectedPosts, id]);
      if (selectedPosts.length + 1 === filteredPosts.length) {
        setSelectAll(true);
      }
    }
  };

  const getStatusBadgeClass = (status: 'published' | 'draft' | 'scheduled') => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (currentFilter === 'all') return matchesSearch;
    return matchesSearch && post.status === currentFilter;
  });

  return (
    <div className="flex h-screen bg-[#f5f7fb] overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold mb-4">Manage Posts</h1>
            
            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-gray-600 hover:text-gray-900 flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-gray-500">&gt;</span>
              <span className="text-[#38bdf8]">Manage Posts</span>
            </div>

            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold">Blog Posts</h2>
                  <Link 
                    to="/admin/posts/add" 
                    className="bg-[#38bdf8] text-white rounded-md px-4 py-2 flex items-center hover:bg-blue-600 transition-colors"
                  >
                    <Plus size={16} className="mr-2" />
                    Add New Post
                  </Link>
                </div>
                
                <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div className="flex flex-1">
                    <div className="relative flex-1">
                      <input
                        type="text"
                        placeholder="Search posts..."
                        className="w-full border border-gray-300 rounded-l-md px-4 py-2 pl-10"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
                    </div>
                    <button className="bg-[#38bdf8] text-white rounded-r-md px-6 py-2 hover:bg-blue-500 transition-colors">
                      Search
                    </button>
                  </div>
                  
                  <div className="flex items-center">
                    <Filter size={16} className="text-gray-500 mr-2" />
                    <select 
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                      value={currentFilter}
                      onChange={(e) => setCurrentFilter(e.target.value)}
                    >
                      <option value="all">All Posts</option>
                      <option value="published">Published</option>
                      <option value="draft">Drafts</option>
                      <option value="scheduled">Scheduled</option>
                    </select>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100 border-b border-gray-200">
                        <th className="p-3 text-left w-10">
                          <input 
                            type="checkbox" 
                            className="h-4 w-4"
                            checked={selectAll}
                            onChange={handleSelectAll}
                          />
                        </th>
                        <th className="p-3 text-left">Title</th>
                        <th className="p-3 text-left">Author</th>
                        <th className="p-3 text-left">Category</th>
                        <th className="p-3 text-left">Status</th>
                        <th className="p-3 text-left">Date</th>
                        <th className="p-3 text-left">Views</th>
                        <th className="p-3 text-left">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredPosts.map((post) => (
                        <tr key={post.id} className="border-b border-gray-200 hover:bg-gray-50">
                          <td className="p-3">
                            <input 
                              type="checkbox" 
                              className="h-4 w-4"
                              checked={selectedPosts.includes(post.id)}
                              onChange={() => handleSelectPost(post.id)}
                            />
                          </td>
                          <td className="p-3 font-medium">
                            <div className="flex items-center">
                              <FileText size={16} className="text-gray-400 mr-2" />
                              {post.title}
                            </div>
                          </td>
                          <td className="p-3">
                            <div className="flex items-center">
                              <User size={16} className="text-gray-400 mr-2" />
                              {post.author}
                            </div>
                          </td>
                          <td className="p-3">
                            <div className="flex items-center">
                              <Tag size={16} className="text-gray-400 mr-2" />
                              {post.category}
                            </div>
                          </td>
                          <td className="p-3">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(post.status)}`}>
                              {post.status.charAt(0).toUpperCase() + post.status.slice(1)}
                            </span>
                          </td>
                          <td className="p-3">
                            <div className="flex items-center">
                              <Calendar size={16} className="text-gray-400 mr-2" />
                              {new Date(post.date).toLocaleDateString()}
                            </div>
                          </td>
                          <td className="p-3">{post.views}</td>
                          <td className="p-3">
                            <div className="flex space-x-2">
                              <Link to={`/admin/posts/${post.id}/edit`} className="text-blue-500 hover:text-blue-700">
                                <Edit size={16} />
                              </Link>
                              <button className="text-red-500 hover:text-red-700">
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="mt-6 flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-600">Showing {filteredPosts.length} out of {posts.length} posts</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="border border-gray-300 rounded-md px-3 py-1 text-gray-600">
                      ⟨
                    </button>
                    <button className="bg-[#38bdf8] text-white rounded-md px-3 py-1">
                      1
                    </button>
                    <button className="border border-gray-300 rounded-md px-3 py-1 text-gray-600">
                      ⟩
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
