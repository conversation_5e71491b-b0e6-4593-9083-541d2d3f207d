/**
 * Test script to verify that languages are being properly saved and retrieved
 * 
 * Run this script with Node.js:
 * node test-verify-languages.js
 */

const fetch = require('node-fetch');

// Configuration
const API_URL = 'http://localhost:3003/api';
const AUTH_TOKEN = 'YOUR_AUTH_TOKEN'; // Replace with your actual auth token
const VIDEO_ID = 'YOUR_VIDEO_ID'; // Replace with the ID of the video you want to check

// Function to get a video by ID
async function getVideo(videoId) {
  try {
    console.log(`Getting video with ID: ${videoId}...`);
    
    const response = await fetch(`${API_URL}/videos/${videoId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('Video retrieved successfully!');
      
      // Check if the video has languages
      if (data.languages && data.languages.length > 0) {
        console.log(`Video has ${data.languages.length} languages:`);
        data.languages.forEach((lang, idx) => {
          console.log(`Language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url}, isDefault: ${lang.isDefault}, flag: ${lang.flag || 'none'}`);
        });
      } else {
        console.log('Video has no languages');
      }
      
      return data;
    } else {
      console.error('Failed to get video:', data);
      return null;
    }
  } catch (error) {
    console.error('Error getting video:', error);
    return null;
  }
}

// Function to update a video with multiple languages
async function updateVideoLanguages(videoId) {
  try {
    // First get the current video data
    const video = await getVideo(videoId);
    if (!video) {
      console.error('Could not get video data');
      return;
    }
    
    // Update the languages
    const updatedVideo = {
      ...video,
      languages: [
        {
          code: 'en',
          name: 'English',
          flag: '🇺🇸',
          isDefault: true,
          url: 'XLcMq2' // Use a valid Engaxe ID
        },
        {
          code: 'hi',
          name: 'Hindi',
          flag: '🇮🇳',
          isDefault: false,
          url: 'suZKhW' // Use a different valid Engaxe ID
        }
      ]
    };
    
    console.log('Updating video with multiple languages...');
    console.log('Updated languages:', JSON.stringify(updatedVideo.languages, null, 2));
    
    const response = await fetch(`${API_URL}/videos/${videoId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      },
      body: JSON.stringify(updatedVideo)
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('Video updated successfully!');
      console.log('Response:', JSON.stringify(data, null, 2));
      
      // Verify the update
      await getVideo(videoId);
    } else {
      console.error('Failed to update video:', data);
    }
  } catch (error) {
    console.error('Error updating video:', error);
  }
}

// Run the functions
if (VIDEO_ID === 'YOUR_VIDEO_ID') {
  console.log('Please replace YOUR_VIDEO_ID with an actual video ID');
} else {
  // First get the video to see its current languages
  getVideo(VIDEO_ID)
    .then(() => {
      // Then update the video with multiple languages
      return updateVideoLanguages(VIDEO_ID);
    })
    .catch(error => {
      console.error('Error in test script:', error);
    });
}
