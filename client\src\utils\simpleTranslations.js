/**
 * Simple translations for common phrases
 */

const translations = {
  // Hindi translations
  'hi': {
    'hello': 'नमस्ते',
    'hi': 'नमस्ते',
    'hii': 'नमस्ते',
    'how are you': 'आप कैसे हैं?',
    'how can i help you': 'मैं आपकी कैसे मदद कर सकता हूँ?',
    'may i help you': 'क्या मैं आपकी मदद कर सकता हूँ?',
    'good morning': 'सुप्रभात',
    'i am happy': 'मैं खुश हूँ',
    'why are you happy': 'आप खुश क्यों हैं?',
    'because my classes are ending': 'क्योंकि मेरी कक्षाएं समाप्त हो रही हैं',
    'do want to play game': 'क्या आप गेम खेलना चाहते हैं?',
    'it is ok': 'यह ठीक है',
    'this is not working': 'यह काम नहीं कर रहा है',
    'can you come with me': 'क्या आप मेरे साथ आ सकते हैं?'
  },
  
  // Marathi translations
  'mr': {
    'hello': 'नमस्कार',
    'hi': 'नमस्कार',
    'hii': 'नमस्कार',
    'how are you': 'तुम्ही कसे आहात?',
    'how can i help you': 'मी तुम्हाला कशी मदत करू शकतो?',
    'may i help you': 'मी तुम्हाला मदत करू शकतो का?',
    'good morning': 'सुप्रभात',
    'i am happy': 'मी आनंदी आहे',
    'why are you happy': 'तुम्ही आनंदी का आहात?',
    'because my classes are ending': 'कारण माझे वर्ग संपत आहेत',
    'do want to play game': 'तुम्हाला खेळ खेळायचा आहे का?',
    'it is ok': 'हे ठीक आहे',
    'this is not working': 'हे काम करत नाही',
    'can you come with me': 'तुम्ही माझ्यासोबत येऊ शकता का?'
  },
  
  // Gujarati translations
  'gu': {
    'hello': 'નમસ્તે',
    'hi': 'નમસ્તે',
    'hii': 'નમસ્તે',
    'how are you': 'તમે કેમ છો?',
    'how can i help you': 'હું તમને કેવી રીતે મદદ કરી શકું?',
    'may i help you': 'શું હું તમને મદદ કરી શકું?',
    'good morning': 'સુપ્રભાત',
    'i am happy': 'હું ખુશ છું',
    'why are you happy': 'તમે શા માટે ખુશ છો?',
    'because my classes are ending': 'કારણ કે મારા વર્ગો સમાપ્ત થઈ રહ્યા છે',
    'do want to play game': 'શું તમે રમત રમવા માંગો છો?',
    'it is ok': 'તે બરાબર છે',
    'this is not working': 'આ કામ કરતું નથી',
    'can you come with me': 'શું તમે મારી સાથે આવી શકો છો?'
  }
};

/**
 * Get translation for a phrase in a specific language
 * @param {string} phrase - The phrase to translate
 * @param {string} language - The target language code
 * @returns {string} - The translated phrase or the original if no translation exists
 */
export function getTranslation(phrase, language) {
  if (!phrase || language === 'en') {
    return phrase;
  }
  
  // Convert to lowercase for case-insensitive matching
  const lowerPhrase = phrase.toLowerCase().trim();
  
  // Check if we have a translation for this language
  if (translations[language] && translations[language][lowerPhrase]) {
    return translations[language][lowerPhrase];
  }
  
  // If no translation found, return the original phrase with a language prefix
  const prefixes = {
    'hi': '[हिंदी] ',
    'mr': '[मराठी] ',
    'gu': '[ગુજરાતી] ',
    'ta': '[தமிழ்] ',
    'te': '[తెలుగు] ',
    'bn': '[বাংলা] ',
    'kn': '[ಕನ್ನಡ] ',
    'ml': '[മലയാളം] ',
    'es': '[Español] ',
    'fr': '[Français] ',
    'de': '[Deutsch] ',
    'zh': '[中文] ',
    'ja': '[日本語] ',
    'ru': '[Русский] ',
    'ar': '[العربية] '
  };
  
  return prefixes[language] + phrase;
}

export default {
  getTranslation,
  translations
};
