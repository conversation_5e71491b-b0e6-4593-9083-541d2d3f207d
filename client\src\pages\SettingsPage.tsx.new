import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminNavbar from '@/components/admin/AdminNavbar';
import { Settings as SettingsIcon } from 'lucide-react';
import FeatureUsageModal from '@/components/modals/FeatureUsageModal';
import WebsiteInformation from '@/components/settings/WebsiteInformation';
import EmailSetup from '@/components/settings/EmailSetup';
import SocialLoginSettings from '@/components/settings/SocialLoginSettings';

// Settings sidebar options
const settingsOptions = [
  { id: 'general', label: 'General Configuration' },
  { id: 'website-information', label: 'Website Information' },
  { id: 'email', label: 'E-mail Setup' },
  { id: 'social', label: 'Social Login Settings' },
];

export default function SettingsPage() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeSection, setActiveSection] = useState('general');
  const [isFeatureModalOpen, setIsFeatureModalOpen] = useState(false);
  const [featureModalName, setFeatureModalName] = useState('');

  // Set active section based on URL path
  useEffect(() => {
    const path = location.pathname;
    if (path === '/settings') {
      setActiveSection('general');
    } else if (path.startsWith('/settings/')) {
      const section = path.split('/')[2];
      // Special handling for website-information
      if (section === 'website-information') {
        setActiveSection('website-information');
      } else if (settingsOptions.some(option => option.id === section)) {
        setActiveSection(section);
      }
    }
  }, [location.pathname]);

  // Redirect non-admin users to home page
  useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // If not admin, don't render the settings page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />
      <div className="flex-1 flex flex-col">
        <AdminNavbar />
        <div className="flex-1 bg-gray-100 p-6 overflow-y-auto">
          <FeatureUsageModal isOpen={isFeatureModalOpen} onClose={() => setIsFeatureModalOpen(false)} featureName={featureModalName} />

          {/* Settings Content */}
          <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-6">
            <h1 className="text-xl font-bold mb-6 text-black">{settingsOptions.find(o => o.id === activeSection)?.label}</h1>

            {(activeSection === 'website' || activeSection === 'website-information') && (
              <WebsiteInformation />
            )}

            {activeSection === 'general' && (
              <div>
                {/* Breadcrumb Navigation */}
                <div className="flex items-center gap-2 text-sm mb-4 bg-blue-50 p-2 rounded">
                  <span className="flex items-center gap-1 text-black font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                      <polyline points="9 22 9 12 15 12 15 22"></polyline>
                    </svg>
                    Admin Panel
                  </span>
                  <span className="text-gray-600">›</span>
                  <span className="text-black">Settings</span>
                  <span className="text-gray-600">›</span>
                  <span className="text-blue-600 font-medium">General Configuration</span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left Column */}
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h2 className="text-lg font-semibold mb-4 text-black">General Configuration</h2>

                    {/* Developer Mode */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Developer Mode</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Turn on error reporting so developer can see errors.</p>
                    </div>

                    {/* Developers (API System) */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Developers (API System)</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Show /developers page to all users for API requests.</p>
                    </div>

                    {/* Maintenance Mode */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Maintenance Mode</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Turn the whole site under Maintenance.<br />You can get the site back by visiting https://engaxe.com/login?access=admin</p>
                    </div>

                    {/* SEO Links */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">SEO Links</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Enable SEO links E.g: site.com/this-is-a-video-_ID.html, this will improve your Google Ranking</p>
                    </div>

                    {/* History System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">History System</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Users will be able to view their watched videos.</p>
                    </div>

                    {/* Popular Channels */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Popular Channels</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Show popular channels ranked by most subscribers.</p>
                    </div>

                    {/* Article System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Article System</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Create articles in blog section.</p>
                    </div>

                    {/* Show Articles In Home Page */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Show Articles In Home Page</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Articles will seen in home page.</p>
                    </div>

                    {/* +18 Pop-up */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">+18 Pop-up</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Show +18 Pop-up when user access the site.</p>
                    </div>

                    {/* +18 Block Time */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">+18 Block Time</h3>
                      <input type="number" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="1" />
                      <p className="text-sm text-gray-700">Set the amount of hours to block a user which isn't above 18 years old.</p>
                    </div>

                    {/* Language Modal */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Language Modal</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Show language modal when user access the site.</p>
                    </div>

                    {/* Default Language */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Default Language</h3>
                      <select className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="english">
                        <option value="english">English</option>
                        <option value="spanish">Spanish</option>
                        <option value="french">French</option>
                        <option value="german">German</option>
                        <option value="hindi">Hindi</option>
                      </select>
                      <p className="text-sm text-gray-700">Choose the site default language.</p>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h2 className="text-lg font-semibold mb-4 text-black">Login & Registration</h2>

                    {/* Auto Username On Register */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Auto Username On Register</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Generate an auto username on sign up.<br />Registration form will ask for user's first name and last name.</p>
                    </div>

                    {/* Two-Factor Settings */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Two-Factor Settings</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Send confirmation code to email or SMS when user login.</p>
                    </div>

                    {/* Google Authenticator Settings */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Google Authenticator Settings</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Google Authenticator code when user login.</p>
                    </div>

                    {/* Authy Settings */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Authy Settings</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Authy code when user login.</p>
                    </div>

                    {/* Authy Token */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Authy Token</h3>
                      <input type="text" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" placeholder="Enter Authy Token" />
                      <p className="text-sm text-gray-700">Authy Token from your twilio account</p>
                    </div>

                    {/* Password Complexity System */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Password Complexity System</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">The system will require a powerful password on sign up, including letters, numbers and special characters.</p>
                    </div>

                    {/* Remember This Device */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Remember This Device</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Remember this device in welcome page.</p>
                    </div>

                    {/* Recaptcha */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Recaptcha</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Enable reCaptcha to prevent spam.</p>
                    </div>

                    {/* Recaptcha Key */}
                    <div className="border-b pb-4 mb-4">
                      <h3 className="font-medium mb-2 text-gray-800">Recaptcha Key</h3>
                      <input type="text" className="w-full border border-gray-300 rounded px-3 py-2 mb-2 text-black" defaultValue="6LeiIZcqAAAAGtfRjWyjOK8hgOYCA0Kp_3wyjNy" />
                      <p className="text-sm text-gray-700">Your reCaptcha site key</p>
                    </div>

                    {/* Prevent Bad Login Attempts */}
                    <div className="border-b pb-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-800">Prevent Bad Login Attempts</h3>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                        </label>
                      </div>
                      <p className="text-sm text-gray-700">Enable this feature to track and stop brute-force attacks.</p>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end mt-6">
                  <button
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    onClick={() => alert('Settings saved successfully!')}
                  >
                    Save Changes
                  </button>
                </div>
              </div>
            )}

            {activeSection === 'email' && <EmailSetup />}

            {activeSection === 'social' && <SocialLoginSettings />}

            {activeSection !== 'general' && activeSection !== 'email' && activeSection !== 'social' && activeSection !== 'website-information' && (
              <div className="py-8 text-center text-gray-500">
                <p>Settings for {settingsOptions.find(o => o.id === activeSection)?.label} will be configured here.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
