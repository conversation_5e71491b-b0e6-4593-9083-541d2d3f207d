/**
 * Utility functions for handling H5P content
 */

/**
 * Initialize H5P fullscreen functionality
 * This function adds event listeners to handle H5P fullscreen buttons
 */
/**
 * Initialize specific H5P fullscreen button functionality
 * This function adds event listeners to handle the specific H5P fullscreen button with class "h5p-control h5p-fullscreen"
 */
export const initSpecificH5PFullscreenButton = (): void => {
  // Function to handle the specific H5P fullscreen button
  const handleSpecificH5PFullscreenButton = (): void => {
    // Find all elements matching the specific H5P fullscreen button
    // Use multiple selectors to ensure we catch all possible fullscreen buttons
    const fullscreenButtons = [
      ...document.querySelectorAll('div[role="button"][tabindex="0"].h5p-control.h5p-fullscreen'),
      ...document.querySelectorAll('div[role="button"][tabindex="0"][aria-label="Fullscreen"]'),
      ...document.querySelectorAll('.h5p-control.h5p-fullscreen'),
      ...document.querySelectorAll('.h5p-fullscreen')
    ];

    if (fullscreenButtons.length === 0) {
      // If no buttons found, try again after a short delay
      setTimeout(handleSpecificH5PFullscreenButton, 300);
      return;
    }

    // Add click event listeners to each button
    fullscreenButtons.forEach(button => {
      // Remove any existing listeners to avoid duplicates
      button.removeEventListener('click', handleFullscreenButtonClick);

      // Add the click event listener
      button.addEventListener('click', handleFullscreenButtonClick);

      // Make sure the button is visible and clickable
      const buttonEl = button as HTMLElement;
      buttonEl.style.pointerEvents = 'auto';
      buttonEl.style.cursor = 'pointer';
      buttonEl.style.zIndex = '9999';

      // Add a data attribute to mark this as processed
      buttonEl.setAttribute('data-fullscreen-enabled', 'true');

      // Log that we found and set up the button
      console.log('H5P fullscreen button found and set up:', buttonEl);
    });
  };

  // Function to handle fullscreen button click
  const handleFullscreenButtonClick = (event: Event): void => {
    // Prevent default behavior and stop propagation
    event.preventDefault();
    event.stopPropagation();

    console.log('H5P fullscreen button clicked');

    // Get the button element
    const button = event.currentTarget as HTMLElement;

    // Find the appropriate container for fullscreen
    // First try to find the engaxe-player-container as it's the most specific
    let container = document.getElementById('engaxe-player-container');

    // If not found, try to find other containers by traversing up from the button
    if (!container) {
      container = button.closest('.h5p-iframe-wrapper') ||
                 button.closest('.h5p-content') ||
                 button.closest('.video-player') ||
                 button.closest('.h5p-video-container');
    }

    // If still not found, try to find the closest video container
    if (!container) {
      const videoContainer = document.querySelector('.video-player') ||
                            document.querySelector('.h5p-iframe-wrapper') ||
                            document.querySelector('#video-container');
      if (videoContainer) {
        container = videoContainer as HTMLElement;
      }
    }

    // If we found a container, toggle fullscreen
    if (container) {
      console.log('Found container for fullscreen:', container);

      try {
        if (document.fullscreenElement) {
          console.log('Exiting fullscreen');
          // Exit fullscreen
          document.exitFullscreen().catch(err => {
            console.error('Error exiting fullscreen:', err);

            // Try alternative methods
            try {
              if ((document as any).mozCancelFullScreen) {
                (document as any).mozCancelFullScreen();
              } else if ((document as any).webkitExitFullscreen) {
                (document as any).webkitExitFullscreen();
              } else if ((document as any).msExitFullscreen) {
                (document as any).msExitFullscreen();
              }
            } catch (altErr) {
              console.error('Alternative exit fullscreen methods failed:', altErr);
            }
          });
        } else {
          console.log('Entering fullscreen');
          // Prepare container for fullscreen
          container.style.position = 'relative';
          container.style.width = '100%';
          container.style.height = '100%';

          // Enter fullscreen
          const requestFullscreen = container.requestFullscreen ||
                                   (container as any).mozRequestFullScreen ||
                                   (container as any).webkitRequestFullscreen ||
                                   (container as any).msRequestFullscreen;

          if (requestFullscreen) {
            requestFullscreen.call(container).catch(err => {
              console.error('Error entering fullscreen:', err);

              // Try with document.body as a fallback
              try {
                const bodyRequestFullscreen = document.body.requestFullscreen ||
                                           (document.body as any).mozRequestFullScreen ||
                                           (document.body as any).webkitRequestFullscreen ||
                                           (document.body as any).msRequestFullscreen;

                if (bodyRequestFullscreen) {
                  bodyRequestFullscreen.call(document.body);
                }
              } catch (bodyErr) {
                console.error('Fallback to body fullscreen failed:', bodyErr);
              }
            });
          } else {
            console.error('Fullscreen API is not supported in this browser');
          }
        }
      } catch (error) {
        console.error('Error toggling fullscreen:', error);
      }
    } else {
      console.error('No suitable container found for fullscreen');
    }
  };

  // Initial call to set up the buttons
  handleSpecificH5PFullscreenButton();

  // Set up a mutation observer to detect when new H5P fullscreen buttons are added to the DOM
  const observer = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any of the added nodes contain H5P fullscreen buttons
        for (let i = 0; i < mutation.addedNodes.length; i++) {
          const node = mutation.addedNodes[i];
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;
            if (element.querySelector('div[role="button"][tabindex="0"].h5p-control.h5p-fullscreen')) {
              // If a new H5P fullscreen button is found, set it up
              handleSpecificH5PFullscreenButton();
              break;
            }
          }
        }
      }
    }
  });

  // Start observing the document body for changes
  observer.observe(document.body, { childList: true, subtree: true });

  // Removed aggressive interval checking to prevent performance issues and unwanted reloads
  // The mutation observer should be sufficient to detect new H5P buttons

  // Add a direct document-level event listener for the specific button
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;

    // Check if the clicked element matches our specific selector
    if (
      target &&
      target.getAttribute('role') === 'button' &&
      target.getAttribute('tabindex') === '0' &&
      target.classList.contains('h5p-control') &&
      target.classList.contains('h5p-fullscreen')
    ) {
      console.log('Direct click on H5P fullscreen button detected');

      // If the button doesn't have our data attribute, it hasn't been processed yet
      if (!target.hasAttribute('data-fullscreen-enabled')) {
        // Prevent default behavior
        event.preventDefault();
        event.stopPropagation();

        // Call our handler directly
        handleFullscreenButtonClick.call(target, event);
      }
    }
  }, true);
};

export const initH5PFullscreen = (): void => {
  // Function to handle fullscreen toggle
  const toggleH5PFullscreen = (container: HTMLElement): void => {
    try {
      if (document.fullscreenElement) {
        // Exit fullscreen
        document.exitFullscreen().catch(err => {
          console.error('Error exiting fullscreen:', err);
        });
      } else {
        // Enter fullscreen
        const requestFullscreen = container.requestFullscreen ||
                                 (container as any).mozRequestFullScreen ||
                                 (container as any).webkitRequestFullscreen ||
                                 (container as any).msRequestFullscreen;

        if (requestFullscreen) {
          requestFullscreen.call(container).catch(err => {
            console.error('Error entering fullscreen:', err);
          });
        } else {
          console.error('Fullscreen API is not supported in this browser');
        }
      }
    } catch (error) {
      console.error('Error toggling H5P fullscreen:', error);
    }
  };

  // Function to find the closest container element
  const findH5PContainer = (element: HTMLElement): HTMLElement | null => {
    // Try to find the H5P container by traversing up the DOM
    let current = element;

    // Look for common H5P container classes
    while (current && current !== document.body) {
      if (
        current.classList.contains('h5p-content') ||
        current.classList.contains('h5p-iframe-wrapper') ||
        current.classList.contains('h5p-iframe') ||
        current.hasAttribute('data-content-id')
      ) {
        return current;
      }

      // If we find an iframe, use its parent as the container
      if (current.tagName === 'IFRAME') {
        return current.parentElement;
      }

      current = current.parentElement;
    }

    // If no specific container found, return the element itself
    return element;
  };

  // Add a global click event listener to catch H5P fullscreen button clicks
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;

    // Check if the clicked element is an H5P fullscreen button
    if (
      target &&
      (
        target.classList.contains('h5p-fullscreen') ||
        target.classList.contains('h5p-control') ||
        (
          target.hasAttribute('aria-label') &&
          target.getAttribute('aria-label')?.toLowerCase().includes('fullscreen')
        )
      )
    ) {
      // Find the container element
      const container = findH5PContainer(target);

      if (container) {
        // Prevent default behavior
        event.preventDefault();
        event.stopPropagation();

        // Toggle fullscreen
        toggleH5PFullscreen(container);
      }
    }
  }, true);

  // Also handle fullscreen buttons inside iframes
  const setupIframeListeners = (): void => {
    const iframes = document.querySelectorAll('iframe');

    iframes.forEach(iframe => {
      try {
        // Try to access iframe content
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

        if (iframeDoc) {
          // Add event listener to the iframe document
          iframeDoc.addEventListener('click', (event) => {
            const target = event.target as HTMLElement;

            if (
              target &&
              (
                target.classList.contains('h5p-fullscreen') ||
                target.classList.contains('h5p-control') ||
                (
                  target.hasAttribute('aria-label') &&
                  target.getAttribute('aria-label')?.toLowerCase().includes('fullscreen')
                )
              )
            ) {
              // Use the iframe's parent element as the container
              const container = iframe.parentElement || iframe;

              // Prevent default behavior
              event.preventDefault();
              event.stopPropagation();

              // Toggle fullscreen
              toggleH5PFullscreen(container);
            }
          }, true);
        }
      } catch (error) {
        // Ignore cross-origin errors
        // This is expected for iframes from different domains
      }
    });
  };

  // Set up iframe listeners initially
  setupIframeListeners();

  // Set up a mutation observer to handle dynamically added iframes
  const observer = new MutationObserver((mutations) => {
    let shouldSetupIframes = false;

    mutations.forEach(mutation => {
      if (mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach(node => {
          if (node.nodeName === 'IFRAME') {
            shouldSetupIframes = true;
          } else if (node.nodeType === Node.ELEMENT_NODE) {
            if ((node as Element).querySelector('iframe')) {
              shouldSetupIframes = true;
            }
          }
        });
      }
    });

    if (shouldSetupIframes) {
      setupIframeListeners();
    }
  });

  // Start observing the document
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
};
