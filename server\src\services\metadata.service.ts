import axios from 'axios';
import { extractVideoInfo } from '../utils/video.utils';
import { createBadRequestError } from '../utils/errors/AppError';
import { ErrorCodes } from '../utils/errors/errorCodes';
import { engaxeApiService } from './engaxe-api.service';
import { UserModel } from '../models';

/**
 * Parse duration string in format "MM:SS" or "HH:MM:SS" to seconds
 */
function parseDurationToSeconds(durationStr: string | number): number {
  if (!durationStr) {
    return 0;
  }

  // If it's already a number, return it
  if (typeof durationStr === 'number') {
    return durationStr;
  }

  // If it's a string that doesn't contain colons, try to parse as number
  if (typeof durationStr === 'string' && !durationStr.includes(':')) {
    const parsed = parseInt(durationStr, 10);
    return isNaN(parsed) ? 0 : parsed;
  }

  // Handle MM:SS or HH:MM:SS format
  if (typeof durationStr === 'string') {
    const cleanDuration = durationStr.trim();
    const parts = cleanDuration.split(':');

    if (parts.length === 2) {
      // MM:SS format
      const minutes = parseInt(parts[0], 10) || 0;
      const seconds = parseInt(parts[1], 10) || 0;
      return (minutes * 60) + seconds;
    } else if (parts.length === 3) {
      // HH:MM:SS format
      const hours = parseInt(parts[0], 10) || 0;
      const minutes = parseInt(parts[1], 10) || 0;
      const seconds = parseInt(parts[2], 10) || 0;
      return (hours * 3600) + (minutes * 60) + seconds;
    }
  }

  return 0;
}

/**
 * Service for fetching metadata from external sources
 */
export class MetadataService {
  /**
   * Fetch video metadata from a URL
   * @param url Video URL (Engaxe only)
   * @param userId Optional user ID to use their Engaxe credentials
   * @returns Video metadata
   */
  async fetchVideoMetadata(url: string, userId?: string) {
    console.log(`Attempting to fetch metadata for URL: ${url}, userId: ${userId || 'none'}`);

    // Check if the URL is just a video ID (no slashes, dots, or protocol)
    if (!url.includes('/') && !url.includes('.') && !url.includes(':') && url.length > 3 && url.length < 20) {
      console.log(`URL appears to be a direct video ID: ${url}`);
      return this.fetchEngaxeMetadata(url, userId);
    }

    // Try to extract video info from URL
    const videoInfo = extractVideoInfo(url);

    if (!videoInfo) {
      console.error(`Failed to extract video info from URL: ${url}`);

      // Try to extract video ID directly from the URL as a fallback
      const urlParts = url.split('/');
      const potentialVideoId = urlParts[urlParts.length - 1];

      if (potentialVideoId && potentialVideoId.length > 3 && potentialVideoId.length < 20 && !potentialVideoId.includes('.')) {
        console.log(`Using last URL segment as video ID: ${potentialVideoId}`);
        return this.fetchEngaxeMetadata(potentialVideoId, userId);
      }

      throw createBadRequestError('Invalid video URL. Only Engaxe URLs are supported.', ErrorCodes.INVALID_URL);
    }

    console.log(`Successfully extracted video ID: ${videoInfo.videoId} from platform: ${videoInfo.platform}`);

    const { videoId } = videoInfo;
    return this.fetchEngaxeMetadata(videoId, userId);
  }

  /**
   * Fetch metadata from Engaxe
   * @param videoId Engaxe video ID
   * @param userId Optional user ID to use their Engaxe credentials
   * @returns Video metadata
   */
  /**
   * Process video details from Engaxe API and convert to our metadata format
   * @param videoDetails Video details from Engaxe API
   * @param videoId Video ID
   * @returns Processed metadata
   */
  private processVideoDetails(videoDetails: any, videoId: string) {
    // Log the video details for debugging
    console.log('Processing Engaxe video details:', JSON.stringify(videoDetails, null, 2));

    // Ensure thumbnail URL is valid
    let thumbnailUrl = videoDetails.thumbnail;

    // If thumbnail is missing or invalid, generate a fallback URL
    if (!thumbnailUrl || thumbnailUrl === 'undefined' || thumbnailUrl === 'null') {
      // Use a reliable placeholder service
      thumbnailUrl = `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`;
      console.log(`Using fallback thumbnail URL: ${thumbnailUrl}`);
    } else {
      console.log(`Using original thumbnail URL: ${thumbnailUrl}`);
    }

    // Parse duration properly
    const durationInSeconds = parseDurationToSeconds(videoDetails.duration);
    console.log(`Duration parsing in metadata service: "${videoDetails.duration}" -> ${durationInSeconds} seconds`);

    // Map Engaxe response to our metadata format
    const metadata = {
      title: videoDetails.title || `Engaxe Video ${videoId}`,
      description: videoDetails.description || '',
      thumbnailUrl: thumbnailUrl,
      embedUrl: `https://engaxe.com/embed/${videoId}`,
      platform: 'engaxe',
      externalId: videoId,
      originalUrl: `https://engaxe.com/v/${videoId}`, // Use the correct URL format
      author: videoDetails.owner?.username || videoDetails.publisher?.name || 'Engaxe Creator',
      authorUrl: videoDetails.publisher?.url || `https://engaxe.com/creator/${videoDetails.owner?.id || videoDetails.user_id || ''}`,
      duration: durationInSeconds,
      duration_raw: videoDetails.duration, // Keep original for debugging
      views: typeof videoDetails.views === 'string' ? parseInt(videoDetails.views, 10) || 0 : (videoDetails.views || 0),
      likes: typeof videoDetails.likes === 'string' ? parseInt(videoDetails.likes, 10) || 0 : (videoDetails.likes || 0),
      dislikes: typeof videoDetails.dislikes === 'string' ? parseInt(videoDetails.dislikes, 10) || 0 : (videoDetails.dislikes || 0),
      publishedAt: videoDetails.time || videoDetails.published_at || new Date().toISOString(),
      category: videoDetails.category_name || videoDetails.category || 'Uncategorized',
      tags: videoDetails.tags?.split(',') || [],
      videoUrl: videoDetails.video_location || `https://engaxe.com/videos/${videoId}/stream`,
    };

    console.log('Mapped metadata:', JSON.stringify(metadata, null, 2));
    return metadata;
  }

  private async fetchEngaxeMetadata(videoId: string, userId?: string) {
    try {
      console.log(`Fetching Engaxe metadata for video ID: ${videoId}`);

      // If userId is provided, try to get Engaxe credentials
      let sessionId, engaxeUserId;

      if (userId) {
        const user = await UserModel.findOne({ id: userId, deletedAt: null });
        if (user?.engaxe?.sessionId && user?.engaxe?.userId) {
          sessionId = user.engaxe.sessionId;
          engaxeUserId = user.engaxe.userId;
          console.log(`Using Engaxe credentials for user ${userId}: sessionId=${sessionId}, engaxeUserId=${engaxeUserId}`);
        } else {
          console.log(`No Engaxe credentials found for user ${userId}`);
        }
      }

      // First try the direct API test endpoint
      try {
        console.log('Trying direct API test endpoint');
        const apiUrl = `http://localhost:3003/api/v1/api-test/engaxe/${videoId}`;
        const response = await axios.get(apiUrl, { timeout: 5000 });

        if (response.data.success && response.data.data) {
          console.log('Successfully fetched metadata from API test endpoint');
          const videoDetails = response.data.data;
          return this.processVideoDetails(videoDetails, videoId);
        } else {
          console.warn('API test endpoint did not return successful response');
        }
      } catch (apiTestError) {
        console.warn('Error using API test endpoint:', apiTestError);
      }

      // If API test endpoint fails, try the Engaxe API service
      console.log(`Falling back to engaxeApiService.getVideoDetails with videoId=${videoId}`);
      const videoDetails = await engaxeApiService.getVideoDetails(videoId);

      // Process the video details using our helper method
      return this.processVideoDetails(videoDetails, videoId);
    } catch (error: any) {
      console.error('Error fetching Engaxe metadata:', error?.message || error);

      // For development, provide fallback metadata instead of throwing an error
      console.warn('Providing fallback metadata for development');

      // Create mock video details
      const mockVideoDetails = {
        title: `Engaxe Video ${videoId}`,
        description: 'This is a sample description for an Engaxe video.',
        thumbnail: `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`,
        user_id: '456',
        owner: {
          username: 'Engaxe Creator',
          id: '456'
        },
        duration: 330, // 5:30 in seconds
        views: 1000,
        likes: 50,
        dislikes: 5,
        time: new Date().toISOString(),
        category_name: 'Education',
        tags: 'video,sample,engaxe',
        video_location: `https://engaxe.com/videos/${videoId}/stream`,
      };

      // Process the mock video details using our helper method
      return this.processVideoDetails(mockVideoDetails, videoId);
    }
  }
}
