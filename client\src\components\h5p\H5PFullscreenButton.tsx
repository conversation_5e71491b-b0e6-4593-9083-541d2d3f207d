import React, { useRef, useEffect } from 'react';
import { Maximize } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface H5PFullscreenButtonProps {
  targetSelector?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * A button component that toggles fullscreen mode for H5P content
 * 
 * @param targetSelector - CSS selector for the container element to make fullscreen (defaults to closest H5P container)
 * @param className - Additional CSS classes to apply to the button
 * @param size - Size of the button (sm, md, lg)
 */
const H5PFullscreenButton: React.FC<H5PFullscreenButtonProps> = ({
  targetSelector,
  className = '',
  size = 'sm'
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Function to toggle fullscreen
  const toggleFullscreen = () => {
    try {
      // Find the target element
      let targetElement: HTMLElement | null = null;
      
      if (targetSelector) {
        // If a selector is provided, use it to find the target
        targetElement = document.querySelector(targetSelector);
      } else if (buttonRef.current) {
        // Otherwise, try to find the closest H5P container
        let current = buttonRef.current.parentElement;
        
        while (current && current !== document.body) {
          if (
            current.classList.contains('h5p-content') || 
            current.classList.contains('h5p-iframe-wrapper') ||
            current.classList.contains('h5p-iframe') ||
            current.hasAttribute('data-content-id')
          ) {
            targetElement = current;
            break;
          }
          
          // If we find an iframe, use its parent as the container
          if (current.querySelector('iframe')) {
            targetElement = current;
            break;
          }
          
          current = current.parentElement;
        }
      }
      
      // If no target element found, use the document body
      if (!targetElement) {
        console.warn('No H5P container found, using document body');
        targetElement = document.body;
      }
      
      // Toggle fullscreen
      if (document.fullscreenElement) {
        document.exitFullscreen().catch(err => {
          console.error('Error exiting fullscreen:', err);
        });
      } else {
        const requestFullscreen = targetElement.requestFullscreen || 
                               (targetElement as any).mozRequestFullScreen ||
                               (targetElement as any).webkitRequestFullscreen ||
                               (targetElement as any).msRequestFullscreen;
        
        if (requestFullscreen) {
          requestFullscreen.call(targetElement).catch(err => {
            console.error('Error entering fullscreen:', err);
          });
        } else {
          console.error('Fullscreen API is not supported in this browser');
        }
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error);
    }
  };

  return (
    <Button
      ref={buttonRef}
      variant="ghost"
      size={size}
      className={`text-white p-1 h5p-fullscreen ${className}`}
      onClick={toggleFullscreen}
      aria-label="Fullscreen"
    >
      <Maximize className={size === 'sm' ? 'h-4 w-4' : size === 'md' ? 'h-5 w-5' : 'h-6 w-6'} />
    </Button>
  );
};

export default H5PFullscreenButton;
