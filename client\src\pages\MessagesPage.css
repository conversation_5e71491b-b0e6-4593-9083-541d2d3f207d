/* Theme variables */
:root {
  /* Dark theme colors (default) */
  --msg-bg-primary: #1a242f;
  --msg-bg-secondary: #1e2a38;
  --msg-bg-tertiary: #2c3e50;
  --msg-bg-active: #34495e;
  --msg-text-primary: #ecf0f1;
  --msg-text-secondary: #bdc3c7;
  --msg-text-muted: #7f8c8d;
  --msg-border-color: #2c3e50;
  --msg-accent-color: #3498db;
  --msg-accent-hover: #2980b9;
  --msg-sent-bubble: #3498db;
  --msg-received-bubble: #2c3e50;
  --msg-error-color: #e74c3c;
  --msg-online-indicator: #2ecc71;
  --msg-offline-indicator: #7f8c8d;
  --msg-unread-badge: #e74c3c;
  --msg-shadow-color: rgba(0, 0, 0, 0.2);
  --msg-shadow-color-hover: rgba(0, 0, 0, 0.3);
  --msg-input-bg: #1a242f;
  --msg-input-border: #2c3e50;
  --msg-input-focus-border: #3498db;
  --msg-sender-name: #e67e22;
  --msg-original-text: #ccc;
}

/* Light theme colors */
.light {
  --msg-bg-primary: #f5f8fa;
  --msg-bg-secondary: #ffffff;
  --msg-bg-tertiary: #e9ecef;
  --msg-bg-active: #d1d9e6;
  --msg-text-primary: #2c3e50;
  --msg-text-secondary: #5a6268;
  --msg-text-muted: #6c757d;
  --msg-border-color: #dee2e6;
  --msg-accent-color: #3498db;
  --msg-accent-hover: #2980b9;
  --msg-sent-bubble: #3498db;
  --msg-received-bubble: #e9ecef;
  --msg-error-color: #dc3545;
  --msg-online-indicator: #28a745;
  --msg-offline-indicator: #6c757d;
  --msg-unread-badge: #dc3545;
  --msg-shadow-color: rgba(0, 0, 0, 0.1);
  --msg-shadow-color-hover: rgba(0, 0, 0, 0.15);
  --msg-input-bg: #ffffff;
  --msg-input-border: #ced4da;
  --msg-input-focus-border: #3498db;
  --msg-sender-name: #e67e22;
  --msg-original-text: #6c757d;
}

/* Ensure message time is visible on sent messages in light theme */
.light .message.sent .message-time {
  color: #ffffff;
}

/* Dark theme colors (explicit) */
.dark {
  --msg-bg-primary: #1a242f;
  --msg-bg-secondary: #1e2a38;
  --msg-bg-tertiary: #2c3e50;
  --msg-bg-active: #34495e;
  --msg-text-primary: #ecf0f1;
  --msg-text-secondary: #bdc3c7;
  --msg-text-muted: #7f8c8d;
  --msg-border-color: #2c3e50;
  --msg-accent-color: #3498db;
  --msg-accent-hover: #2980b9;
  --msg-sent-bubble: #3498db;
  --msg-received-bubble: #2c3e50;
  --msg-error-color: #e74c3c;
  --msg-online-indicator: #2ecc71;
  --msg-offline-indicator: #7f8c8d;
  --msg-unread-badge: #e74c3c;
  --msg-shadow-color: rgba(0, 0, 0, 0.2);
  --msg-shadow-color-hover: rgba(0, 0, 0, 0.3);
  --msg-input-bg: #1a242f;
  --msg-input-border: #2c3e50;
  --msg-input-focus-border: #3498db;
  --msg-sender-name: #e67e22;
  --msg-original-text: #ccc;
}

/* Ensure message time is visible on sent messages in dark theme */
.dark .message.sent .message-time {
  color: #ffffff;
}

/* Alternative color schemes for sent messages */

/* Green gradient */
.green-theme .message.sent .message-content {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}
.green-theme .message.sent .message-content:after {
  border-left-color: #2ecc71;
}

/* Purple gradient */
.purple-theme .message.sent .message-content {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
}
.purple-theme .message.sent .message-content:after {
  border-left-color: #9b59b6;
}

/* Orange gradient */
.orange-theme .message.sent .message-content {
  background: linear-gradient(135deg, #d35400, #e67e22);
}
.orange-theme .message.sent .message-content:after {
  border-left-color: #e67e22;
}

/* Teal gradient */
.teal-theme .message.sent .message-content {
  background: linear-gradient(135deg, #16a085, #1abc9c);
}
.teal-theme .message.sent .message-content:after {
  border-left-color: #1abc9c;
}

/* Red gradient */
.red-theme .message.sent .message-content {
  background: linear-gradient(135deg, #c0392b, #e74c3c);
}
.red-theme .message.sent .message-content:after {
  border-left-color: #e74c3c;
}

.messages-page {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 60px);
  background-color: var(--msg-bg-primary);
  color: var(--msg-text-primary);
}

.back-button-container {
  padding: 15px;
  background-color: var(--msg-bg-secondary);
  border-bottom: 1px solid var(--msg-border-color);
}

.back-button {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background-color: var(--msg-accent-color);
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-weight: bold;
  transition: background-color 0.2s, transform 0.1s;
  box-shadow: 0 2px 5px var(--msg-shadow-color);
}

.back-button:hover {
  background-color: var(--msg-accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--msg-shadow-color-hover);
}

.back-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 3px var(--msg-shadow-color);
}

.back-button i {
  margin-right: 8px;
}

.messages-main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.messages-sidebar {
  width: 320px;
  border-right: 1px solid var(--msg-border-color);
  display: flex;
  flex-direction: column;
  background-color: var(--msg-bg-secondary);
}

.messages-header {
  padding: 15px;
  border-bottom: 1px solid var(--msg-border-color);
}

.messages-header h2 {
  margin: 0;
  font-size: 18px;
}

.search-container {
  padding: 15px;
  border-bottom: 1px solid var(--msg-border-color);
}

.conversations-list {
  flex-grow: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 15px;
  cursor: pointer;
  border-bottom: 1px solid var(--msg-border-color);
  transition: background-color 0.2s;
}

.conversation-item:hover {
  background-color: var(--msg-bg-tertiary);
}

.conversation-item.active {
  background-color: var(--msg-bg-active);
}

.conversation-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  margin-right: 15px;
  flex-shrink: 0;
}

.conversation-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid var(--msg-bg-secondary);
}

.status-indicator.online {
  background-color: var(--msg-online-indicator);
}

.status-indicator.offline {
  background-color: var(--msg-offline-indicator);
}

.conversation-info {
  flex-grow: 1;
  overflow: hidden;
}

.conversation-name {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-subject {
  font-size: 12px;
  color: var(--msg-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 10px;
}

.conversation-time {
  font-size: 12px;
  color: var(--msg-text-muted);
}

.unread-badge {
  background-color: var(--msg-unread-badge);
  color: white;
  font-size: 12px;
  font-weight: bold;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 5px;
}

.messages-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.conversation-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--msg-border-color);
  background-color: var(--msg-bg-secondary);
}

.conversation-status {
  font-size: 12px;
  color: var(--msg-text-muted);
}

.conversation-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.language-selector {
  position: relative;
}

.language-select {
  padding: 6px 10px;
  background-color: var(--msg-bg-tertiary);
  color: var(--msg-text-primary);
  border: 1px solid var(--msg-border-color);
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
  appearance: none;
  padding-right: 30px;
  transition: all 0.3s;
}

.language-select:hover {
  background-color: var(--msg-bg-active);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px var(--msg-shadow-color);
}

.language-select:focus {
  outline: none;
  border-color: var(--msg-accent-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

/* Add a glow effect when translating */
.language-select[data-translating="true"] {
  border-color: var(--msg-accent-color);
  box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.5);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(52, 152, 219, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

.language-selector::after {
  content: '▼';
  font-size: 10px;
  color: var(--msg-text-muted);
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.translation-indicator {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(52, 152, 219, 0.1);
  border-left: 3px solid var(--msg-accent-color);
  margin-bottom: 15px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.translation-indicator:hover {
  background-color: rgba(52, 152, 219, 0.2);
}

.translation-icon {
  font-size: 16px;
  margin-right: 8px;
}

.translation-text {
  font-size: 13px;
  color: var(--msg-text-primary);
}

.translation-loading {
  margin-left: auto;
  font-size: 12px;
  color: var(--msg-text-muted);
  font-style: italic;
  position: relative;
  padding-right: 20px;
}

.translation-loading:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  width: 16px;
  height: 16px;
  margin-top: -8px;
  border-radius: 50%;
  border: 2px solid rgba(52, 152, 219, 0.3);
  border-top-color: var(--msg-accent-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.messages-container {
  flex-grow: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background-color: var(--msg-bg-primary);
}

/* Dark theme pattern */
.dark .messages-container {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%232c3e50' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
}

/* Light theme pattern */
.light .messages-container {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23dee2e6' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E");
}

.messages-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 10px 0;
}

.message {
  display: flex;
  margin-bottom: 15px;
  max-width: 70%;
  position: relative;
}

.message.sent {
  align-self: flex-end;
}

.message.received {
  align-self: flex-start;
}

/* Avatar styles removed */

.message-content {
  background-color: var(--msg-received-bubble);
  padding: 10px 15px;
  border-radius: 15px;
  position: relative;
  box-shadow: 0 1px 2px var(--msg-shadow-color);
  width: 100%;
}

.message.sent .message-content {
  background-color: var(--msg-sent-bubble);
  border-radius: 15px;
  border-top-right-radius: 5px;
}

.message.received .message-content {
  border-radius: 15px;
  border-top-left-radius: 5px;
}

/* Add chat bubble tails */
.message.sent .message-content:after {
  content: '';
  position: absolute;
  top: 0;
  right: -10px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-left-color: var(--msg-sent-bubble);
  border-right: 0;
  border-top: 0;
  margin-top: 0;
}

.message.received .message-content:after {
  content: '';
  position: absolute;
  top: 0;
  left: -10px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-right-color: var(--msg-received-bubble);
  border-left: 0;
  border-top: 0;
  margin-top: 0;
}

.message-sender {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 5px;
  color: var(--msg-sender-name);
}

.message-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-original {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  margin-bottom: 4px;
  border-left: 2px solid var(--msg-accent-color);
}

.message-language-label {
  font-size: 11px;
  font-weight: bold;
  color: var(--msg-text-muted);
  margin-bottom: 2px;
}

.message-text {
  word-wrap: break-word;
  font-size: 14px;
  line-height: 1.4;
  position: relative;
}

.message-text.original {
  color: var(--msg-original-text);
  font-style: italic;
}

.message-text[data-translated="true"] {
  padding-left: 18px;
}

.message-text[data-translated="true"]::before {
  content: "🌐";
  position: absolute;
  left: 0;
  top: 2px;
  font-size: 12px;
  opacity: 0.7;
}

/* Style for the translating indicator */
.translating-indicator {
  display: inline-block;
  color: var(--msg-accent-color);
  font-style: italic;
  position: relative;
  padding-left: 20px;
  animation: pulse-text 1.5s infinite;
}

.translating-indicator::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 14px;
  height: 14px;
  margin-top: -7px;
  border-radius: 50%;
  border: 2px solid rgba(52, 152, 219, 0.3);
  border-top-color: var(--msg-accent-color);
  animation: spin 1s linear infinite;
}

@keyframes pulse-text {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}

.message-time {
  font-size: 11px;
  color: var(--msg-text-primary);
  opacity: 0.8;
}

.message-translate-options {
  display: flex;
  align-items: center;
}

.message-translate-button {
  font-size: 10px;
  color: var(--msg-text-muted);
  cursor: pointer;
  padding: 2px 5px;
  border-radius: 3px;
  transition: all 0.2s;
}

.message-translate-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--msg-text-primary);
}

.message-translate-dropdown {
  position: absolute;
  background-color: var(--msg-bg-secondary);
  border: 1px solid var(--msg-border-color);
  border-radius: 5px;
  box-shadow: 0 4px 12px var(--msg-shadow-color);
  z-index: 1000;
  width: 180px;
  overflow: hidden;
}

.message-translate-dropdown-header {
  padding: 8px 12px;
  font-size: 12px;
  font-weight: bold;
  border-bottom: 1px solid var(--msg-border-color);
  color: var(--msg-text-primary);
}

.message-translate-dropdown-option {
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--msg-text-primary);
}

.message-translate-dropdown-option:hover {
  background-color: var(--msg-bg-tertiary);
}

.typing-indicator {
  align-self: flex-start;
  font-size: 12px;
  color: var(--msg-text-muted);
  font-style: italic;
  margin-bottom: 10px;
}

.message-input-container {
  display: flex;
  padding: 15px;
  border-top: 1px solid var(--msg-border-color);
  background-color: var(--msg-bg-secondary);
}

.message-input {
  flex-grow: 1;
  padding: 10px 15px;
  border: 1px solid var(--msg-input-border);
  border-radius: 5px;
  background-color: var(--msg-input-bg);
  color: var(--msg-text-primary);
  font-size: 14px;
  margin-right: 10px;
}

.message-input:focus {
  outline: none;
  border-color: var(--msg-input-focus-border);
}

.send-button {
  padding: 10px 20px;
  background-color: var(--msg-accent-color);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: var(--msg-accent-hover);
}

.no-conversation-selected {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-conversation-message {
  text-align: center;
  color: var(--msg-text-muted);
}

.no-conversation-message h3 {
  margin-bottom: 10px;
}

.loading, .error, .no-conversations, .no-messages {
  padding: 20px;
  text-align: center;
  color: var(--msg-text-muted);
}

.error {
  color: var(--msg-error-color);
}
