{"metadata": {"totalVideos": 1, "lastUpdated": "2025-05-31T11:16:58.837Z", "version": "1.0.0"}, "videos": [{"id": "sample_test_video", "title": "Sample Test Video - JSON Storage Demo", "description": "This is a sample video created to demonstrate the automatic JSON storage functionality in LawEngaxe platform. It shows how videos are automatically stored in JSON format for fast search and retrieval.", "url": "XLcMq2", "thumbnailUrl": "https://via.placeholder.com/480x360/333333/FFFFFF?text=Sample+Video", "duration": 180, "userId": "sample_user", "channelId": "sample_channel", "visibility": "public", "tags": ["sample", "test", "json-storage", "demo", "lawengaxe"], "category": "Technology", "contentRating": "general", "processingStatus": "ready", "stats": {"views": 42, "likes": 5, "dislikes": 0, "comments": 2, "shares": 1, "playlistAdds": 0, "averageWatchTime": 120, "retentionRate": 0.8}, "file": {"originalName": "sample-video.mp4", "size": 15728640, "mimeType": "video/mp4"}, "languages": [{"code": "en", "name": "English", "flag": "🇺🇸", "url": "XLcMq2", "isDefault": true}], "source": {"type": "embed", "originalUrl": "XLcMq2", "platform": "engaxe"}, "createdAt": "2025-05-31T11:16:58.836Z", "updatedAt": "2025-05-31T11:16:58.837Z", "searchKeywords": ["sample", "test", "json", "storage", "demo", "lawengaxe", "technology", "video"], "watchUrl": "http://localhost:5173/watch?id=sample_test_video"}]}