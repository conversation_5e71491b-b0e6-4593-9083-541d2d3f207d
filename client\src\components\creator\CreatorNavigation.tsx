import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/context/LanguageContext';
import { Home, LayoutDashboard, Video, MessageSquare } from 'lucide-react';

interface CreatorNavigationProps {
  activeTab: string;
}

export default function CreatorNavigation({ activeTab }: CreatorNavigationProps) {
  const { t } = useLanguage();

  const tabs = [
    { id: 'home', label: t('creator.home'), icon: <Home className="h-4 w-4 mr-2" />, path: '/' },
    { id: 'dashboard', label: t('creator.dashboard'), icon: <LayoutDashboard className="h-4 w-4 mr-2" /> },
    { id: 'videos', label: t('creator.your_videos'), icon: <Video className="h-4 w-4 mr-2" /> },
    { id: 'comments', label: t('creator.comments'), icon: <MessageSquare className="h-4 w-4 mr-2" /> },
    { id: 'series', label: t('creator.series'), icon: null }
  ];

  return (
    <div className="flex overflow-x-auto pb-2 mb-6">
      {tabs.map((tab) => (
        <Link
          key={tab.id}
          to={tab.path || `/creator-studio?tab=${tab.id}`}
          className={cn(
            "flex items-center px-4 py-2 rounded-full text-sm font-medium mr-2 whitespace-nowrap",
            activeTab === tab.id
              ? "bg-lingstream-accent text-white"
              : "bg-lingstream-card hover:bg-lingstream-card/80"
          )}
        >
          {tab.icon}
          {tab.label}
        </Link>
      ))}
    </div>
  );
}
