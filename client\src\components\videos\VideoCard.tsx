import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { FallbackAvatar } from '@/components/ui/fallback-avatar';
import { CheckCircle, Clock } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { formatNumber, formatDuration } from '@/lib/utils';

interface VideoCardProps {
  video: {
    id: string;
    title: string;
    thumbnailUrl: string;
    duration: number;
    createdAt: string;
    stats: {
      views: number;
    };
    channel?: {
      id: string;
      name: string;
      displayName: string;
      avatar?: string;
      isVerified: boolean;
    };
  };
  hideChannelInfo?: boolean;
}

const VideoCard: React.FC<VideoCardProps> = ({ video, hideChannelInfo = false }) => {
  const { id, title, thumbnailUrl, duration, createdAt, stats, channel } = video;

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow border-0">
      <Link to={`/video/${id}`} className="block">
        <div className="relative">
          <img
            src={thumbnailUrl || '/placeholder.svg'}
            alt={title}
            className="w-full aspect-video object-cover"
            onError={(e) => {
              console.warn(`Failed to load thumbnail for video ${id}`, e);
              e.currentTarget.src = '/placeholder.svg';
            }}
          />
          {duration > 0 && (
            <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-1 py-0.5 rounded">
              {formatDuration(duration)}
            </div>
          )}
        </div>
      </Link>

      <CardContent className="p-3">
        <div className="flex gap-3">
          {!hideChannelInfo && channel && (
            <Link to={`/channel/${channel.name}`} className="flex-shrink-0 mt-1">
              <FallbackAvatar name={channel.displayName} size="sm" className="h-9 w-9" />
            </Link>
          )}

          <div className="flex-1 min-w-0">
            <Link to={`/video/${id}`} className="block">
              <h3 className="font-medium text-sm line-clamp-2 mb-1">{title}</h3>
            </Link>

            {!hideChannelInfo && channel && (
              <Link to={`/channel/${channel.name}`} className="flex items-center text-xs text-muted-foreground hover:text-foreground">
                <span className="truncate">{channel.displayName}</span>
                {channel.isVerified && (
                  <CheckCircle className="h-3 w-3 text-blue-500 ml-1 flex-shrink-0" />
                )}
              </Link>
            )}

            <div className="flex items-center text-xs text-muted-foreground mt-1">
              <span>{formatNumber(stats.views)} views</span>
              <span className="mx-1">•</span>
              <span>{formatDistanceToNow(new Date(createdAt), { addSuffix: true })}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VideoCard;
