/**
 * Test the search API to verify it's working
 */

async function testSearchAPI() {
  console.log('🧪 Testing Search API');
  console.log('====================');
  
  try {
    // Test JSON search API
    console.log('\n1. Testing JSON Search API for "game"');
    const jsonResponse = await fetch('http://localhost:3001/api/v1/videos/json/videos/search?q=game');
    console.log(`   Status: ${jsonResponse.status}`);
    
    if (jsonResponse.ok) {
      const jsonData = await jsonResponse.json();
      console.log(`   Found ${jsonData.data?.length || 0} videos in JSON storage`);
      
      if (jsonData.data && jsonData.data.length > 0) {
        jsonData.data.forEach((video, index) => {
          console.log(`   ${index + 1}. ${video.title}`);
          console.log(`      🔗 ${video.watchUrl}`);
        });
      }
    } else {
      console.log(`   ❌ JSON API failed: ${jsonResponse.statusText}`);
    }
    
    // Test database search API
    console.log('\n2. Testing Database Search API for "game"');
    const dbResponse = await fetch('http://localhost:3001/api/v1/videos?search=game&limit=5');
    console.log(`   Status: ${dbResponse.status}`);
    
    if (dbResponse.ok) {
      const dbData = await dbResponse.json();
      const videos = dbData.data?.videos || dbData.videos || [];
      console.log(`   Found ${videos.length} videos in database`);
      
      if (videos.length > 0) {
        videos.forEach((video, index) => {
          console.log(`   ${index + 1}. ${video.title}`);
          console.log(`      🔗 http://localhost:5173/watch?id=${video.url || video.id}`);
        });
      }
    } else {
      console.log(`   ❌ Database API failed: ${dbResponse.statusText}`);
    }
    
    // Test "game theory" search
    console.log('\n3. Testing "game theory" search');
    const gameTheoryResponse = await fetch('http://localhost:3001/api/v1/videos/json/videos/search?q=game%20theory');
    console.log(`   Status: ${gameTheoryResponse.status}`);
    
    if (gameTheoryResponse.ok) {
      const gameTheoryData = await gameTheoryResponse.json();
      console.log(`   Found ${gameTheoryData.data?.length || 0} videos for "game theory"`);
      
      if (gameTheoryData.data && gameTheoryData.data.length > 0) {
        gameTheoryData.data.forEach((video, index) => {
          console.log(`   ${index + 1}. ${video.title}`);
          console.log(`      🔗 ${video.watchUrl}`);
          console.log(`      📂 Category: ${video.category}`);
          console.log(`      👀 Views: ${video.stats?.views || 0}`);
        });
      }
    }
    
    console.log('\n✅ API Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSearchAPI();
