
import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import Navbar from '@/components/layout/Navbar';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, Languages, Video, Users } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function SubscriptionPage() {
  const { currentUser } = useAuth();
  const [subscriptionTier, setSubscriptionTier] = useState<'free' | 'premium' | 'creator'>('free');
  
  const plans = [
    {
      name: 'Free Plan',
      price: '$0',
      features: [
        'Access to free videos',
        'Limited language switching',
        'Standard quality'
      ],
      tier: 'free',
      cta: 'Current Plan'
    },
    {
      name: 'Premium Learner',
      price: '$9.99',
      period: '/month',
      features: [
        'Unlimited language switching',
        'HD quality videos',
        'No ads',
        'Download videos for offline learning',
        'Priority leaderboard tracking'
      ],
      tier: 'premium',
      cta: 'Upgrade'
    },
    {
      name: 'Creator Pro',
      price: '$19.99',
      period: '/month',
      features: [
        'All Premium Learner features',
        'Upload unlimited videos',
        'Connect multiple language versions',
        'Analytics and insights',
        'Monetization opportunities',
        'Priority support'
      ],
      tier: 'creator',
      cta: 'Become a Creator'
    }
  ];

  if (!currentUser) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container py-8 flex items-center justify-center">
          <Card>
            <CardHeader>
              <CardTitle>Please Sign In</CardTitle>
              <CardDescription>
                You need to be signed in to manage your subscription
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/signin">
                <Button>Sign In</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const handleSubscribe = (tier: 'free' | 'premium' | 'creator') => {
    // Here you would integrate with a payment provider like Stripe
    console.log(`Subscribing to ${tier} plan`);
    
    // Mock subscription change
    setSubscriptionTier(tier);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="container py-8">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold mb-2">Choose Your LingStream Plan</h1>
            <p className="text-lingstream-muted">
              Get access to multilingual content that helps you learn faster
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan) => (
              <Card 
                key={plan.tier}
                className={plan.tier === subscriptionTier ? 'border-lingstream-accent' : ''}
              >
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {plan.name}
                    {plan.tier === 'premium' && <Languages className="h-5 w-5 text-lingstream-accent" />}
                    {plan.tier === 'creator' && <Video className="h-5 w-5 text-lingstream-accent" />}
                  </CardTitle>
                  <CardDescription>
                    <div className="flex items-baseline">
                      <span className="text-2xl font-bold">{plan.price}</span>
                      {plan.period && <span className="text-sm text-lingstream-muted">{plan.period}</span>}
                    </div>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <Check className="h-4 w-4 mr-2 text-green-500 shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full"
                    variant={plan.tier === subscriptionTier ? 'secondary' : 'default'}
                    onClick={() => handleSubscribe(plan.tier as 'free' | 'premium' | 'creator')}
                    disabled={plan.tier === subscriptionTier}
                  >
                    {plan.tier === subscriptionTier ? 'Current Plan' : plan.cta}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
          
          <div className="mt-12 text-center">
            <p className="text-sm text-lingstream-muted">
              All plans renew automatically. Cancel anytime from your profile page.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
