import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Home, ChevronRight, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';

export default function EditCustomFieldPage() {
  const { fieldId } = useParams();
  const navigate = useNavigate();

  // Sample field data (in a real app, you would fetch this from an API)
  const [field, setField] = useState({
    id: fieldId,
    name: 'Gemini API Key',
    type: 'Text box',
    length: 40,
    placement: 'Profile settings',
    description: 'Gemini API Key allows you to use AI to generate interactions for your videos',
    required: true,
    showOnRegistration: false,
    showOnUserProfile: false,
    active: true
  });

  // Form state
  const [fieldName, setFieldName] = useState(field.name);
  const [fieldType, setFieldType] = useState(field.type);
  const [fieldLength, setFieldLength] = useState(field.length.toString());
  const [fieldDescription, setFieldDescription] = useState(field.description);
  const [fieldPlacement, setFieldPlacement] = useState(field.placement);
  const [isRequired, setIsRequired] = useState(field.required);
  const [showOnRegistration, setShowOnRegistration] = useState(field.showOnRegistration);
  const [showOnUserProfile, setShowOnUserProfile] = useState(field.showOnUserProfile);
  const [isActive, setIsActive] = useState(field.active);

  const handleSave = () => {
    const updatedField = {
      ...field,
      name: fieldName,
      type: fieldType,
      length: parseInt(fieldLength),
      description: fieldDescription,
      placement: fieldPlacement,
      required: isRequired,
      showOnRegistration,
      showOnUserProfile,
      active: isActive
    };

    console.log('Saving field:', updatedField);
    navigate('/admin/users/custom-fields');
  };

  return (
    <div className="min-h-screen flex dark:bg-gray-900">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <div className="p-6 flex-1">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
            <Link to="/admin" className="hover:text-primary flex items-center gap-1">
              <Home size={16} />
              Admin Panel
            </Link>
            <ChevronRight size={16} />
            <Link to="/admin/users" className="hover:text-primary">Users</Link>
            <ChevronRight size={16} />
            <Link to="/admin/users/custom-fields" className="hover:text-primary">
              Manage Custom Profile Fields
            </Link>
            <ChevronRight size={16} />
            <span>Edit Field</span>
          </div>

          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Edit Custom Profile Field</h1>
            <Button onClick={handleSave}>Save Changes</Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Field Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="fieldName">Field Name</Label>
                  <Input
                    id="fieldName"
                    value={fieldName}
                    onChange={(e) => setFieldName(e.target.value)}
                    placeholder="Enter field name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fieldType">Field Type</Label>
                  <Select value={fieldType} onValueChange={setFieldType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select field type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Text box">Text box</SelectItem>
                      <SelectItem value="Text area">Text area</SelectItem>
                      <SelectItem value="Select box">Select box</SelectItem>
                      <SelectItem value="Radio buttons">Radio buttons</SelectItem>
                      <SelectItem value="Checkboxes">Checkboxes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fieldLength">Field Length</Label>
                  <Input
                    id="fieldLength"
                    type="number"
                    value={fieldLength}
                    onChange={(e) => setFieldLength(e.target.value)}
                    placeholder="Enter field length"
                  />
                  <p className="text-sm text-muted-foreground">Default: 32, Maximum: 1000</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="placement">Placement</Label>
                  <Select value={fieldPlacement} onValueChange={setFieldPlacement}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select placement" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Profile settings">Profile settings</SelectItem>
                      <SelectItem value="General">General</SelectItem>
                      <SelectItem value="Social Links">Social Links</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={fieldDescription}
                  onChange={(e) => setFieldDescription(e.target.value)}
                  placeholder="Enter field description"
                />
                <p className="text-sm text-muted-foreground">The description will show under the field</p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="required"
                    checked={isRequired}
                    onCheckedChange={(checked) => setIsRequired(checked as boolean)}
                  />
                  <Label htmlFor="required">Required Field</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="registration"
                    checked={showOnRegistration}
                    onCheckedChange={(checked) => setShowOnRegistration(checked as boolean)}
                  />
                  <Label htmlFor="registration">Show on Registration Page</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="profile"
                    checked={showOnUserProfile}
                    onCheckedChange={(checked) => setShowOnUserProfile(checked as boolean)}
                  />
                  <Label htmlFor="profile">Show on User Profile</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="active"
                    checked={isActive}
                    onCheckedChange={(checked) => setIsActive(checked as boolean)}
                  />
                  <Label htmlFor="active">Active</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
