const mongoose = require('mongoose');
const puppeteer = require('puppeteer');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/lawengaxe';

// Define video schema
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  tags: [String],
  category: String,
  contentRating: String,
  processingStatus: String,
  stats: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    playlistAdds: { type: Number, default: 0 },
    averageWatchTime: { type: Number, default: 0 },
    retentionRate: { type: Number, default: 0 }
  },
  file: {
    originalName: String,
    size: Number,
    mimeType: String
  },
  languages: [{
    code: String,
    name: String,
    flag: String,
    isDefault: Boolean,
    url: String
  }],
  source: {
    type: String,
    originalUrl: String,
    platform: String,
    externalId: String
  },
  createdAt: Date,
  updatedAt: Date,
  deletedAt: Date
});

const Video = mongoose.model('Video', VideoSchema);

/**
 * Parse duration string in format "MM:SS" or "HH:MM:SS" to seconds
 */
function parseDurationToSeconds(durationStr) {
  if (!durationStr || typeof durationStr !== 'string') {
    return 0;
  }

  const cleanDuration = durationStr.trim();
  const parts = cleanDuration.split(':');
  
  if (parts.length === 2) {
    // MM:SS format
    const minutes = parseInt(parts[0], 10) || 0;
    const seconds = parseInt(parts[1], 10) || 0;
    return (minutes * 60) + seconds;
  } else if (parts.length === 3) {
    // HH:MM:SS format
    const hours = parseInt(parts[0], 10) || 0;
    const minutes = parseInt(parts[1], 10) || 0;
    const seconds = parseInt(parts[2], 10) || 0;
    return (hours * 3600) + (minutes * 60) + seconds;
  }
  
  return 0;
}

/**
 * Extract duration from Engaxe video page
 */
async function extractDurationFromEngaxePage(videoId, browser) {
  let page;
  try {
    console.log(`  🔍 Extracting duration for video ${videoId}...`);
    
    page = await browser.newPage();
    page.setDefaultTimeout(30000);
    
    // Navigate to the Engaxe video page
    const url = `https://engaxe.com/v/${videoId}`;
    console.log(`  📡 Loading: ${url}`);
    
    await page.goto(url, { waitUntil: 'networkidle2' });
    
    // Wait for the page to load
    await page.waitForTimeout(5000);
    
    // Try multiple selectors to find the duration
    const durationSelectors = [
      'span.human-time[aria-hidden="true"]',
      '.duration',
      '.video-duration',
      '.time-display',
      '[data-duration]',
      '.player-time',
      '.video-time',
      '.duration-display'
    ];
    
    let duration = 0;
    let foundSelector = null;
    
    for (const selector of durationSelectors) {
      try {
        const elements = await page.$$(selector);
        for (const element of elements) {
          const durationText = await page.evaluate(el => {
            return el.textContent || el.getAttribute('data-duration') || el.getAttribute('title');
          }, element);
          
          if (durationText) {
            const parsedDuration = parseDurationToSeconds(durationText);
            if (parsedDuration > 0) {
              duration = parsedDuration;
              foundSelector = selector;
              console.log(`  ✅ Found duration: ${durationText} (${duration}s) using selector: ${selector}`);
              break;
            }
          }
        }
        if (duration > 0) break;
      } catch (error) {
        // Continue to next selector
      }
    }
    
    if (duration === 0) {
      console.log(`  ❌ Could not extract duration for video ${videoId}`);
    }
    
    return duration;
    
  } catch (error) {
    console.error(`  ❌ Error extracting duration for video ${videoId}:`, error.message);
    return 0;
  } finally {
    if (page) {
      await page.close();
    }
  }
}

/**
 * Fix video durations by extracting them from actual video sources
 */
async function fixVideoDurations() {
  let browser;
  
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    console.log('🚀 Starting Puppeteer...');
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox', 
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });
    console.log('✅ Puppeteer started');
    
    // Get all videos (prioritize those with incorrect durations)
    const videos = await Video.find({
      deletedAt: null,
      processingStatus: 'ready'
    }).select('id title url duration').limit(20); // Process 20 videos at a time
    
    console.log(`📊 Found ${videos.length} videos to process`);
    
    if (videos.length === 0) {
      console.log('No videos found to process');
      return;
    }
    
    console.log('\n🔧 Fixing Video Durations:');
    console.log('=' .repeat(80));
    
    let successCount = 0;
    let failureCount = 0;
    let skippedCount = 0;
    
    for (let i = 0; i < videos.length; i++) {
      const video = videos[i];
      console.log(`\n${i + 1}/${videos.length}: Processing video ${video.id}`);
      console.log(`  📝 Title: ${video.title}`);
      console.log(`  🔗 URL: ${video.url}`);
      console.log(`  ⏱️  Current duration: ${video.duration || 0}s`);
      
      // Skip if video already has a reasonable duration (> 30 seconds)
      if (video.duration && video.duration > 30) {
        console.log(`  ⏭️  Skipping - already has valid duration: ${video.duration}s`);
        skippedCount++;
        continue;
      }
      
      // Extract duration from Engaxe page
      const extractedDuration = await extractDurationFromEngaxePage(video.url || video.id, browser);
      
      if (extractedDuration > 0) {
        try {
          // Update the video duration in database
          const updateResult = await Video.updateOne(
            { _id: video._id },
            { 
              $set: { 
                duration: extractedDuration,
                updatedAt: new Date()
              } 
            }
          );
          
          if (updateResult.modifiedCount > 0) {
            console.log(`  ✅ Updated video ${video.id} duration: ${video.duration || 0}s → ${extractedDuration}s`);
            successCount++;
          } else {
            console.log(`  ⚠️  No changes made to video ${video.id}`);
            failureCount++;
          }
        } catch (updateError) {
          console.error(`  ❌ Failed to update video ${video.id} in database:`, updateError.message);
          failureCount++;
        }
      } else {
        console.log(`  ❌ Could not extract duration for video ${video.id}`);
        failureCount++;
      }
      
      // Add delay between requests to be respectful
      if (i < videos.length - 1) {
        console.log(`  ⏳ Waiting 3 seconds before next video...`);
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }
    
    console.log('\n📊 Final Summary:');
    console.log('=' .repeat(50));
    console.log(`✅ Successfully updated: ${successCount} videos`);
    console.log(`⏭️  Skipped (already valid): ${skippedCount} videos`);
    console.log(`❌ Failed to update: ${failureCount} videos`);
    console.log(`📊 Total processed: ${videos.length} videos`);
    
    // Show updated statistics
    console.log('\n📈 Updated Database Statistics:');
    const updatedVideos = await Video.find({ deletedAt: null }).select('duration');
    const zeroCount = updatedVideos.filter(v => !v.duration || v.duration === 0).length;
    const validCount = updatedVideos.filter(v => v.duration && v.duration > 0).length;
    
    console.log(`   Total videos: ${updatedVideos.length}`);
    console.log(`   Videos with valid duration: ${validCount}`);
    console.log(`   Videos with zero duration: ${zeroCount}`);
    console.log(`   Success rate: ${((validCount / updatedVideos.length) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('❌ Error fixing video durations:', error);
  } finally {
    if (browser) {
      await browser.close();
      console.log('🔒 Puppeteer closed');
    }
    
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('🔒 MongoDB disconnected');
    }
  }
}

// Run the script
if (require.main === module) {
  fixVideoDurations()
    .then(() => {
      console.log('✅ Duration fix script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Duration fix script failed:', error);
      process.exit(1);
    });
}

module.exports = { fixVideoDurations, extractDurationFromEngaxePage, parseDurationToSeconds };
