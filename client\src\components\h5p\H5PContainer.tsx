import React, { useRef, useEffect, useState } from 'react';
import H5PFullscreenButton from './H5PFullscreenButton';
import H5PMinimizeButton from './H5PMinimizeButton';

interface H5PContainerProps {
  src: string;
  width?: string | number;
  height?: string | number;
  className?: string;
  showFullscreenButton?: boolean;
  fullscreenButtonPosition?: 'top-right' | 'bottom-right' | 'custom';
  fullscreenButtonClassName?: string;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

/**
 * A container component for embedding H5P content with fullscreen support
 *
 * @param src - URL of the H5P content
 * @param width - Width of the container
 * @param height - Height of the container
 * @param className - Additional CSS classes to apply to the container
 * @param showFullscreenButton - Whether to show a custom fullscreen button
 * @param fullscreenButtonPosition - Position of the fullscreen button
 * @param fullscreenButtonClassName - Additional CSS classes for the fullscreen button
 * @param onLoad - Callback function when the content is loaded
 * @param onError - Callback function when an error occurs
 */
const H5PContainer: React.FC<H5PContainerProps> = ({
  src,
  width = '100%',
  height = '400px',
  className = '',
  showFullscreenButton = true,
  fullscreenButtonPosition = 'top-right',
  fullscreenButtonClassName = '',
  onLoad,
  onError
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    try {
      // Clear the container
      containerRef.current.innerHTML = '';
      setIsLoading(true);
      setError(null);

      // Create the iframe
      const iframe = document.createElement('iframe');
      iframe.src = src;
      iframe.width = '100%';
      iframe.height = '100%';
      iframe.style.border = 'none';
      iframe.allowFullscreen = true;
      iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share';
      iframe.className = 'h5p-iframe';

      // Add event listeners
      iframe.onload = () => {
        setIsLoading(false);
        if (onLoad) onLoad();
      };

      iframe.onerror = (e) => {
        setIsLoading(false);
        const error = new Error('Failed to load H5P content');
        setError(error);
        if (onError) onError(error);
      };

      // Add the iframe to the container
      containerRef.current.appendChild(iframe);
      iframeRef.current = iframe;

      // Set a timeout to handle cases where the iframe doesn't trigger onload
      const timeout = setTimeout(() => {
        if (isLoading) {
          setIsLoading(false);
        }
      }, 10000); // 10 seconds timeout

      return () => {
        clearTimeout(timeout);
        if (iframeRef.current) {
          iframeRef.current.onload = null;
          iframeRef.current.onerror = null;
        }
      };
    } catch (err) {
      setIsLoading(false);
      const error = err instanceof Error ? err : new Error('Unknown error loading H5P content');
      setError(error);
      if (onError) onError(error);
    }
  }, [src, onLoad, onError]);

  // Determine fullscreen button position classes
  const getFullscreenButtonPositionClass = () => {
    switch (fullscreenButtonPosition) {
      case 'top-right':
        return 'absolute top-2 right-2';
      case 'bottom-right':
        return 'absolute bottom-2 right-2';
      case 'custom':
        return '';
      default:
        return 'absolute top-2 right-2';
    }
  };

  return (
    <div
      className={`h5p-container relative ${className}`}
      style={{ width, height }}
    >
      <div
        ref={containerRef}
        className="h5p-content w-full h-full"
      />

      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-10">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-white"></div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-500 bg-opacity-20 z-10">
          <div className="bg-white p-4 rounded shadow-lg max-w-md">
            <h3 className="text-lg font-bold text-red-600 mb-2">Error Loading Content</h3>
            <p className="text-gray-800">{error.message}</p>
          </div>
        </div>
      )}

      {showFullscreenButton && !isLoading && !error && (
        <div className={getFullscreenButtonPositionClass()}>
          <H5PFullscreenButton
            className={`bg-black bg-opacity-50 hover:bg-opacity-70 ${fullscreenButtonClassName}`}
          />
        </div>
      )}
    </div>
  );
};

export default H5PContainer;
