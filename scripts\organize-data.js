#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// Data organization configuration
const DATA_ORGANIZATION = {
  // Files to move to server/data/
  serverData: [
    'response.json',
    'response_ald.json',
    'ald_response.json',
    'last_ald_response.json',
    'last_nmt_response.json',
    'last_openai_response.json',
    'last_response_nmt_response.json',
    'last_tts_response.json',
    'raw_language_predictions.json',
    'detected_language.txt',
    'ald_language_details.json'
  ],
  
  // Files to move to server/temp/
  tempFiles: [
    'temp_audio/',
    'recording_*.wav'
  ],
  
  // Files to move to docs/
  documentation: [
    'ENV_SETUP_GUIDE.md',
    'INTEGRATION_GUIDE.md',
    'PYTHON_SETUP_GUIDE.md',
    'INSTALLATION_GUIDE.md',
    'add-video-with-multiple-languages.md',
    'language-dropdown-test-guide.md',
    'test-existing-videos-language-dropdown.md',
    'test-language-dropdown.md',
    'test-language-switching-updated.md',
    'test-language-switching.md'
  ],
  
  // Files to keep in root
  keepInRoot: [
    'README.md',
    'package.json',
    'package-lock.json',
    '.gitignore',
    'restart-all.bat',
    'restart-server.bat',
    'start-client.bat',
    'start-server.bat',
    'setup-env.bat'
  ],
  
  // Directories to create
  createDirectories: [
    'server/data',
    'server/temp',
    'docs',
    'scripts'
  ]
};

function createDirectories() {
  console.log(colorize('\n📁 Creating directory structure...', 'blue'));
  
  DATA_ORGANIZATION.createDirectories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(colorize(`✓ Created: ${dir}`, 'green'));
    } else {
      console.log(colorize(`- Exists: ${dir}`, 'yellow'));
    }
  });
}

function moveFiles() {
  console.log(colorize('\n📦 Organizing data files...', 'blue'));
  
  // Move server data files
  DATA_ORGANIZATION.serverData.forEach(file => {
    if (fs.existsSync(file)) {
      const destination = path.join('server/data', path.basename(file));
      try {
        fs.renameSync(file, destination);
        console.log(colorize(`✓ Moved: ${file} → ${destination}`, 'green'));
      } catch (error) {
        console.log(colorize(`✗ Failed to move: ${file} (${error.message})`, 'red'));
      }
    }
  });
  
  // Move documentation files
  DATA_ORGANIZATION.documentation.forEach(file => {
    if (fs.existsSync(file)) {
      const destination = path.join('docs', path.basename(file));
      try {
        fs.renameSync(file, destination);
        console.log(colorize(`✓ Moved: ${file} → ${destination}`, 'green'));
      } catch (error) {
        console.log(colorize(`✗ Failed to move: ${file} (${error.message})`, 'red'));
      }
    }
  });
}

function cleanupRootDirectory() {
  console.log(colorize('\n🧹 Cleaning up root directory...', 'blue'));
  
  const rootFiles = fs.readdirSync('.');
  const filesToRemove = rootFiles.filter(file => {
    // Skip directories
    if (fs.statSync(file).isDirectory()) return false;
    
    // Skip files we want to keep
    if (DATA_ORGANIZATION.keepInRoot.includes(file)) return false;
    
    // Skip hidden files
    if (file.startsWith('.')) return false;
    
    return true;
  });
  
  if (filesToRemove.length === 0) {
    console.log(colorize('✓ Root directory is already clean', 'green'));
    return;
  }
  
  console.log(colorize(`Found ${filesToRemove.length} files to review:`, 'yellow'));
  filesToRemove.forEach(file => {
    console.log(colorize(`  - ${file}`, 'yellow'));
  });
  
  // For safety, just log what would be removed
  console.log(colorize('\nℹ️  These files can be manually reviewed and removed if not needed', 'cyan'));
}

function createDataIndexFiles() {
  console.log(colorize('\n📝 Creating index files...', 'blue'));
  
  // Create server/data/README.md
  const serverDataReadme = `# Server Data Files

This directory contains data files used by the server:

## API Response Files
- \`response.json\` - Main Bhashini API configuration
- \`response_ald.json\` - Audio Language Detection configuration
- \`ald_response.json\` - Latest ALD API response
- \`last_*_response.json\` - Cached API responses for debugging

## Language Detection Files
- \`detected_language.txt\` - Last detected language information
- \`ald_language_details.json\` - Detailed language predictions
- \`raw_language_predictions.json\` - Raw prediction data

## Usage
These files are automatically generated and updated by the application.
Do not manually edit unless you know what you're doing.
`;

  fs.writeFileSync('server/data/README.md', serverDataReadme);
  console.log(colorize('✓ Created: server/data/README.md', 'green'));
  
  // Create docs/README.md
  const docsReadme = `# Documentation

This directory contains all project documentation:

## Setup Guides
- \`INSTALLATION_GUIDE.md\` - Complete installation instructions
- \`ENV_SETUP_GUIDE.md\` - Environment configuration
- \`PYTHON_SETUP_GUIDE.md\` - Python environment setup

## Integration & Testing
- \`INTEGRATION_GUIDE.md\` - API integration examples
- \`test-*.md\` - Testing guides and procedures

## Feature Documentation
- \`add-video-with-multiple-languages.md\` - Video upload guide
- \`language-dropdown-test-guide.md\` - Language selector testing

## Quick Links
- [Main README](../README.md)
- [Installation Guide](INSTALLATION_GUIDE.md)
- [Environment Setup](ENV_SETUP_GUIDE.md)
`;

  fs.writeFileSync('docs/README.md', docsReadme);
  console.log(colorize('✓ Created: docs/README.md', 'green'));
}

function updateGitignore() {
  console.log(colorize('\n📄 Updating .gitignore...', 'blue'));
  
  const gitignoreAdditions = `
# Data files
server/data/*.json
server/data/*.txt
server/temp/
temp_audio/

# Response files
*_response.json
detected_language.txt
ald_language_details.json
raw_language_predictions.json

# Audio files
*.wav
*.mp3
*.m4a

# Logs
*.log
logs/
`;

  try {
    let gitignoreContent = '';
    if (fs.existsSync('.gitignore')) {
      gitignoreContent = fs.readFileSync('.gitignore', 'utf8');
    }
    
    // Check if additions are already present
    if (!gitignoreContent.includes('# Data files')) {
      fs.appendFileSync('.gitignore', gitignoreAdditions);
      console.log(colorize('✓ Updated .gitignore with data file patterns', 'green'));
    } else {
      console.log(colorize('- .gitignore already contains data file patterns', 'yellow'));
    }
  } catch (error) {
    console.log(colorize(`✗ Failed to update .gitignore: ${error.message}`, 'red'));
  }
}

function generateSummary() {
  console.log(colorize('\n📊 Organization Summary', 'cyan'));
  console.log(colorize('========================', 'cyan'));
  
  const summary = {
    'Root files': fs.readdirSync('.').filter(f => fs.statSync(f).isFile()).length,
    'Server data files': fs.existsSync('server/data') ? fs.readdirSync('server/data').length : 0,
    'Documentation files': fs.existsSync('docs') ? fs.readdirSync('docs').length : 0,
    'Client files': fs.existsSync('client') ? 'Organized' : 'Missing',
    'Server files': fs.existsSync('server') ? 'Organized' : 'Missing',
    'Scripts': fs.existsSync('scripts') ? fs.readdirSync('scripts').length : 0
  };
  
  Object.entries(summary).forEach(([key, value]) => {
    console.log(colorize(`${key}: ${value}`, 'white'));
  });
  
  console.log(colorize('\n✅ Data organization completed!', 'green'));
  console.log(colorize('\nNext steps:', 'blue'));
  console.log('1. Review moved files in their new locations');
  console.log('2. Update any hardcoded file paths in your code');
  console.log('3. Test the application to ensure everything works');
  console.log('4. Commit the organized structure to version control');
}

// Main execution
function organizeData() {
  console.log(colorize('🗂️  LawEngaxe Data Organization Tool', 'cyan'));
  console.log(colorize('=====================================\n', 'cyan'));
  
  try {
    createDirectories();
    moveFiles();
    cleanupRootDirectory();
    createDataIndexFiles();
    updateGitignore();
    generateSummary();
  } catch (error) {
    console.error(colorize(`\n❌ Organization failed: ${error.message}`, 'red'));
    process.exit(1);
  }
}

// Run if executed directly
if (require.main === module) {
  organizeData();
}

module.exports = { organizeData };
