/**
 * Initialize JSON storage with empty structure
 * This script creates the initial all-videos.json file
 */

const fs = require('fs');
const path = require('path');

// Define paths
const JSON_DIR = path.join(__dirname, 'json', 'videos');
const ALL_VIDEOS_FILE = path.join(JSON_DIR, 'all-videos.json');
const BACKUP_DIR = path.join(JSON_DIR, 'backups');

/**
 * Initialize storage directories and files
 */
function initializeStorage() {
  try {
    console.log('🚀 Initializing JSON storage...');
    
    // Create main videos directory
    if (!fs.existsSync(JSON_DIR)) {
      fs.mkdirSync(JSON_DIR, { recursive: true });
      console.log('✅ Created videos directory');
    } else {
      console.log('ℹ️ Videos directory already exists');
    }

    // Create backup directory
    if (!fs.existsSync(BACKUP_DIR)) {
      fs.mkdirSync(BACKUP_DIR, { recursive: true });
      console.log('✅ Created backups directory');
    } else {
      console.log('ℹ️ Backups directory already exists');
    }

    // Create initial all-videos.json if it doesn't exist
    if (!fs.existsSync(ALL_VIDEOS_FILE)) {
      const initialData = {
        metadata: {
          totalVideos: 0,
          lastUpdated: new Date().toISOString(),
          version: '1.0.0',
          description: 'Automatic JSON storage for all uploaded videos in LawEngaxe platform'
        },
        videos: []
      };
      
      fs.writeFileSync(ALL_VIDEOS_FILE, JSON.stringify(initialData, null, 2));
      console.log('✅ Created initial all-videos.json file');
    } else {
      console.log('ℹ️ all-videos.json file already exists');
      
      // Read and display current stats
      try {
        const data = fs.readFileSync(ALL_VIDEOS_FILE, 'utf8');
        const jsonData = JSON.parse(data);
        console.log(`📊 Current videos in storage: ${jsonData.videos?.length || 0}`);
        console.log(`📅 Last updated: ${jsonData.metadata?.lastUpdated || 'Unknown'}`);
      } catch (error) {
        console.error('❌ Error reading existing file:', error.message);
      }
    }

    console.log('\n📁 Storage structure:');
    console.log(`   Main file: ${ALL_VIDEOS_FILE}`);
    console.log(`   Backups: ${BACKUP_DIR}`);
    
    console.log('\n✅ JSON storage initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Error initializing JSON storage:', error);
    process.exit(1);
  }
}

/**
 * Add a sample video for testing
 */
function addSampleVideo() {
  try {
    console.log('\n🧪 Adding sample video for testing...');
    
    // Read current data
    const data = fs.readFileSync(ALL_VIDEOS_FILE, 'utf8');
    const jsonData = JSON.parse(data);
    
    // Check if sample video already exists
    const sampleVideoId = 'sample_test_video';
    const existingVideo = jsonData.videos.find(v => v.id === sampleVideoId);
    
    if (existingVideo) {
      console.log('ℹ️ Sample video already exists');
      return;
    }
    
    // Create sample video
    const sampleVideo = {
      id: sampleVideoId,
      title: 'Sample Test Video - JSON Storage Demo',
      description: 'This is a sample video created to demonstrate the automatic JSON storage functionality in LawEngaxe platform. It shows how videos are automatically stored in JSON format for fast search and retrieval.',
      url: 'XLcMq2',
      thumbnailUrl: 'https://via.placeholder.com/480x360/333333/FFFFFF?text=Sample+Video',
      duration: 180,
      userId: 'sample_user',
      channelId: 'sample_channel',
      visibility: 'public',
      tags: ['sample', 'test', 'json-storage', 'demo', 'lawengaxe'],
      category: 'Technology',
      contentRating: 'general',
      processingStatus: 'ready',
      stats: {
        views: 42,
        likes: 5,
        dislikes: 0,
        comments: 2,
        shares: 1,
        playlistAdds: 0,
        averageWatchTime: 120,
        retentionRate: 0.8
      },
      file: {
        originalName: 'sample-video.mp4',
        size: 15728640,
        mimeType: 'video/mp4'
      },
      languages: [
        {
          code: 'en',
          name: 'English',
          flag: '🇺🇸',
          url: 'XLcMq2',
          isDefault: true
        }
      ],
      source: {
        type: 'embed',
        originalUrl: 'XLcMq2',
        platform: 'engaxe'
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // Additional fields for search functionality
      searchKeywords: ['sample', 'test', 'json', 'storage', 'demo', 'lawengaxe', 'technology', 'video'],
      watchUrl: `http://localhost:5173/watch?id=${sampleVideoId}`
    };
    
    // Add to videos array
    jsonData.videos.push(sampleVideo);
    
    // Update metadata
    jsonData.metadata.totalVideos = jsonData.videos.length;
    jsonData.metadata.lastUpdated = new Date().toISOString();
    
    // Write back to file
    fs.writeFileSync(ALL_VIDEOS_FILE, JSON.stringify(jsonData, null, 2));
    
    console.log('✅ Sample video added successfully');
    console.log(`   Title: ${sampleVideo.title}`);
    console.log(`   Watch URL: ${sampleVideo.watchUrl}`);
    console.log(`   Search keywords: ${sampleVideo.searchKeywords.join(', ')}`);
    
  } catch (error) {
    console.error('❌ Error adding sample video:', error);
  }
}

/**
 * Display usage information
 */
function showUsage() {
  console.log('\n📖 Usage Information:');
  console.log('   1. Upload/import videos through the platform - they will be automatically stored in JSON');
  console.log('   2. Use the search bar in HomePage.tsx - it will search through the JSON storage');
  console.log('   3. Access API endpoints:');
  console.log('      - GET /api/v1/videos/json/videos (get all videos)');
  console.log('      - GET /api/v1/videos/json/videos/search?q=query (search videos)');
  console.log('      - GET /api/v1/videos/json/stats (get statistics)');
  console.log('   4. Run sync script: node sync-videos-to-json.js');
  console.log('\n🔍 Search functionality:');
  console.log('   - Searches in titles, descriptions, tags, categories');
  console.log('   - Returns video links in format: http://localhost:5173/watch?id=videoId');
  console.log('   - Automatically includes relevant videos based on keywords');
}

/**
 * Main function
 */
function main() {
  console.log('🎬 LawEngaxe Video JSON Storage Initializer');
  console.log('===========================================');
  
  initializeStorage();
  addSampleVideo();
  showUsage();
  
  console.log('\n🎉 Ready to use! Videos will be automatically stored in JSON format.');
}

// Run if executed directly
if (require.main === module) {
  main();
}

module.exports = { initializeStorage, addSampleVideo };
