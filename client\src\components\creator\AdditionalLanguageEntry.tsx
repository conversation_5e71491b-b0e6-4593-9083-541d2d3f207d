import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Trash2 } from 'lucide-react';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Language } from '@/types';

export interface AdditionalLanguageEntry {
  id: string;
  languageCode: string;
  url: string;
}

interface AdditionalLanguageEntryProps {
  entry: AdditionalLanguageEntry;
  availableLanguages: Language[];
  onUpdate: (id: string, field: 'languageCode' | 'url', value: string) => void;
  onRemove: (id: string) => void;
}

export default function AdditionalLanguageEntry({ 
  entry, 
  availableLanguages, 
  onUpdate, 
  onRemove 
}: AdditionalLanguageEntryProps) {
  return (
    <div className="flex items-end gap-2 mt-2">
      <div className="flex-1 space-y-1">
        <label className="text-xs">Language</label>
        <Select 
          value={entry.languageCode} 
          onValueChange={(value) => onUpdate(entry.id, 'languageCode', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select language" />
          </SelectTrigger>
          <SelectContent>
            {availableLanguages.map((lang) => (
              <SelectItem key={lang.code} value={lang.code}>
                <div className="flex items-center gap-2">
                  <span>{lang.flag}</span>
                  <span>{lang.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex-1 space-y-1">
        <label className="text-xs">Engaxe URL</label>
        <Input
          value={entry.url}
          onChange={(e) => onUpdate(entry.id, 'url', e.target.value)}
          placeholder="Paste Engaxe URL"
        />
      </div>
      
      <Button 
        variant="ghost" 
        size="icon" 
        onClick={() => onRemove(entry.id)}
        className="text-red-500 hover:text-red-600 hover:bg-red-100/10"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
}
