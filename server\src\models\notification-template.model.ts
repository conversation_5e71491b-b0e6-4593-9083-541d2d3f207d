import mongoose, { <PERSON><PERSON><PERSON>, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * NotificationTemplate interface extending the base entity
 */
export interface INotificationTemplate extends IBaseEntity {
  /** Human-readable name of the template */
  name: string;

  /** Detailed description of when and how this template is used */
  description: string;

  /**
   * Unique code for programmatic access
   * Example: 'new_comment', 'video_processed', 'subscription_renewal'
   */
  code: string;

  /**
   * Category for grouping related templates
   * Examples: 'content', 'social', 'account', 'billing'
   */
  category: string;

  /**
   * Notification channels this template supports
   * Array can include: 'email', 'push', 'sms', 'in_app'
   */
  channels: string[];

  /** Whether this template is currently active */
  isActive: boolean;

  /** Default delivery priority (1-5, where 1 is highest) */
  priority: number;

  /** Channel-specific content templates */
  templates: {
    /** Email-specific template */
    email?: {
      /** Email subject line */
      subject: string;

      /** Plain text email body */
      body: string;

      /** HTML formatted email body */
      html: string;
    };

    /** Push notification template */
    push?: {
      /** Push notification title */
      title: string;

      /** Push notification body text */
      body: string;
    };

    /** SMS message template */
    sms?: {
      /** SMS message content */
      message: string;
    };

    /** In-app notification template */
    inApp?: {
      /** In-app notification content */
      content: string;
    };
  };

  /**
   * Variables that can be used in the template
   * These are placeholders that get replaced with actual values when sending
   */
  variables: Array<{
    /** Variable name (e.g., 'user_name', 'reset_link') */
    name: string;

    /**
     * Expected data type
     * Examples: 'string', 'number', 'date', 'url'
     */
    type: string;

    /** Whether this variable must be provided when sending */
    required: boolean;

    /** Fallback value if the variable is not provided */
    defaultValue?: string;
  }>;

  /** Default user preferences for this notification type */
  defaultPreferences: {
    /** Whether this notification is enabled by default */
    enabled: boolean;

    /** Default delivery channels in order of preference */
    channels: string[];

    /** Time-based delivery settings */
    scheduling?: {
      /** Timezone to use for scheduling decisions */
      timezone: string;

      /**
       * Hours during which notifications should not be sent
       * Used to avoid disturbing users during sleep hours
       */
      quietHours?: {
        /** Start time of quiet hours (format: 'HH:MM') */
        start: string;

        /** End time of quiet hours (format: 'HH:MM') */
        end: string;
      };
    };
  };
}

/**
 * NotificationTemplate schema definition
 */
const NotificationTemplateSchema = new Schema<INotificationTemplate>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
    },
    code: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      match: /^[a-z0-9_.-]+$/,
    },
    category: {
      type: String,
      required: true,
      trim: true,
    },
    channels: {
      type: [String],
      enum: ['email', 'push', 'sms', 'in_app'],
      default: ['email', 'in_app'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    priority: {
      type: Number,
      default: 3,
      min: 1,
      max: 5,
    },
    templates: {
      email: {
        subject: {
          type: String,
        },
        body: {
          type: String,
        },
        html: {
          type: String,
        },
      },
      push: {
        title: {
          type: String,
        },
        body: {
          type: String,
        },
      },
      sms: {
        message: {
          type: String,
        },
      },
      inApp: {
        content: {
          type: String,
        },
      },
    },
    variables: [
      {
        name: {
          type: String,
          required: true,
        },
        type: {
          type: String,
          required: true,
        },
        required: {
          type: Boolean,
          default: false,
        },
        defaultValue: {
          type: String,
        },
      },
    ],
    defaultPreferences: {
      enabled: {
        type: Boolean,
        default: true,
      },
      channels: {
        type: [String],
        default: ['email', 'in_app'],
      },
      scheduling: {
        timezone: {
          type: String,
          default: 'UTC',
        },
        quietHours: {
          start: {
            type: String,
            match: /^([01]\d|2[0-3]):([0-5]\d)$/,
          },
          end: {
            type: String,
            match: /^([01]\d|2[0-3]):([0-5]\d)$/,
          },
        },
      },
    },
  }
);

// Merge with base schema
NotificationTemplateSchema.add(BaseSchema);

// Add compound index for code with soft delete support
NotificationTemplateSchema.index({ code: 1, deletedAt: 1 }, { unique: true, sparse: true });

// Create and export the NotificationTemplate model
const NotificationTemplateModel = mongoose.model<INotificationTemplate>('NotificationTemplate', NotificationTemplateSchema);
export default NotificationTemplateModel;
