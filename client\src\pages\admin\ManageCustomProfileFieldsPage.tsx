import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Home, ChevronRight, PlusCircle, Edit, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Sample data for custom profile fields
const initialFields = [
  {
    id: 1,
    name: 'Gemini API Key',
    type: 'Text box',
    length: 40,
    placement: 'Profile settings',
    description: 'Gemini API Key allows you to use AI to generate interactions for your videos',
    showOnRegistration: 'No',
    showOnUserProfile: 'No',
    active: 'Yes'
  }
];

export default function ManageCustomProfileFieldsPage() {
  const [customFields, setCustomFields] = useState(initialFields);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedField, setSelectedField] = useState(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);

  // Form state
  const [fieldName, setFieldName] = useState('');
  const [fieldType, setFieldType] = useState('Text box');
  const [fieldLength, setFieldLength] = useState('40');
  const [fieldPlacement, setFieldPlacement] = useState('Profile settings');
  const [fieldDescription, setFieldDescription] = useState('');
  const [showOnRegistration, setShowOnRegistration] = useState('No');
  const [showOnUserProfile, setShowOnUserProfile] = useState('No');
  const [isActive, setIsActive] = useState('Yes');

  const handleAddField = () => {
    setFieldName('');
    setFieldType('Text box');
    setFieldLength('40');
    setFieldPlacement('Profile settings');
    setFieldDescription('');
    setShowOnRegistration('No');
    setShowOnUserProfile('No');
    setIsActive('Yes');
    setShowAddDialog(true);
  };

  const handleEditField = (field) => {
    setSelectedField(field);
    setFieldName(field.name);
    setFieldType(field.type);
    setFieldLength(field.length.toString());
    setFieldPlacement(field.placement);
    setFieldDescription(field.description || '');
    setShowOnRegistration(field.showOnRegistration || 'No');
    setShowOnUserProfile(field.showOnUserProfile || 'No');
    setIsActive(field.active || 'Yes');
    setShowEditDialog(true);
  };

  const handleSaveNewField = () => {
    const newField = {
      id: customFields.length > 0 ? Math.max(...customFields.map(f => f.id)) + 1 : 1,
      name: fieldName,
      type: fieldType,
      length: parseInt(fieldLength),
      placement: fieldPlacement,
      description: fieldDescription,
      showOnRegistration: showOnRegistration,
      showOnUserProfile: showOnUserProfile,
      active: isActive
    };

    setCustomFields([...customFields, newField]);
    setShowAddDialog(false);
  };

  const handleUpdateField = () => {
    const updatedFields = customFields.map(field =>
      field.id === selectedField.id
        ? {
            ...field,
            name: fieldName,
            type: fieldType,
            length: parseInt(fieldLength),
            placement: fieldPlacement,
            description: fieldDescription,
            showOnRegistration: showOnRegistration,
            showOnUserProfile: showOnUserProfile,
            active: isActive
          }
        : field
    );

    setCustomFields(updatedFields);
    setShowEditDialog(false);
  };

  const handleDeleteField = (id) => {
    setCustomFields(customFields.filter(field => field.id !== id));
  };

  const handleDeleteSelected = () => {
    setCustomFields(customFields.filter(field => !selectedRows.includes(field.id)));
    setSelectedRows([]);
  };

  const toggleSelectRow = (id) => {
    if (selectedRows.includes(id)) {
      setSelectedRows(selectedRows.filter(rowId => rowId !== id));
    } else {
      setSelectedRows([...selectedRows, id]);
    }
  };

  const toggleSelectAll = () => {
    if (selectedRows.length === customFields.length) {
      setSelectedRows([]);
    } else {
      setSelectedRows(customFields.map(field => field.id));
    }
  };

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <div className="p-6 flex-1 bg-gray-50">
          {/* Breadcrumb */}
          <div className="flex items-center text-sm mb-6">
            <Link to="/admin" className="text-blue-600 hover:underline flex items-center">
              <Home size={14} className="mr-1" />
              Admin Panel
            </Link>
            <ChevronRight size={14} className="mx-2 text-gray-500" />
            <Link to="/admin/users" className="text-blue-600 hover:underline">
              Users
            </Link>
            <ChevronRight size={14} className="mx-2 text-gray-500" />
            <span className="text-gray-600">Custom Profile Fields</span>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-6">Custom Profile Fields</h1>

            <div className="bg-gray-50 p-6 rounded-lg mb-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Manage & Edit Custom Profile Fields</h2>

              <Button
                className="mb-6 bg-teal-500 hover:bg-teal-600 text-white"
                onClick={handleAddField}
              >
                <PlusCircle size={16} className="mr-2" />
                Create New Custom Field
              </Button>

              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="w-12 px-4 py-3 border">
                        <input
                          type="checkbox"
                          className="h-4 w-4"
                          checked={selectedRows.length === customFields.length && customFields.length > 0}
                          onChange={toggleSelectAll}
                        />
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                        ID <span className="text-gray-400">↑</span>
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                        FIELD NAME <span className="text-gray-400">↓</span>
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                        TYPE <span className="text-gray-400">↓</span>
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                        LENGTH <span className="text-gray-400">↓</span>
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                        PLACEMENT <span className="text-gray-400">↓</span>
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                        REGISTRATION
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                        PROFILE
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                        ACTIVE
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                        ACTION
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {customFields.map((field) => (
                      <tr key={field.id}>
                        <td className="px-4 py-3 whitespace-nowrap border">
                          <input
                            type="checkbox"
                            className="h-4 w-4"
                            checked={selectedRows.includes(field.id)}
                            onChange={() => toggleSelectRow(field.id)}
                          />
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                          {field.id}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                          {field.name}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                          {field.type}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                          {field.length}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                          {field.placement}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                          {field.showOnRegistration || 'No'}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                          {field.showOnUserProfile || 'No'}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                          <span className={field.active === 'Yes' ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                            {field.active || 'No'}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium border">
                          <div className="flex space-x-2">
                            <Link
                              to={`/admin/users/custom-fields/${field.id}`}
                              className="flex items-center px-2 py-1 bg-green-100 text-green-700 rounded text-xs"
                            >
                              <Edit size={12} className="mr-1" />
                              Edit
                            </Link>
                            <button
                              className="flex items-center px-2 py-1 bg-red-100 text-red-700 rounded text-xs"
                              onClick={() => handleDeleteField(field.id)}
                            >
                              <Trash2 size={12} className="mr-1" />
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {selectedRows.length > 0 && (
                <Button
                  className="mt-4 bg-red-500 hover:bg-red-600 text-white"
                  onClick={handleDeleteSelected}
                >
                  <Trash2 size={16} className="mr-2" />
                  Delete Selected
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add Field Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add New Custom Field</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="fieldName">Field Name</Label>
              <Input
                id="fieldName"
                value={fieldName}
                onChange={(e) => setFieldName(e.target.value)}
                placeholder="Enter field name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="fieldType">Field Type</Label>
              <Select value={fieldType} onValueChange={setFieldType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select field type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Text box">Text box</SelectItem>
                  <SelectItem value="Long text area">Long text area</SelectItem>
                  <SelectItem value="Select box">Select box</SelectItem>
                  <SelectItem value="Multi-select">Multi-select</SelectItem>
                  <SelectItem value="Date">Date</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="fieldLength">Field Length: <span className="text-sm text-gray-500">Default value is 32, and max value is 1000</span></Label>
              <Input
                id="fieldLength"
                type="number"
                value={fieldLength}
                onChange={(e) => setFieldLength(e.target.value)}
                placeholder="Enter field length"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="fieldDescription">Field Description: <span className="text-sm text-gray-500">The description will show under the field</span></Label>
              <textarea
                id="fieldDescription"
                value={fieldDescription}
                onChange={(e) => setFieldDescription(e.target.value)}
                placeholder="Enter field description"
                className="w-full p-2 border border-gray-300 rounded text-black min-h-[100px]"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="fieldPlacement">Field placement</Label>
              <Select value={fieldPlacement} onValueChange={setFieldPlacement}>
                <SelectTrigger>
                  <SelectValue placeholder="Select placement" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Profile settings">Profile settings</SelectItem>
                  <SelectItem value="General">General</SelectItem>
                  <SelectItem value="Social Links">Social Links</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="showOnRegistration">Show On The Registration Page</Label>
              <Select value={showOnRegistration} onValueChange={setShowOnRegistration}>
                <SelectTrigger>
                  <SelectValue placeholder="Select option" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Yes">Yes</SelectItem>
                  <SelectItem value="No">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="showOnUserProfile">Show On User Profile Page</Label>
              <Select value={showOnUserProfile} onValueChange={setShowOnUserProfile}>
                <SelectTrigger>
                  <SelectValue placeholder="Select option" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Yes">Yes</SelectItem>
                  <SelectItem value="No">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="isActive">Active</Label>
              <Select value={isActive} onValueChange={setIsActive}>
                <SelectTrigger>
                  <SelectValue placeholder="Select option" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Yes">Yes</SelectItem>
                  <SelectItem value="No">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>Cancel</Button>
            <Button className="bg-blue-500 hover:bg-blue-600" onClick={handleSaveNewField}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Field Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Custom Field #{selectedField?.id}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="editFieldName">Field Name</Label>
              <Input
                id="editFieldName"
                value={fieldName}
                onChange={(e) => setFieldName(e.target.value)}
                placeholder="Enter field name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="editFieldLength">Field Length: <span className="text-sm text-gray-500">Default value is 32, and max value is 1000</span></Label>
              <Input
                id="editFieldLength"
                type="number"
                value={fieldLength}
                onChange={(e) => setFieldLength(e.target.value)}
                placeholder="Enter field length"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="editFieldDescription">Field Description: <span className="text-sm text-gray-500">The description will show under the field</span></Label>
              <textarea
                id="editFieldDescription"
                value={fieldDescription}
                onChange={(e) => setFieldDescription(e.target.value)}
                placeholder="Enter field description"
                className="w-full p-2 border border-gray-300 rounded text-black min-h-[100px]"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="editFieldPlacement">Field placement</Label>
              <Select value={fieldPlacement} onValueChange={setFieldPlacement}>
                <SelectTrigger>
                  <SelectValue placeholder="Select placement" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Profile settings">Profile settings</SelectItem>
                  <SelectItem value="General">General</SelectItem>
                  <SelectItem value="Social Links">Social Links</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="editShowOnRegistration">Show On The Registration Page</Label>
              <Select value={showOnRegistration} onValueChange={setShowOnRegistration}>
                <SelectTrigger>
                  <SelectValue placeholder="Select option" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Yes">Yes</SelectItem>
                  <SelectItem value="No">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="editShowOnUserProfile">Show On User Profile Page</Label>
              <Select value={showOnUserProfile} onValueChange={setShowOnUserProfile}>
                <SelectTrigger>
                  <SelectValue placeholder="Select option" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Yes">Yes</SelectItem>
                  <SelectItem value="No">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="editActive">Active</Label>
              <Select value={isActive} onValueChange={setIsActive}>
                <SelectTrigger>
                  <SelectValue placeholder="Select option" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Yes">Yes</SelectItem>
                  <SelectItem value="No">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>Cancel</Button>
            <Button className="bg-blue-500 hover:bg-blue-600" onClick={handleUpdateField}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
