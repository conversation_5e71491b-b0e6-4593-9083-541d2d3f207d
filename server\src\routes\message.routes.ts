import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Type } from '@sinclair/typebox';
import { messageController } from '../controllers/message.controller';
import { authenticate } from '../middleware/auth';
import { ErrorResponseSchema, SuccessResponseSchema } from '../middleware/swagger';

/**
 * Message routes
 */
export default async function messageRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // Apply authentication middleware to all routes
  fastify.addHook('preHandler', authenticate);

  // Get messages for a conversation
  fastify.get('/conversation/:conversationId', {
    schema: {
      tags: ['Messages'],
      description: 'Get messages for a conversation',
      params: Type.Object({
        conversationId: Type.String(),
      }),
      querystring: Type.Object({
        page: Type.Optional(Type.Number({ minimum: 1 })),
        limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100 })),
        sort: Type.Optional(Type.String()),
        before: Type.Optional(Type.String({ format: 'date-time' })),
        after: Type.Optional(Type.String({ format: 'date-time' })),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Array(
            Type.Object({
              id: Type.String(),
              conversationId: Type.String(),
              senderId: Type.String(),
              senderType: Type.String(),
              content: Type.String(),
              originalContent: Type.Optional(Type.String()),
              originalLanguage: Type.Optional(Type.String()),
              contentType: Type.String(),
              mediaUrl: Type.Optional(Type.String()),
              mediaMimeType: Type.Optional(Type.String()),
              isRead: Type.Boolean(),
              readAt: Type.Optional(Type.String({ format: 'date-time' })),
              isDelivered: Type.Boolean(),
              deliveredAt: Type.Optional(Type.String({ format: 'date-time' })),
              metadata: Type.Object({
                clientId: Type.Optional(Type.String()),
                tags: Type.Array(Type.String()),
                isAnswer: Type.Boolean(),
                location: Type.Optional(
                  Type.Object({
                    latitude: Type.Number(),
                    longitude: Type.Number(),
                    address: Type.Optional(Type.String()),
                  })
                ),
              }),
              createdAt: Type.String({ format: 'date-time' }),
              updatedAt: Type.String({ format: 'date-time' }),
              sender: Type.Object({
                id: Type.String(),
                username: Type.String(),
                displayName: Type.String(),
                avatar: Type.Optional(Type.String()),
              }),
            })
          ),
          pagination: Type.Object({
            total: Type.Number(),
            page: Type.Number(),
            limit: Type.Number(),
            pages: Type.Number(),
          }),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, messageController.getMessagesByConversation);

  // Send a text message
  fastify.post('/text', {
    schema: {
      tags: ['Messages'],
      description: 'Send a text message',
      body: Type.Object({
        conversationId: Type.String(),
        content: Type.String({ minLength: 1 }),
        clientId: Type.Optional(Type.String()),
        metadata: Type.Optional(
          Type.Object({
            tags: Type.Optional(Type.Array(Type.String())),
            isAnswer: Type.Optional(Type.Boolean()),
            customFields: Type.Optional(Type.Record(Type.String(), Type.Any())),
          })
        ),
      }),
      response: {
        201: Type.Object({
          success: Type.Boolean(),
          data: Type.Object({
            id: Type.String(),
            conversationId: Type.String(),
            senderId: Type.String(),
            senderType: Type.String(),
            content: Type.String(),
            contentType: Type.String(),
            isRead: Type.Boolean(),
            isDelivered: Type.Boolean(),
            deliveredAt: Type.String({ format: 'date-time' }),
            metadata: Type.Object({
              clientId: Type.Optional(Type.String()),
              tags: Type.Array(Type.String()),
              isAnswer: Type.Boolean(),
            }),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          }),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, messageController.sendTextMessage);

  // Send a media message
  fastify.post('/media', {
    schema: {
      tags: ['Messages'],
      description: 'Send a media message (voice, image, video, file)',
      body: Type.Object({
        conversationId: Type.String(),
        contentType: Type.Enum({
          voice: 'voice',
          image: 'image',
          video: 'video',
          file: 'file',
        }),
        mediaUrl: Type.String(),
        mediaMimeType: Type.String(),
        content: Type.Optional(Type.String()),
        clientId: Type.Optional(Type.String()),
        metadata: Type.Optional(
          Type.Object({
            tags: Type.Optional(Type.Array(Type.String())),
            isAnswer: Type.Optional(Type.Boolean()),
            customFields: Type.Optional(Type.Record(Type.String(), Type.Any())),
          })
        ),
      }),
      response: {
        201: Type.Object({
          success: Type.Boolean(),
          data: Type.Object({
            id: Type.String(),
            conversationId: Type.String(),
            senderId: Type.String(),
            senderType: Type.String(),
            content: Type.String(),
            contentType: Type.String(),
            mediaUrl: Type.String(),
            mediaMimeType: Type.String(),
            isRead: Type.Boolean(),
            isDelivered: Type.Boolean(),
            deliveredAt: Type.String({ format: 'date-time' }),
            metadata: Type.Object({
              clientId: Type.Optional(Type.String()),
              tags: Type.Array(Type.String()),
              isAnswer: Type.Boolean(),
            }),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          }),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, messageController.sendMediaMessage);

  // Mark a message as read
  fastify.post('/:id/read', {
    schema: {
      tags: ['Messages'],
      description: 'Mark a message as read',
      params: Type.Object({
        id: Type.String(),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, messageController.markMessageAsRead);

  // Delete a message
  fastify.delete('/:id', {
    schema: {
      tags: ['Messages'],
      description: 'Delete a message',
      params: Type.Object({
        id: Type.String(),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, messageController.deleteMessage);

  // Translate a message
  fastify.post('/:id/translate', {
    schema: {
      tags: ['Messages'],
      description: 'Translate a message to a different language',
      params: Type.Object({
        id: Type.String(),
      }),
      body: Type.Object({
        targetLanguage: Type.String(),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Object({
            id: Type.String(),
            conversationId: Type.String(),
            senderId: Type.String(),
            senderType: Type.String(),
            content: Type.String(),
            originalContent: Type.String(),
            originalLanguage: Type.String(),
            contentType: Type.String(),
            isRead: Type.Boolean(),
            isDelivered: Type.Boolean(),
            metadata: Type.Object({
              clientId: Type.Optional(Type.String()),
              tags: Type.Array(Type.String()),
              isAnswer: Type.Boolean(),
            }),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          }),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, messageController.translateMessage);
}
