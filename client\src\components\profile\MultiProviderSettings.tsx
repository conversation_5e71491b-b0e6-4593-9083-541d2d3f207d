import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AIAssistantProvider, useChatbot } from '@/context/ChatbotContext';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Info, Check, AlertCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

export default function MultiProviderSettings() {
  const {
    isAIAssistantEnabled,
    aiProviders,
    updateAIProvider
  } = useChatbot();
  const { toast } = useToast();

  const [activeTab, setActiveTab] = useState<AIAssistantProvider>('openrouter');
  const [testingApiKey, setTestingApiKey] = useState<boolean>(false);
  const [apiKeyValid, setApiKeyValid] = useState<boolean | null>(null);

  const handleToggleProvider = (provider: AIAssistantProvider, enabled: boolean) => {
    updateAIProvider(provider, { enabled });
  };

  const handleApiKeyChange = (provider: AIAssistantProvider, apiKey: string) => {
    updateAIProvider(provider, { apiKey });
    // Reset validation state when key changes
    setApiKeyValid(null);
  };

  const handleEndpointChange = (provider: AIAssistantProvider, endpoint: string) => {
    updateAIProvider(provider, { endpoint });
  };

  const handleModelChange = (provider: AIAssistantProvider, model: string) => {
    updateAIProvider(provider, { model });
  };

  // Test OpenAI API key by making a simple request
  const testOpenAIApiKey = async () => {
    const apiKey = aiProviders.openai.apiKey;

    if (!apiKey || apiKey.includes('REPLACE_WITH_YOUR_ACTUAL_OPENAI_API_KEY')) {
      toast({
        title: "Invalid API Key",
        description: "Please enter a valid OpenAI API key",
        variant: "destructive"
      });
      setApiKeyValid(false);
      return;
    }

    setTestingApiKey(true);

    try {
      // Make a simple request to OpenAI's models endpoint to verify the key
      const response = await fetch('https://api.openai.com/v1/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        toast({
          title: "API Key Valid",
          description: "Your OpenAI API key is working correctly",
          variant: "default"
        });
        setApiKeyValid(true);
      } else {
        const errorData = await response.json();
        toast({
          title: "API Key Invalid",
          description: errorData.error?.message || "Your OpenAI API key is not valid",
          variant: "destructive"
        });
        setApiKeyValid(false);
      }
    } catch (error) {
      console.error('Error testing API key:', error);
      toast({
        title: "Connection Error",
        description: "Could not connect to OpenAI API. Please check your internet connection.",
        variant: "destructive"
      });
      setApiKeyValid(false);
    } finally {
      setTestingApiKey(false);
    }
  };

  // Test OpenRouter API key by making a simple request
  const testOpenRouterApiKey = async () => {
    const apiKey = aiProviders.openrouter.apiKey;

    if (!apiKey) {
      toast({
        title: "Invalid API Key",
        description: "Please enter a valid OpenRouter API key",
        variant: "destructive"
      });
      setApiKeyValid(false);
      return;
    }

    setTestingApiKey(true);

    try {
      // Make a simple request to OpenRouter's models endpoint to verify the key
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin, // Required by OpenRouter
          'X-Title': 'LawEngaxe Creator Chat'
        }
      });

      if (response.ok) {
        toast({
          title: "API Key Valid",
          description: "Your OpenRouter API key is working correctly",
          variant: "default"
        });
        setApiKeyValid(true);
      } else {
        const errorData = await response.json();
        toast({
          title: "API Key Invalid",
          description: errorData.error?.message || "Your OpenRouter API key is not valid",
          variant: "destructive"
        });
        setApiKeyValid(false);
      }
    } catch (error) {
      console.error('Error testing API key:', error);
      toast({
        title: "Connection Error",
        description: "Could not connect to OpenRouter API. Please check your internet connection.",
        variant: "destructive"
      });
      setApiKeyValid(false);
    } finally {
      setTestingApiKey(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl font-bold">AI Assistant for Video Chats</CardTitle>
            <CardDescription>
              Configure AI assistants to respond to user messages on your videos
            </CardDescription>
          </div>
          <Switch
            checked={isAIAssistantEnabled}
            disabled={!Object.values(aiProviders).some(p => p.enabled && p.apiKey)}
          />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Enable AI assistants to automatically respond to user messages on your videos.
            You can configure multiple AI providers and enable them simultaneously.
          </p>

          <Tabs defaultValue="openrouter" value={activeTab} onValueChange={(value) => setActiveTab(value as AIAssistantProvider)}>
            <TabsList className="grid grid-cols-5 mb-4">
              <TabsTrigger value="deepseek">DeepSeek</TabsTrigger>
              <TabsTrigger value="openai">OpenAI</TabsTrigger>
              <TabsTrigger value="openrouter">OpenRouter</TabsTrigger>
              <TabsTrigger value="bhashini">Bhashini</TabsTrigger>
              <TabsTrigger value="custom">Custom</TabsTrigger>
            </TabsList>

            {/* DeepSeek Tab */}
            <TabsContent value="deepseek" className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="deepseek-enabled" className="font-medium">Enable DeepSeek AI</Label>
                <Switch
                  id="deepseek-enabled"
                  checked={aiProviders.deepseek.enabled}
                  onCheckedChange={(checked) => handleToggleProvider('deepseek', checked)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="deepseek-api-key" className="font-medium">API Key</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Enter your DeepSeek API key</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="deepseek-api-key"
                  type="password"
                  placeholder="Enter your DeepSeek API key"
                  value={aiProviders.deepseek.apiKey}
                  onChange={(e) => handleApiKeyChange('deepseek', e.target.value)}
                  disabled={!aiProviders.deepseek.enabled}
                />
              </div>
            </TabsContent>

            {/* OpenAI Tab */}
            <TabsContent value="openai" className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="openai-enabled" className="font-medium">Enable OpenAI</Label>
                <Switch
                  id="openai-enabled"
                  checked={aiProviders.openai.enabled}
                  onCheckedChange={(checked) => handleToggleProvider('openai', checked)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="openai-api-key" className="font-medium">API Key</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Enter your OpenAI API key</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Input
                      id="openai-api-key"
                      type="password"
                      placeholder="Enter your OpenAI API key"
                      value={aiProviders.openai.apiKey}
                      onChange={(e) => handleApiKeyChange('openai', e.target.value)}
                      disabled={!aiProviders.openai.enabled || testingApiKey}
                      className={`pr-8 ${apiKeyValid === true ? 'border-green-500' : apiKeyValid === false ? 'border-red-500' : ''}`}
                    />
                    {apiKeyValid === true && (
                      <Check className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                    )}
                    {apiKeyValid === false && (
                      <AlertCircle className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={testOpenAIApiKey}
                    disabled={!aiProviders.openai.enabled || testingApiKey || !aiProviders.openai.apiKey}
                    className="whitespace-nowrap"
                  >
                    {testingApiKey ? 'Testing...' : 'Test Key'}
                  </Button>
                </div>
                {apiKeyValid === false && (
                  <p className="text-xs text-red-500 mt-1">
                    Your API key appears to be invalid. Please check it and try again.
                  </p>
                )}
                {apiKeyValid === true && (
                  <p className="text-xs text-green-500 mt-1">
                    API key verified successfully! Voice recording should now work.
                  </p>
                )}
              </div>
            </TabsContent>

            {/* OpenRouter Tab */}
            <TabsContent value="openrouter" className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="openrouter-enabled" className="font-medium">Enable OpenRouter</Label>
                <Switch
                  id="openrouter-enabled"
                  checked={aiProviders.openrouter.enabled}
                  onCheckedChange={(checked) => handleToggleProvider('openrouter', checked)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="openrouter-api-key" className="font-medium">API Key</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Enter your OpenRouter API key</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Input
                      id="openrouter-api-key"
                      type="password"
                      placeholder="Enter your OpenRouter API key"
                      value={aiProviders.openrouter.apiKey}
                      onChange={(e) => handleApiKeyChange('openrouter', e.target.value)}
                      disabled={!aiProviders.openrouter.enabled || testingApiKey}
                      className={`pr-8 ${apiKeyValid === true ? 'border-green-500' : apiKeyValid === false ? 'border-red-500' : ''}`}
                    />
                    {apiKeyValid === true && (
                      <Check className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                    )}
                    {apiKeyValid === false && (
                      <AlertCircle className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={testOpenRouterApiKey}
                    disabled={!aiProviders.openrouter.enabled || testingApiKey || !aiProviders.openrouter.apiKey}
                    className="whitespace-nowrap"
                  >
                    {testingApiKey ? 'Testing...' : 'Test Key'}
                  </Button>
                </div>
                {apiKeyValid === false && (
                  <p className="text-xs text-red-500 mt-1">
                    Your API key appears to be invalid. Please check it and try again.
                  </p>
                )}
                {apiKeyValid === true && (
                  <p className="text-xs text-green-500 mt-1">
                    API key verified successfully!
                  </p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Get your API key from <a href="https://openrouter.ai/keys" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">openrouter.ai/keys</a>
                </p>
              </div>

              <div className="space-y-2 mt-4">
                <div className="flex items-center gap-2">
                  <Label htmlFor="openrouter-model" className="font-medium">Model</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Select the AI model to use with OpenRouter</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Select
                  value={aiProviders.openrouter.model || 'openai/gpt-3.5-turbo'}
                  onValueChange={(value) => handleModelChange('openrouter', value)}
                  disabled={!aiProviders.openrouter.enabled}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openai/gpt-3.5-turbo">OpenAI GPT-3.5 Turbo</SelectItem>
                    <SelectItem value="openai/gpt-4">OpenAI GPT-4</SelectItem>
                    <SelectItem value="anthropic/claude-3-opus">Anthropic Claude 3 Opus</SelectItem>
                    <SelectItem value="anthropic/claude-3-sonnet">Anthropic Claude 3 Sonnet</SelectItem>
                    <SelectItem value="anthropic/claude-3-haiku">Anthropic Claude 3 Haiku</SelectItem>
                    <SelectItem value="google/gemini-pro">Google Gemini Pro</SelectItem>
                    <SelectItem value="meta-llama/llama-3-70b-instruct">Meta Llama 3 70B</SelectItem>
                    <SelectItem value="meta-llama/llama-3-8b-instruct">Meta Llama 3 8B</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  OpenRouter provides access to multiple AI models from different providers through a single API.
                  You can use models from OpenAI, Anthropic, Google, and more with a single API key.
                </p>
              </div>
            </TabsContent>

            {/* Bhashini Tab */}
            <TabsContent value="bhashini" className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="bhashini-enabled" className="font-medium">Enable Bhashini</Label>
                <Switch
                  id="bhashini-enabled"
                  checked={aiProviders.bhashini.enabled}
                  onCheckedChange={(checked) => handleToggleProvider('bhashini', checked)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="bhashini-user-id" className="font-medium">User ID</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Your Bhashini User ID</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="bhashini-user-id"
                  type="text"
                  value="cee60134c6bb4d179efd3fda48ff32fe"
                  readOnly
                  disabled={!aiProviders.bhashini.enabled}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="bhashini-ulca-key" className="font-medium">ULCA API Key</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Your Bhashini ULCA API Key</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="bhashini-ulca-key"
                  type="password"
                  value="13a647c84b-2747-4f0c-afcd-2ac8235f5318"
                  readOnly
                  disabled={!aiProviders.bhashini.enabled}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="bhashini-endpoint" className="font-medium">API Endpoint</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Bhashini API Endpoint URL</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="bhashini-endpoint"
                  type="text"
                  value="https://bhashini.gov.in/api/v1/inference/translation"
                  readOnly
                  disabled={!aiProviders.bhashini.enabled}
                />
              </div>

              <Alert className="bg-green-50 border-green-200">
                <Check className="h-4 w-4 text-green-500" />
                <AlertDescription className="text-green-800">
                  Bhashini API is configured and ready to use. You can now translate messages in Indian languages.
                </AlertDescription>
              </Alert>

              <Button
                type="button"
                className="w-full"
                disabled={!aiProviders.bhashini.enabled}
                onClick={() => {
                  // Test the Bhashini API
                  const translationContext = (window as any).__TRANSLATION_CONTEXT__;
                  if (translationContext) {
                    translationContext.translateText("Hello, how are you?", "hi", "en")
                      .then((result: string) => {
                        toast({
                          title: "Translation Test",
                          description: `Translation result: ${result}`,
                        });
                      })
                      .catch((error: any) => {
                        toast({
                          title: "Translation Test Failed",
                          description: `Error: ${error.message}`,
                          variant: "destructive",
                        });
                      });
                  }
                }}
              >
                Test Bhashini API
              </Button>
            </TabsContent>

            {/* Custom Tab */}
            <TabsContent value="custom" className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="custom-enabled" className="font-medium">Enable Custom Provider</Label>
                <Switch
                  id="custom-enabled"
                  checked={aiProviders.custom.enabled}
                  onCheckedChange={(checked) => handleToggleProvider('custom', checked)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="custom-api-key" className="font-medium">API Key</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Enter your custom provider API key</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="custom-api-key"
                  type="password"
                  placeholder="Enter your custom provider API key"
                  value={aiProviders.custom.apiKey}
                  onChange={(e) => handleApiKeyChange('custom', e.target.value)}
                  disabled={!aiProviders.custom.enabled}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="custom-endpoint" className="font-medium">Endpoint URL</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Enter the endpoint URL for your custom provider</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="custom-endpoint"
                  type="text"
                  placeholder="https://api.example.com/v1/chat"
                  value={aiProviders.custom.endpoint || ''}
                  onChange={(e) => handleEndpointChange('custom', e.target.value)}
                  disabled={!aiProviders.custom.enabled}
                />
              </div>
            </TabsContent>
          </Tabs>

          <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
            <p className="text-sm text-orange-800">
              Chat option will appear under your videos where they can ask questions, and the AI will respond on your behalf using your selected provider(s).
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
