import React from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Palette, Check, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

// Sample themes data
const systemThemes = [
  {
    id: 1,
    name: 'Default',
    version: 'v1.0',
    author: '<PERSON><PERSON>',
    isActive: true
  },
  {
    id: 2,
    name: 'Interactive',
    version: 'v1.0',
    author: 'Gaurav Agrawal',
    isActive: false
  },
  {
    id: 3,
    name: 'NtrxPlay',
    version: 'v1.0.0',
    author: 'Gaurav Agrawal',
    isActive: false
  },
  {
    id: 4,
    name: 'YouPlay',
    version: 'v1.0',
    author: '<PERSON><PERSON>',
    isActive: false
  }
];

const thirdPartyThemes = [
  {
    id: 1,
    name: 'Playtag - The Ultimate Theme',
    icon: '🔖'
  },
  {
    id: 2,
    name: 'Vidplay - The Elegant Theme',
    icon: '📹'
  }
];

export default function ThemesPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <div className="flex-1 bg-gray-100 p-6 overflow-y-auto">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 mb-4 text-sm">
            <Link to="/admin" className="flex items-center text-blue-600">
              <Home size={16} className="mr-1" />
              Admin Panel
            </Link>
            <ChevronRight size={14} className="text-gray-500" />
            <Link to="/admin/design" className="text-blue-600">
              Design
            </Link>
            <ChevronRight size={14} className="text-gray-500" />
            <span className="text-gray-600">Themes</span>
          </div>

          <div className="mb-6">
            <h1 className="text-2xl font-bold text-black">Themes</h1>
          </div>

          {/* System Themes */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {systemThemes.map((theme) => (
              <div key={theme.id} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <h3 className="text-lg font-bold text-black">{theme.name}</h3>
                    <span className="ml-2 px-2 py-0.5 bg-gray-200 text-gray-700 rounded text-xs">{theme.version}</span>
                  </div>
                </div>

                <div className="flex items-center text-sm text-gray-600 mb-4">
                  <span className="inline-block h-2 w-2 rounded-full bg-gray-500 mr-2"></span>
                  <span>Author: {theme.author}</span>
                </div>

                {theme.isActive ? (
                  <Button className="bg-blue-400 hover:bg-blue-500 text-white px-6 py-2 rounded">
                    <Check className="h-4 w-4 mr-2" /> Activated
                  </Button>
                ) : (
                  <Button className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded">
                    Activate
                  </Button>
                )}
              </div>
            ))}
          </div>

          {/* Third Party Themes */}
          <h2 className="text-xl font-bold text-black mb-6">3rd Party Themes</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {thirdPartyThemes.map((theme) => (
              <div key={theme.id} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-start">
                  <div className="h-16 w-16 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-500 text-2xl mr-4">
                    {theme.icon}
                  </div>

                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-black mb-4">{theme.name}</h3>
                    <Button className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded">
                      Get Theme
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
