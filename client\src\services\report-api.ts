import api from './api';

/**
 * Report API service
 */
export const reportAPI = {
  /**
   * Get all reports with filtering and pagination
   */
  getAllReports: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    reportType?: string;
    search?: string;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
  }) => {
    const response = await api.get('/reports', { params });
    return response.data;
  },

  /**
   * Get a report by ID
   */
  getReportById: async (id: string) => {
    const response = await api.get(`/reports/${id}`);
    return response.data;
  },

  /**
   * Create a new report
   */
  createReport: async (reportData: {
    reportType: 'video' | 'comment' | 'user' | 'channel';
    targetId: string;
    category: string;
    reason: string;
    description?: string;
    evidence?: string[];
  }) => {
    const response = await api.post('/reports', reportData);
    return response.data;
  },

  /**
   * Update a report's status
   */
  updateReportStatus: async (id: string, data: {
    status: 'pending' | 'investigating' | 'resolved' | 'rejected' | 'safe';
    note?: string;
    adminComment?: string;
  }) => {
    const response = await api.put(`/reports/${id}/status`, data);
    return response.data;
  },

  /**
   * Delete a report
   */
  deleteReport: async (id: string) => {
    const response = await api.delete(`/reports/${id}`);
    return response.data;
  },

  /**
   * Bulk update report statuses
   */
  bulkUpdateReportStatus: async (data: {
    ids: string[];
    status: 'pending' | 'investigating' | 'resolved' | 'rejected' | 'safe';
    note?: string;
  }) => {
    const response = await api.put('/reports/bulk/status', data);
    return response.data;
  },

  /**
   * Bulk delete reports
   */
  bulkDeleteReports: async (data: {
    ids: string[];
  }) => {
    const response = await api.delete('/reports/bulk', { data });
    return response.data;
  },

  /**
   * Create a new issue report
   */
  createIssueReport: async (data: {
    title: string;
    type: 'bug' | 'suggestion' | 'other';
    description: string;
    evidence?: string[];
  }) => {
    console.log('Sending issue report data:', data);
    try {
      const response = await api.post('/reports/issue', data);
      console.log('Issue report response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating issue report:', error);
      throw error;
    }
  },
};

export default reportAPI;
