import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface CreatorApplicationFormProps {
  onApply: (channelUrl: string) => void;
}

export default function CreatorApplicationForm({ onApply }: CreatorApplicationFormProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [channelUrl, setChannelUrl] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!channelUrl.trim()) {
      toast({
        title: "URL Required",
        description: "Please enter your Engaxe channel URL",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // In a real app, this would be an API call to submit the application
      // For this demo, we'll simulate a submission with a timeout
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Call the onApply callback with the channel URL
      onApply(channelUrl);

      // Show success message
      toast({
        title: "Application Submitted",
        description: "Your creator application has been submitted successfully. We'll review it and get back to you soon."
      });

      // Close the dialog
      setIsOpen(false);

      // Reset form
      setChannelUrl('');
    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your application. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="secondary" className="w-full">Apply for Creator</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Apply to Become a Creator</DialogTitle>
          <DialogDescription>
            Enter your Engaxe channel URL to apply for creator status.
            This will allow you to upload and manage multilingual videos.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label htmlFor="channelUrl" className="text-sm font-medium">Engaxe Channel URL</label>
            <Input
              id="channelUrl"
              value={channelUrl}
              onChange={(e) => setChannelUrl(e.target.value)}
              placeholder="https://engaxe.com/channel/your-channel"
              disabled={isSubmitting}
            />
            <p className="text-xs text-lingstream-muted">
              Your channel must have at least 5 videos and 100 subscribers to qualify.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!channelUrl.trim() || isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : 'Apply'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
