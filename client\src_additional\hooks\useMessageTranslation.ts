import { useState, useCallback, useEffect } from 'react';
import { translateText, translateMessages, isIndianLanguage } from '../utils/translationUtils';

interface UseMessageTranslationProps {
  messages: any[];
  onMessagesTranslated?: (translatedMessages: any[]) => void;
}

/**
 * Custom hook for translating messages when language is changed
 */
export function useMessageTranslation({ 
  messages, 
  onMessagesTranslated 
}: UseMessageTranslationProps) {
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const [translatedMessages, setTranslatedMessages] = useState<any[]>(messages);
  const [error, setError] = useState<string | null>(null);

  // Update translatedMessages when messages prop changes
  useEffect(() => {
    setTranslatedMessages(messages);
  }, [messages]);

  /**
   * Handle language change from dropdown
   */
  const handleLanguageChange = useCallback(async (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newLanguage = event.target.value;
    
    // If language is the same, do nothing
    if (newLanguage === currentLanguage) {
      return;
    }
    
    // Update current language
    setCurrentLanguage(newLanguage);
    
    // If switching to English, just use original messages
    if (newLanguage === 'en') {
      setTranslatedMessages(messages);
      if (onMessagesTranslated) {
        onMessagesTranslated(messages);
      }
      return;
    }
    
    // Only translate to Indian languages
    if (!isIndianLanguage(newLanguage)) {
      setError(`Translation to ${newLanguage} is not supported by Bhashini API`);
      return;
    }
    
    setError(null);
    setIsTranslating(true);
    
    try {
      // Translate all messages
      const translated = await translateMessages(messages, newLanguage);
      
      // Update state with translated messages
      setTranslatedMessages(translated);
      
      // Call callback if provided
      if (onMessagesTranslated) {
        onMessagesTranslated(translated);
      }
    } catch (err) {
      setError(`Translation failed: ${err}`);
      console.error('Translation error:', err);
    } finally {
      setIsTranslating(false);
    }
  }, [currentLanguage, messages, onMessagesTranslated]);

  /**
   * Translate a single message
   */
  const translateMessage = useCallback(async (message: string): Promise<string> => {
    if (currentLanguage === 'en' || !isIndianLanguage(currentLanguage)) {
      return message;
    }
    
    try {
      return await translateText(message, 'en', currentLanguage);
    } catch (err) {
      console.error('Error translating message:', err);
      return message;
    }
  }, [currentLanguage]);

  return {
    currentLanguage,
    isTranslating,
    translatedMessages,
    error,
    handleLanguageChange,
    translateMessage
  };
}

export default useMessageTranslation;
