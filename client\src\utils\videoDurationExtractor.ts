/**
 * Utility functions to extract video duration from embedded video players
 */

/**
 * Parse duration string in format "MM:SS" or "HH:MM:SS" to seconds
 */
export function parseDurationToSeconds(durationStr: string): number {
  if (!durationStr || typeof durationStr !== 'string') {
    return 0;
  }

  // Remove any extra whitespace
  const cleanDuration = durationStr.trim();
  
  // Split by colon
  const parts = cleanDuration.split(':');
  
  if (parts.length === 2) {
    // MM:SS format
    const minutes = parseInt(parts[0], 10) || 0;
    const seconds = parseInt(parts[1], 10) || 0;
    return (minutes * 60) + seconds;
  } else if (parts.length === 3) {
    // HH:MM:SS format
    const hours = parseInt(parts[0], 10) || 0;
    const minutes = parseInt(parts[1], 10) || 0;
    const seconds = parseInt(parts[2], 10) || 0;
    return (hours * 3600) + (minutes * 60) + seconds;
  }
  
  return 0;
}

/**
 * Extract duration from embedded video player by monitoring DOM for human-time elements
 */
export function extractDurationFromVideoPlayer(
  containerId: string = 'engaxe-player-container',
  timeout: number = 10000
): Promise<number> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const checkForDuration = () => {
      // Check if we've exceeded the timeout
      if (Date.now() - startTime > timeout) {
        reject(new Error('Timeout: Could not find video duration'));
        return;
      }

      // Find the container
      const container = document.getElementById(containerId);
      if (!container) {
        // Try again after a short delay
        setTimeout(checkForDuration, 500);
        return;
      }

      // Look for the human-time span element within the container
      const humanTimeElement = container.querySelector('span.human-time[aria-hidden="true"]');
      
      if (humanTimeElement && humanTimeElement.textContent) {
        const durationText = humanTimeElement.textContent.trim();
        console.log(`Found video duration: ${durationText}`);
        
        const durationInSeconds = parseDurationToSeconds(durationText);
        if (durationInSeconds > 0) {
          resolve(durationInSeconds);
          return;
        }
      }

      // Also check for other common duration selectors
      const alternativeSelectors = [
        '.duration',
        '.video-duration',
        '.time-display',
        '[data-duration]',
        '.player-time'
      ];

      for (const selector of alternativeSelectors) {
        const element = container.querySelector(selector);
        if (element) {
          const durationText = element.textContent?.trim() || element.getAttribute('data-duration');
          if (durationText) {
            const durationInSeconds = parseDurationToSeconds(durationText);
            if (durationInSeconds > 0) {
              console.log(`Found video duration from ${selector}: ${durationText}`);
              resolve(durationInSeconds);
              return;
            }
          }
        }
      }

      // Try again after a short delay
      setTimeout(checkForDuration, 500);
    };

    // Start checking
    checkForDuration();
  });
}

/**
 * Monitor video player for duration changes using MutationObserver
 */
export function monitorVideoDuration(
  containerId: string = 'engaxe-player-container',
  onDurationFound: (duration: number) => void,
  timeout: number = 15000
): () => void {
  let observer: MutationObserver | null = null;
  let timeoutId: NodeJS.Timeout | null = null;
  let found = false;

  const cleanup = () => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  const checkForDuration = (target?: Element) => {
    if (found) return;

    const container = target || document.getElementById(containerId);
    if (!container) return;

    // Look for the human-time span element
    const humanTimeElement = container.querySelector('span.human-time[aria-hidden="true"]');
    
    if (humanTimeElement && humanTimeElement.textContent) {
      const durationText = humanTimeElement.textContent.trim();
      const durationInSeconds = parseDurationToSeconds(durationText);
      
      if (durationInSeconds > 0) {
        console.log(`Monitored video duration found: ${durationText} (${durationInSeconds}s)`);
        found = true;
        onDurationFound(durationInSeconds);
        cleanup();
        return;
      }
    }

    // Check alternative selectors
    const alternativeSelectors = [
      '.duration',
      '.video-duration', 
      '.time-display',
      '[data-duration]',
      '.player-time'
    ];

    for (const selector of alternativeSelectors) {
      const element = container.querySelector(selector);
      if (element) {
        const durationText = element.textContent?.trim() || element.getAttribute('data-duration');
        if (durationText) {
          const durationInSeconds = parseDurationToSeconds(durationText);
          if (durationInSeconds > 0) {
            console.log(`Monitored video duration found from ${selector}: ${durationText} (${durationInSeconds}s)`);
            found = true;
            onDurationFound(durationInSeconds);
            cleanup();
            return;
          }
        }
      }
    }
  };

  // Initial check
  checkForDuration();

  // Set up mutation observer to watch for changes
  const container = document.getElementById(containerId);
  if (container) {
    observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          // Check added nodes
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              checkForDuration(node as Element);
            }
          });
        } else if (mutation.type === 'attributes' || mutation.type === 'characterData') {
          checkForDuration(mutation.target as Element);
        }
      }
    });

    observer.observe(container, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true,
      attributeFilter: ['data-duration', 'aria-label']
    });
  }

  // Set timeout
  timeoutId = setTimeout(() => {
    if (!found) {
      console.warn('Video duration monitoring timed out');
      cleanup();
    }
  }, timeout);

  // Return cleanup function
  return cleanup;
}

/**
 * Format seconds back to duration string
 */
export function formatSecondsToTime(seconds: number): string {
  if (seconds <= 0) return '0:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

/**
 * Test duration extraction on current page
 */
export function testDurationExtractionOnPage(): void {
  console.log('🧪 Testing Duration Extraction on Current Page');
  console.log('=' .repeat(60));
  
  extractDurationFromVideoPlayer('engaxe-player-container', 10000)
    .then((duration) => {
      console.log(`✅ Duration extraction successful: ${duration}s (${formatSecondsToTime(duration)})`);
    })
    .catch((error) => {
      console.error(`❌ Duration extraction failed: ${error.message}`);
      
      // Try to find any time-like elements on the page
      console.log('\n🔍 Searching for any time-like elements on the page:');
      const timeRegex = /\d{1,2}:\d{2}(:\d{2})?/;
      const allElements = document.querySelectorAll('*');
      let found = false;
      
      for (let i = 0; i < allElements.length; i++) {
        const element = allElements[i];
        const text = element.textContent?.trim();
        
        if (text && timeRegex.test(text) && text.length < 20) {
          console.log(`Found time-like text: "${text}" in element:`, element);
          found = true;
        }
      }
      
      if (!found) {
        console.log('No time-like elements found on the page');
      }
    });
}

// Make functions available globally for testing
if (typeof window !== 'undefined') {
  (window as any).videoDurationExtractor = {
    parseDurationToSeconds,
    formatSecondsToTime,
    extractDurationFromVideoPlayer,
    monitorVideoDuration,
    testDurationExtractionOnPage
  };
}
