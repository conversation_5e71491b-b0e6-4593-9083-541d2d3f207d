import { FastifyInstance, FastifyPluginAsync, FastifySchema, FastifySchemaCompiler } from 'fastify';
import fastifyPlugin from 'fastify-plugin';
import { TypeCompiler } from '@sinclair/typebox/compiler';
import { ValueError } from '@sinclair/typebox/errors';
import { TSchema, Type } from '@sinclair/typebox';
import { logger } from '../utils/logger';

/**
 * Helper function to convert string query parameters to their proper types
 */
function convertQueryParams(data: any, schema: any): any {
  if (!data || typeof data !== 'object' || !schema || !schema.properties) {
    return data;
  }

  const result = { ...data };

  // Process each property in the schema
  for (const [key, propSchemaUnknown] of Object.entries(schema.properties)) {
    if (!result[key]) continue;

    // Type assertion to access properties safely
    const propSchema = propSchemaUnknown as { type?: string };

    // Handle number conversion
    if (propSchema.type === 'number' || propSchema.type === 'integer') {
      const numValue = Number(result[key]);
      if (!isNaN(numValue)) {
        result[key] = numValue;
      }
    }
    // Handle boolean conversion
    else if (propSchema.type === 'boolean') {
      if (result[key] === 'true') result[key] = true;
      else if (result[key] === 'false') result[key] = false;
    }
    // Handle array conversion (if it's a string)
    else if (propSchema.type === 'array' && typeof result[key] === 'string') {
      try {
        // Try to parse as JSON
        result[key] = JSON.parse(result[key]);
      } catch (e) {
        // If not valid JSON, split by comma
        result[key] = result[key].split(',').map((item: string) => item.trim());
      }
    }
  }

  return result;
}

/**
 * Plugin to add TypeBox schema compiler to Fastify
 */
const typeboxPlugin: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  // Set TypeBox as the schema compiler
  fastify.setValidatorCompiler(({ schema, url, method }) => {
    const compiledSchema = TypeCompiler.Compile(schema as TSchema);
    return (data) => {
      try {
        // For GET requests, convert query parameters to proper types
        if (method === 'GET' && url && schema.querystring) {
          data = convertQueryParams(data, schema.querystring);
        }

        const result = compiledSchema.Check(data);
        if (result) return true;

        // Get validation errors
        const errors = [...compiledSchema.Errors(data)];
        logger.debug('Validation errors:', errors);

        // Format errors for better readability
        const formattedErrors = errors.map(error => ({
          path: error.path,
          message: error.message,
          type: error.type,
          value: error.value
        }));

        // Create error with detailed information
        const validationError = new Error('Validation failed');
        (validationError as any).validationErrors = formattedErrors;

        return { error: validationError };
      } catch (error: any) {
        logger.error('Error during schema validation:', error);
        return { error: new Error(`Schema validation error: ${error?.message || 'Unknown error'}`) };
      }
    };
  });
};

export default fastifyPlugin(typeboxPlugin);
