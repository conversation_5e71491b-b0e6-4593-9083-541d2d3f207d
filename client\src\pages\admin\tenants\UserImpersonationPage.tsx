import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Home, Search, UserCheck, UserX, Filter, AlertTriangle, UserCog } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import { User } from '@/types';

interface TenantUser {
  id: string;
  username: string;
  email: string;
  tenantId: string;
  tenantName: string;
  role: 'admin' | 'user' | 'manager';
  status: 'active' | 'inactive';
  lastLogin: string;
  avatar: string;
}

export default function UserImpersonationPage() {
  const { theme } = useTheme();
  const { currentUser, switchAccount } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [impersonationHistory, setImpersonationHistory] = useState<{
    timestamp: string;
    adminId: string;
    adminName: string;
    userId: string;
    userName: string;
    tenantName: string;
  }[]>([
    {
      timestamp: '2023-12-15 14:32:45',
      adminId: '1',
      adminName: 'admin',
      userId: 'user-1',
      userName: 'johndoe',
      tenantName: 'LegalAid Corp'
    },
    {
      timestamp: '2023-12-14 09:15:22',
      adminId: '1',
      adminName: 'admin',
      userId: 'user-2',
      userName: 'janedoe',
      tenantName: 'Smith & Associates'
    }
  ]);

  // Sample data for tenant users
  const tenantUsers: TenantUser[] = [
    {
      id: 'user-1',
      username: 'johndoe',
      email: '<EMAIL>',
      tenantId: '1',
      tenantName: 'LegalAid Corp',
      role: 'admin',
      status: 'active',
      lastLogin: '2023-12-15 10:30:22',
      avatar: '/placeholder.svg'
    },
    {
      id: 'user-2',
      username: 'janedoe',
      email: '<EMAIL>',
      tenantId: '1',
      tenantName: 'LegalAid Corp',
      role: 'user',
      status: 'active',
      lastLogin: '2023-12-14 15:45:10',
      avatar: '/placeholder.svg'
    },
    {
      id: 'user-3',
      username: 'robertsmith',
      email: '<EMAIL>',
      tenantId: '2',
      tenantName: 'Smith & Associates',
      role: 'admin',
      status: 'active',
      lastLogin: '2023-12-15 09:12:33',
      avatar: '/placeholder.svg'
    },
    {
      id: 'user-4',
      username: 'sarahwilliams',
      email: '<EMAIL>',
      tenantId: '2',
      tenantName: 'Smith & Associates',
      role: 'manager',
      status: 'active',
      lastLogin: '2023-12-13 11:20:45',
      avatar: '/placeholder.svg'
    },
    {
      id: 'user-5',
      username: 'michaeljohnson',
      email: '<EMAIL>',
      tenantId: '3',
      tenantName: 'Johnson Legal Services',
      role: 'admin',
      status: 'inactive',
      lastLogin: '2023-12-10 14:30:15',
      avatar: '/placeholder.svg'
    }
  ];

  const handleImpersonate = (user: TenantUser) => {
    // In a real application, this would make an API call to impersonate the user
    // For this demo, we'll just show an alert
    alert(`Impersonating user: ${user.username} from tenant: ${user.tenantName}`);
    
    // Add to impersonation history
    const newHistoryEntry = {
      timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
      adminId: currentUser?.id || '1',
      adminName: currentUser?.username || 'admin',
      userId: user.id,
      userName: user.username,
      tenantName: user.tenantName
    };
    
    setImpersonationHistory([newHistoryEntry, ...impersonationHistory]);
  };

  const filteredUsers = tenantUsers.filter(user => {
    const matchesSearch = 
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.tenantName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = selectedRole === 'all' || user.role === selectedRole;
    const matchesStatus = selectedStatus === 'all' || user.status === selectedStatus;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const getRoleBadgeClass = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-purple-100 text-purple-800';
      case 'manager':
        return 'bg-blue-100 text-blue-800';
      case 'user':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex h-screen bg-[#f5f7fb] overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold mb-4">User Impersonation</h1>
            
            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-gray-600 hover:text-gray-900 flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-gray-500">&gt;</span>
              <Link to="/admin/tenants" className="text-gray-600 hover:text-gray-900">
                Tenant Management
              </Link>
              <span className="mx-2 text-gray-500">&gt;</span>
              <span className="text-[#38bdf8]">User Impersonation</span>
            </div>

            {/* Warning Banner */}
            <div className="bg-amber-50 border-l-4 border-amber-500 p-4 mb-6 rounded-md">
              <div className="flex items-start">
                <AlertTriangle className="h-6 w-6 text-amber-500 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-amber-800 font-medium">Important Information About User Impersonation</p>
                  <p className="text-amber-700 mt-1">
                    User impersonation allows you to view the application as if you were logged in as another user. 
                    This is useful for troubleshooting user-specific issues. All actions performed while impersonating 
                    a user are logged for security purposes. Use this feature responsibly.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
              <div className="p-6">
                <h2 className="text-xl font-bold mb-6">Impersonate User</h2>
                
                <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div className="flex flex-1">
                    <div className="relative flex-1">
                      <input
                        type="text"
                        placeholder="Search users by name, email, or tenant..."
                        className="w-full border border-gray-300 rounded-l-md px-4 py-2 pl-10"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
                    </div>
                    <button className="bg-[#38bdf8] text-white rounded-r-md px-6 py-2 hover:bg-blue-500 transition-colors">
                      Search
                    </button>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <Filter size={16} className="text-gray-500 mr-2" />
                      <select 
                        className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                        value={selectedRole}
                        onChange={(e) => setSelectedRole(e.target.value)}
                      >
                        <option value="all">All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="manager">Manager</option>
                        <option value="user">User</option>
                      </select>
                    </div>
                    
                    <div className="flex items-center">
                      <UserCheck size={16} className="text-gray-500 mr-2" />
                      <select 
                        className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                      >
                        <option value="all">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100 border-b border-gray-200">
                        <th className="p-3 text-left">User</th>
                        <th className="p-3 text-left">Email</th>
                        <th className="p-3 text-left">Tenant</th>
                        <th className="p-3 text-left">Role</th>
                        <th className="p-3 text-left">Status</th>
                        <th className="p-3 text-left">Last Login</th>
                        <th className="p-3 text-left">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredUsers.map((user) => (
                        <tr key={user.id} className="border-b border-gray-200 hover:bg-gray-50">
                          <td className="p-3">
                            <div className="flex items-center">
                              <img 
                                src={user.avatar} 
                                alt={user.username} 
                                className="w-8 h-8 rounded-full mr-3"
                              />
                              <span className="font-medium">{user.username}</span>
                            </div>
                          </td>
                          <td className="p-3">{user.email}</td>
                          <td className="p-3">{user.tenantName}</td>
                          <td className="p-3">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleBadgeClass(user.role)}`}>
                              {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                            </span>
                          </td>
                          <td className="p-3">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(user.status)}`}>
                              {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                            </span>
                          </td>
                          <td className="p-3">{user.lastLogin}</td>
                          <td className="p-3">
                            <button 
                              onClick={() => handleImpersonate(user)}
                              className="bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-1 rounded-md text-sm font-medium flex items-center"
                              disabled={user.status === 'inactive'}
                            >
                              <UserCog size={14} className="mr-1" />
                              Impersonate
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {filteredUsers.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No users found matching your search criteria.
                  </div>
                )}
              </div>
            </div>

            {/* Impersonation History */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="p-6">
                <h2 className="text-xl font-bold mb-6">Impersonation History</h2>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100 border-b border-gray-200">
                        <th className="p-3 text-left">Timestamp</th>
                        <th className="p-3 text-left">Admin</th>
                        <th className="p-3 text-left">User</th>
                        <th className="p-3 text-left">Tenant</th>
                      </tr>
                    </thead>
                    <tbody>
                      {impersonationHistory.map((entry, index) => (
                        <tr key={index} className="border-b border-gray-200 hover:bg-gray-50">
                          <td className="p-3">{entry.timestamp}</td>
                          <td className="p-3">{entry.adminName}</td>
                          <td className="p-3">{entry.userName}</td>
                          <td className="p-3">{entry.tenantName}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {impersonationHistory.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No impersonation history found.
                  </div>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
