import React from 'react';
import { Link } from 'react-router-dom';
import { Play, Clock } from 'lucide-react';

interface DatabaseVideoCardProps {
  video: {
    id: string;
    title: string;
    description?: string;
    thumbnailUrl?: string;
    thumbnail?: string;
    duration?: number;
    url?: string;
    stats?: {
      views?: number;
    };
    category?: string;
  };
  compact?: boolean;
}

// Parse duration string in format "MM:SS" or "HH:MM:SS" to seconds
const parseDurationToSeconds = (durationStr: string | number): number => {
  if (!durationStr) return 0;

  // If it's already a number, return it
  if (typeof durationStr === 'number') {
    return durationStr;
  }

  // If it's a string that doesn't contain colons, try to parse as number
  if (typeof durationStr === 'string' && !durationStr.includes(':')) {
    const parsed = parseInt(durationStr, 10);
    return isNaN(parsed) ? 0 : parsed;
  }

  // Handle MM:SS or HH:MM:SS format
  if (typeof durationStr === 'string') {
    const cleanDuration = durationStr.trim();
    const parts = cleanDuration.split(':');

    if (parts.length === 2) {
      // MM:SS format
      const minutes = parseInt(parts[0], 10) || 0;
      const seconds = parseInt(parts[1], 10) || 0;
      return (minutes * 60) + seconds;
    } else if (parts.length === 3) {
      // HH:MM:SS format
      const hours = parseInt(parts[0], 10) || 0;
      const minutes = parseInt(parts[1], 10) || 0;
      const seconds = parseInt(parts[2], 10) || 0;
      return (hours * 3600) + (minutes * 60) + seconds;
    }
  }

  return 0;
};

// Helper function to format duration
const formatDuration = (duration: string | number): string => {
  const seconds = parseDurationToSeconds(duration);
  if (seconds <= 0) return '0:00';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
};

export default function DatabaseVideoCard({ video, compact = false }: DatabaseVideoCardProps) {
  // Get the video URL for navigation
  const videoUrl = video.url || video.id;
  const thumbnailUrl = video.thumbnailUrl || video.thumbnail || '/placeholder.svg';
  
  // Compact layout for chat messages (horizontal)
  if (compact) {
    return (
      <div className="my-2 w-full max-w-md">
        <Link
          to={`/watch?id=${videoUrl}`}
          className="flex items-center bg-gray-50 dark:bg-gray-800 rounded-lg p-2 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow group"
        >
          {/* Thumbnail */}
          <div className="relative w-20 h-14 bg-gray-200 dark:bg-gray-700 rounded-md overflow-hidden flex-shrink-0">
            <img
              src={thumbnailUrl}
              alt={video.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
              onError={(e) => {
                e.currentTarget.src = '/placeholder.svg';
              }}
            />

            {/* Play button overlay */}
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200">
              <Play className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            </div>

            {/* Duration badge */}
            {video.duration && video.duration > 0 && (
              <div className="absolute bottom-1 right-1 bg-black bg-opacity-80 text-white text-xs px-1 py-0.5 rounded text-[10px]">
                {formatDuration(video.duration)}
              </div>
            )}
          </div>

          {/* Video info */}
          <div className="flex-1 ml-3 min-w-0">
            <h3 className="font-medium text-sm text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
              {video.title}
            </h3>
            <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 mt-1">
              {video.stats?.views && (
                <span>{video.stats.views.toLocaleString()} views</span>
              )}
              {video.category && (
                <>
                  {video.stats?.views && <span>•</span>}
                  <span>{video.category}</span>
                </>
              )}
            </div>
          </div>
        </Link>
      </div>
    );
  }

  // Regular layout for search results (vertical)
  return (
    <div className="my-4 w-full max-w-sm">
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
        <Link
          to={`/watch?id=${videoUrl}`}
          className="block group"
        >
          {/* Thumbnail */}
          <div className="relative aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden mb-3">
            <img
              src={thumbnailUrl}
              alt={video.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
              onError={(e) => {
                e.currentTarget.src = '/placeholder.svg';
              }}
            />

            {/* Play button overlay */}
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200">
              <Play className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            </div>

            {/* Duration badge */}
            {video.duration && video.duration > 0 && (
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded">
                {formatDuration(video.duration)}
              </div>
            )}
          </div>

          {/* Video info */}
          <div>
            <h3 className="font-medium text-sm text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors overflow-hidden"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}>
              {video.title}
            </h3>
            
            {/* Video metadata */}
            <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400 mt-2">
              {video.stats?.views && (
                <span>{video.stats.views.toLocaleString()} views</span>
              )}
              {video.category && (
                <>
                  {video.stats?.views && <span>•</span>}
                  <span>{video.category}</span>
                </>
              )}
            </div>

            {/* Description (optional, truncated) */}
            {video.description && (
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 overflow-hidden"
                 style={{
                   display: '-webkit-box',
                   WebkitLineClamp: 2,
                   WebkitBoxOrient: 'vertical'
                 }}>
                {video.description}
              </p>
            )}
          </div>
        </Link>
      </div>
    </div>
  );
}
