/**
 * Migration script to update all videos to use Engaxe IDs consistently
 * 
 * This script:
 * 1. Finds all videos in the database
 * 2. Ensures each video has a valid Engaxe ID as both its ID and URL
 * 3. Creates mappings for backward compatibility
 * 
 * Run with: npm run migrate-to-engaxe-ids
 */

import mongoose from 'mongoose';
import { config } from '../config';
import { VideoModel } from '../models';
import { VideoIdMappingModel } from '../models/video-id-mapping.model';
import { isValidEngaxeId, generateEngaxeId } from '../utils/id';

// Connect to MongoDB
async function connectToDatabase() {
  try {
    await mongoose.connect(config.mongodb.uri);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

// Create a mapping between a hash ID and an Engaxe ID
async function createMapping(hashId: string, engaxeId: string, videoTitle: string = 'Untitled Video') {
  try {
    // Check if a mapping already exists for this hash ID
    const existingMapping = await VideoIdMappingModel.findOne({ hashId });
    if (existingMapping) {
      console.log(`Mapping already exists for hash ID ${hashId}: ${existingMapping.engaxeId}`);
      return;
    }

    // Create the mapping
    const mapping = new VideoIdMappingModel({
      hashId,
      engaxeId,
      videoTitle,
    });

    // Save the mapping
    await mapping.save();
    console.log(`Created new mapping: ${hashId} -> ${engaxeId}`);
  } catch (error) {
    console.error(`Error creating mapping for hash ID ${hashId}:`, error);
  }
}

// Migrate all videos to use Engaxe IDs
async function migrateVideos() {
  try {
    // Get all videos
    const videos = await VideoModel.find({ deletedAt: null });
    console.log(`Found ${videos.length} videos in the database`);

    let fixedCount = 0;

    // Fix each video
    for (const video of videos) {
      let needsUpdate = false;
      let newEngaxeId = '';

      // Case 1: ID is already a valid Engaxe ID
      if (isValidEngaxeId(video.id)) {
        console.log(`Video ID ${video.id} is already a valid Engaxe ID`);
        newEngaxeId = video.id;
        
        // Ensure URL matches ID
        if (video.url !== video.id) {
          console.log(`Video URL ${video.url} doesn't match ID ${video.id}, updating URL`);
          needsUpdate = true;
        }
      }
      // Case 2: ID is not a valid Engaxe ID (legacy hash ID)
      else {
        console.log(`Video ID ${video.id} is not a valid Engaxe ID (legacy hash ID)`);
        
        // Check if URL is a valid Engaxe ID
        if (isValidEngaxeId(video.url)) {
          console.log(`Using existing valid URL ${video.url} as the new Engaxe ID`);
          newEngaxeId = video.url;
          needsUpdate = true;
        }
        // Neither ID nor URL is a valid Engaxe ID, generate a new one
        else {
          newEngaxeId = generateEngaxeId();
          console.log(`Generated new Engaxe ID: ${newEngaxeId}`);
          needsUpdate = true;
        }
        
        // Create a mapping for backward compatibility
        await createMapping(video.id, newEngaxeId, video.title || 'Untitled Video');
      }

      if (needsUpdate) {
        // Update the video URL to match the Engaxe ID
        video.url = newEngaxeId;
        
        // If the ID is not a valid Engaxe ID, update it too (for new videos)
        if (!isValidEngaxeId(video.id)) {
          // For new videos, we'll update the ID to be the Engaxe ID
          // This is a breaking change, but it's the right approach going forward
          // We've created a mapping for backward compatibility
          console.log(`Updating video ID from ${video.id} to ${newEngaxeId}`);
          video.id = newEngaxeId;
        }

        // Also update the URL in all languages
        if (video.languages && video.languages.length > 0) {
          for (const lang of video.languages) {
            console.log(`Setting language ${lang.code} URL to video's Engaxe ID: ${newEngaxeId}`);
            lang.url = newEngaxeId;
          }
        }

        // Update the source.originalUrl and source.externalId if they exist
        if (video.source) {
          if (video.source.originalUrl) {
            console.log(`Setting source.originalUrl to video's Engaxe ID: ${newEngaxeId}`);
            video.source.originalUrl = newEngaxeId;
          }
          if (video.source.externalId) {
            console.log(`Setting source.externalId to video's Engaxe ID: ${newEngaxeId}`);
            video.source.externalId = newEngaxeId;
          }
        }

        // Save the video
        await video.save();
        console.log(`Successfully updated video ${video.id}`);
        fixedCount++;
      }
    }

    // Verify that all videos now have valid Engaxe IDs
    const verifiedVideos = await VideoModel.find({ deletedAt: null });
    let validCount = 0;
    for (const video of verifiedVideos) {
      if (isValidEngaxeId(video.id) && video.id === video.url) {
        validCount++;
      } else {
        console.error(`Video ${video.id} still has issues: ID=${video.id}, URL=${video.url}`);
      }
    }

    console.log(`Migration complete: Fixed ${fixedCount} out of ${videos.length} videos`);
    console.log(`Verification result: ${validCount} out of ${verifiedVideos.length} videos have valid Engaxe IDs`);
  } catch (error) {
    console.error('Error migrating videos:', error);
  }
}

// Main function
async function main() {
  try {
    await connectToDatabase();
    await migrateVideos();
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
main();
