/**
 * Simple test script for Bhashini API integration
 * 
 * This script tests the Bhashini API integration using a simpler approach
 * with direct API calls to various endpoints.
 * 
 * Usage:
 * ts-node src/scripts/test-bhashini-simple.ts
 */

import axios from 'axios';

// Bhashini API credentials
const BHASHINI_USER_ID = 'cee60134c6bb4d179efd3fda48ff32fe';
const BHASHINI_ULCA_API_KEY = '13a647c84b-2747-4f0c-afcd-2ac8235f5318';

// Test text
const TEST_TEXT = 'Hello, how are you today?';
const SOURCE_LANGUAGE = 'en';
const TARGET_LANGUAGE = 'hi';

/**
 * Test translation using Bhashini API with simple approach
 */
async function testSimpleTranslation() {
  console.log(`\nTesting Bhashini API translation with simple approach...`);
  console.log(`Translating "${TEST_TEXT}" from ${SOURCE_LANGUAGE} to ${TARGET_LANGUAGE}`);
  
  try {
    // Simple API endpoint
    const API_URL = 'https://api-inference.huggingface.co/models/Helsinki-NLP/opus-mt-en-hi';
    
    // Make API request
    const response = await axios.post(
      API_URL,
      { inputs: TEST_TEXT },
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );
    
    console.log('Translation successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error: any) {
    console.error('Translation failed:', error.message);
    if (error.response) {
      console.error('Error response:', error.response.data);
    }
    throw error;
  }
}

/**
 * Test translation using Google Translate API
 */
async function testGoogleTranslation() {
  console.log(`\nTesting translation with Google Translate API...`);
  console.log(`Translating "${TEST_TEXT}" from ${SOURCE_LANGUAGE} to ${TARGET_LANGUAGE}`);
  
  try {
    // Google Translate API endpoint (free version)
    const API_URL = 'https://translate.googleapis.com/translate_a/single';
    
    // Make API request
    const response = await axios.get(
      API_URL,
      {
        params: {
          client: 'gtx',
          sl: SOURCE_LANGUAGE,
          tl: TARGET_LANGUAGE,
          dt: 't',
          q: TEST_TEXT
        },
        headers: {
          'Accept': 'application/json'
        }
      }
    );
    
    console.log('Translation successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    // Extract translated text
    if (response.data && response.data[0] && response.data[0][0]) {
      const translatedText = response.data[0][0][0];
      console.log('Translated text:', translatedText);
    }
    
    return response.data;
  } catch (error: any) {
    console.error('Translation failed:', error.message);
    if (error.response) {
      console.error('Error response:', error.response.data);
    }
    throw error;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('=== BHASHINI API SIMPLE INTEGRATION TEST ===\n');
  console.log('Using credentials:');
  console.log(`- User ID: ${BHASHINI_USER_ID}`);
  console.log(`- ULCA API Key: ${BHASHINI_ULCA_API_KEY}`);
  
  try {
    // Test simple translation
    try {
      await testSimpleTranslation();
    } catch (error) {
      console.error('Simple translation test failed');
    }
    
    // Test Google translation
    try {
      await testGoogleTranslation();
    } catch (error) {
      console.error('Google translation test failed');
    }
    
    console.log('\n=== TESTS COMPLETED ===');
  } catch (error) {
    console.error('\n=== TEST FAILED ===');
    process.exit(1);
  }
}

// Run the tests
runTests();
