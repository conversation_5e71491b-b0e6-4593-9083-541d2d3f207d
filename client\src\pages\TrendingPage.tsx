
import { useVideos } from '@/context/VideoContext';
import Layout from '@/components/layout/Layout';
import VideoCard from '@/components/video/VideoCard';

export default function TrendingPage() {
  const { trendingVideos } = useVideos();

  return (
    <Layout>
      <h1 className="text-2xl font-bold mb-6">Trending Videos</h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {trendingVideos.map((video) => (
          <VideoCard key={video.id} video={video} />
        ))}
      </div>
    </Layout>
  );
}
