import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Home,
  ArrowUpDown,
  Edit,
  Trash2,
  Plus,
  Search,
  FileText,
  Calendar,
  Eye,
  EyeOff,
  Filter,
  MoreHorizontal
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

// Import shadcn components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Enhanced page model
interface CustomPage {
  id: string;
  pageName: string;
  pageTitle: string;
  status: 'published' | 'draft' | 'archived';
  createdAt: string;
  updatedAt: string;
  views: number;
}

// Mock data for custom pages
const mockPages: CustomPage[] = [
  {
    id: '1',
    pageName: 'about-us',
    pageTitle: 'About Our Organization',
    status: 'published',
    createdAt: '2023-09-15T10:30:00Z',
    updatedAt: '2023-10-20T14:45:00Z',
    views: 1245
  },
  {
    id: '2',
    pageName: 'privacy-policy',
    pageTitle: 'Privacy Policy',
    status: 'published',
    createdAt: '2023-08-05T09:15:00Z',
    updatedAt: '2023-11-10T11:20:00Z',
    views: 876
  },
  {
    id: '3',
    pageName: 'terms-of-service',
    pageTitle: 'Terms of Service',
    status: 'published',
    createdAt: '2023-07-22T13:45:00Z',
    updatedAt: '2023-10-18T16:30:00Z',
    views: 932
  },
  {
    id: '4',
    pageName: 'contact-information',
    pageTitle: 'Contact Information',
    status: 'draft',
    createdAt: '2023-11-01T08:20:00Z',
    updatedAt: '2023-11-05T10:15:00Z',
    views: 0
  },
  {
    id: '5',
    pageName: 'faq',
    pageTitle: 'Frequently Asked Questions',
    status: 'archived',
    createdAt: '2023-06-10T15:30:00Z',
    updatedAt: '2023-09-25T09:45:00Z',
    views: 543
  }
];

export default function ManageCustomPagesPage() {
  const [pages, setPages] = useState<CustomPage[]>(mockPages);
  const [selectedPages, setSelectedPages] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof CustomPage>('id');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [showAddEditDialog, setShowAddEditDialog] = useState(false);
  const [currentPage, setCurrentPage] = useState<CustomPage | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Filter pages based on search term and status
  const filteredPages = pages.filter(page => {
    const matchesSearch =
      page.pageName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.pageTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || page.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Sort pages
  const sortedPages = [...filteredPages].sort((a, b) => {
    const fieldA = a[sortField];
    const fieldB = b[sortField];

    if (fieldA < fieldB) return sortDirection === 'asc' ? -1 : 1;
    if (fieldA > fieldB) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Handle sort
  const handleSort = (field: keyof CustomPage) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Handle page selection
  const handleSelectPage = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedPages([...selectedPages, id]);
    } else {
      setSelectedPages(selectedPages.filter(pageId => pageId !== id));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPages(sortedPages.map(page => page.id));
    } else {
      setSelectedPages([]);
    }
  };

  // Handle search
  const handleSearch = () => {
    // In a real app, this would trigger an API call or filter the data
    console.log('Searching for:', searchTerm);
  };

  // Handle delete selected
  const handleDeleteSelected = () => {
    if (selectedPages.length === 0) return;

    if (window.confirm('Are you sure you want to delete the selected pages?')) {
      setPages(pages.filter(page => !selectedPages.includes(page.id)));
      setSelectedPages([]);
    }
  };

  // Handle edit
  const handleEdit = (page: CustomPage) => {
    setCurrentPage(page);
    setShowAddEditDialog(true);
  };

  // Handle delete
  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this page?')) {
      setPages(pages.filter(page => page.id !== id));
      setSelectedPages(selectedPages.filter(pageId => pageId !== id));
    }
  };

  // Handle add new page
  const handleAddPage = () => {
    setCurrentPage(null);
    setShowAddEditDialog(true);
  };

  // Handle save page
  const handleSavePage = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    const newPage: CustomPage = {
      id: currentPage?.id || (pages.length + 1).toString(),
      pageName: formData.get('pageName') as string,
      pageTitle: formData.get('pageTitle') as string,
      status: formData.get('status') as 'published' | 'draft' | 'archived',
      createdAt: currentPage?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      views: currentPage?.views || 0
    };

    if (currentPage) {
      // Update existing page
      setPages(pages.map(page => page.id === currentPage.id ? newPage : page));
    } else {
      // Add new page
      setPages([...pages, newPage]);
    }

    setShowAddEditDialog(false);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Published</Badge>;
      case 'draft':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Draft</Badge>;
      case 'archived':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-200">Archived</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header and Breadcrumb */}
            <div>
              <h1 className="text-2xl font-bold mb-2">Manage Custom Pages</h1>

              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="/admin" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Admin Panel
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Content</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Pages</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Manage Custom Pages</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {/* Main Content */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div>
                  <CardTitle>Manage & Edit Custom Pages</CardTitle>
                  <CardDescription>
                    Create and manage custom pages for your website
                  </CardDescription>
                </div>
                <Button onClick={handleAddPage} className="flex items-center gap-1">
                  <Plus className="h-4 w-4" /> Create New Page
                </Button>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Search and Filter */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="search">Search Pages</Label>
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="search"
                        placeholder="Search by name or title..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>

                  <div className="w-full sm:w-48 space-y-2">
                    <Label htmlFor="status-filter">Filter by Status</Label>
                    <Select
                      value={statusFilter}
                      onValueChange={setStatusFilter}
                    >
                      <SelectTrigger id="status-filter" className="w-full">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="published">Published</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Table */}
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedPages.length === sortedPages.length && sortedPages.length > 0}
                            onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                          />
                        </TableHead>
                        <TableHead
                          className="cursor-pointer"
                          onClick={() => handleSort('id')}
                        >
                          <div className="flex items-center">
                            ID <ArrowUpDown className="ml-1 h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer"
                          onClick={() => handleSort('pageName')}
                        >
                          <div className="flex items-center">
                            Page Name <ArrowUpDown className="ml-1 h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer"
                          onClick={() => handleSort('pageTitle')}
                        >
                          <div className="flex items-center">
                            Page Title <ArrowUpDown className="ml-1 h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer"
                          onClick={() => handleSort('status')}
                        >
                          <div className="flex items-center">
                            Status <ArrowUpDown className="ml-1 h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer"
                          onClick={() => handleSort('updatedAt')}
                        >
                          <div className="flex items-center">
                            Last Updated <ArrowUpDown className="ml-1 h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer"
                          onClick={() => handleSort('views')}
                        >
                          <div className="flex items-center">
                            Views <ArrowUpDown className="ml-1 h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {sortedPages.length > 0 ? (
                        sortedPages.map((page) => (
                          <TableRow key={page.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedPages.includes(page.id)}
                                onCheckedChange={(checked) => handleSelectPage(page.id, checked as boolean)}
                              />
                            </TableCell>
                            <TableCell>{page.id}</TableCell>
                            <TableCell className="font-medium">{page.pageName}</TableCell>
                            <TableCell>{page.pageTitle}</TableCell>
                            <TableCell>{getStatusBadge(page.status)}</TableCell>
                            <TableCell className="whitespace-nowrap">
                              <div className="flex items-center">
                                <Calendar className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                                {formatDate(page.updatedAt)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Eye className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                                {page.views.toLocaleString()}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200"
                                  onClick={() => handleEdit(page)}
                                >
                                  <Edit className="h-3.5 w-3.5 mr-1" /> Edit
                                </Button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      className="text-blue-600"
                                      onClick={() => handleEdit(page)}
                                    >
                                      <Edit className="h-4 w-4 mr-2" /> Edit
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      className="text-red-600"
                                      onClick={() => handleDelete(page.id)}
                                    >
                                      <Trash2 className="h-4 w-4 mr-2" /> Delete
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem>
                                      <Eye className="h-4 w-4 mr-2" /> View Page
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                            No custom pages found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>

              <CardFooter className="flex justify-between">
                <div>
                  {selectedPages.length > 0 && (
                    <Button
                      variant="destructive"
                      onClick={handleDeleteSelected}
                      className="flex items-center gap-1"
                    >
                      <Trash2 className="h-4 w-4" /> Delete Selected ({selectedPages.length})
                    </Button>
                  )}
                </div>
                <div className="text-sm text-muted-foreground">
                  Showing {sortedPages.length} of {pages.length} pages
                </div>
              </CardFooter>
            </Card>
          </div>
        </main>
      </div>

      {/* Add/Edit Page Dialog */}
      <Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{currentPage ? 'Edit Page' : 'Create New Page'}</DialogTitle>
            <DialogDescription>
              {currentPage
                ? 'Update the details for this custom page.'
                : 'Fill in the details to create a new custom page.'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSavePage}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="pageName" className="text-right">
                  Page Name
                </Label>
                <Input
                  id="pageName"
                  name="pageName"
                  placeholder="about-us"
                  defaultValue={currentPage?.pageName || ''}
                  className="col-span-3"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="pageTitle" className="text-right">
                  Page Title
                </Label>
                <Input
                  id="pageTitle"
                  name="pageTitle"
                  placeholder="About Our Organization"
                  defaultValue={currentPage?.pageTitle || ''}
                  className="col-span-3"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="text-right">
                  Status
                </Label>
                <Select
                  name="status"
                  defaultValue={currentPage?.status || 'draft'}
                >
                  <SelectTrigger id="status" className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="content" className="text-right pt-2">
                  Content
                </Label>
                <Textarea
                  id="content"
                  name="content"
                  placeholder="Page content goes here..."
                  className="col-span-3 min-h-[150px]"
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowAddEditDialog(false)}>
                Cancel
              </Button>
              <Button type="submit">
                {currentPage ? 'Update Page' : 'Create Page'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
