
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Video } from '@/types';
import { formatDistanceToNow } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { FallbackAvatar } from '@/components/ui/fallback-avatar';
import { useWatchlist, WatchlistVideo } from '@/context/WatchlistContext';
import { useLanguage } from '@/context/LanguageContext';
import { useToast } from '@/hooks/use-toast';
import { Clock, Check, Globe, Bookmark, BookmarkCheck } from 'lucide-react';
import { getInitials, handleAvatarError } from '@/lib/avatar-utils';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { videoAPI } from '@/services/api';

interface VideoCardProps {
  video: Video;
}

export default function VideoCard({ video }: VideoCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const { addToWatchlist, isInWatchlist, removeFromWatchlist } = useWatchlist();
  const { t } = useLanguage();
  const { toast } = useToast();
  const { user } = useAuth();

  // Using the shared getInitials function from avatar-utils

  // Function to check if video is saved
  const checkIfVideoIsSaved = async () => {
    try {
      if (!user) {
        // Check local storage for non-authenticated users
        const savedVideos = JSON.parse(localStorage.getItem('savedVideos') || '[]');
        const isAlreadySaved = savedVideos.some((savedVideo: any) =>
          savedVideo.id === video.id || savedVideo.url === video.url
        );
        setIsSaved(isAlreadySaved);
      } else {
        // For authenticated users, check their channels
        const channelsResponse = await videoAPI.getUserChannels();
        if (channelsResponse && channelsResponse.length > 0) {
          const channelId = channelsResponse[0].id;
          const response = await videoAPI.getChannelVideos(channelId);
          if (response && response.videos) {
            const isAlreadySaved = response.videos.some((savedVideo: any) =>
              savedVideo.id === video.id || savedVideo.url === video.url
            );
            setIsSaved(isAlreadySaved);
          }
        }
      }
    } catch (error) {
      console.error('Error checking if video is saved:', error);
    }
  };

  // Check if video is saved on component mount and when user changes
  useEffect(() => {
    checkIfVideoIsSaved();
  }, [user, video.id]);

  // Listen for video saved events to update the state
  useEffect(() => {
    const handleVideoSaved = () => {
      checkIfVideoIsSaved();
    };

    window.addEventListener('videoSaved', handleVideoSaved);
    return () => {
      window.removeEventListener('videoSaved', handleVideoSaved);
    };
  }, []);

  // Validate the video object to prevent rendering errors
  if (!video || !video.id || !video.title || !video.creator || !video.languages) {
    console.error('Invalid video object:', video);
    return (
      <div className="bg-gray-800 rounded-lg p-4 h-full">
        <div className="text-red-500">Error: Invalid video data</div>
      </div>
    );
  }

  const handleWatchlistToggle = () => {
    const watchlistVideo: WatchlistVideo = {
      id: video.id,
      title: video.title,
      thumbnail: video.thumbnail,
      creator: {
        name: video.creator.username,
        avatar: video.creator.avatar
      },
      views: video.views,
      duration: video.duration || '0:00',
      addedAt: new Date().toISOString()
    };

    if (isInWatchlist(video.id)) {
      removeFromWatchlist(video.id);
      toast({
        title: "Removed from watchlist",
        description: "Video has been removed from your watchlist"
      });
    } else {
      addToWatchlist(watchlistVideo);
      toast({
        title: "Added to watchlist",
        description: "Video has been added to your watchlist"
      });
    }
  };

  const handleSaveVideo = async () => {
    try {
      setIsSaving(true);

      if (!user) {
        // Handle local storage for non-authenticated users
        const savedVideos = JSON.parse(localStorage.getItem('savedVideos') || '[]');

        // Check if video is already saved
        const existingVideoIndex = savedVideos.findIndex((savedVideo: any) =>
          savedVideo.id === video.id || savedVideo.url === video.url
        );

        if (existingVideoIndex !== -1) {
          // Video is already saved, so remove it (unsave)
          savedVideos.splice(existingVideoIndex, 1);
          localStorage.setItem('savedVideos', JSON.stringify(savedVideos));
          setIsSaved(false);
          // Dispatch custom event to notify SavedVideos component
          window.dispatchEvent(new CustomEvent('videoUnsaved'));
          toast({
            title: "Video Unsaved",
            description: "Video has been unsaved"
          });
        } else {
          // Video is not saved, so save it
          const videoToSave = {
            id: video.id,
            url: video.url,
            title: video.title,
            description: video.description,
            thumbnail: video.thumbnail,
            creator: video.creator,
            views: video.views,
            likes: video.likes,
            createdAt: video.createdAt,
            languages: video.languages,
            category: video.category,
            savedAt: new Date().toISOString()
          };

          savedVideos.push(videoToSave);
          localStorage.setItem('savedVideos', JSON.stringify(savedVideos));
          setIsSaved(true);
          // Dispatch custom event to notify SavedVideos component
          window.dispatchEvent(new CustomEvent('videoSaved'));
          toast({
            title: "Video Saved",
            description: "Video has been saved"
          });
        }
        return;
      }

      // For authenticated users, handle save/unsave to their channel
      const channelsResponse = await videoAPI.getUserChannels();

      if (!channelsResponse || !channelsResponse.length) {
        // Create a default channel if the user doesn't have one
        const username = user.username || user.email?.split('@')[0] || 'user';
        const defaultChannelName = `${username}-channel`;

        try {
          const createChannelResponse = await videoAPI.createChannel({
            name: defaultChannelName,
            description: `Default channel for ${username}`,
            visibility: 'public'
          });

          if (createChannelResponse && createChannelResponse.id) {
            // Save the video to the newly created channel (can't unsave if no channel exists)
            const saveResponse = await videoAPI.saveEngaxeVideo(
              video.url || video.id,
              createChannelResponse.id,
              video.languages
            );

            if (saveResponse && saveResponse.success) {
              setIsSaved(true);
              toast({
                title: "Video Saved",
                description: "Video has been saved to your channel"
              });
            } else {
              throw new Error(saveResponse?.message || "Failed to save video");
            }
          } else {
            throw new Error("Failed to create a default channel");
          }
        } catch (error) {
          console.error("Error creating default channel:", error);
          toast({
            title: "Error",
            description: error.message || "Could not create a default channel",
            variant: "destructive"
          });
        }
      } else {
        // Use the first channel if the user has channels
        const channelId = channelsResponse[0].id;

        // Check if video is already saved in the channel
        const channelVideosResponse = await videoAPI.getChannelVideos(channelId);
        const existingVideo = channelVideosResponse?.videos?.find((savedVideo: any) =>
          savedVideo.id === video.id || savedVideo.url === video.url
        );

        if (existingVideo) {
          // Video is already saved, so remove it (unsave)
          try {
            const deleteResponse = await videoAPI.deleteVideo(existingVideo.id);
            if (deleteResponse && deleteResponse.success) {
              setIsSaved(false);
              toast({
                title: "Video Unsaved",
                description: "Video has been removed from your channel"
              });
            } else {
              throw new Error(deleteResponse?.message || "Failed to unsave video");
            }
          } catch (error) {
            console.error("Error unsaving video:", error);
            toast({
              title: "Error",
              description: error.message || "Could not unsave the video",
              variant: "destructive"
            });
          }
        } else {
          // Video is not saved, so save it
          try {
            const saveResponse = await videoAPI.saveEngaxeVideo(
              video.url || video.id,
              channelId,
              video.languages
            );

            if (saveResponse && saveResponse.success) {
              setIsSaved(true);
              toast({
                title: "Video Saved",
                description: "Video has been saved to your channel"
              });
            } else {
              throw new Error(saveResponse?.message || "Failed to save video");
            }
          } catch (error) {
            console.error("Error saving video:", error);
            toast({
              title: "Error",
              description: error.message || "Could not save the video",
              variant: "destructive"
            });
          }
        }
      }
    } catch (error) {
      console.error("Error in save video flow:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred while saving the video",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // No need for a separate YouTube handler - we'll use the VideoPlayer component

  // Render the video thumbnail section
  const renderThumbnail = () => (
    <div className="relative aspect-video overflow-hidden bg-gray-800">
      <img
        src={video.thumbnail || video.thumbnailUrl || '/placeholder.svg'}
        alt={video.title}
        className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
        onError={(e) => {
          console.warn(`Failed to load thumbnail for video ${video.id}`, e);
          // Try to load from a different property if available
          if (e.currentTarget.src !== '/placeholder.svg') {
            console.log(`Attempting to use fallback image for video ${video.id}`);
            e.currentTarget.src = '/placeholder.svg';
          }
        }}
      />
      {video.isLive && (
        <div className="absolute top-2 left-2 bg-lingstream-live rounded-full px-2 py-0.5 text-xs font-medium text-white flex items-center gap-1">
          <span className="h-2 w-2 rounded-full bg-white animate-pulse-dot"></span>
          LIVE
        </div>
      )}
      {video.savedLocally && (
        <div className={`absolute ${video.isLive ? 'top-8' : 'top-2'} left-2 bg-yellow-500 rounded-full px-2 py-0.5 text-xs font-medium text-white flex items-center gap-1`}>
          LOCAL
        </div>
      )}
      <div className="absolute bottom-2 right-2 bg-white border border-black text-black rounded-full px-2 py-1 text-xs flex items-center gap-1">
        <Globe className="h-3 w-3" />
        <span>
          {Array.isArray(video.languages) ? video.languages.length : 0} {Array.isArray(video.languages) && video.languages.length === 1 ? t('video.language') : t('video.languages')}
        </span>
      </div>

      {/* Watchlist button */}
      <Button
        variant="ghost"
        size="icon"
        className={`absolute top-2 right-12 h-8 w-8 rounded-full bg-white border border-black text-black ${isHovered ? 'opacity-100' : 'opacity-0'} transition-opacity`}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          handleWatchlistToggle();
        }}
      >
        {isInWatchlist(video.id) ? (
          <Check className="h-4 w-4 text-green-500" />
        ) : (
          <Clock className="h-4 w-4" />
        )}
      </Button>

      {/* Save button */}
      <Button
        variant="ghost"
        size="icon"
        className={`absolute top-2 right-2 h-8 w-8 rounded-full bg-white border border-black text-black ${isHovered ? 'opacity-100' : 'opacity-0'} transition-opacity`}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          handleSaveVideo();
        }}
        disabled={isSaving}
      >
        {isSaving ? (
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
        ) : isSaved ? (
          <BookmarkCheck className="h-4 w-4 text-green-500" />
        ) : (
          <Bookmark className="h-4 w-4" />
        )}
      </Button>
    </div>
  );

  // Render the video title
  const renderTitle = () => {
    // Safely get creator info with fallbacks
    const creatorName = video.creator?.username || 'Unknown';
    const creatorAvatar = video.creator?.avatar || '/placeholder.svg';
    const creatorId = video.creator?.id || 'unknown';

    // Safely format date
    let formattedDate = 'Unknown date';
    try {
      if (video.createdAt) {
        formattedDate = formatDistanceToNow(new Date(video.createdAt), { addSuffix: true });
      }
    } catch (error) {
      console.warn(`Failed to format date for video ${video.id}:`, error);
    }

    return (
      <div className="mt-2">
        <div className="flex gap-2">
          {/* Standardized avatar implementation */}
          <Link to={`/channel/${video.creator?.username || creatorId}`}>
            <FallbackAvatar name={creatorName} size="sm" className="h-8 w-8" />
          </Link>
          <div>
            <Link
              to={`/video/${video.url}`}
              className="font-medium line-clamp-2 text-sm leading-tight hover:text-lingstream-accent"
              state={{
                video: video.savedLocally ? video : undefined
              }}
            >
              {video.title}
            </Link>
            <div className="mt-1 text-xs text-lingstream-muted">
              <Link to={`/channel/${video.creator?.username || creatorId}`} className="hover:text-lingstream-accent">
                {creatorName}
              </Link>
              <div className="flex items-center gap-1">
                <span>{(video.views || 0).toLocaleString()} {t('video.views')}</span>
                <span>•</span>
                <span>{formattedDate}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Use try-catch to handle any rendering errors
  try {
    return (
      <div className="group" onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
        <Link
          to={`/video/${video.url}`}
          className="block overflow-hidden rounded-lg"
          state={{
            video: video.savedLocally ? video : undefined
          }}
        >
          {renderThumbnail()}
        </Link>
        {renderTitle()}
      </div>
    );
  } catch (error) {
    console.error(`Error rendering VideoCard for video ${video?.id || 'unknown'}:`, error);
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <div className="text-red-500">Error rendering video</div>
      </div>
    );
  }
}
