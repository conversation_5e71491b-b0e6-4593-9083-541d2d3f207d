import { FastifyRequest, FastifyReply, RouteGenericInterface } from 'fastify';
import { likeService } from '../services/like.service';
import { AuthenticatedUser } from '../types/user';

// Define a custom request type with authenticated user
interface AuthenticatedRequest<T extends RouteGenericInterface = RouteGenericInterface> extends FastifyRequest<T> {
  user: AuthenticatedUser;
}

/**
 * Controller for like-related operations
 */
export class LikeController {
  /**
   * Toggle like for a video
   */
  toggleLike = async (request: AuthenticatedRequest<{
    Params: { videoId: string }
  }>, reply: FastifyReply) => {
    try {
      const { videoId } = request.params;
      const userId = request.user.id;

      const result = await likeService.toggleLike(videoId, userId);

      return reply.code(200).send({
        success: true,
        isLiked: result.isLiked,
        likeCount: result.likeCount,
      });
    } catch (error: any) {
      console.error('Error toggling like:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to toggle like',
      });
    }
  };

  /**
   * Check if a user has liked a video
   */
  hasUserLiked = async (request: AuthenticatedRequest<{
    Params: { videoId: string }
  }>, reply: FastifyReply) => {
    try {
      const { videoId } = request.params;
      const userId = request.user.id;

      const isLiked = await likeService.hasUserLiked(videoId, userId);

      return reply.code(200).send({
        success: true,
        isLiked,
      });
    } catch (error: any) {
      console.error('Error checking if user liked video:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to check if user liked video',
      });
    }
  };

  /**
   * Get like count for a video
   */
  getLikeCount = async (request: FastifyRequest<{
    Params: { videoId: string }
  }>, reply: FastifyReply) => {
    try {
      const { videoId } = request.params;

      const likeCount = await likeService.getLikeCount(videoId);

      return reply.code(200).send({
        success: true,
        likeCount,
      });
    } catch (error: any) {
      console.error('Error getting like count:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get like count',
      });
    }
  };
}
