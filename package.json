{"name": "lawengaxe-main", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "clean:all": "npm run clean:client && npm run clean:server && npm run clean:root", "clean:root": "rm -rf node_modules package-lock.json", "clean:client": "cd client && rm -rf node_modules dist package-lock.json && cd..", "clean:server": "cd server && rm -rf node_modules dist package-lock.json && cd..", "fresh-install": "npm run clean:all && npm run install:fresh", "install:fresh": "npm install && npm run install:server && npm run install:client && npm run setup:env", "install": "npm run install:server && npm run install:client && npm run setup:env", "install:server": "cd server && npm install && cd..", "install:client": "cd client && npm install && cd..", "install:python": "pip install -r server/requirements.txt", "setup:env": "node scripts/setup-environment.js", "dev": "concurrently \"cd server && npm run dev\" \"cd client && npm run dev\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "dev:server:port": "cd server && npm run dev -- --port", "dev:client:port": "cd client && npm run dev -- --port", "start": "concurrently \"cd server && npm run start\" \"cd client && npm run build && npm run preview\"", "start:server": "cd server && npm run start", "start:client": "cd client && npm run build && npm run preview", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "clean": "npm run clean:client && npm run clean:server", "organize": "node scripts/organize-data.js", "postinstall": "npm run setup:env"}, "keywords": ["legal", "ai", "translation", "<PERSON><PERSON><PERSON>", "multilingual"], "author": "LawEngaxe Team", "license": "ISC", "description": "LawEngaxe - AI-powered multilingual legal assistance platform", "dependencies": {"axios": "^1.9.0", "react-intersection-observer": "^9.16.0"}, "devDependencies": {"concurrently": "^9.1.2", "vite": "^5.3.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}