import { useState, useCallback, useEffect } from 'react';
import { translateText, isLanguageSupported } from '../utils/bhashiniTranslation';

interface UseTranslationOptions {
  defaultSourceLang?: string;
  defaultTargetLang?: string;
  autoTranslate?: boolean;
}

/**
 * Custom hook for handling translations in the application
 */
export function useTranslation({
  defaultSourceLang = 'en',
  defaultTargetLang = 'hi',
  autoTranslate = false
}: UseTranslationOptions = {}) {
  const [sourceLang, setSourceLang] = useState<string>(defaultSourceLang);
  const [targetLang, setTargetLang] = useState<string>(defaultTargetLang);
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const [translationEnabled, setTranslationEnabled] = useState<boolean>(autoTranslate);
  const [error, setError] = useState<string | null>(null);

  // Reset error when languages change
  useEffect(() => {
    setError(null);
  }, [sourceLang, targetLang]);

  /**
   * Translate text from source language to target language
   */
  const translate = useCallback(async (text: string): Promise<string> => {
    if (!text || sourceLang === targetLang) {
      return text;
    }

    // Verify languages are supported
    if (!isLanguageSupported(sourceLang) || !isLanguageSupported(targetLang)) {
      const unsupportedLang = !isLanguageSupported(sourceLang) ? sourceLang : targetLang;
      const errorMsg = `Language '${unsupportedLang}' is not supported for translation`;
      setError(errorMsg);
      return text;
    }

    setIsTranslating(true);
    setError(null);

    try {
      const translatedText = await translateText(text, sourceLang, targetLang);
      
      // Check if result starts with "Error:" which indicates translation failed
      if (translatedText.startsWith('Error:')) {
        setError(translatedText);
        return text;
      }
      
      return translatedText;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : String(err);
      setError(`Translation failed: ${errorMsg}`);
      return text;
    } finally {
      setIsTranslating(false);
    }
  }, [sourceLang, targetLang]);

  /**
   * Handle language change from language selector
   */
  const handleLanguageChange = useCallback((lang: string) => {
    // If the selected language is the same as source, do nothing
    if (lang === sourceLang) return;
    
    // Update target language
    setTargetLang(lang);
    
    // Enable translation if selecting a non-English language
    if (lang !== 'en') {
      setTranslationEnabled(true);
    } else {
      // If switching to English, set it as source and disable translation
      setSourceLang('en');
      setTranslationEnabled(false);
    }
  }, [sourceLang]);

  /**
   * Toggle translation on/off
   */
  const toggleTranslation = useCallback(() => {
    setTranslationEnabled(prev => !prev);
  }, []);

  return {
    sourceLang,
    targetLang,
    isTranslating,
    translationEnabled,
    error,
    translate,
    handleLanguageChange,
    toggleTranslation,
    setSourceLang,
    setTargetLang,
    setTranslationEnabled
  };
}

export default useTranslation;
