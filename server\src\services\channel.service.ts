import { ChannelModel, IChannel } from '../models';
import { AppError, createNotFoundError, createConflictError, createBadRequestError, createForbiddenError } from '../utils/errors';
import mongoose from 'mongoose';

/**
 * Service for channel-related operations
 */
export class ChannelService {
  /**
   * Create a new channel
   */
  async createChannel(userId: string, channelData: {
    name: string;
    displayName?: string;
    description: string;
    visibility?: 'public' | 'private' | 'unlisted';
    avatar?: string;
    banner?: string;
    category?: string;
    tags?: string[];
    socialLinks?: {
      website?: string;
      facebook?: string;
      twitter?: string;
      instagram?: string;
      linkedin?: string;
      youtube?: string;
    };
    settings?: {
      defaultCommentsEnabled?: boolean;
      moderateComments?: boolean;
      showSubscriberCount?: boolean;
    };
  }): Promise<IChannel> {
    try {
      // Check if channel name already exists
      const existingChannel = await ChannelModel.findOne({
        name: channelData.name.toLowerCase(),
        deletedAt: null
      });

      if (existingChannel) {
        throw new AppError('Channel name already in use', 409, 'CONFLICT');
      }

      // Create new channel
      const channel = new ChannelModel({
        ...channelData,
        name: channelData.name.toLowerCase(),
        displayName: channelData.displayName || channelData.name,
        ownerId: userId,
        moderators: [userId],
        stats: {
          subscribers: 0,
          totalViews: 0,
          videoCount: 0
        }
      });

      await channel.save();
      return channel;
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }

      if (error.name === 'MongoServerError' && error.code === 11000) {
        throw createConflictError('Channel name already in use', 'CONFLICT');
      }

      throw createBadRequestError('Failed to create channel', 'BAD_REQUEST', error);
    }
  }

  /**
   * Update an existing channel
   */
  async updateChannel(userId: string, channelId: string, channelData: {
    name?: string;
    displayName?: string;
    description?: string;
    avatar?: string;
    banner?: string;
    category?: string;
    tags?: string[];
    socialLinks?: {
      website?: string;
      facebook?: string;
      twitter?: string;
      instagram?: string;
      linkedin?: string;
      youtube?: string;
    };
    settings?: {
      defaultCommentsEnabled?: boolean;
      moderateComments?: boolean;
      showSubscriberCount?: boolean;
    };
  }): Promise<IChannel> {
    try {
      // Find channel
      const channel = await ChannelModel.findOne({
        _id: channelId,
        deletedAt: null
      });

      if (!channel) {
        throw createNotFoundError('Channel not found', 'NOT_FOUND');
      }

      // Check if user is owner or moderator
      if (channel.ownerId !== userId && !channel.moderators.includes(userId)) {
        throw createForbiddenError('You do not have permission to update this channel', 'FORBIDDEN');
      }

      // If name is being updated, check if it's already in use
      if (channelData.name && channelData.name !== channel.name) {
        const existingChannel = await ChannelModel.findOne({
          name: channelData.name.toLowerCase(),
          _id: { $ne: channelId },
          deletedAt: null
        });

        if (existingChannel) {
          throw createConflictError('Channel name already in use', 'CONFLICT');
        }

        // Convert name to lowercase
        channelData.name = channelData.name.toLowerCase();
      }

      // Update channel
      Object.assign(channel, channelData);
      await channel.save();

      return channel;
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }

      if (error.name === 'MongoServerError' && error.code === 11000) {
        throw createConflictError('Channel name already in use', 'CONFLICT');
      }

      throw createBadRequestError('Failed to update channel', 'BAD_REQUEST', error);
    }
  }

  /**
   * Get a channel by ID
   */
  async getChannelById(channelId: string): Promise<IChannel> {
    try {
      const channel = await ChannelModel.findOne({
        _id: channelId,
        deletedAt: null
      });

      if (!channel) {
        throw createNotFoundError('Channel not found', 'NOT_FOUND');
      }

      return channel;
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }

      throw createBadRequestError('Failed to get channel', 'BAD_REQUEST', error);
    }
  }

  /**
   * Get a channel by name
   */
  async getChannelByName(channelName: string): Promise<IChannel> {
    try {
      const channel = await ChannelModel.findOne({
        name: channelName.toLowerCase(),
        deletedAt: null
      });

      if (!channel) {
        throw createNotFoundError('Channel not found', 'NOT_FOUND');
      }

      return channel;
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }

      throw createBadRequestError('Failed to get channel', 'BAD_REQUEST', error);
    }
  }

  /**
   * Get channels with pagination and filtering
   */
  async getChannels(params: {
    page?: number;
    limit?: number;
    sort?: 'newest' | 'popular' | 'trending';
    category?: string;
    featured?: boolean | string;
    search?: string;
    visibility?: 'public' | 'private' | 'unlisted';
    userId?: string;
  } = {}): Promise<{ channels: IChannel[], pagination: { total: number, page: number, limit: number, pages: number } }> {
    try {
      const {
        page = 1,
        limit = 10,
        sort = 'newest',
        category,
        featured,
        search,
        visibility = 'public',
        userId
      } = params;

      // Build query
      const query: any = { deletedAt: null };

      if (category) {
        query.category = category;
      }

      if (featured !== undefined) {
        // Convert string 'true'/'false' to boolean if needed
        if (typeof featured === 'string') {
          query.isFeatured = featured === 'true';
        } else {
          query.isFeatured = featured;
        }
      }

      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { displayName: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      // Handle visibility
      if (userId) {
        // If userId is provided, show public channels and user's own channels
        query.$or = query.$or || [];
        query.$or.push(
          { visibility: 'public' },
          { ownerId: userId }
        );
      } else {
        // If no userId, only show public channels
        query.visibility = visibility;
      }

      // Build sort
      let sortOptions: any = {};
      switch (sort) {
        case 'popular':
          sortOptions = { 'stats.subscribers': -1 };
          break;
        case 'trending':
          sortOptions = { 'stats.totalViews': -1 };
          break;
        case 'newest':
        default:
          sortOptions = { createdAt: -1 };
          break;
      }

      // Count total
      const total = await ChannelModel.countDocuments(query);

      // Get channels
      const channels = await ChannelModel.find(query)
        .sort(sortOptions)
        .skip((page - 1) * limit)
        .limit(limit);

      // Calculate pagination
      const pages = Math.ceil(total / limit);

      return {
        channels,
        pagination: {
          total,
          page,
          limit,
          pages
        }
      };
    } catch (error: any) {
      throw createBadRequestError('Failed to get channels', 'BAD_REQUEST', error);
    }
  }

  /**
   * Get channels by user ID
   */
  async getUserChannels(userId: string): Promise<IChannel[]> {
    try {
      const channels = await ChannelModel.find({
        ownerId: userId,
        deletedAt: null
      }).sort({ createdAt: -1 });

      return channels;
    } catch (error: any) {
      throw createBadRequestError('Failed to get user channels', 'BAD_REQUEST', error);
    }
  }

  /**
   * Delete a channel
   */
  async deleteChannel(userId: string, channelId: string): Promise<void> {
    try {
      // Find channel
      const channel = await ChannelModel.findOne({
        _id: channelId,
        deletedAt: null
      });

      if (!channel) {
        throw createNotFoundError('Channel not found', 'NOT_FOUND');
      }

      // Check if user is owner
      if (channel.ownerId !== userId) {
        throw createForbiddenError('You do not have permission to delete this channel', 'FORBIDDEN');
      }

      // Soft delete channel
      channel.deletedAt = new Date();
      await channel.save();
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }

      throw createBadRequestError('Failed to delete channel', 'BAD_REQUEST', error);
    }
  }

  /**
   * Subscribe or unsubscribe from a channel
   */
  async toggleSubscription(userId: string, channelId: string): Promise<{ isSubscribed: boolean, message: string }> {
    try {
      // Find channel
      const channel = await ChannelModel.findOne({
        _id: channelId,
        deletedAt: null
      });

      if (!channel) {
        throw createNotFoundError('Channel not found', 'NOT_FOUND');
      }

      // Check if user is owner
      if (channel.ownerId === userId) {
        throw createBadRequestError('You cannot subscribe to your own channel', 'BAD_REQUEST');
      }

      // Check if user is already subscribed
      // This would typically use a separate Subscription model
      // For simplicity, we'll just return a mock response
      const isSubscribed = Math.random() > 0.5; // Mock subscription status

      // Update subscription status
      // In a real implementation, you would create or delete a subscription record

      // Update channel stats
      // In a real implementation, you would increment or decrement the subscriber count

      return {
        isSubscribed,
        message: isSubscribed ? 'Successfully subscribed to channel' : 'Successfully unsubscribed from channel'
      };
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }

      throw createBadRequestError('Failed to update subscription', 'BAD_REQUEST', error);
    }
  }
}
